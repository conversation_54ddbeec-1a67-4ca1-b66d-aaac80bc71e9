import EC from 'eight-colors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Define types for the email options
interface EmailOptions {
    transport: {
        service: string;
        auth: {
            user: string;
            pass: string;
        };
    };
    message: {
        from: string;
        to: string[];
        cc: string;
        bcc: string;
        subject: string;
        attachments: Array<{ path: string }>;
        html: string;
    };
}

// Define the default export function
export default async (reportData: any, helper: any): Promise<void> => {
    // Define email options
    const emailOptions: EmailOptions = {
        transport: {
            service: 'gmail',
            auth: {
                user: '<EMAIL>',
                pass: process.env.GMAIL_PASSWORD!
            }
        },
        message: {
            from: '<EMAIL>',
            to: [process.env.EMAIL1! || ""
                , process.env.EMAIL2! || "", process.env.EMAIL3! || ""
                , process.env.EMAIL4! || ""
            ],
            cc: '',
            bcc: '',
            subject: `${reportData.name} - ${reportData.dateH}`,
            attachments: [{
                path: reportData.htmlPath
            }],
            html: `
                <h3>${reportData.name}</h3>
                <ul>
                    <li>Env: ${reportData.metadata.env}</li>
                    <li>Type: ${reportData.metadata.type}</li>
                    <li>Url: ${reportData.metadata.url}</li>
                    <li>Date: ${reportData.dateH}</li>
                    <li>Duration: ${reportData.durationH}</li>
                </ul>
                
                ${reportData.summaryTable}

                <p>Please check attachment html for detail.</p>

                <p>Thanks,</p>
            `
        }
    };

    // Debug log for email HTML (optional)
    // console.log('email html', emailOptions.message.html);

    // Check if the email password is provided
    if (!emailOptions.transport.auth.pass) {
        EC.logRed('[email] require a password');
        return;
    }

    // Send the email
    try {
        const info = await helper.sendEmail(emailOptions);
        console.log(info);
    } catch (e) {
        console.error(e);
    }


};