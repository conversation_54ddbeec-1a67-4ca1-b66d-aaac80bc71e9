import { test } from '@playwright/test';
import { BaseTest } from '../../utilities/BaseTest';
import { generateToken } from 'authenticator'

// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
test('test SSO Login', async ({ page }) => {
  test.setTimeout(8 * 60 * 1000);
  await page.goto(process.env.BaseServerURL as string);
  await page.goto(process.env.BaseServerURL as string + 'login/sso?continue=/app/list/d');
  await page.getByRole('link', { name: 'Login with google' }).click();
  await page.getByLabel('Email or phone').click();
  await page.getByLabel('Email or phone').fill('*******@devtron.ai');
  await page.getByLabel('Email or phone').press('Enter');
  await page.getByLabel('Enter your password').fill('******@1234');
  await page.getByLabel('Enter your password').press('Enter');
  await page.getByRole('button', { name: 'Try another way' }).click();
  await page.getByRole('link', { name: 'Get a verification code from' }).click();
  await page.getByLabel('Enter code').click();

  const otp = generateToken(process.env.AUTHENTICATOR_SECRET as string)
  console.log('Generated TOTP code:', otp);
  await page.getByLabel('Enter code').fill(otp);
  await page.getByLabel('Enter code').press('Enter');
});






