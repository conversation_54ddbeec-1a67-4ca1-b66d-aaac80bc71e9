import { LicenseDashboardPage } from '../../Pages/Devtron License/licenseDashboardPage';
import { test } from '../../utilities/Fixtures';
import { ApiUtils } from '../../utilities/ApiUtils';

test('Magic link login first, then Devtron-License Activation flow via SSO',{ tag: '@globalConfigurations' }, async ({ page, AllObjects,request }, testInfo) => {
    test.setTimeout(10 * 60 * 1000);

    if (process.env.isDevtronLicense === 'false') {
        test.skip();
    }

    await test.step('Step 1: Magic link login via email', async () => {
        await page.goto(`${process.env.BASE_SERVER_URL}/license-auth`);
        await AllObjects.productDashboardPage.verifyProductDashboardPage();

        const newPage = await AllObjects.productDashboardPage.clickLicenseButton();
        const licenseDashboardPage = new LicenseDashboardPage(newPage);

        await licenseDashboardPage.sendMagicLinkToEmail(process.env.GMAIL_EMAIL!);
        await licenseDashboardPage.verifyMagicLinkSentAndResend(process.env.GMAIL_EMAIL!);
    });

    await test.step('Step 2: Devtron-License Activationflow via SSO', async () => {
        let apiUtils = new ApiUtils(request);
        let token = await apiUtils.login(process.env.PASSWORD!);
        await page.goto(`${process.env.BASE_SERVER_URL}/license-auth`);
        await AllObjects.productDashboardPage.verifyProductDashboardPage();

        const newPage = await AllObjects.productDashboardPage.clickLicenseButton();
        const licenseDashboardPage = new LicenseDashboardPage(newPage);
        // loginwithGoogle is giving security error, so skipped for testing
        await licenseDashboardPage.loginWithGoogle();
        await licenseDashboardPage.verifyLicenseFormFields();
        await licenseDashboardPage.fillLicenseForm("amit", "kumar", "+91", "**********", "devtron", "India");
        await licenseDashboardPage.enterFingerPrint();
        await licenseDashboardPage.devtronEnterpriseLicensePage();
        await AllObjects.productDashboardPage.enterLicenseKey();
        
        await AllObjects.productDashboardPage.loginAsAdministrator(process.env.PASSWORD!);
        await AllObjects.createAppPage.verifyAboutAndLicenseDialog();
        await AllObjects.createAppPage.devtronStackManager();
        // Add cluster and verify limitations of freemium license
        await apiUtils.addCluster(token, "my-test-cluster", false);
        await AllObjects.createAppPage.verifyLimitationsOfFreemiumLicense();

    });
});