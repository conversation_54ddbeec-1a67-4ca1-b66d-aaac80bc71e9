import { test } from '../../utilities/Fixtures';
import { NodeSSH } from "node-ssh";
import * as dotenv from "dotenv";
import { BaseTest } from '../../utilities/BaseTest';
import { DataSet } from '../../utilities/DataObjectss.ts/multipleTestDataObject';
import { expect } from '@playwright/test';
import { ApiUtils } from '../../utilities/ApiUtils';
import { OctokitClient } from '../../utilities/OctokitClient';
import { ResourceBrowserPage } from '../../Pages/Infrastructure Management/ResourceBrowserPage';
import { request } from 'http';


// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppFixtureNames: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6);
let appsCreated: string[] = [devtronAppFixtureNames];
// Main test case
let imageBuilt: string;
let filterName: string;
let cdFilterDeletion: boolean = false;
let cdFilterUrl: string;
let applicationUrl: string;
let containerRegistryArray: string[] = [];
let virtualEnvPushPull: string;



test.describe('test cases with ci trigger', async () => {

  test.use({ storageState: './LoginAuth.json', triggerCI: [true], devtronApps: [devtronAppFixtureNames], repoNumber: 1 });
  test.beforeEach('end-to-end test', async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
    // Set a timeout for the entire test
    test.setTimeout(15 * 60 * 1000);
    applicationUrl = devtronAppCreation.applicationUrl[0];
    imageBuilt = devtronAppCreation.imageBuilt![0];
  });
  test('helm deployment_oss', async ({ AllObjects, request }) => {
    // Verify Continuous Deployment (CD) status on Workflow Page
    await AllObjects.workflowPage.verifyCdStatusOnWorkflowPage("Succeeded", 1);
    // Select the environment on Deployment History Page
    await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[0]);
    // Verify Deployment Status on Deployment History Page
    await AllObjects.deploymentHistory.verifyDeploymentStatus();
    // Verify Artifacts on Deployment History Page
    await AllObjects.deploymentHistory.verifyArtifactsOnDeploymentHistory(imageBuilt);
    // Verify Source Info on Deployment History Page
    await AllObjects.deploymentHistory.verifySourceInfo(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.BranchName[0]);
    // Verify Deployment Popup Card
    await AllObjects.deploymentHistory.verifyDeploymentPopupCard();
    // Select environment on Application Details Page - CI/CD section
    await AllObjects.appDetailsPageCiCd.selectEnvironment(credentials.EnvNameForCD[0]);
    // Verify Application and Deployment Status on Application Details Page - CI/CD section
    await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus()
    await AllObjects.chartStoreAppDetailsPage.verifyDeploymentStatus();
    // Verify Terminal on Chart Store Application Details Page
    await AllObjects.chartStoreAppDetailsPage.goToTerminal();
    await AllObjects.chartStoreAppDetailsPage.verifyTerminal([credentials.TerminalScript[0]], credentials.TerminalExpectedValue[0]);
    //await AllObjects.chartStoreAppDetailsPage.verifyManifest(imageBuilt);
    await AllObjects.chartStoreAppDetailsPage.clickOnDeployButton();
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0, [imageBuilt.split(':')[1]], [1], 0, true);
    await AllObjects.chartStoreAppDetailsPage.verifyDeploymentStatus('In progress');

  });

  test("Check running pods in VM", async ({page,AllObjects,request}) => {
        const apiUtils=new ApiUtils(request);
        let token=await apiUtils.login(process.env.PASSWORD!);

        await page.goto(`${process.env.BASE_SERVER_URL}license-auth`);
        await AllObjects.productDashboardPage.loginAsAdministrator(process.env.PASSWORD!);
        let fingerprint=await AllObjects.createAppPage.verifyKnowMoreSection();

        dotenv.config(); // Load environment variables

        const ssh = new NodeSSH();

        const VM_IP = process.env.VM_IP;
        const VM_USER = process.env.VM_USER;
        const VM_PASSWORD = process.env.VM_PASSWORD;
    try {
        console.log(`Connecting to VM: ${VM_IP}...`);

        // Establish SSH connection
        await ssh.connect({
        host: VM_IP,
        username: VM_USER,
        password: VM_PASSWORD,
        tryKeyboard: true, // Helps handle password prompts
        });

        console.log("Connected successfully! Running 'kubectl get pods'...");

        // Run 'kubectl get pods' inside the VM
        //const result = await ssh.execCommand("kubectl get pods -n devtroncd");
        const result = await ssh.execCommand("microk8s kubectl get pods -n devtroncd");


        console.log("Command Output:\n", result.stdout);

        if (result.stderr) {
        console.error("Error Output:\n", result.stderr);
        }

        //
        // Execute curl command
        const curlCommand = `curl --location 'http://localhost:9090/license-manager/license' \
                            --header 'token: ${token}'\
                            --header 'api-secret: shivam-secret' \
                            --header 'Content-Type: application/json' \
                            --data-raw '{
                            "fingerPrint": "${fingerprint}",
                            "expiryInDays": 100,
                            "orgAdminEmail": "<EMAIL>",
                            "userDetails": {
                                "companyName": "devtron",
                                "firstName": "Deepak",
                                "lastName": "Panwar",
                                "phoneNumber": "**********",
                                "companyHeadquarter": "India",
                                "jobTitle": "QA"
                            },
                            "gracePeriodInDays": 1
                            }'`;

        const curlResult = await ssh.execCommand(curlCommand);
        console.log("Curl Output:\n", curlResult.stdout);
        if (curlResult.stderr) console.error("Curl Error Output:\n", curlResult.stderr);


        // Close SSH connection
        ssh.dispose();
    } catch (error) {
        console.error("SSH Connection Error:", error);
    }

        await page.goto(`${process.env.BASE_SERVER_URL}license-auth`);
        await AllObjects.productDashboardPage.loginAsAdministrator(process.env.PASSWORD!);
        await AllObjects.createAppPage.verifyKnowMoreSection(100);

    });

    


})


test.afterEach('deletingNodes', async ({ page, AllObjects, context, request }, testInfo) => {
  test.setTimeout(8 * 1000 * 60);
  let apiUtils = new ApiUtils(request);
  try {
    for (var i = 0; i < appsCreated.length; i++) {
      console.log('app we are going to delete is' + appsCreated[i]);
      await apiUtils.deleteAllNodesOfApplication(appsCreated[i], await apiUtils.login(process.env.PASSWORD!));
      if (cdFilterDeletion) {
        await page.goto(cdFilterUrl);
        await AllObjects.filterConditionPage.deleteFilterCondition(filterName);
        cdFilterDeletion = false;
      }
    }
  }
  catch (error) {
    console.log('issue in deleting the app');
  }

  if ((testInfo.title) == "Push Charts using OCI-registry and pull using chart") {
    await apiUtils.deleteEnvFromCluster(await apiUtils.login(process.env.PASSWORD!), virtualEnvPushPull);
    await expect(async () => {
      await AllObjects.containerRegistryPage.goToContainerRegistry();
      await AllObjects.containerRegistryPage.deleteRegistry(containerRegistryArray);
    }).toPass({ timeout: 4 * 1000 * 60 });
  }
})
