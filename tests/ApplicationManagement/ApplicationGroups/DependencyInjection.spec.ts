import { test } from '../../../utilities/Fixtures';
import { DataSet } from '../../../utilities/DataObjectss.ts/multipleTestDataObject';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { BaseTest } from '../../../utilities/BaseTest';


/**
 * Variable Declarations
 */
let data: any
let apiUtils: ApiUtils;
let apiAuthenticationToken: string;
let devtronAppNames: string[] = [BaseTest.generateRandomStringWithCharsOnly(4), BaseTest.generateRandomStringWithCharsOnly(4)];

/**
 * Test Configuration
 */
test.use({ storageState: './LoginAuth.json', devtronApps: devtronAppNames, devtronEnvs: ["automation", "devtron-demo"], triggerCI: [true, true] });

/**
 * Setting up two devtron apps
 */
test.beforeEach('setting up pre-requiste apps', async ({ devtronAppCreation, page }, testInfo) => {
    test.setTimeout(10 * 1000 * 60);
    (testInfo as any).appNames = devtronAppNames;
    await page.goto(devtronAppCreation.applicationUrl[0]);
    data = DataSet.dependencyInjection.dependencyInjectionData(devtronAppCreation.appNames[0], devtronAppCreation.appNames[1]);
})


/**
 * this test case will check dependency injection using 2 apps .
 * we will check details like image present and it's respected app and env
 * we will check both upflow and downflow in both apps 
 */
test('dependency injection', { tag: '@cicd' }, async ({ AllObjects, page, request }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    let dependencyUr: string
    let appId: number | string;
    await test.step('normal-happyflow', async () => {
        await AllObjects.overViewPage.clickOnOverviewTab();
        await AllObjects.overViewPage.addDependency((testInfo as any).appNames[1]);
        await AllObjects.overViewPage.mapEnvironments(data.envMapping);
        await AllObjects.overViewPage.verifyDependencyDetails(data.dependencyDetails);
        dependencyUr = page.url();
        await AllObjects.overViewPage.clickOnAnyParentDependentApp((testInfo as any).appNames[1]);
        await AllObjects.overViewPage.clickOnOverviewTab();
        await AllObjects.overViewPage.dependencyTab.click();
        await AllObjects.overViewPage.verifyDependencyDetails(data.dependencyDetails);
        appId = page.url().split('/')[5];

    });
    await test.step('deleting env and checking', async () => {
        apiUtils = new ApiUtils(request);
        apiAuthenticationToken = await apiUtils.login(process.env.PASSWORD!);
        const workFlowid: number = await apiUtils.getCdWorkflowId(apiAuthenticationToken, appId as string, 'automation');
        await apiUtils.deleteWorkflowCd(apiAuthenticationToken, Number(appId), workFlowid);
        await page.goto(dependencyUr);
        await AllObjects.overViewPage.verifyDependencyDetails(data.dependencydetailsAfterDeletion);
        await AllObjects.overViewPage.editDependencyButton.click();
        await AllObjects.overViewPage.mapEnvironments([{ currentAppEnvName: 'automation', mappedAppEnvName: 'devtron-demo' }], false);
        await AllObjects.overViewPage.removalOfDependencies([(testInfo as any).appNames[1]]);
        await AllObjects.overViewPage.verifyNullStateOfDependencyInjection();
    })

})
test.afterEach('deleting the data', async ({ AllObjects, page }, testInfo) => {
    test.setTimeout(5 * 1000 * 60);
    for (let i = 0; i < (testInfo as any).appNames.length; i++) {
        await apiUtils.deleteAllNodesOfApplication((testInfo as any).appNames[i], apiAuthenticationToken);
    }
})
