import { test } from "../../../utilities/Fixtures";
import { BaseTest } from "../../../utilities/BaseTest";
import { DevtronAllChartVersion, DevtronDeploymentChart } from "../../../enums/ApplicationManagement/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum";
import { DeploymentStrategyEnum } from "../../../enums/ApplicationManagement/Applications/DeploymentStrategyEnum";
import { ApiUtils } from "../../../utilities/ApiUtils";

// Create unique test applications with descriptive namesx
let devtronApps: string[] = [
  'rolling' + BaseTest.generateRandomStringWithCharsOnly(5),
  'recreate' + BaseTest.generateRandomStringWithCharsOnly(5),
  'bluegreen' + BaseTest.generateRandomStringWithCharsOnly(5),
  'canary' + BaseTest.generateRandomStringWithCharsOnly(5)
];

// Initialize required variables
let apiUtils: ApiUtils;
let token: string;
let envIds: number[] = [];
let cmcsConfigDetails = [{ key: 'custom-key', value: 'custom-value', name: 'custom-test', isSecret: false }];
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let environments: string[] = ['automation'];

// Run tests in sequence to avoid conflicts
test.describe.configure({ mode: 'serial' });

test.describe('Canary Deployment', () => {
  test.use({
    storageState: "./LoginAuth.json",
    devtronApps: devtronApps,
    triggerCI: [false, false, false, false],
    deploymentTemplateChart: [
      DevtronDeploymentChart.RolloutDeployment,
      DevtronDeploymentChart.RolloutDeployment,
      DevtronDeploymentChart.RolloutDeployment,
      DevtronDeploymentChart.RolloutDeployment
    ],
    allChartVersion: [
      DevtronAllChartVersion['4.20.0'],
      DevtronAllChartVersion['5.1.0'],
      DevtronAllChartVersion['5.1.0'],
      DevtronAllChartVersion['5.1.0']
    ],
    devtronEnvs: [environments[0]]
  });




  test('canary deployment', { tag: '@cicd' }, async ({ AllObjects, page, devtronAppCreation, request, createExternalCiWorkflow }) => {
    test.setTimeout(20 * 60 * 1000);
    const strategies = Object.values(DeploymentStrategyEnum);



    console.log("Phase 1: Setting up workflows and initial deployments");
    for (let i = 0; i < devtronApps.length; i++) {
      await AllObjects.appConfigurationPage.goToAppConfigurationPage(devtronAppCreation.applicationUrl[i]);
      if (i != 0) {
        await AllObjects.appConfigurationPage.updateCdStrategy('automation', strategies[i]);
      }
    }

    console.log("Phase 2: Configuring application for each strategy");
    for (let i = 0; i < 4; i++) {
      console.log(`Testing ${strategies[i]} with ${environments[0]}`);

      apiUtils = new ApiUtils(request);
      token = await apiUtils.login(process.env.PASSWORD!);

      // Navigate to app configuration
      let appId = await apiUtils.getAppIdFromAppName(devtronApps[i], token);
      await page.goto(devtronAppCreation.applicationUrl[i]);

      // Configure deployment template overrides (setting replica count to 2)
      await AllObjects.appConfigurationPage.appConfigurationTab.click({ delay: 300 });
      await AllObjects.jobsPage.operBaseOrEnvOverRideResources(environments[0]);
      await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({
        configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: false }
      });
      await AllObjects.baseDeploymentTemplatePage.addNewField(['replicaCount'], ['2']);
      await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();

      // Add config/secret to environment
      let envObject = await apiUtils.getEnvObject(token, environments[0]);
      envIds.push(envObject.id);

      for (let config of cmcsConfigDetails) {
        await apiUtils.addConfigOrSecretInEnv([{
          appId: Number(appId),
          token: token,
          key: config.key,
          value: config.value,
          name: config.name,
          isSecret: config.isSecret,
          envId: Number(envObject.id),
        }]);
      }

      // Test Rolling deployment strategy
      if (strategies[i] == DeploymentStrategyEnum.ADD_STRATEGY_ROLLING) {
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
        await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Progressing');

        // Navigate to app deails and verify initial pod state
        await AllObjects.appDetailsPageCiCd.goToAppDetails();
        await AllObjects.appDetailsPageCiCd.selectEnvironment(environments[0]);
        //expect(await AllObjects.appDetailsPageCiCd.getPodCount('old')).toBe(1);
        // Verify final pod distribution (0 old, 2 new)
        await AllObjects.appDetailsPageCiCd.waitForPodDistribution(
          { old: 0, new: 2 }
        );
      }

      // Test Recreate deployment strategy
      if (strategies[i] == DeploymentStrategyEnum.ADD_STRATEGY_RECREATE) {
        // Trigger deployment and verify it's in progress
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
        await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Progressing');

        // Navigate to app details and verify pod count
        await AllObjects.appDetailsPageCiCd.goToAppDetails();
        await AllObjects.appDetailsPageCiCd.selectEnvironment(environments[0]);
        await AllObjects.appDetailsPageCiCd.waitForPodDistribution(
          { old: 0, new: 2 }
        );
        await AllObjects.appConfigurationPage.goToAppConfigurationPage(devtronAppCreation.applicationUrl[i]);
        await AllObjects.appConfigurationPage.updateCdStrategy('automation', DeploymentStrategyEnum.ADD_STRATEGY_CANARY, 0);
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await AllObjects.workflowPage.clickOnSelectImageButtonOnBuildAndDeployPage(0, 0);
        await AllObjects.workflowPage.chooseStrategyAtArtifactListingModall('CANARY');
        await AllObjects.workflowPage.triggerDeploymentAndVerifyToast('Deployment Initiated', false);
        await page.goto(devtronAppCreation.applicationUrl[i])
        await AllObjects.workflowPage.clickOnSelectImageButtonOnBuildAndDeployPage(0, 0);
        await AllObjects.workflowPage.updateStrategyUsingLastSavedAndDeployedConfigModal(strategies[i]);
        await AllObjects.workflowPage.triggerDeploymentAndVerifyToast('Deployment Initiated', false);
      }




      // Test Blue-Green deployment strategy
      if (strategies[i] == DeploymentStrategyEnum.ADD_STRATEGY_BLUE_GREEN) {
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await AllObjects.appConfigurationPage.goToAppConfigurationPage(devtronAppCreation.applicationUrl[i]);
        await AllObjects.appConfigurationPage.updateDeploymentStrategyToCustomYaml(environments[0], 'blueGreen');

        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
        await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Progressing');
        await AllObjects.appDetailsPageCiCd.goToAppDetails();
        await AllObjects.appDetailsPageCiCd.manageBlueGreenModal("Swap traffic", environments[0]);
        await AllObjects.appDetailsPageCiCd.waitForPodDistribution(
          { old: 0, new: 2 }
        );
      }

      // Test Canary deployment strategy
      if (strategies[i] == DeploymentStrategyEnum.ADD_STRATEGY_CANARY) {
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await AllObjects.appConfigurationPage.goToAppConfigurationPage(devtronAppCreation.applicationUrl[i]);
        await AllObjects.appConfigurationPage.updateDeploymentStrategyToCustomYaml(environments[0], 'canary');
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
        await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Progressing');

        await AllObjects.appDetailsPageCiCd.goToAppDetails();
        await AllObjects.appDetailsPageCiCd.selectEnvironment(environments[0]);
        await AllObjects.appDetailsPageCiCd.manageCanaryTrafficModal('rollout-to-next-step', '50')
        await AllObjects.appDetailsPageCiCd.waitForPodDistribution(
          { old: 1, new: 1 }
        );
        await AllObjects.appDetailsPageCiCd.manageCanaryTrafficModal('rollout-to-users', '100')
        await AllObjects.appDetailsPageCiCd.waitForPodDistribution(
          { old: 0, new: 2 }
        );
      }
    }
  });




  //Clean up resources after each test
  test.afterEach('deleting the apps', async ({ request }) => {
    test.setTimeout(5 * 60 * 1000);

    for (let app of devtronApps) {
      let apiUtils = new ApiUtils(request);
      const token = await apiUtils.login(process.env.PASSWORD!);
      await apiUtils.deleteAllNodesOfApplication(app, token);
    }
  });
});