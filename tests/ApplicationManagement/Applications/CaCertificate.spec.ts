import { globalConfigurationsChildComponentEnums } from '../../../enums/Navbar/childComponentEnum';
import { sidePanelParentComponentsEnum } from '../../../enums/Navbar/sidePanelParentComponentsEnum';
import { BaseTest } from '../../../utilities/BaseTest'
import { test } from "../../../utilities/Fixtures";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json"); // This credentials is variable of object type that stores all the data from BaseCredentials
const registryName = "harbor-" + BaseTest.generateRandomStringWithCharsOnly(5);
// Use Jest test framework with storage state for authentication
test.use({ storageState: "./LoginAuth.json", containerRegistryName: registryName, containerRepository: "library/nginx", triggerCI: [false] });

// Define the test function

test.beforeEach("", async ({ page, AllObjects }) => {
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.globalConfigurations, globalConfigurationsChildComponentEnums.containerOciRegistry);
    await AllObjects.containerRegistryPage.selectAndAddRegistry(registryName, "Other.Harbor", "private", credentials.registryData)
})
test("CA-Certificate",{ tag: '@globalConfigurations' }, async ({ page, AllObjects, devtronAppCreation }) => {
    // Set a timeout for the entire test.
    test.setTimeout(5 * 60 * 1000);
    await AllObjects.appConfigurationPage.clickOnScanninWithVulnerabilities();
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Succeeded");
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Succeeded");
});