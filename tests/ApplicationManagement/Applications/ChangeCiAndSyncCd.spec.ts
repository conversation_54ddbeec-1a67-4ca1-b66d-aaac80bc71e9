import { ApiUtils } from "../../../utilities/ApiUtils";
import { BaseTest } from "../../../utilities/BaseTest";
import { changeCiDataObject } from "../../../utilities/DataObjectss.ts/ChangeCiDataObjects";
import { test } from "../../../utilities/Fixtures";
test.use({ storageState: './LoginAuth.json', triggerCI: [false] });

/**
 * Variable Declarations
 */
let appCreated: string = '';
let creteWorkflowNumber2Config: any = process.env.clusterType == "enterprise" ? { workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] }, cdConfig: { envNameForCd: 'playwright-vir', clusterName: 'virtual-cluster-automation', deploymentStrat: 'ROLLING', virtualEnvConfig: { pushOrNot: 'Push to registry', regName: 'bains', repoName: `deep10/${BaseTest.generateRandomStringWithCharsOnly(4)}` }, autoOrManual: 'Auto' } } : { workflowType: 'build-deploy-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] }, cdConfig: { envNameForCd: 'devtron-demo', clusterName: 'default_cluster', helmOrGitops: 'helm', autoOrManual: 'Auto', deploymentStrat: 'ROLLING' } }
let dataObject = changeCiDataObject;
let devtronAppUrl: string = '';
/** */

/**
 * setting up the app and required workflow before running the actual test
 * for oss we will just run change ci as there is no linked cd 
 * we are using diff envs also based on cluster type to save resources
 */
test.beforeEach('setting up the app and workflows', async ({ AllObjects, page, devtronAppCreation }) => {
    appCreated = devtronAppCreation.appNames[0];
    devtronAppUrl = devtronAppCreation.applicationUrl[0];
    await page.goto(devtronAppCreation.applicationUrl[0]);
    let linkedciParentPipelineId = await AllObjects.appConfigurationPage.fetchPipelineIdOfAnyCiNode(0);
    dataObject[0].changeWorkflowConfig.workflowCreationConfig.config!.sourcePipelineName = linkedciParentPipelineId!;
    dataObject[0].changeWorkflowConfig.workflowCreationConfig.config!.appName = appCreated;
    appCreated = devtronAppCreation.appNames[0];
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await AllObjects.appConfigurationPage.createWorkflows(creteWorkflowNumber2Config);
    process.env.clusterType == "enterprise" ? await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'linked-cd', config: { sourceEnv: 'playwright-vir', destEnv: 'env9' } }) : null
})


/**
 * This test case will verify the change ci and sync cd functionality
 * we will change ci to build from source , linked build , job and deploy image
 * and will verify the cd is also changed accordingly
 * we are also verifying correct image is getting shown in linked cd 
 */
test('Change Ci and Sync CD',{ tag: '@cicd' }, async ({ AllObjects, page }) => {
    test.setTimeout(25 * 1000 * 60);
    await page.goto(devtronAppUrl);
    for (let object of dataObject) {
        await AllObjects.appConfigurationPage.clickOnChangeImageSourceButtonOfWorkflow(object.changeWorkflowConfig.workflowNumber);
        await AllObjects.appConfigurationPage.verifyBlockedWorkflowTypeWhileCreatingWorkflows(object.changeWorkflowConfig.visibleAndNotVisibleModals);
        await AllObjects.appConfigurationPage.createWorkflows(object.changeWorkflowConfig.workflowCreationConfig as any, true);
        await AllObjects.appConfigurationPage.verifyDetailsOfWorkflow(object.changeWorkflowConfig.textVerificationDetails);
        if (process.env.clusterType == "enterprise" && object.changeWorkflowConfig.triggerRelatedData.triggerCi) {
            let artifactGenerated: string = '';
            if (object.changeWorkflowConfig.workflowCreationConfig.workflowType == 'build-from') {
                await AllObjects.workflowPage.triggerCiModule(undefined, object.changeWorkflowConfig.triggerRelatedData.workflowNumber);
                await AllObjects.workflowPage.clickOnDetailsOfAnyNode(object.changeWorkflowConfig.triggerRelatedData.nodeNumberToclickOnDetails!, 1);
                await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['Login Succeeded']);
                artifactGenerated = await AllObjects.buildHistoryPage.verifyArtifacts('test').then((key: string) => key.split(':')[1]);
            }
            if (object.changeWorkflowConfig.workflowCreationConfig.workflowType == 'deploy-image') {
                await AllObjects.externalCIPage.clickOnExternalSourceConfigButton();
                await AllObjects.externalCIPage.externalCITriggerWithTryItOut();
                artifactGenerated = `e333f7a9-686-7752`;
            }
            if (object.changeWorkflowConfig.workflowCreationConfig.workflowType == "linked-build") {
                await AllObjects.workflowPage.triggerCiModule(undefined, object.changeWorkflowConfig.triggerRelatedData.workflowNumber);
                await AllObjects.workflowPage.clickOnDetailsOfAnyNode(0, 0);
                await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['Login Succeeded']);
                artifactGenerated = await AllObjects.buildHistoryPage.verifyArtifacts('test').then((key: string) => key.split(':')[1]);
            }
            await AllObjects.workflowPage.verifyCiCdStatus(1, object.changeWorkflowConfig.triggerRelatedData.cdNodeNumberToVerifyStatus!, 'Succeeded');
            await AllObjects.workflowPage.verifyImageOnSelectImageModal(0, [artifactGenerated], 2, [1]);
            await AllObjects.workflowPage.ciModalCloseIcon.click();
        }
    }

})


/**
 * deleting the data
 */
test.afterEach('deleting the data', async ({ request }) => {
    let apiUtils = new ApiUtils(request);
    await apiUtils.deleteAllNodesOfApplication(appCreated, await apiUtils.login(process.env.PASSWORD!));
})
