import { BaseTest } from "../../../utilities/BaseTest";
import { test } from "../../../utilities/Fixtures";
import { SourceTypeEnum } from "../../../enums/ApplicationManagement/Applications/SourceTypeEnum";
import { DeploymentTypeEnum } from "../../../enums/ApplicationManagement/Applications/DeploymentTypeEnum";
import { ApiUtils } from '../../../utilities/ApiUtils';

const devtronAppName = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6);

test.use({
    storageState: './LoginAuth.json',
});

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");

test('verify integrated CI/CD flow along with Node Selector Tag And Missing PVC Tag',{ tag: '@cicd' }, async ({ AllObjects }) => {
    test.setTimeout(10 * 60 * 1000);
    const projectName = credentials.ProjectName;
    const baseServerURL = process.env.BASE_SERVER_URL!;

    await test.step('Create a new Devtron application', async () => {
        await AllObjects.createAppPage.createCustomAppJob('app', devtronAppName, projectName, baseServerURL);
    });

    await test.step('Connect GitHub repository', async () => {
        await AllObjects.gitRepositoryPage.addGitHubRepository(
            process.env.GIT_REPO_URL?.split(',')[1] as string,
            'Github Public'
        );
    });

    await test.step('Configure build settings', async () => {
        await AllObjects.buildConfigurationPage.saveBuildConfigurations('devtron-test', 'test');
    });

    await test.step('Configure deployment template', async () => {
        await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
    });

    await test.step('Configure GitOps and save', async () => {
        await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    });

    await test.step('Create new workflow with source build and deploy setup', async () => {
        await AllObjects.appConfigurationPage.ClickOnNewWorkflowButton();
        await AllObjects.appConfigurationPage.clickBuildAndDeployFromSourceCodeOption();
        await AllObjects.appConfigurationPage.VarifyBuildAndDeployFromSourceCodeModal();
        await AllObjects.appConfigurationPage.selectSpecificSourceType(SourceTypeEnum.BranchFixed);
        await AllObjects.appConfigurationPage.EnterUserSpecificBranchName('main');
        await AllObjects.appConfigurationPage.selectEnvironmentFromDropDown('devtron-demo');
        await AllObjects.appConfigurationPage.selectHelmOrGitops(DeploymentTypeEnum.Helm);
        await AllObjects.appConfigurationPage.ClickCreateWorkflowButton();
    });

    await test.step('Add node selector tag and verify node scheduling failure', async () => {
        await AllObjects.appConfigurationPage.navigateToAppOverviewTab();
        await AllObjects.overViewPage.addTagsViaAppOverviewPage('devtron.ai/node-selector', '{"purpose": "arm-ci"}');
        await AllObjects.workflowPage.triggerCiModule();
        await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

        const expectedMessageForNode =
            "Unschedulable: 0/1 nodes are available: 1 node(s) didn't match Pod's node affinity/selector. " +
            "preemption: 0/1 nodes are available: 1 Preemption is not helpful for scheduling.";
        await AllObjects.buildHistoryPage.verifyWorkerStatusAndMessage('Waiting to start', expectedMessageForNode);
    });

    await test.step('Remove node selector tag', async () => {
        await AllObjects.appConfigurationPage.navigateToAppOverviewTab();
        await AllObjects.overViewPage.removeDesiredTags('devtron.ai/node-selector', '{"purpose": "arm-ci"}');
    });

    await test.step('Add missing PVC tag and verify PVC error', async () => {
        await AllObjects.overViewPage.addTagsViaAppOverviewPage('devtron.ai/ci-pvc-all', 'cache-pvc');
        await AllObjects.workflowPage.triggerCiModule();
        await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

        const expectedMessageForPVC =
            'Unschedulable: 0/1 nodes are available: persistentvolumeclaim "cache-pvc" not found. preemption: 0/1 nodes are available: 1 Preemption is not helpful for scheduling.'
        await AllObjects.buildHistoryPage.verifyWorkerStatusAndMessage("Worker: Waiting to start", expectedMessageForPVC);
    });

    await test.step('Remove PVC tag', async () => {
        await AllObjects.appConfigurationPage.navigateToAppOverviewTab();
        await AllObjects.overViewPage.removeDesiredTags('devtron.ai/ci-pvc-all', 'cache-pvc');
    });

    await test.step('Trigger CI/CD and verify successful pipeline', async () => {
        await AllObjects.workflowPage.triggerCiModule();
        await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Succeeded');
        await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Progressing');
    });

    await test.step('Verify final app and deployment status', async () => {
        await AllObjects.chartStoreAppDetailsPage.verifyDeploymentStatus("Progressing");
    });
});

test.afterEach('Deleting application after verifications', async ({ request }) => {
    const apiUtils = new ApiUtils(request);
    const token = await apiUtils.login(process.env.PASSWORD!);
    await apiUtils.deleteAllNodesOfApplication(devtronAppName, token);
});





