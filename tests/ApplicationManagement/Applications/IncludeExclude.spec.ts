import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { expect } from 'playwright/test';



// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppName: string = 'uiauto-' + BaseTest.generateRandomStringWithCharsOnly(4);
test.use({ storageState: './LoginAuth.json' });




  // test case
  test('Feature Include Exclude Git Commit ', { tag: '@cicd' }, async ({ page, AllObjects, request }, testInfo) => {
    test.setTimeout(15 * 60 * 1000);
    // Step 1: Create a custom application
    console.log("app we are going to create is"+ devtronAppName);
    await AllObjects.createAppPage.createCustomAppJob("app", devtronAppName, credentials.ProjectName, process.env.BASE_SERVER_URL as string);
    // Step 2: Add a GitHub repository
    AllObjects.gitRepositoryPage.addGitHubRepository("https://github.com/badal773/test.git", credentials.GitAccountName);
    // Step 3: Save build configurations
    await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
    // Step 4: Save deployment template
    await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
    // Step 5: save gitops configuration if turned on
    await AllObjects.workflowPage.getCountOfWorkFlow();
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    // Step 6: Create and Trigger new workflow
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] } });
    //   await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 0);
    await AllObjects.gitRepositoryPage.excludeSpecificFileFolder("img");
    await AllObjects.workflowPage.selectCIMaterialAndShowExcludedCommit();
    await AllObjects.workflowPage.verifyDetailsOfCiCommitCard("21609cc");
    await expect(AllObjects.workflowPage.excludedGitCommitCard.nth(0)).toBeVisible({ timeout: 10000 });
     await AllObjects.workflowPage.showIconFilter.click();
    await AllObjects.workflowPage.hideExcludedCommits.click();
    await AllObjects.workflowPage.verifyDetailsOfCiCommitCard("932b2f3");
    await AllObjects.workflowPage.ciModalCloseIcon.click();
    await AllObjects.gitRepositoryPage.excludeSpecificFileFolder("!img");
    await AllObjects.workflowPage.selectCIMaterialAndShowExcludedCommit();
    await AllObjects.workflowPage.ciCommitSearchInputField.fill("932b2f3", { timeout: 300 });
    await AllObjects.workflowPage.page.keyboard.press('Enter');
    await expect(AllObjects.workflowPage.excludedGitCommitCard.nth(0)).toBeVisible({ timeout: 10000 });

  })


  test.afterEach('deleting the apps', async ({ request }) => {
    test.setTimeout(5 * 60 * 1000);
    let apiUtils = new ApiUtils(request);
    const token = await apiUtils.login(process.env.PASSWORD!);
    await apiUtils.deleteAllNodesOfApplication(devtronAppName, token);
  });