import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { ApiUtils } from '../../../utilities/ApiUtils';

// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppNames: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4);
test.use({ storageState: './LoginAuth.json' });
let apiUtils: ApiUtils;
let token: string;
let appId: number;
let externalCiNodeId: number;
// Main test case
test.beforeEach('making devtron app', async ({ AllObjects, request }) => {
  test.setTimeout(15 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronAppNames, credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  // Step 2: Add a GitHub repository
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  // Step 3: Save build configurations
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  // Step 4: Save deployment template
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  // Step 5: save gitops configuration if turned on
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  // Step 6: Create and Trigger new workflow
  await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] }, cdConfig: { envNameForCd: 'automation', clusterName: 'default_cluster', deploymentStrat: 'ROLLING', helmOrGitops: 'helm', autoOrManual: 'Auto' } });

  await apiUtils.deleteAllNodesOfApplication(devtronAppNames, token, false);
  appId = await apiUtils.getAppIdFromAppName(devtronAppNames, token);
  await apiUtils.createNewWorkflowOfExternalCi(token, appId, 1, 'devtron-demo');
  externalCiNodeId = await apiUtils.getObjectDetailsOfExternalCiAndReturnComponentId(token, 0, appId);
  await apiUtils.triggerExternalCi(token, externalCiNodeId, true);


})
test("Rollback_oss", { tag: '@cicd' }, async ({ AllObjects }, testInfo) => {

  (testInfo as any).appCreated = devtronAppNames;
  test.setTimeout(15 * 60 * 1000);
  await AllObjects.rollback.applyRollback();
  await AllObjects.rollback.checkEmptyRollbackImages();
  await AllObjects.rollback.closeRollbackModal();
  await AllObjects.buildHistoryPage.gotoAppConfigurationsPage();
  await AllObjects.jobsPage.operBaseOrEnvOverRideResources('base-configurations');
  await AllObjects.baseDeploymentTemplatePage.editAnyField(["GracePeriod"], ["575835234"]);
  await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate('Updated');

  await apiUtils.triggerExternalCi(token, externalCiNodeId, true);
  await AllObjects.rollback.applyRollback();
  await AllObjects.rollback.selectOptionFromDeployConfigDropdown("Config deployed with selected image");
  await AllObjects.rollback.checkNoOfRollbackImages(1);
  await AllObjects.rollback.checkConfigDiffAndReview();
  await AllObjects.baseDeploymentTemplatePage.checkDiffInCompareAndApproveSection([{ field: "GracePeriod", value: '575835234', isEditable: true, sideToVerifyDiff: 'left' }]);//GracePeriod", "575835234", true, "left
  await AllObjects.rollback.closeRollbackModal();
  await AllObjects.buildHistoryPage.gotoAppConfigurationsPage();
  await AllObjects.jobsPage.operBaseOrEnvOverRideResources('base-configurations');
  await AllObjects.baseDeploymentTemplatePage.editAnyField(["MaxSurge"], ["2"]);
  await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate('Updated');
  await AllObjects.rollback.applyRollback();
  await AllObjects.rollback.selectOptionFromDeployConfigDropdown("Last saved config");
  await AllObjects.rollback.checkConfigDiffAndReview();
  await AllObjects.baseDeploymentTemplatePage.checkDiffInCompareAndApproveSection([{ field: 'MaxSurge', value: "2", isEditable: true, sideToVerifyDiff: 'right' }]);//"portDiscovery", "false", true
  await AllObjects.workflowPage.triggerDeploymentAndVerifyToast('Rollback Initiated');
})
test.afterEach('deleting the data', async ({ AllObjects, page }) => {
  await page.goto(process.env.BASE_SERVER_URL!);
  await AllObjects.createAppPage.searchAndSelectAppsFromList(devtronAppNames);
  await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication('app');
})
