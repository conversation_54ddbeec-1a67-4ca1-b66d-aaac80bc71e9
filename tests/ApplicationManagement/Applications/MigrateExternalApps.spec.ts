import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { validArgoApplicationObject1 } from '../../../utilities/clipboardyYamls.ts/extArgoApplicationObject';
import { sidePanelParentComponentsEnum } from '../../../enums/Navbar/sidePanelParentComponentsEnum';
import { infrastructureManagementChildComponentsEnum } from '../../../enums/Navbar/childComponentEnum';


// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppNames: string = 'mynk20june' + BaseTest.generateRandomStringWithCharsOnly(4);
let namespace: string = 'ns-' + BaseTest.generateRandomStringWithCharsOnly(6);
console.log("Namespace created: " + namespace);
test.use({ storageState: './LoginAuth.json' });
let apiUtils: ApiUtils;
let token: string;
const ArgoCDClusterName = 'testmynk';
const HelmReleaseClusterName = 'default_cluster';
const destNsForValidArgoapp1 = 'autons-759';
let releaseName : string = 'release1' + BaseTest.generateRandomStringWithCharsOnly(4);

let devtronApps: string[] = [
  'github' + BaseTest.generateRandomStringWithCharsOnly(5),
  'bitbucket' + BaseTest.generateRandomStringWithCharsOnly(5),
  'azure' + BaseTest.generateRandomStringWithCharsOnly(5),
  'bitbucketssh' + BaseTest.generateRandomStringWithCharsOnly(5),
  devtronAppNames
];
let appId: number;
let externalCiNodeId: number;
// Main test case
test.describe('Gitops', () => {
test.describe.configure({ mode: 'serial' });
test('Github devtron app',{ tag: '@globalConfigurations' }, async ({ AllObjects, request }) => {
  test.setTimeout(15 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);

  // Get Github ID 
  const githubId = (await apiUtils.getGitopsProviderDetail(token)).find((provider: any) => provider.provider === 'GITHUB')?.id ?? null;
  const requestType = githubId ? 'put' : 'post';
        await apiUtils.addGitopsProvider(
        requestType,                           // requestType
        token,                          // Apitoken
        githubId,                       // id (from the JSON payload)
        'GITHUB',                       // provider
        'mayank-devtron',              // userName
        'https://github.com/',         // host
        '*********************************************************************************************', // token
        'gitHubOrgId',                 // orgIdKeyName
        'mayankgitops',                // orgIdValue
        {                              // options object
          active: true,
          allowCustomRepository: false,
          enableTLSVerification: false,
          tlsConfig: null,
          isCADataPresent: false,
          isTLSCertDataPresent: false,
          isTLSKeyDataPresent: false,
          azureProjectName: '',
          bitBucketWorkspaceId: '',
          bitBucketProjectKey: ''
        }
      );

  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronApps[0], credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  // Step 2: Add a GitHub repository
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  // Step 3: Save build configurations
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  // Step 4: Save deployment template
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  // Step 5: save gitops configuration if turned on
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  // Step 6: Create and Trigger new workflow
  appId = await apiUtils.getAppIdFromAppName(devtronApps[0], token);
  await apiUtils.createNewWorkflowOfExternalCi(token, appId, 1, 'devtron-demo','argo_cd');
  externalCiNodeId = await apiUtils.getObjectDetailsOfExternalCiAndReturnComponentId(token, 0, appId);
  await apiUtils.triggerExternalCi(token, externalCiNodeId, true);
  await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[1]);
  //Verify GitOps Deployment on Deployment History Page
  await AllObjects.deploymentHistory.verifyGitopsDeployment('0');

})

test('Bitbucket devtron app',{ tag: '@globalConfigurations' }, async ({ AllObjects, request }) => {
  test.setTimeout(15 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);

  // Get Bitbucket ID in one line
  const bitbucketId = (await apiUtils.getGitopsProviderDetail(token)).find((provider: any) => provider.provider === 'BITBUCKET_CLOUD')?.id ?? null;
  const requestType = bitbucketId ? 'put' : 'post';

      await apiUtils.addGitopsProvider(
        requestType,                     // requestType (POST for new provider)
        token,                          // Apitoken
        bitbucketId,                    // id (null for new provider)
        'BITBUCKET_CLOUD',              // provider
        'mynk-28',                      // userName
        'https://bitbucket.org/',       // host
        'ATBBeLDYF49fWxPX7YtDULzKrY7r1FB81D81', // token
        'bitBucketWorkspaceId',         // orgIdKeyName (for Bitbucket, we use workspace)
        'newgitopsws',                  // orgIdValue (workspace ID)
        {                               // options object
          active: true,
          allowCustomRepository: false,
          enableTLSVerification: false,
          tlsConfig: null,
          isCADataPresent: false,
          isTLSCertDataPresent: false,
          isTLSKeyDataPresent: false,
          azureProjectName: '',
          bitBucketWorkspaceId: 'newgitopsws',    // Workspace ID
          bitBucketProjectKey: 'TPROJ'            // Project Key
        }
      );

  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronApps[1], credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  // Step 2: Add a GitHub repository
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  // Step 3: Save build configurations
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  // Step 4: Save deployment template
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  // Step 5: save gitops configuration if turned on
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  // Step 6: Create and Trigger new workflow
  appId = await apiUtils.getAppIdFromAppName(devtronApps[1], token);
  await apiUtils.createNewWorkflowOfExternalCi(token, appId, 1, 'devtron-demo','argo_cd');
  externalCiNodeId = await apiUtils.getObjectDetailsOfExternalCiAndReturnComponentId(token, 0, appId);
  await apiUtils.triggerExternalCi(token, externalCiNodeId, true);
  await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[1]);
  // Verify GitOps Deployment on Deployment History Page
  await AllObjects.deploymentHistory.verifyGitopsDeployment('0');
})


test('Azure devtron app',{ tag: '@globalConfigurations' }, async ({ AllObjects, request }) => {
  test.setTimeout(15 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);

  // Get azure ID in one line
  const azureId = (await apiUtils.getGitopsProviderDetail(token)).find((provider: any) => provider.provider === 'AZURE_DEVOPS')?.id ?? null;
  const requestType = azureId ? 'put' : 'post';
      console.log(`azure ID: ${azureId}`); 
      await apiUtils.addGitopsProvider(
        requestType,                    // requestType (PUT for updating existing provider)
        token,                          // Apitoken
        azureId,                        // id
        'AZURE_DEVOPS',                 // provider
        'akshat.sinha',                 // userName
        'https://dev.azure.com/devtron-qa', // host
        '5gZBUEUtde0vzpCbdKkhEuYn7htWIU6dAZ3AhQM9xokRscHrIeBoJQQJ99BBACAAAAA6iRDdAAASAZDO2IgA', // token
        'azureProjectName',             // orgIdKeyName (for Azure DevOps, we use project name)
        'QA',                           // orgIdValue (Azure project name)
        {                               // options object
          active: true,
          allowCustomRepository: false,
          enableTLSVerification: false,
          tlsConfig: null,
          isCADataPresent: false,
          isTLSCertDataPresent: false,
          isTLSKeyDataPresent: false,
          azureProjectName: 'QA',       // Azure project name
          bitBucketWorkspaceId: '',     // Empty for Azure
          bitBucketProjectKey: ''       // Empty for Azure
        }
      );


  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronApps[2], credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  appId = await apiUtils.getAppIdFromAppName(devtronApps[2], token);
  await apiUtils.createNewWorkflowOfExternalCi(token, appId, 1, 'devtron-demo','argo_cd');
  externalCiNodeId = await apiUtils.getObjectDetailsOfExternalCiAndReturnComponentId(token, 0, appId);
  await apiUtils.triggerExternalCi(token, externalCiNodeId, true);
  await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[1]);
  // Verify GitOps Deployment on Deployment History Page
  await AllObjects.deploymentHistory.verifyGitopsDeployment('0');
  console.log('Just ensuring are we reaching this step......... ')
})



test('Bitbucket DC with ssh devtron app',{ tag: '@globalConfigurations' }, async ({ AllObjects, request }) => {
  test.setTimeout(15 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);

  // Get azure ID in one line
  const bitbucketsshid = (await apiUtils.getGitopsProviderDetail(token)).find((provider: any) => provider.provider === 'BITBUCKET_DC')?.id ?? null;
  const requestType = bitbucketsshid ? 'put' : 'post';

  await apiUtils.addGitopsProvider(
          requestType,                     // requestType (POST for new provider)
          token,                          // Apitoken
          bitbucketsshid,                 // id (null for new provider)
          'BITBUCKET_DC',                 // provider
          'admin',                        // userName
          'https://bitbucket-cloud.devtron.info/', // host
          'BBDC-MTY4MjI0NjM0MTEzOk1oRoccXqZlKlHdZKndIKeeh1/5', // token
          'bitBucketProjectKey',          // orgIdKeyName (for Bitbucket DC, we use project key)
          'TEST',                         // orgIdValue (project key value)
          {                               // options object - UPDATED TO MATCH CURL
            active: true,                 
            allowCustomRepository: false, 
            enableTLSVerification: false,
            tlsConfig: null,
            isCADataPresent: false,
            isTLSCertDataPresent: false,
            isTLSKeyDataPresent: false,
            azureProjectName: '',
            bitBucketWorkspaceId: '',
            bitBucketProjectKey: 'TEST',  
            authMode: 'PAT_AND_SSH',      
            sshKey: `********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
            sshHost: undefined        
          }
        );

  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronApps[3], credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  appId = await apiUtils.getAppIdFromAppName(devtronApps[3], token);
  await apiUtils.createNewWorkflowOfExternalCi(token, appId, 1, 'devtron-demo','argo_cd');
  externalCiNodeId = await apiUtils.getObjectDetailsOfExternalCiAndReturnComponentId(token, 0, appId);
  await apiUtils.triggerExternalCi(token, externalCiNodeId, true);
  await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[1]);
  // Verify GitOps Deployment on Deployment History Page
  await AllObjects.deploymentHistory.verifyGitopsDeployment('0');
})

// test case
test('helmrelease devtron app',{ tag: '@cicd' }, async ({ page,AllObjects, request }, testInfo) => {
  test.setTimeout(15 * 60 * 1000);
  process.env.isPacketTesting=="false"? test.skip() : null;
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
  const clusterId = await apiUtils.getClusterId(token, HelmReleaseClusterName);
  let isEnvAlreadYAvailable = await apiUtils.getEnvObject(token,destNsForValidArgoapp1);
    if (!isEnvAlreadYAvailable) {
        await apiUtils.addEnv(token, "post", null, namespace, false, clusterId.id, 'normal');
    } else {
        console.log("Environment already exists: " + namespace);
    }

  await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceBrowser)
  await AllObjects.resourceBrowserPage.goToCluster(HelmReleaseClusterName);
    await AllObjects.resourceBrowserPage.executeCommandsInDefaultTerminal(
    [{ commandToExecute: `helm install ${releaseName} oci://registry-1.docker.io/amit24nov2000/mynk --version 1.0.350-DEPLOY-21609cce-239-77 -n ${namespace}`, resultOfCommandToVerify: '"STATUS: deployed"', count: 0 }]
  );
  //await AllObjects.resourceBrowserPage.createArgoObjectResource(validArgoApplicationObject1);
  
  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronAppNames, credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  // Step 2: Add a GitHub repository
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  // Step 3: Save build configurations
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  // Step 4: Save deployment template
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWithCustomChart('helmrelease-virtualenv-21609cce-239-77')
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  // Step 5: save gitops configuration if turned on
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  // Step 6: Create and Trigger new workflow
  await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] }});
  await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 0);
  await AllObjects.appConfigurationPage.migrateExternalAppsToDevtron("helm",HelmReleaseClusterName,releaseName)
  await AllObjects.appConfigurationPage.closeUpdationModal();
  await AllObjects.appDetailsPageCiCd.goToAppDetails();
  await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();

})
test.skip("argo_oss", { tag: '@cicd' }, async ({ page, AllObjects, request }, testInfo) => {
  test.setTimeout(15 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
  const clusterId = await apiUtils.getClusterId(token, ArgoCDClusterName);
  let isEnvAlreadYAvailable = await apiUtils.getEnvObject(token,destNsForValidArgoapp1);
    if (!isEnvAlreadYAvailable) {
        await apiUtils.addEnv(token, "post", null, destNsForValidArgoapp1, false, clusterId.id, 'normal');
    } else {
        console.log("Environment already exists: " + destNsForValidArgoapp1);
    }

await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceBrowser)
  await AllObjects.resourceBrowserPage.goToCluster(ArgoCDClusterName);
  await AllObjects.resourceBrowserPage.createArgoObjectResource(validArgoApplicationObject1);
  
  // Step 1: Create a custom application
  await AllObjects.createAppPage.createCustomAppJob("app", devtronAppNames, credentials.ProjectName, process.env.BASE_SERVER_URL as string);
  // Step 2: Add a GitHub repository
  AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
  // Step 3: Save build configurations
  await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
  // Step 4: Save deployment template
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWithCustomChart('forvalidauto-1')
  await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
  // Step 5: save gitops configuration if turned on
  await AllObjects.workflowPage.getCountOfWorkFlow();
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  // Step 6: Create and Trigger new workflow
  await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] }});
  await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 0);
  await AllObjects.appConfigurationPage.migrateExternalAppsToDevtron("argocd",ArgoCDClusterName,"validautomation-1")
  await AllObjects.appConfigurationPage.closeUpdationModal();
  await AllObjects.appDetailsPageCiCd.goToAppDetails();
  await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();

})


test.afterEach('deleting the apps', async ({ request }) => {
    test.setTimeout(2 * 60 * 1000);
    
    try {
        let apiUtils = new ApiUtils(request);

        let cleanupToken = await apiUtils.login(process.env.PASSWORD!);
        

        for (let i = 0; i < devtronApps.length; i++) {
            try {
                if (devtronApps[i]) {
                    console.log(`Cleaning up application: ${devtronApps[i]}`);
                    

                    await apiUtils.deleteAllNodesOfApplication(devtronApps[i], cleanupToken, true);
                    console.log(`Successfully deleted app via API: ${devtronApps[i]}`);
                    
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            } catch (appError) {
                console.error(`Error deleting app ${devtronApps[i]}: ${appError.message}`);
            }
        }
        
        if (devtronAppNames) {
            try {
                console.log(`Cleaning up specific test app: ${devtronAppNames}`);
                await apiUtils.deleteAllNodesOfApplication(devtronAppNames, cleanupToken, true);
                console.log(`Successfully deleted specific app: ${devtronAppNames}`);
            } catch (specificAppError) {
                console.error(`Error deleting specific app ${devtronAppNames}: ${specificAppError.message}`);
            }
        }
        
    } catch (globalError) {
        console.error(`Error during global cleanup: ${globalError.message}`);
    }
});

});