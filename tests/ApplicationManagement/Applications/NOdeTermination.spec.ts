import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { DeploymentType, SymbolDeploymentType } from '../../../enums/ApplicationManagement/Applications/DeploymentTriggerEnum';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { InfrastructureManagementUrls } from '../../../enums/UrlNavigations/InfrastructureManagementUrls';
// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppFixtureNames: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6);
// Main test case
test.use({ storageState: './LoginAuth.json', triggerCI: [false] ,devtronApps: [devtronAppFixtureNames]});
test('CI-CD Node Termination', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation }) => {

    await AllObjects.buildHistoryPage.gotoAppConfigurationsPage();
    //Configure Pre-Post CD

    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.EnvNameForCD[0]);
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute','Manual',false);
    await AllObjects.jobsPage.executeCustomScript('Added Sleep (Pre-Build Task)', [`sleep 40\necho $key`], false);
    
    await AllObjects.prePostCiCd.addPrePostTask('post', 'execute','Manual',false);
    await AllObjects.jobsPage.executeCustomScript('Added Sleep (Pre-Build Task)', [`sleep 40\necho $key`], true);
    //Terminating Node 

    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.jobsPage.executeCustomScript('Added Sleep', [`sleep 40\necho $key`], true);
    await AllObjects.workflowPage.triggerCiModule("Pipeline Triggered", 0, [credentials.ciCdNodeTermination]);
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus('Added Sleep (Pre-Build Task)', "Running");
    var pageUrl = await page.url();
    const ciBuildId = await AllObjects.buildHistoryPage.getCiCdTriggerIdFromPageUrl(pageUrl);
    await page.goto(`${process.env.BASE_SERVER_URL}${InfrastructureManagementUrls.resourceBrowserPage}/1/pod/k8sEmptyGroup?searchKey=${ciBuildId}`);
    await AllObjects.resourceBrowserPage.externalDeletionOfResource();
    //Verify Autotrigger and Runtime Param value
    await page.goto(pageUrl);

    await AllObjects.buildHistoryPage.validateBuildTriggerInfoAndUserCountMapping({
        buildTriggerInfo: {
            triggerBy: 'admin',
            triggerNumber: 1,
            status: 'Failed'
        },
        UserCountMapping: [
            { users: 'auto trigger', userCounts: 1 }
        ]
    });
    await AllObjects.buildHistoryPage.clickOnTriggerBuildBytTriggerNumber(0);
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(credentials.ciCdNodeTermination.split(':')[1]);

    AllObjects.workflowPage.goToBuildAndDeployTab();
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
    await AllObjects.workflowPage.clickOnDetailsOfAnyNode(1);
    pageUrl = await page.url();
    const cdTriggerId = await AllObjects.buildHistoryPage.getCiCdTriggerIdFromPageUrl(pageUrl);
    await page.goto(`${process.env.BASE_SERVER_URL}${InfrastructureManagementUrls.resourceBrowserPage}/1/pod/k8sEmptyGroup?searchKey=${cdTriggerId}`)
    await AllObjects.resourceBrowserPage.externalDeletionOfResource();
    //Verify Autotrigger and Runtime Param value
    await page.goto(pageUrl);
    await AllObjects.buildHistoryPage.verifyNumberOfBuildTriggerByUser([{ users: 'auto trigger', userCounts: 1 }]);

})
test.afterEach('deleting the data', async ({ AllObjects, page, request }) => {
    let apiUtils = new ApiUtils(request);
    await apiUtils.deleteAllNodesOfApplication(devtronAppFixtureNames, await apiUtils.login(process.env.PASSWORD!));
})

