import { BaseTest } from "../../../utilities/BaseTest";
import { test } from "../../../utilities/Fixtures"
import { OctokitClient } from "../../../utilities/ThirdPartyClients/OctokitClient";
import { ApiUtils } from "../../../utilities/ApiUtils";
import { sidePanelParentComponentsEnum } from "../../../enums/Navbar/sidePanelParentComponentsEnum";
import { infrastructureManagementChildComponentsEnum } from "../../../enums/Navbar/childComponentEnum";


// const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let appName: string[] = ['recommendation-' + BaseTest.generateRandomStringWithCharsOnly(6)];
test.use({ storageState: './LoginAuth.json', triggerCI: [false], repoNumber: 2, devtronApps: appName })
test.beforeEach('setting up the app', async ({ devtronAppCreation, AllObjects }, testInfo) => {
    test.setTimeout(5 * 1000 * 60);
    // ✅ Update the global credentials with our app name (to use in resource recommender testcase)
    const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
    credentials.GlobalApplicationName = appName[0];
    BaseTest.writeParametersToFile("TestData/BaseCredentials.json", credentials);
    console.log('Updated GlobalApplicationName to:', appName[0]);
    (testInfo as any).appUrl = devtronAppCreation.applicationUrl;
    (testInfo as any).appCreated = appName[0];
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.appConfigurationPage.setCiCdAutoOrManual('Auto');
    await AllObjects.appConfigurationPage.updatePipelineButton.click();
})
test('ci-auto-trigger_oss', { tag: '@cicd' }, async ({ AllObjects }) => {
    test.setTimeout(10 * 1000 * 60);
    let octokitclinetPage = new OctokitClient(process.env.GITHUB_OCTOKIT_TOKEN!);
    let contentBody = BaseTest.generateRandomStringWithCharsOnly(6);
    let latestCommitShaCreated = await octokitclinetPage.createCommit('aman10000q', 'sample-go-app', 'auto-trigger', 'README.md', contentBody);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Running');
    await AllObjects.workflowPage.clickOnSelectMaterialOrSelectImage(true, 0, 0);
    await AllObjects.workflowPage.checkCommitFilters(latestCommitShaCreated.substring(0, 5));
})

//  i will use this resource(devtronapp) for resource recommender 
test.afterEach('deleting the resource', async ({ AllObjects, page, request }, testInfo) => {
    test.setTimeout(5 * 1000 * 60);
    if (process.env.isResourceRecommenderEnabled === 'true') {
        await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceBrowser);
        await AllObjects.resourceBrowserPage.reScanResourceRecommendationForACluster('default_cluster');
    } else {
        console.log('Resource recommender is not enabled');
        let apiUtils = new ApiUtils(request);
        
        await apiUtils.deleteAllNodesOfApplication(appName[0], await apiUtils.login(process.env.PASSWORD!));
    }
})