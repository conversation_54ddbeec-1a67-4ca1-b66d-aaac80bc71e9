import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { ALL } from 'dns';

// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let appName: string[] = ["ui-autom" + BaseTest.generateRandomStringWithCharsOnly(4)];

let apiUtils: ApiUtils;
let token: string;
test.beforeEach('setting up global cm cs', async ({ AllObjects, request }) => {
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);
    let globalCmCs = await apiUtils.getExistingGlobalCmCs(token);
    if (!globalCmCs || !globalCmCs.find(key => key.name == "automation-test")) {
        await apiUtils.createGlobalCmCsEnv(token, 'automation-test', { key: 'testcm', value: 'testvalue' }, 'CONFIGMAP', 'CI/CD');
    }
})
test.use({ storageState: './LoginAuth.json', devtronApps: appName, triggerCI: [false] });
// Main test casen
var appUrl: string
test("Runtime Parameters", {
    tag: '@cicd'
}, async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
    // Set a timeout for the entire test.
    test.setTimeout(20 * 60 * 1000);
    appUrl = devtronAppCreation.applicationUrl[0];
    for (let i = 0; i < credentials.RuntimeParametersData.length; i++) {
        let cdStatusToVerify: any = i == 0 ? 'Not Deployed' : 'Progressing';
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
        if (i != 0)
            await AllObjects.prePostCiCd.deleteTask(credentials.RuntimeParametersData[i - 1].stage.toString());
        await AllObjects.prePostCiCd.addPrePostTask(credentials.RuntimeParametersData[i].stage.toString(), "execute");
        await AllObjects.jobsPage.executeCustomScript(credentials.RuntimeParametersData[i].taskName.toString(), credentials.RuntimeParametersData[i].script as string[], false);
        await AllObjects.prePostCiCd.addOutputVariables([credentials.RuntimeParametersData[i].keyValuePair[0]], ['String']);
        await AllObjects.prePostCiCd.setPassFailureCondition(credentials.RuntimeParametersData[i].condition.toString(), `${credentials.RuntimeParametersData[i].keyValuePair[0]}:${credentials.RuntimeParametersData[i].keyValuePair[1]}`);
        await AllObjects.workflowPage.triggerCiModule("Pipeline Triggered", 0, [`${credentials.RuntimeParametersData[i].keyValuePair[0]}:${credentials.RuntimeParametersData[i].keyValuePair[1]}`]);
        await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
        await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(credentials.RuntimeParametersData[i].verificationTextForLogs, credentials.RuntimeParametersData[i].status.toString(),);
        await AllObjects.workflowPage.verifyCiCdStatus(0, 1, cdStatusToVerify);
        await AllObjects.buildHistoryPage.gotoAppConfigurationsPage();

    }
});
test.afterEach('deleting the data', async ({ AllObjects, page, request }, testInfo) => {
    await apiUtils.deleteAllNodesOfApplication(appName[0], token);
})



