import { applicationManagementChildComponentEnum } from '../../../enums/Navbar/childComponentEnum';
import { sidePanelParentComponentsEnum } from '../../../enums/Navbar/sidePanelParentComponentsEnum';
import { BaseTest } from '../../../utilities/BaseTest';
import { test } from "../../../utilities/Fixtures";
test.use({ storageState: './LoginAuth.json' });     // for sso login
var key: string;
let projectName: string
test('Global Tag', { tag: '@globalConfigurations' }, async ({ page, AllObjects }) => {
    // Set a timeout for the entire test
    test.setTimeout(15 * 60 * 1000);
    key = "version" + BaseTest.generateRandomString(3);
    projectName = 'project-' + BaseTest.generateRandomStringWithCharsOnly(6)
    await AllObjects.projectPage.addProject(projectName, process.env.BASE_SERVER_URL!);
    await BaseTest.clickOnDarkMode(page);
    await BaseTest.clickOnToolTipOkayButton(page);
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement,applicationManagementChildComponentEnum.tagPolicy);
    await AllObjects.globalTag.createTag("Mandatory", key, projectName);
    await AllObjects.createAppPage.checkMandatTagWarning(projectName, key);
})
test.afterEach('deleting the data ', async ({ AllObjects }) => {
   
   await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement,applicationManagementChildComponentEnum.tagPolicy);
    await AllObjects.globalTag.deleteTags(key);
    await AllObjects.projectPage.navigateToProjectsPage(process.env.BASE_SERVER_URL!);
    await AllObjects.projectPage.deleteProject(projectName, process.env.BASE_SERVER_URL!);
})