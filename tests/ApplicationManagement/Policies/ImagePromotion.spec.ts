import { applicationManagementChildComponentEnum } from "../../../enums/Navbar/childComponentEnum";
import { sidePanelParentComponentsEnum } from "../../../enums/Navbar/sidePanelParentComponentsEnum";
import { WorkflowPage } from "../../../Pages/ApplicationManagement/Applications/WorkflowPage";
import { ApiUtils } from "../../../utilities/ApiUtils";
import { BaseTest } from "../../../utilities/BaseTest";
import { dataForProfileCreation, imagePromotionOnEnvLevel } from "../../../utilities/DataObjectss.ts/ImagePromotionDataObject";
import { test } from "../../../utilities/Fixtures";

let envNames: string[] = ['automation', 'devtron-demo', 'env1', 'env2'];
let devtronAppName: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(5);
console.log('app we are creating is' + devtronAppName);
let devtronAppUrl: string = ''
let apiutils: ApiUtils
let token: string
let superAdminToken: string = ''
let dataObject = dataForProfileCreation();
test.use({ storageState: './LoginAuth.json', devtronApps: [devtronAppName], devtronEnvs: ['automation', 'devtron-demo'] });
test.beforeEach('setting up pre-requisite', async ({ AllObjects, request, devtronAppCreation }) => {
    apiutils = new ApiUtils(request);
    token = await apiutils.login(process.env.PASSWORD!);
    devtronAppUrl = devtronAppCreation.applicationUrl[0];
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Succeeded');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 2, 'Succeeded');
    for (let i = 0; i <= 1; i++) {
        let autoOrManual: 'Auto' | "Manual" = i == 0 ? 'Manual' : 'Auto';
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, envNames[i], 0);
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: envNames[2 + i], clusterName: 'default_cluster', helmOrGitops: 'helm', autoOrManual: autoOrManual });
    }
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'automation');
    await AllObjects.appConfigurationPage.setCiCdAutoOrManual('Manual');
    await AllObjects.appConfigurationPage.updatePipelineButton.click();
    await AllObjects.appConfigurationPage.closeUpdationModal();
    superAdminToken = await apiutils.getApiTokenObject(token, 'playwright-super-admin').then(key => key?.tokenValue) as string;

})
test('image promotion', { tag: '@globalCOnfigurations' }, async ({ AllObjects, page, request, browser }) => {
    test.setTimeout(24 * 1000 * 60);
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement,applicationManagementChildComponentEnum.imagePromotion);
    for (let i = 0; i < dataObject.length; i++) {
        await AllObjects.imagePromotionPage.createProfile(dataObject[i]);
        await apiutils.applyImagePromotionProfile(token, [{ appName: devtronAppName, envName: envNames[i] }], [dataObject[i].profileName]);
    }
    await page.goto(devtronAppUrl);
    for (let key of imagePromotionOnEnvLevel().envLevelConfiguration) {
        await AllObjects.workflowPage.promoteAnImage(key.imageSource, key.envConfiguration);
    }
    await AllObjects.workflowPage.checkEligibilityAndApprovePromotedImages(imagePromotionOnEnvLevel().approvalData.user1Data);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Succeeded');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 4, 'Progressing');
    let pageUrlToNavigate = page.url();
    await AllObjects.workflowPage.verifyTagsOnImage('CI Pipeline', true, 0);
    let superAdminPage = await BaseTest.createNewPageWithCustomCookies(browser, superAdminToken);
    await superAdminPage.goto(pageUrlToNavigate);
    let superAdminWorkflowPage = new WorkflowPage(superAdminPage);
    await BaseTest.clickOnDarkMode(superAdminPage);
    await BaseTest.clickOnToolTipOkayButton(superAdminPage);
    await superAdminWorkflowPage.checkEligibilityAndApprovePromotedImages(imagePromotionOnEnvLevel().approvalData.user2Data);
    await superAdminWorkflowPage.verifyImageAndTriggerDeployment(2, [], [], 0, false, { isEligibleForTrigger: false, isTriggerPageVisible: true });
})
test.afterEach('deleting the data', async ({ AllObjects, page }) => {
    await page.bringToFront();
    await apiutils.deleteAllNodesOfApplication(devtronAppName, token);
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement,applicationManagementChildComponentEnum.imagePromotion);
    for (let key of dataObject) {
        await AllObjects.imagePromotionPage.deleteAProfile(key.profileName);
        await page.reload();
    }
})
