import { applicationManagementChildComponentEnum, globalConfigurationsChildComponentEnums } from '../../../enums/Navbar/childComponentEnum';
import { sidePanelParentComponentsEnum } from '../../../enums/Navbar/sidePanelParentComponentsEnum';
import { BaseTest } from '../../../utilities/BaseTest';
import { test } from "../../../utilities/Fixtures";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
test.use({ storageState: './LoginAuth.json' });     // for sso login
let repoName: string;
let repoType: 'public' | 'private';
let repoUrl: string
let chartNameToBeDeployed: string


test('Add Chart Repo_oss_eaMode', { tag: '@globalConfigurations' }, async ({ page, AllObjects }) => {
    test.setTimeout(25 * 60 * 1000);
    for (let i = 0; i <= 1; i++) {
        i == 0 ? repoName = "autorepo-" + BaseTest.generateRandomStringWithCharsOnly(6) : "autorepo-" + BaseTest.generateRandomStringWithCharsOnly(6);
        i == 0 ? repoType = "public" : repoType = "private";
        i == 0 ? repoUrl = 'https://devtron-labs.github.io/helm-pilot/' : repoUrl = 'https://raw.githubusercontent.com/aman10000q/testing/main/';
       await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.chartRepositories);
        chartNameToBeDeployed = BaseTest.generateRandomStringWithCharsOnly(6);
        await AllObjects.addChartRepo.addRepo({ repoName: repoName, repoType: repoType, repoUrl: repoUrl, userName: 'aman10000q', password: process.env.GITHUB_OCTOKIT_TOKEN });
       await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.chartStore);
        await AllObjects.chartStorePage.fetchCharts('memcached', repoName);
        if (i == 0) {
           await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.chartStore);
            await AllObjects.chartStorePage.SelectingChart('memcached', repoName);
            await AllObjects.deployChartPage.deployingChart(chartNameToBeDeployed, credentials.ProjectName, credentials.envNameForCharts, "helm");
           await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.chartRepositories);
            await AllObjects.addChartRepo.repoDelete(repoName, false);
            let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
            await page.goto(process.env.BASE_SERVER_URL! + urlTogo);
            await AllObjects.createAppPage.searchAndSelectAppsFromList(chartNameToBeDeployed, true);
            // Deleting the app
            await AllObjects.deployChartPage.deletingChartStoreApp();
        }
        await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.chartRepositories);
        await AllObjects.addChartRepo.repoDelete(repoName, true);
    }
    // Set a timeout for the entire test
});
test.afterEach('deleting repos', async ({ AllObjects, page }, testInfo) => {
    if (testInfo.status == "failed") {
        try {
            let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
            await page.goto(process.env.BASE_SERVER_URL! + urlTogo);
            await AllObjects.createAppPage.searchAndSelectAppsFromList(chartNameToBeDeployed, true);
            // Deleting the app
            await AllObjects.deployChartPage.deletingChartStoreApp();
        }
        catch (error) {
            console.log('not able to delete the app might be deleted in the test case');
        }
        try {
            await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.chartRepositories);
            await AllObjects.addChartRepo.repoDelete(repoName, true);
        }
        catch (error) {
            console.log('not able to delete the repo');
        }
    }
})

