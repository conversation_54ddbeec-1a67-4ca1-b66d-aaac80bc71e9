import { test } from '../utilities/Fixtures';
import { BaseTest } from '../utilities/BaseTest';
import { Constants } from '../utilities/Constants';
import { ApiUtils } from '../utilities/ApiUtils';
import { hostname } from 'os';
import { preRequistiteDataObject } from '../utilities/DataObjectss.ts/PreRequisiteDataObject';


// Configuration & Data Initialization
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
test.use({ storageState: './LoginAuth.json' });
let cmSecretData = preRequistiteDataObject().cmcsData;
let registryData = preRequistiteDataObject().containerRegistryData;
var requiredUserName: string = preRequistiteDataObject().requiredUserName;
var requiredGitopsProvider: string = preRequistiteDataObject().requiredGitopsProvider;
var requiredPat: string = preRequistiteDataObject().requiredPat;
var requiredGroupId: string = preRequistiteDataObject().requiredGroupId;
var providerUrl: string = preRequistiteDataObject().providerUrl;
var orgIdKeyName: string = preRequistiteDataObject().orgIdKeyName;
let apiUtilPage: ApiUtils;
let requiredEnvCreation = preRequistiteDataObject().envCreationData
let token: string;


test.setTimeout(5 * 60 * 1000);
// Skip test conditionally based on environment variable
if (process.env.isPreRequisteRequired == 'false') {
  test.skip();
}


test('pre-requisite data creation', async ({ request }) => {
  apiUtilPage = new ApiUtils(request);
  token = await apiUtilPage.login(process.env.PASSWORD!);
  //----------------------------addition of chart repo and sync it ----------------------------------------
  let chartRepoObject = await apiUtilPage.getObjectDetailsOfChartRepo(credentials.ChartSource, token);
  if (!chartRepoObject.repoId) {
    await apiUtilPage.addPublicChartRepository(token, credentials.ChartSource, credentials.addChartRepo.PublicRepositoryURL);
    chartRepoObject = await apiUtilPage.getObjectDetailsOfChartRepo(credentials.ChartSource, token);
    await apiUtilPage.syncChartsForAChartRepo(token, chartRepoObject.repoId!);
    console.log('playwright chart repo added successfully! ' + credentials.ChartSource);
  }
  else { console.log('chart repo already exists'); }

  //--------------------------Creating ConfigMaps and Secrets creation -------------------------
  await test.step('creating cm and secret', async () => {
    for (const key of cmSecretData) {
      await apiUtilPage.createResource(token, key.resourceType, key.resourceName, key.keyName, key.valueName);
    }
  });

  //--------------------------------------------------------------------------------------------------




  //------------------------------ Creating Registry Data----------------------------------------------
  await test.step('creating registry', async () => {
    if (!process.env.clusterType?.includes('ea')) {
      const registries = await apiUtilPage.getRegistryList(token);
      for (const key of registryData) {
        let callType: string = "Post";
        if (registries == null || !registries.find((key2) => {
          let result = false;
          if (key2.id == key.registryName) {
            if (key2.ociRegistryConfig.CHART) {
              result = true;
            }
            else {
              callType = "Put";
            }
          }
          return result;
        })) {
          await apiUtilPage.createRegistry(token, key.registryName, key.registryUrl, key.registryUsername, key.registryPassword, key.registrytype, callType);
        }
      }
    }
  });
  //--------------------------------------------------------------------------------------------------------




  //----------------------------- Setting Up Required Environments-------------------------------------------
  for (let i = 0; i < requiredEnvCreation.length; i++) {
    if (requiredEnvCreation[i].clusterName != "default_cluster" && process.env.clusterType != 'enterprise') {
      continue;
    }
    let isEnvAlreadYAvailable = await apiUtilPage.getEnvObject(token, requiredEnvCreation[i].envName);
    if (!isEnvAlreadYAvailable) {
      let clusterId = await apiUtilPage.getClusterId(token, requiredEnvCreation[i].clusterName);
      if (!clusterId) {
        await apiUtilPage.addVirtualCluster(credentials.VirtualClusterName, token);
        clusterId = await apiUtilPage.getClusterId(token, requiredEnvCreation[i].clusterName);
      }
      let clusterType: 'virtual' | 'normal' = requiredEnvCreation[i].clusterName == credentials.VirtualClusterName ? 'virtual' : 'normal';
      await apiUtilPage.addEnv(token, "post", null, requiredEnvCreation[i].envName, true, clusterId.id, clusterType);
      console.log('env we are going to create environment ' + requiredEnvCreation[i].envName);
    }
  }
  //---------------------------------------------------------------------------------------------------------------





  // ----------------------------------------Creating GitOps Data------------------------------------
  if (!process.env.clusterType?.includes('ea') && process.env.isStaging == "false") {
    console.log('going to save gitops provider');
    var currentSetupGitops = await apiUtilPage.getGitopsProviderDetail(token);
    if (currentSetupGitops.length < 1 || !currentSetupGitops.find(key => key.provider == requiredGitopsProvider)) {
      await apiUtilPage.addGitopsProvider('post', token, null, requiredGitopsProvider, requiredUserName, providerUrl, requiredPat, orgIdKeyName, requiredGroupId);
    }
    else {
      var result = currentSetupGitops.find(key => key.provider == requiredGitopsProvider);
      await apiUtilPage.addGitopsProvider('put', token, result.id, requiredGitopsProvider, requiredUserName, providerUrl, requiredPat, orgIdKeyName, requiredGroupId);
    }
  }
  //-----------------------------------------------------------------------------------------------------




  //---------------------------------------- Turning Off Global Image Pull---------------------------------
  if (process.env.clusterType == "enterprise") {
    await apiUtilPage.turnOffGlobalImagePullDigest(token);
  }
  //-----------------------------------------------------------------------------------------------------


  //---------------------------------------- Setting Up Required API Tokens---------------------------------
  if (!process.env.clusterType?.includes('ea')) {
    for (let key of preRequistiteDataObject().apiTokenData) {
      let apiTokenId: number;
      let apiTokenObjectDetails = await apiUtilPage.getApiTokenObject(token, key.tokenName);
      if (!apiTokenObjectDetails) {
        await apiUtilPage.createApiTokenWithoutAnyAccess(token, key.tokenName);
        apiTokenId = await apiUtilPage.getApiTokenObject(token, key.tokenName).then(key => apiTokenId = key?.userId!);
        await apiUtilPage.updateAccessOfApiToken(token, key.superAdmin, key.tokenName, apiTokenId);
      }
    }
  }
  //-----------------------------------------------------------------------------------------------------

  // Setting Up Global ConfigMaps and Secrets
  if (process.env.clusterType == "enterprise") {
    let globalCmCs = await apiUtilPage.getExistingGlobalCmCs(token);
    for (let key of credentials.globalCMCSdata) {
      if (!globalCmCs || !globalCmCs.find(key => key.name == key.name)) {
        await apiUtilPage.createGlobalCmCsEnv(token, key.name, { key: key.data.key, value: key.data.value }, key.configOrSecret, key.cicd);
      }
    }
  }
})