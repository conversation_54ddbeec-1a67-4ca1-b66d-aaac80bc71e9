
import { BaseTest } from '../../utilities/BaseTest';
import { test } from "../../utilities/Fixtures";
import { ALL } from "dns";
import { DataSet } from '../../utilities/DataObjectss.ts/multipleTestDataObject';
import { expect } from 'playwright/test';
var resourceType: string, clusterName: string;
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
test.use({ storageState: './LoginAuth.json', launchOptions: { slowMo: 800 } });
// Test scenario for deploying a resource (deployment) and performing various checks.
test.setTimeout(5 * 1000 * 60);
test("CreateDeploymentAndVerifyAllThePossibleOperation_oss_eaMode", { tag: '@resourceBrowser' }, async ({ AllObjects }, testInfo) => {
  // Set a timeout for the entire test.
  test.setTimeout(5 * 60 * 1000);
  //generate a random deployment name.
  const deployName = "deploy-" + BaseTest.generateRandomStringWithCharsOnly(6);
  clusterName = "default_cluster";
  const replicas: number = +credentials.resourceBrowserData.DeploymentReplicas;
  resourceType = "deployment";
  const namespaceForFilter = "automation";
  (testInfo as any).resourceName = deployName;
  (testInfo as any).resourceType = resourceType;
  // Navigate to the Resource Browser and the default cluster.
  await AllObjects.resourceBrowserPage.goToResourceBrowser(
    process.env.BASE_SERVER_URL as string
  );
  await AllObjects.resourceBrowserPage.goToCluster(clusterName);

  // Check column filter options for the Node view.
  await AllObjects.resourceBrowserPage.checkColumnFilterForNode(10);

  // Create a deployment resource with the generated name.

  await AllObjects.resourceBrowserPage.createResource(
    deployName,
    resourceType,
    credentials.resourceBrowserData
  );
  // Check the default terminal for the deployment.
  await AllObjects.resourceBrowserPage.executeCommandsInDefaultTerminal(
    [{ commandToExecute: `kubectl get ${resourceType} -n automation | grep ${deployName}`, resultOfCommandToVerify: deployName, count: 2 }]
  );

  await AllObjects.resourceBrowserPage.checkDefaultTerminalEvents();

  // Check manifests in the default terminal.
  await AllObjects.resourceBrowserPage.checkDefaultTerminalManifests();

  // Check disconnection of the default terminal.
  await AllObjects.resourceBrowserPage.checkDefaultTerminalDisconnection();

  // Check the count and status of pods for the deployment.
  await AllObjects.resourceBrowserPage.checkPodsCountAndStatus(
    deployName,
    replicas
  );

  // Perform operations in the pod terminal .
  await AllObjects.resourceBrowserPage.podTerminalOperations(
    deployName,
    credentials.TerminalScript[0],
    credentials.TerminalExpectedValue[0]
  );

  // Check the recreation of a pod after deletion.
  await AllObjects.resourceBrowserPage.checkRecreationOfPod(deployName, replicas);
  //Check for namespace filter
  await AllObjects.resourceBrowserPage.applyResourceFilter(namespaceForFilter, deployName);
});

// Test scenario for working with a secret resource, including creation, update, check decoded value and internal deletion.
test("CreateSecretAndVerifyAllThePossibleOperation_oss_eaMode", { tag: '@resourceBrowser' }, async ({ AllObjects }, testInfo) => {
  // Set a timeout for the entire test.
  test.setTimeout(5 * 60 * 1000);
  //generate a random secret name.
  const secretName = "sec-" + BaseTest.generateRandomStringWithCharsOnly(6);
  clusterName = "default_cluster";
  resourceType = "secret";
  (testInfo as any).resourceName = secretName;
  (testInfo as any).resourceType = resourceType;

  // Navigate to the Resource Browser and the default cluster.
  await AllObjects.resourceBrowserPage.goToResourceBrowser(process.env.BASE_SERVER_URL as string);
  await AllObjects.resourceBrowserPage.goToCluster(clusterName);

  // Create a secret resource with the generated name.
  await AllObjects.resourceBrowserPage.createResource(secretName, resourceType, credentials.resourceBrowserData);

  // Update the created secret resource.
  await AllObjects.resourceBrowserPage.updateResource(secretName, credentials.resourceBrowserData);
  // Check the decoded values option and perform an internal deletion of the secret resource.
  await AllObjects.resourceBrowserPage.internalDeletionOfResource(secretName, resourceType, credentials.resourceBrowserData);
});

test('cel filters', { tag: '@resourceBrowser' }, async ({ page, AllObjects }, testInfo) => {
  test.setTimeout(5 * 1000 * 60);
  await page.goto(process.env.BASE_SERVER_URL! + '/resource-browser/1/node/k8sEmptyGroup');
  let deploymentName = BaseTest.generateRandomStringWithCharsOnly(5);
  (testInfo as any).resourceType = "Deployment";
  (testInfo as any).resourceName = deploymentName;
  await AllObjects.resourceBrowserPage.createResource(deploymentName, 'deployment', { DeploymentReplicas: 1, DeploymentContainerName: 'nginx', DeploymentContainerImage: 'nginx:latest' }, 'automation');
  await page.reload();
  let result: { type: string, key?: string, value: string } = { type: '', value: '' };
  for (let i = 0; i < 3; i++) {
    console.log('mine value is' + i);
    let type: string = i == 0 ? 'filter by label' : i == 1 ? "filter by field selector" : 'filter by cel';
    console.log('type value is' + type);
    let value: string = i == 0 ? `app=${deploymentName}` : i == 1 ? `${deploymentName}` : `self.metadata.name=="${deploymentName}"`;
    result.type = type
    result.value = value
    if (i == 1) {
      result.key = 'metadata.name'
    }
    await AllObjects.resourceBrowserPage.searchAnyResourceType('Deployment');
    await AllObjects.resourceBrowserPage.applyCelFilter(result);
    await AllObjects.resourceBrowserPage.checkPodsCountAndStatus(deploymentName, 1, 'Deployment', false);
    await AllObjects.resourceBrowserPage.searchAnyResourceName(deploymentName, true, false);
    // await AllObjects.resourceBrowserPage.searchAnyResourceType('Secret');
    // await AllObjects.resourceBrowserPage.searchAnyResourceName(deploymentName, false, false);
    // await AllObjects.resourceBrowserPage.clearFiltersButton.click();
  }
});
test('download logs files and ephemeral containers_oss_eaMode', { tag: '@resourceBrowser' }, async ({ AllObjects, page }, testInfo) => {
  await page.goto(process.env.BASE_SERVER_URL! + '/resource-browser/1/node/k8sEmptyGroup');
  let deploymentName = BaseTest.generateRandomStringWithCharsOnly(5);
  (testInfo as any).resourceType = "Deployment";
  (testInfo as any).resourceName = deploymentName;
  await AllObjects.resourceBrowserPage.createResource(deploymentName, 'deployment', { DeploymentReplicas: 1, DeploymentContainerName: 'nginx', DeploymentContainerImage: 'quay.io/devtron/test:7d001d98-8733-63818' }, 'automation');
  await page.reload();
  await AllObjects.resourceBrowserPage.searchAnyResourceType('Pod');
  await expect(async () => {
    await AllObjects.resourceBrowserPage.clickOnAnyResource(deploymentName);
    await AllObjects.resourceBrowserPage.podTerminalButton.click();
  }).toPass({ timeout: 2 * 1000 * 60 });
  await test.step('Ephemeral Containers ', async () => {
    for (let i = 0; i <= 1; i++) {
      for (let j = 0; j < 1; j++) {
        if (i == 1 && j == credentials.ephimeralContainer.length - 1) {
          continue;
        }
        let targetContainerName: string = credentials.ephimeralContainer[j].targetContainerName && i == 0 ? credentials.ephimeralContainer[j].targetContainerName : 'nginx';
        await AllObjects.chartStoreAppDetailsPage.launchEphimeralContainer(targetContainerName, credentials.ephimeralContainer[j].containerNamePrefix, Boolean(i), credentials.ephimeralContainer[j].isSuccessfull, credentials.ephimeralContainer[j].containerImage);
        await AllObjects.chartStoreAppDetailsPage.verifyTerminal([credentials.TerminalScript[0]], credentials.TerminalExpectedValue[0]);
        if (credentials.ephimeralContainer[j].isSuccessfull) {
          await page.reload();
          await page.waitForTimeout(2000);
          await AllObjects.chartStoreAppDetailsPage.removeEphimeralContainer(credentials.ephimeralContainer[j].containerNamePrefix);
        }
        await AllObjects.chartStoreAppDetailsPage.verifyContainerListing('Ephemeral', false, credentials.ephimeralContainer[j].containerNamePrefix);
      }
    }
  });
  if (!process.env.clusterType?.includes('oss')) {
    await test.step('download file', async () => {
      await AllObjects.chartStoreAppDetailsPage.downloadFile('/myfile.txt', true, 'Hello, this is my file content!', true);
    });
  }
  await test.step('download logs', async () => {
    await AllObjects.resourceBrowserPage.podLogsButton.click();
    let data = DataSet.DownloadLogs.dataObjectForDownloadLogs();
    for (let key of data) {
      await AllObjects.chartStoreAppDetailsPage.downloadLogs({ valueToSelectFromDropdown: key.valueToSelectFromDropdown, customDataSet: key.customDataSet });
    }
  })
})


test.afterEach('Deleting the resources after verification', async ({ AllObjects, page }, testInfo) => {
  if (testInfo.title != "cel filters") {
    await AllObjects.resourceBrowserPage.clickOnResourceBrowserLink();
    await AllObjects.resourceBrowserPage.goToCluster('default_cluster');
    await AllObjects.resourceBrowserPage.searchAnyResourceType((testInfo as any).resourceType);
    await AllObjects.resourceBrowserPage.searchAnyResourceName((testInfo as any).resourceName, false);
    if (! await AllObjects.resourceBrowserPage.checkNullState()) {
      await AllObjects.resourceBrowserPage.externalDeletionOfResource();
    }
  }
})