import { BaseTest } from '../../utilities/BaseTest';
import { test } from "../../utilities/Fixtures";
import { dataObjectForRelease } from '../../utilities/DataObjectss.ts/dataForRelease';
import { ApiUtils } from '../../utilities/ApiUtils';
import { completeReleaseStatus, resourceStatusToVerfyEnum } from '../../enums/SoftwareReleaseManagement/ReleaseRolloutPageEnum';
import { CompareValueManifestCategorySection } from '../../enums/ApplicationManagement/Applications/CompareValueManifestCategorySectionEnum';
import { sidePanelParentComponentsEnum } from '../../enums/Navbar/sidePanelParentComponentsEnum';
import { applicationManagementChildComponentEnum, softwareReleaseManagementChildComponentsEnum } from '../../enums/Navbar/childComponentEnum';

//----------------------- TEST DATA INITIALIZATION ---------------------//
// Load credentials and generate random test application names
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let applications: string[] = [BaseTest.generateRandomStringWithCharsOnly(5), BaseTest.generateRandomStringWithCharsOnly(5)];
let environments: string[] = ['automation', credentials.VirtualEnvironment]
let applicationUrls: string[]
// Configure test environment with authentication and test data
test.use({ storageState: './LoginAuth.json', devtronApps: applications, devtronEnvs: [environments[0]], newEnvToCreate: environments, triggerCI: [false, false], launchOptions: { slowMo: 900 } });
let filterName: string = BaseTest.generateRandomStringWithCharsOnly(5);
// Create test data for release verification
let data = dataObjectForRelease(applications, credentials.VirtualEnvironment, environments[0]);
//--------------------------------------------------------//


test.describe('Release management test suite', { tag: '@release' }, async () => {
    // Skip tests if running on staging environment
    if (process.env.isStaging == "true") {
        test.skip();
    }
    // Run tests in sequential order
    test.describe.configure({ mode: 'serial' });


    test('release data creation and normal deployment', async ({ AllObjects, page, devtronAppCreation, createExternalCiWorkflow }) => {
        test.setTimeout(30 * 1000 * 60);

        // Configure CD workflow for each application
        for (let i = 0; i <= 1; i++) {
            await page.goto(devtronAppCreation.applicationUrl[i]);
            await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'webhook', 1);
            await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: environments[1], clusterName: credentials.VirtualClusterName, virtualEnvConfig: { pushOrNot: 'Do not push' } });
            await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1);
        }
        applicationUrls = devtronAppCreation.applicationUrl;

        // Create release track and initial release
        await AllObjects.releaseHubPage.createReleaseTrack(data.releaseTrackName);
        await AllObjects.releaseHubPage.createReleaseOrCloneRelease(data.releaseCreation);
        await AllObjects.releaseHubPage.clickOnReleaseTrackToAddApps(data.releaseTrackName);

        // Configure applications, images, tenants and release channels
        await AllObjects.releaseTrackConfigurePage.manageApplications(data.dataForManageApps);
        await AllObjects.releaseHubPage.clickOnSpecificRelease(data.releaseCreation.releaseName, [data.releaseTrackName]);
        await AllObjects.requirementPage.editApplications(false, data.editApplicationsData);
        await AllObjects.requirementPage.setImageForAnApp(data.setImageForAppData);

        // Add tenant and create release channel
        await AllObjects.tenantsPage.addTenant(data.addTenantData);
        await AllObjects.tenantsPage.createReleaseChannel(data.releaseChannelData);
        await AllObjects.tenantsPage.clickOnAnyTenant(data.addTenantData.tenantName);

        // Add installation and map environments
        await AllObjects.installationPage.addInstallation(data.installationData);
        for (let key of data.mapEnvToInstallationData)
            await AllObjects.installationPage.mapEnvironmentsToInstallations(key.installationName, key.mappingData, key.registryData);
        await AllObjects.installationPage.fetchHostNameAndToken(data.mapEnvToInstallationData[1].installationName);

        // Verify environment mappings and trigger deployment
        await AllObjects.releaseHubPage.clickOnSpecificRelease(data.releaseCreation.releaseName, [data.releaseTrackName]);
        await AllObjects.requirementPage.verifyEnvMappingToTenants(data.verifyEnvMappingToTenants);
        await AllObjects.requirementPage.turnOnOffTargetInstallationOnChannelAndTenantLevel({ turnOnTargetInstallation: false, performActionOnChannelLevel: false, tenantName: data.addTenantData.tenantName, installationName: data.installationData[1].installationName });

        // Perform deployment with different statuses
        for (let key of data.normalTriggerDeploymentData) {
            let status: string = key.isHoldOrRescinded ? "On hold" : "Ready for release"
            await AllObjects.requirementPage.changeStatus(status);
            await AllObjects.rolloutPage.turnOnOffInstallationView(false);
            await AllObjects.rolloutPage.triggerDeployment(key);

            // Verify deployment status based on release status
            if (status == "Ready for release") {
                await AllObjects.rolloutPage.verifyRolloutStatus({ 'Ongoing': '1/1' });
                await page.bringToFront();
            }
            else {
                await AllObjects.rolloutPage.verifyVisibilityOfApplicationsOnTriggerReleasePage('playwright-vir', applications[0], false);
            }
        }
    })

    test('Filter testing and virtual deployment verification', async ({ AllObjects, page }) => {
        test.setTimeout(15 * 1000 * 60);
        await BaseTest.clickOnToolTipOkayButton(page);
        await BaseTest.clickOnDarkMode(page);

        // Configure target installation settings
        await AllObjects.releaseHubPage.clickOnSpecificRelease(data.releaseCreation.releaseName, [data.releaseTrackName]);
        await AllObjects.requirementPage.requirementsTab.click();
        await AllObjects.requirementPage.turnOnOffTargetInstallationOnChannelAndTenantLevel({ turnOnTargetInstallation: true, performActionOnChannelLevel: false, tenantName: data.addTenantData.tenantName, installationName: data.installationData[1].installationName });
        await AllObjects.requirementPage.verifyToggleButtonForTargetInstallationIsDisabledOrNot(data.addTenantData.tenantName, data.installationData[0].installationName, true);

        // Test filter functionality and trigger virtual deployment
        await AllObjects.rolloutPage.applyFiltersAndVerifyResult(data.appliedFilterAndVerificationData);
        await AllObjects.requirementPage.changeStatus('Ready for release');
        await AllObjects.rolloutPage.triggerDeployment(data.triggerDeploymentDataForVirtual);
    })

    test('Deployment history and configuration verification', async ({ AllObjects, page }) => {
        test.setTimeout(5 * 1000 * 60);
        await BaseTest.clickOnDarkMode(page);
        await BaseTest.clickOnToolTipOkayButton(page);

        // View and verify deployment history
        await AllObjects.releaseHubPage.clickOnSpecificRelease(data.releaseCreation.releaseName, [data.releaseTrackName]);
        await AllObjects.rolloutHistoryPage.setAppAndEnv(applications[0], environments[0]);
        await AllObjects.rolloutHistoryPage.verifyDeploymenyHistory(true, 1);

        // Verify configuration comparison functionality
        await AllObjects.rolloutHistoryPage.clickOnConfigurationSectionAndSelectPreviousDeployment({ verificationInRelease: undefined, configType: 'Deployment Template', deploymentNumberToCompare: 0, deploymentNumberToCompareWith: 0 });
        await AllObjects.baseDeploymentTemplatePage.checkDiffInCompareAndApproveSection(undefined, false);

        // Edit deployment template and verify changes
        await AllObjects.releaseConfigurationPage.configurationTab.click();
        await AllObjects.rolloutHistoryPage.setAppAndEnv(applications[0], environments[0], false);
        await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } });
        await AllObjects.baseDeploymentTemplatePage.editAnyField(['GracePeriod'], ['31']);
        await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();
        await AllObjects.baseDeploymentTemplatePage.clickOnCompareWithConfigOrManifestAndSelectComparisonDropdowns({ compareWithEntity: environments[0], compareWithEntityPrevDeployments: 2, isManifestOutput: false });
        await AllObjects.baseDeploymentTemplatePage.verifyTheResultOfCompareWithConfigOrManifest({ verificationRelatedData: [{ field: "GracePeriod", value: "31", isEditable: true, sideToVerifyDiff: 'right' }], hasDifference: true, categoryToCheck: CompareValueManifestCategorySection.DeploymentTemplate });
    })


    test('Cloning release and verifying review/update functionality', async ({ AllObjects, page, request }) => {
        test.setTimeout(10 * 1000 * 60);
        await BaseTest.clickOnDarkMode(page);
        await BaseTest.clickOnToolTipOkayButton(page);

        // Clone existing release and verify configuration
        await AllObjects.releaseHubPage.createReleaseOrCloneRelease(data.cloneRelease);
        await AllObjects.releaseHubPage.clickOnSpecificRelease('0.2.0', [data.releaseTrackName]);
        await AllObjects.requirementPage.verifyVisibilityOfAppsOnRequirementPage([{ appName: applications[0], isVisible: true }]);
        await AllObjects.requirementPage.editApplications(true, [{ appName: applications[0], isDeletionBlocked: true }]);
        await AllObjects.requirementPage.verifyTheCountOfStagesOfRelease(1);
        await AllObjects.requirementPage.setImageForAnApp(data.setImageForAnAppCloning);

        // Remove application and verify review and update
        await AllObjects.releaseHubPage.clickOnReleaseTrackToAddApps(data.releaseTrackName);
        await AllObjects.releaseTrackConfigurePage.removeApplications(applications[1]);
        await AllObjects.releaseHubPage.clickOnSpecificRelease('0.2.0', [data.releaseTrackName]);
        await AllObjects.requirementPage.reviewAndUpdate();
        await AllObjects.requirementPage.verifyVisibilityOfAppsOnRequirementPage([{ appName: applications[1], isVisible: false }])

        // Configure deployment and trigger release
        await AllObjects.requirementPage.turnOnOffTargetInstallationOnChannelAndTenantLevel({ turnOnTargetInstallation: false, performActionOnChannelLevel: false, tenantName: data.addTenantData.tenantName, installationName: data.installationData[1].installationName });
        await AllObjects.requirementPage.changeStatus('Ready for release');
        await AllObjects.rolloutPage.triggerDeployment({ envSelectionRelatedData: [{ envName: 'automation', appName: applications[0], isBlocked: false }], feasibilityPage: { canTriggerCount: 1, cantTriggerCount: 0 }, isHoldOrRescinded: false, isVirtualEnv: false });

        // Verify deployment status and application health
        await AllObjects.rolloutPage.turnOnOffAutoPolling(true);
        await AllObjects.rolloutPage.clickOnDeploymentStatusButtonInAnApplicationRow('automation', applications[0]);
        await AllObjects.rolloutPage.verifyResourceStatusOnAppDetailsCard({ "Application Status": resourceStatusToVerfyEnum.healthy, 'Deployment': resourceStatusToVerfyEnum.healthy });
        // await AllObjects.requirementPage.verifyCompleteReleaseStatus(completeReleaseStatus.completelyRelease, 6 * 1000 * 60);

        // Test hibernation functionality
        let apiUtls = new ApiUtils(request);
        let appId = await apiUtls.getAppIdFromAppName(applications[0], await apiUtls.login(process.env.PASSWORD!));
        let envObject = await apiUtls.getEnvObject(await apiUtls.login(process.env.PASSWORD!), environments[0]);
        await apiUtls.HibernateOrUnhibernateDevtronApp(appId, envObject.id, true, await apiUtls.login(process.env.PASSWORD!));
        await AllObjects.rolloutPage.clickOnDeploymentStatusButtonInAnApplicationRow('automation', applications[0]);
        await AllObjects.rolloutPage.verifyResourceStatusOnAppDetailsCard({ "Application Status": resourceStatusToVerfyEnum.hibernating });
    })

    test('Pre/Post CD hooks - testing success and failure scenarios', async ({ AllObjects, page }) => {
        test.setTimeout(25 * 1000 * 60);

        // Test both failure (i=0) and success (i=1) scenarios
        for (let i = 0; i <= 1; i++) {
            // Configure task based on test scenario (fail/succeed)
            let taskToExecute: string[] = i == 0 ? ['exit 1'] : ['echo "hello world"'];
            let rolloutStatusToVerify: { [key: string]: string } = i == 0 ? { 'Failed (Any stage)': '1/1' } : { 'Ongoing': '1/1' };
            let prePostCdStatusToVerify: any = i == 0 ? 'Failed' : 'Succeeded';
            let cdStatusToVerify: any = i == 0 ? 'Succeeded' : 'Progressing';

            // Configure pre/post CD hooks for the application
            await page.goto(applicationUrls[0]);
            await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, environments[0]);
            await AllObjects.prePostCiCd.deleteTask('pre');
            await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
            await AllObjects.jobsPage.executeCustomScript('pre', taskToExecute, false);
            await AllObjects.prePostCiCd.deleteTask('post');
            await AllObjects.prePostCiCd.addPrePostTask('post', 'execute');
            await AllObjects.jobsPage.executeCustomScript('post', taskToExecute, true);
            await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Succeeded');

            // Navigate to release and trigger deployment
            await AllObjects.releaseHubPage.navigateToReleaseHubPage();
            await AllObjects.releaseHubPage.clickOnSpecificRelease('0.2.0', [data.releaseTrackName]);
            await page.reload();
            await AllObjects.requirementPage.verifyCompleteReleaseStatus(completeReleaseStatus.parrtiallyRelease);

            // Trigger pre and post deployment hooks
            await AllObjects.rolloutPage.triggerDeployment({ envSelectionRelatedData: [{ envName: environments[0], appName: applications[0], isBlocked: false }], feasibilityPage: { canTriggerCount: 1, cantTriggerCount: 0 }, isHoldOrRescinded: false, isVirtualEnv: false }, 'Pre');
            if (i === 1) {
                await AllObjects.rolloutPage.turnOnOffAutoPolling(true);
                await AllObjects.requirementPage.verifyCompleteReleaseStatus(
                    completeReleaseStatus.completelyRelease,
                    13 * 1000 * 60
                );
            }
            // await AllObjects.rolloutPage.triggerDeployment({ envSelectionRelatedData: [{ envName: environments[0], appName: applications[0], isBlocked: false }], feasibilityPage: { canTriggerCount: 1, cantTriggerCount: 0 }, isHoldOrRescinded: false, isVirtualEnv: false }, 'Post');
            // Verify statuses based on expected behavior (fail/succeed)
            if (i == 0) {
                i == 0 ? await AllObjects.rolloutPage.verifyRolloutStatus(rolloutStatusToVerify) : null;
                await page.goto(applicationUrls[0]);
                await AllObjects.workflowPage.verifyCiCdStatus(0, 0, prePostCdStatusToVerify);
                await AllObjects.workflowPage.verifyCiCdStatus(0, 1, cdStatusToVerify);
            }
        }
    })

    test('Deployment feasibility checking with filters', async ({ AllObjects, page }) => {
        test.setTimeout(10 * 1000 * 60);

        // Create filter condition for deployment feasibility
        await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.applicationManagement, applicationManagementChildComponentEnum.filterConditions);
        await AllObjects.filterConditionPage.createNewFilterCondition(true, filterName, 'containerImage.contains("quay")', false, applications[0], environments[0]);

        // Delete older release and test deployment with filters
        await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.softwareReleaseManagement, softwareReleaseManagementChildComponentsEnum.releaseHub);
        await AllObjects.releaseHubPage.clickOnSpecificRelease('0.1.0', [data.releaseTrackName]);
        await AllObjects.requirementPage.deleteReleaseFromReleaseTrack('0.1.0', false);
        await AllObjects.rolloutPage.triggerDeployment(data.triggerDeploymentDataFeasibility);
    })

    test.afterEach('Cleanup test resources', async ({ AllObjects, request, page }, testInfo) => {
        test.setTimeout(15 * 1000 * 60);
        if (testInfo.status == "failed" || testInfo.title == "Deployment feasibility checking with filters") {
            let apiUtils = new ApiUtils(request);
            try {
                // Clean up release resources 
               await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.softwareReleaseManagement, softwareReleaseManagementChildComponentsEnum.releaseHub);
                await BaseTest.clickOnToolTipOkayButton(page);
                await BaseTest.clickOnDarkMode(page);
                await AllObjects.tenantsPage.deleteReleaseChannel(data.installationData[0].releaseChannel!);
                await AllObjects.tenantsPage.deleteAtenant(data.addTenantData.tenantName);
            }
            catch (error) {
                console.log('we were not able to delete the release related data so moving with other deletions');
            }
            try {
                // Clean up applications
                for (let i = 0; i < applications.length; i++) {
                    console.log('deleting the app' + applications[i]);
                    await apiUtils.deleteAllNodesOfApplication(applications[i], await apiUtils.login(process.env.PASSWORD!));
                }
            }
            catch (error) {
                console.error('not able to delete the apps');
            }
            // Clean up filter
            await apiUtils.deleteCdFilter(filterName, await apiUtils.login(process.env.PASSWORD!));
        }
    })
})