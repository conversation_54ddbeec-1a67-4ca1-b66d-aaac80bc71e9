import { ResourceWatcherPage } from '../../Pages/InfrastructureManagement/ResourceWatcherPage';
import { BaseTest } from '../../utilities/BaseTest';
import { test } from "../../utilities/Fixtures";
import { RunbookTrigger } from '../../enums/InfrastructureManagement/ResourceWatcher/RunbookTriggerEnum';
import { NamespaceSelectionOption } from '../../enums/InfrastructureManagement/ResourceWatcher/NamespaceSelectionOptionEnum';
import { ApiUtils } from '../../utilities/ApiUtils';
import { sidePanelParentComponentsEnum } from '../../enums/Navbar/sidePanelParentComponentsEnum';
import { infrastructureManagementChildComponentsEnum } from '../../enums/Navbar/childComponentEnum';

let devtronJobName = 'deepnew' + BaseTest.generateRandomStringWithCharsOnly(4);
test.use({ storageState: './LoginAuth.json', jobName: [devtronJobName] });
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
const deploymentName = "deployment" + BaseTest.generateRandomStringWithCharsOnly(6);
const resourceType = "deployment";
const resourceNamespace = 'env10';
const watcherName = `watcher-${BaseTest.generateRandomStringWithCharsOnly(4)}-${Date.now()}`;
let resourceWatcherPage: ResourceWatcherPage;

// In your test
test.skip('verify my app recommendations', async ({ request }) => {
    let apiUtils = new ApiUtils(request);
    let token = await apiUtils.login(process.env.PASSWORD!);
        await apiUtils.getResourceRecommendationsForDeployment(
        token,
        'recommendation-omeimu-automation',
        'automation'
    );
    

});

if(process.env.isPacketTesting=="false"){
  test.skip();
}

test('Resource Watcher: Create watcher, configure intercepter, add runbook, verify intercepted events then clean up',{ tag: '@globalConfigurations' }, async ({ page, AllObjects, jobCreation }) => {
  test.setTimeout(5 * 60 * 1000);

  resourceWatcherPage = new ResourceWatcherPage(page);
  const description = 'Test watcher description';
  const clustersToSelect = ['default_cluster'];
  const namespacesToSelect = ['env10'];
  const resourceKinds = ['Deployment'];
  const celExpression = 'true';
  const jobEnvironment = 'devtron-ci';

  await test.step('Step 1: Navigate to Resource Watcher Page', async () => {
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceWatcher);
    await resourceWatcherPage.clickCreateWatcherButton();
    await resourceWatcherPage.verifySidebarTabsVisible();
  });

  await test.step('Step 2: Fill in basic details and create watcher', async () => {
    await resourceWatcherPage.nameInput.fill(watcherName);
    await resourceWatcherPage.descriptionTextarea.fill(description);
  });

  await test.step('Step 3: Select specific clusters', async () => {
    await resourceWatcherPage.selectSpecificClusters(clustersToSelect);
  });

  await test.step('Step 4: Select specific namespaces for cluster', async () => {
    await resourceWatcherPage.selectSpecificNamespacesToWatchForSpecificCluster(
      clustersToSelect[0],
      namespacesToSelect,
      NamespaceSelectionOption.SPECIFIC_NAMESPACES
    );
  });

  await test.step('Step 5: Verify namespace selection', async () => {
    await resourceWatcherPage.verifyNamespaceSelection(clustersToSelect[0], namespacesToSelect);
  });

  await test.step('Step 6: Configure intercept changes and verify UI', async () => {
    await resourceWatcherPage.goToInterceptChanges();
    await resourceWatcherPage.verifyInterceptChangesUI();
    await resourceWatcherPage.setInterceptChangeConfig(resourceKinds, celExpression);
  });

  await test.step('Step 7: Configure runbook trigger and create watcher', async () => {
    await resourceWatcherPage.goToExecuteRunbook();
    await resourceWatcherPage.selectRunbookTriggerOption(RunbookTrigger.DEVTRON_JOB);
    await resourceWatcherPage.selectDevtronJob(devtronJobName,jobCreation.piplineNamesCreated[0]);
    await resourceWatcherPage.selectJobEnvironment(jobEnvironment);
    await resourceWatcherPage.clickCreateWatcherInsideModalButton();
    await resourceWatcherPage.verifyThatClusterIsVisibleInWatcherList(watcherName);
  });
  await test.step('Step 8: Create Deployment via Resource Browser to verify the intercepted events after applying the filters', async () => {
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceBrowser);
    await AllObjects.resourceBrowserPage.goToCluster("default_cluster");

    await AllObjects.resourceBrowserPage.createResource(deploymentName, resourceType, credentials.resourceBrowserData, resourceNamespace);

    //await AllObjects.resourceBrowserPage.closeCreateKubernetesResourceModal();//Commenting bcz we are already closing the modal.
    await AllObjects.resourceBrowserPage.internalDeletionOfResource(deploymentName, resourceType, credentials.resourceBrowserData);
    const clusterNames = ['default_cluster'];
    const nameSpaces = ['env10'];
    const actionTypes = ['Deleted','Created','Updated'];
    const watchers = [watcherName];
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceWatcher);
    await resourceWatcherPage.clickOnInterceptedEventsTabButton();
   for (const action of actionTypes) {
        await resourceWatcherPage.selectSpecificClustersToApplyFilter(clusterNames);
        await resourceWatcherPage.selectNamespacesToApplyFilter(nameSpaces);
        await resourceWatcherPage.selectSpecificActionTypesToApplyFilter([action]);
        await resourceWatcherPage.selectWatchers(watchers);
        await resourceWatcherPage.verifyFilteredInterceptedEvents('default_cluster', 'env10', action);
        await resourceWatcherPage.clearAllFiltersButton.click()
    }
  });

  await test.step('Step 9: verify search functionality on events list', async () => {
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceWatcher);
    await resourceWatcherPage.clickOnInterceptedEventsTabButton();
    await resourceWatcherPage.searchAndVerifyInterceptedRows(`apps/deployment/${deploymentName}`, watcherName, `default_cluster/${resourceNamespace}`);
  });

  await test.step('Step 10: verify delete watcher functionality', async () => {
    await resourceWatcherPage.deleteWatcherByName(watcherName);
  });
});
test.afterEach('deleting the job', async ({ page, AllObjects }) => {
  try {
    await page.goto(process.env.BASE_SERVER_URL!);
    await AllObjects.createAppPage.searchAndSelectAppsFromList('deep', false, 'job');
    await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication("job", false);
  }
  catch (error) {
    console.log('not able to delete the job');
  }
  try {
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceWatcher);
    await resourceWatcherPage.deleteWatcherByName(watcherName);
  }
  catch (error) {
    console.log('not able to delete the watcher');
  }

})

