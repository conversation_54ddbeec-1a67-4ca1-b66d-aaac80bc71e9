import { test } from "../../../utilities/Fixtures";
import { InfraProvisioningPage } from '../../../Pages/GlobalConfiguration/Clutsers and Environments/InfraProvisioningPage';
import { ClusterInfoOnInstallationPageDTO } from '../../../DTOs/Global Configurations/ClustersAndEnvironments/InfraProvisioning/ClusterInfoOnInstallationPageDTO';
import { BaseTest } from "../../../utilities/BaseTest";
import { Page } from "playwright/test";
import { chromium } from 'playwright'; 
import { sidePanelParentComponentsEnum } from "../../../enums/Navbar/sidePanelParentComponentsEnum";
import { infrastructureManagementChildComponentsEnum } from "../../../enums/Navbar/childComponentEnum";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase()+'-cluster';
let lastPage: Page | undefined;
let isClusterDeleted = false; 
test.use({ storageState: './LoginAuth.json', launchOptions: { slowMo: 800 } });
test.setTimeout(20 * 60 * 1000); 

let infraPage: InfraProvisioningPage;

test.beforeEach(async ({ page }) => {
  infraPage = new InfraProvisioningPage(page);
  await infraPage.goToInfraProvisioningForm(process.env.BASE_SERVER_URL as string);
});

test('verify create cluster with valid configuration',{ tag: '@globalConfigurations' }, async ({ page, AllObjects }) => {
  lastPage = page;
  const infraPage = new InfraProvisioningPage(page);
  const clusterInfoOnInstallationPageDTO: ClusterInfoOnInstallationPageDTO = {
    clusterName: clusterName,
    VPC_CIDR: '10.0.0.0/16',
    Region: 'ap-south-2',
    Team: 'Backend_Team',
    Environment: 'non-production',
    Cluster_Version: '1.31',
    Authentication_Mode: "API_AND_CONFIG_MAP",
    Enable_IRSA: true,
    Allow_Public_Access: true
  };

  await infraPage.createEKSCluster(clusterInfoOnInstallationPageDTO);
  await infraPage.verifyClusterDetailsOnInstallationModal(clusterInfoOnInstallationPageDTO);
  await infraPage.VerifyClusterCreationSuccessfully(20 * 60 * 1000);

  const secretName = "sec-" + BaseTest.generateRandomStringWithCharsOnly(6);
  const resourceType = "secret";
  const resourceNamespace ='default'


  // Navigate to the Resource Browser and the default cluster.
  await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.infrastructureManagement,infrastructureManagementChildComponentsEnum.resourceBrowser)
  await AllObjects.resourceBrowserPage.goToCluster(clusterName);

  // Create a secret resource with the generated name.
  await AllObjects.resourceBrowserPage.createResource(secretName, resourceType, credentials.resourceBrowserData,resourceNamespace);

  // Update the created secret resource.
  await AllObjects.resourceBrowserPage.updateResource(secretName, credentials.resourceBrowserData,resourceNamespace);
  // Check the decoded values option and perform an internal deletion of the secret resource.
  await AllObjects.resourceBrowserPage.internalDeletionOfResource(secretName, resourceType, credentials.resourceBrowserData);

  await infraPage.deleteCluster(clusterName);
  isClusterDeleted = true;

  });


  test.afterAll(async () => {
    if (!clusterName) {
      console.log('[afterAll] No cluster name available. Skipping cleanup.');
      return;
    }
  
    if (isClusterDeleted) {
      console.log(`[afterAll] Cluster "${clusterName}" already deleted during test. Skipping cleanup.`);
      return;
    }
  
    console.log(`[afterAll] Running cleanup for cluster: ${clusterName}`);
  
    try {
      const browser = await chromium.launch({ headless: true });
      const context = await browser.newContext({ storageState: './LoginAuth.json' });
      const page = await context.newPage();
  
      const infraPage = new InfraProvisioningPage(page);
      await infraPage.goToInfraProvisioningForm(process.env.BASE_SERVER_URL as string);
      await infraPage.deleteCluster(clusterName);
  
      console.log(`[afterAll] Cluster "${clusterName}" deleted successfully.`);
      await browser.close();
    } catch (error) {
      console.error(`[afterAll] Failed to delete cluster "${clusterName}":`, error);
    }
  });
  

  