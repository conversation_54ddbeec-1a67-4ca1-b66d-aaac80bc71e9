import { BaseTest } from '../../../utilities/BaseTest'
import { test } from "../../../utilities/Fixtures";


// Define constants for cluster and environment names, types, and credentials
const clusterName = "cluster-" + BaseTest.generateRandomStringWithCharsOnly(6);
const environmentName = "env-" + BaseTest.generateRandomStringWithCharsOnly(6);
const updatedClusterName = "cluster-" + BaseTest.generateRandomStringWithCharsOnly(6);
const clusterType = "virtual";
const environmentType = "production";

// Use Jest test framework with storage state for authentication
test.use({ storageState: "./LoginAuth.json" });

// Define the test function
test("Create,Update,Verify and Delete --Cluster and Environment_oss", { tag: '@globalConfigurations' }, async ({ AllObjects }) => {
    // Set a timeout for the entire test.
    test.setTimeout(5 * 60 * 1000);

    // Navigate to global configurations and clusters/environments page
    await AllObjects.globalConfigPage.goToGlobalConfigurations(process.env.BASE_SERVER_URL as string);
    await AllObjects.clusterAndEnvironmentsPage.goToClustersAndEnvironmentsPage();

    // Add a virtual cluster
    await AllObjects.clusterAndEnvironmentsPage.addVirtualCluster(clusterName);

    // Create an environment within the cluster
    await AllObjects.clusterAndEnvironmentsPage.clickOnAddEnvironmentButtonOnClusterPage(clusterName);
    await AllObjects.clusterAndEnvironmentsPage.createEnvironment(clusterName,clusterType,environmentName,environmentType);
    //await AllObjects.clusterAndEnvironmentsPage.createEnvironment(clusterName, clusterType, environmentName, environmentType);

    // Update the environment
    await AllObjects.clusterAndEnvironmentsPage.updateEnvironment(environmentName);

    // Search for the created environment within the cluster
    await AllObjects.clusterAndEnvironmentsPage.searchEnvironment(environmentName, clusterName);

    // Create an environment based on specific requirements
    await AllObjects.clusterAndEnvironmentsPage.createEnvironmentBasedOnRequirement([environmentName], clusterName, clusterType, [environmentType]);

    // Update the virtual cluster
    await AllObjects.clusterAndEnvironmentsPage.updateVirtualCluster(clusterName, updatedClusterName);

    // Delete the created environment
    await AllObjects.clusterAndEnvironmentsPage.deleteEnvironment([environmentName]);

    // Delete the virtual cluster
    await AllObjects.clusterAndEnvironmentsPage.deleteCluster(updatedClusterName);
});


