import { test } from '../../../fixtures/InfraProvisioningPageFixture'
import { ConnectClusterToDevtronPage } from '../../../Pages/Global Configuration/Clutsers and Environments/ConnectClusterToDevtronPage';
import { BaseTest } from '../../../utilities/BaseTest';
import path from 'path';
import fs from 'fs/promises';
import os from 'os';
test.use({ storageState: "./LoginAuth.json" });
test.describe('ConnectClusterToDevtron', () => {
  let clusterPage: ConnectClusterToDevtronPage;

  const serverUrl = process.env.SERVER_URL_TO_ADD_CLUSTER;
  if (!serverUrl) {
    throw new Error('SERVER_URL is not defined');
  }
  const ClusterToken = process.env.CLUSTER_TOKEN;
  if (!ClusterToken) {
    throw new Error('CLUSTER_TOKEN is not defined');
  }

  test.beforeEach(async ({ page }) => {
    clusterPage = new ConnectClusterToDevtronPage(page);
    await page.goto(`${process.env.BASE_SERVER_URL}/global-config/cluster-env/create/cluster/connect-cluster`);
  });

  test('connect using manual kubeconfig YAML',{tag: '@globalConfigurations'}, async ({ deleteCluster }) => {
    test.setTimeout(3 * 60 * 1000);
    await clusterPage.selectUseKubeconfig();
    const clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase() + '-manual';

    const yaml = ConnectClusterToDevtronPage.getKubeconfigYaml(clusterName, serverUrl, ClusterToken);
    await clusterPage.fillKubeconfigManually(yaml);
    await clusterPage.clickSaveButtonForKubeconfig();
    await clusterPage.validateGetClusterAndVerifyStatusAfterSave(clusterName);
    await deleteCluster(clusterName);
  });

  test('connect using kubeconfig file upload',{tag: '@globalConfigurations'}, async ({ deleteCluster }) => {
    test.setTimeout(3 * 60 * 1000);
    const clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase() + '-manual';
    await clusterPage.selectUseKubeconfig();
    const kubeconfigContent = ConnectClusterToDevtronPage.getKubeconfigYaml(clusterName, serverUrl, ClusterToken);
    const tempFilePath = path.join(os.tmpdir(), `kubeconfig-${Date.now()}.yaml`);
    await fs.writeFile(tempFilePath, kubeconfigContent, 'utf-8');
    await clusterPage.uploadKubeconfigFile(tempFilePath);
    await clusterPage.clickSaveButtonForKubeconfig();
    await clusterPage.validateGetClusterAndVerifyStatusAfterSave(clusterName);
    await fs.unlink(tempFilePath);
    await deleteCluster(clusterName);
  });

  test('connect using Server URL and Token',{tag: '@globalConfigurations'}, async ({ deleteCluster }) => {
    test.setTimeout(3 * 60 * 1000);
    await clusterPage.selectUseServerUrlAndToken();
    const clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase() + '-url-token';

    await clusterPage.fillClusterName(clusterName);
    await clusterPage.fillServerUrl(serverUrl);
    await clusterPage.fillBearerToken(ClusterToken);
    await clusterPage.selectProduction(false);
    await clusterPage.selectConnectionType('DIRECT');
    await clusterPage.toggleSecureTlsConnection(false);
    await clusterPage.clickSaveButton();
    await clusterPage.ValidateClusterAdditionUsingUrlAndToken(clusterName);
    await deleteCluster(clusterName);
  });


  test('connect using Server URL and Token with categories',{tag: '@globalConfigurations'}, async ({ deleteCluster }) => {
    test.setTimeout(3 * 60 * 1000);
    await clusterPage.navigateToCategoriesPage();
    const categoryNames: string[] = [];
    const numberOfCategories = 1;
    for (let i = 0; i < numberOfCategories; i++) {
      const name = `category-${BaseTest.generateRandomStringWithCharsOnly(4)}-${Date.now()}`;
      categoryNames.push(name);
    }
    await clusterPage.addNewCategories(categoryNames);
    for (const name of categoryNames) {
      await clusterPage.searchCategoryByNameAndValidate(name);
    }

    await clusterPage.navigateToAddClusterAndEnvPage();
    await clusterPage.selectUseServerUrlAndToken();
    await clusterPage.selectSpecificCategoryFromDropdown(categoryNames[0]);
    const clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase() + '-url-token';
    await clusterPage.fillClusterName(clusterName);
    await clusterPage.fillServerUrl(serverUrl);
    await clusterPage.fillBearerToken(ClusterToken);
    await clusterPage.selectProduction(false);
    await clusterPage.selectConnectionType('DIRECT');
    await clusterPage.toggleSecureTlsConnection(false);
    await clusterPage.clickSaveButton();
    await clusterPage.ValidateClusterAdditionUsingUrlAndToken(clusterName);
    await clusterPage.validateCategoryAssignmentForCluster(clusterName, categoryNames[0]);
    await deleteCluster(clusterName);
    await clusterPage.navigateToCategoriesPage();
    for (const name of categoryNames) {
      await clusterPage.deleteCategoryByName(name);
    }
  });

  test('CreateCategories>AddClusterWithCategory>CreateEnvWithCategory>DeleteEnv>DeleteCluster>DeleteCategory',{tag: '@globalConfigurations'}, async ({ deleteCluster }) => {
    test.setTimeout(5 * 60 * 1000);
    const clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase() + '-url-token';
    const environmentName = "env-" + BaseTest.generateRandomStringWithCharsOnly(6);
    const clusterType = "virtual";
    const environmentType = "production";
    await clusterPage.navigateToCategoriesPage();
    const categoryNames: string[] = [];
    const numberOfCategories = 1;
    for (let i = 0; i < numberOfCategories; i++) {
      const name = `category-${BaseTest.generateRandomStringWithCharsOnly(4)}-${Date.now()}`;
      categoryNames.push(name);
    }
    await clusterPage.addNewCategories(categoryNames);
    for (const name of categoryNames) {
      await clusterPage.searchCategoryByNameAndValidate(name);
    }

    await clusterPage.navigateToAddClusterAndEnvPage();
    await clusterPage.selectUseServerUrlAndToken();
    await clusterPage.selectSpecificCategoryFromDropdown(categoryNames[0]);
    await clusterPage.fillClusterName(clusterName);
    await clusterPage.fillServerUrl(serverUrl);
    await clusterPage.fillBearerToken(ClusterToken);
    await clusterPage.selectProduction(false);
    await clusterPage.selectConnectionType('DIRECT');
    await clusterPage.toggleSecureTlsConnection(false);
    await clusterPage.clickSaveButton();
    await clusterPage.ValidateClusterAdditionUsingUrlAndToken(clusterName);
    await clusterPage.validateCategoryAssignmentForCluster(clusterName, categoryNames[0]);
    await clusterPage.createEnvironmentWithOptionalCategory(clusterName, clusterType, environmentName, environmentType, categoryNames[0]);
    await clusterPage.deleteSpecificEnvironment(environmentName);
    await deleteCluster(clusterName);
    await clusterPage.navigateToCategoriesPage();
    for (const name of categoryNames) {
      await clusterPage.deleteCategoryByName(name);
    }
  });

  test.skip('connect with SSH connection type',{tag: '@globalConfigurations'}, async () => {
    await clusterPage.selectUseServerUrlAndToken();
    const clusterName = BaseTest.generateRandomStringWithCharsOnly(6).toLowerCase() + '-ssh';

    await clusterPage.fillClusterName(clusterName);
    await clusterPage.fillServerUrl(serverUrl);
    await clusterPage.fillBearerToken(ClusterToken);
    await clusterPage.selectProduction(true);
    await clusterPage.selectConnectionType('SSH');
    await clusterPage.toggleSecureTlsConnection(false);
    await clusterPage.toggleSeeMetrics(false);
    await clusterPage.clickSaveButton();

    await clusterPage.ValidateClusterAdditionUsingUrlAndToken(clusterName);
  });
});

