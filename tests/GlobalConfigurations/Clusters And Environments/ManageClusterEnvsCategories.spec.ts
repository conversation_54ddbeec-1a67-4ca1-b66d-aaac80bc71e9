import { test } from '../../../utilities/Fixtures';
import { ConnectClusterToDevtronPage } from '../../../Pages/Global Configuration/Clutsers and Environments/ConnectClusterToDevtronPage';
import { BaseTest } from '../../../utilities/BaseTest';

test.use({ storageState: './LoginAuth.json' });

test('Categories CRUD: Create multiple and Edit category description',{ tag: '@globalConfigurations' }, async ({ page }) => {
    test.setTimeout(5 * 60 * 1000);

    const connectClusterToDevtronPage = new ConnectClusterToDevtronPage(page);
    const categoryNames: string[] = [];

    await test.step('Step 1: Navigate to Categories Page', async () => {
        await connectClusterToDevtronPage.navigateToCategoriesPage();
    });

    await test.step('Step 2: Create multiple random categories and verify Search', async () => {
        const numberOfCategories = 2;
        for (let i = 0; i < numberOfCategories; i++) {
            const name = `category-${BaseTest.generateRandomStringWithCharsOnly(4)}-${Date.now()}`;
            categoryNames.push(name);
        }
        await connectClusterToDevtronPage.addNewCategories(categoryNames);
        for (const name of categoryNames) {
            await connectClusterToDevtronPage.searchCategoryByNameAndValidate(name);
        }
    });

    await test.step('Step 3: Edit one category description', async () => {
        const categoryToEdit = categoryNames[0];
        const updatedDescription = 'Updated description';
        await connectClusterToDevtronPage.editCategoryByName(categoryToEdit, updatedDescription);
        await connectClusterToDevtronPage.expectCategoryDescriptionByName(categoryToEdit, updatedDescription);
    });

    await test.step('Step 4: Delete all categories', async () => {
        for (const name of categoryNames) {
            await connectClusterToDevtronPage.deleteCategoryByName(name);
        }
    });
});
