import { globalConfigurationsChildComponentEnums } from '../../enums/Navbar/childComponentEnum';
import { sidePanelParentComponentsEnum } from '../../enums/Navbar/sidePanelParentComponentsEnum';
import { BaseTest } from '../../utilities/BaseTest';
import { test } from "../../utilities/Fixtures";
//Todo: add case for update some registries
//Todo: we have create ci for each container registry and verify that image is pushed to the registry
let registryNames: string[];

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");

// Use Jest test framework with storage state for authentication
test.use({ storageState: "./LoginAuth.json" });

// Define the test function
test("Add registry and delete Container registry of multiple types",{tag: '@globalConfigurations'}, async ({ AllObjects }) => {
    // Set a timeout for the entire test.
    test.setTimeout(5 * 60 * 1000);
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.globalConfigurations,globalConfigurationsChildComponentEnums.containerOciRegistry);
    await AllObjects.containerRegistryPage.checkInvalidInputError();
    registryNames = await AllObjects.containerRegistryPage.generateRegistryNamesArray(6);
    await AllObjects.containerRegistryPage.addMultipleRegistry(registryNames, ["Docker", "Ecr", "Azure", "Quay", "Other", "GCP"], ["private", "private", "private", "private", "public", "public"], credentials.registryData);
    await AllObjects.containerRegistryPage.deleteRegistry(registryNames);

});