import { test } from '@playwright/test';
import { SSOProvidersEnum } from '../../../enums/Global Configurations/Authorization/SSOProvidersEnum';
import { ssoYamlTemplates, SSOConfigParams } from '../../../TestData/SSOTestData';
import { SSOLoginServiceProviderPage } from '../../../Pages/Global Configuration/Authorization/SSOLoginServiceProviderPage';

test.use({
  storageState: './LoginAuth.json',
});

const baseUrl = process.env.BASE_SERVER_URL || '';
let redirectURI = '';

try {
  const parsedUrl = new URL(baseUrl);
  redirectURI = `${parsedUrl.origin}/orchestrator/api/dex/callback`;
} catch {
  throw new Error('Invalid BASE_SERVER_URL in .env — must be a valid URL');
}

test.describe.serial('SSO Providers Configuration Flow', () => {

  const runtimeParams: Record<SSOProvidersEnum, SSOConfigParams> = {
    [SSOProvidersEnum.GOOGLE]: {
      clientID: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectURI,
    },
    [SSOProvidersEnum.GITHUB]: {
      clientID: process.env.GITHUB_CLIENT_ID || '',
      clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
      redirectURI,
    },
    [SSOProvidersEnum.GITLAB]: {
      baseURL: process.env.GITLAB_BASE_URL || 'https://gitlab.com',
      clientID: process.env.GITLAB_CLIENT_ID || '',
      clientSecret: process.env.GITLAB_CLIENT_SECRET || '',
      redirectURI: process.env.GITLAB_REDIRECT_URI || redirectURI,
      groups: (process.env.GITLAB_GROUPS || 'users').split(','),
      useLoginAsID: process.env.GITLAB_USE_LOGIN_AS_ID === 'true',
    },
    [SSOProvidersEnum.MICROSOFT]: {
      clientID: process.env.MICROSOFT_CLIENT_ID || '',
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET || '',
      tenant: process.env.MICROSOFT_TENANT || '',
      redirectURI: process.env.MICROSOFT_REDIRECT_URI || redirectURI,
    }
  };

  const providers = Object.keys(runtimeParams) as SSOProvidersEnum[];

  for (const provider of providers) {
    test(`Configure and verify ${provider.toUpperCase()} SSO`,{ tag: '@globalConfigurations' }, async ({ page }) => {
      const ssoLoginServiceProviderPage = new SSOLoginServiceProviderPage(page);
      test.setTimeout(5 * 60 * 1000);

      const configParams = runtimeParams[provider];
      const yamlGenerator = ssoYamlTemplates[provider];

      if (!yamlGenerator) {
        throw new Error(`Missing YAML generator for provider: ${provider}`);
      }

      const yamlConfig = yamlGenerator(configParams);

      await ssoLoginServiceProviderPage.navigateToSSOConfigurationsPage();
      await ssoLoginServiceProviderPage.selectSpecificSSOProvider(provider);
      await ssoLoginServiceProviderPage.fillSSOConfigurationYaml(yamlConfig);
      await ssoLoginServiceProviderPage.SaveUpdateSpecificSSOProvider();
      await ssoLoginServiceProviderPage.clickLogout();
      await ssoLoginServiceProviderPage.verifySSOHasConfiguredForDesiredProvider(provider);
    });
  }
});
