import { expect, Page } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { test } from "../../../utilities/Fixtures";
import { DataSet } from '../../../utilities/DataObjectss.ts/multipleTestDataObject';
import { CreateAppPage } from '../../../Pages/ApplicationManagement/Applications/CreateAppPage';
import { WorkflowPage } from '../../../Pages/ApplicationManagement/Applications/WorkflowPage';
import { JobsPage } from '../../../Pages/Automation and Enablement/Jobs';
import { BuildHistoryPage } from '../../../Pages/ApplicationManagement/Applications/BuildHistoryPage';
import { BaseDeploymentTemplatePage } from '../../../Pages/ApplicationManagement/Applications/BaseDeploymentTemplatePage';
import { CiCdAppDetailsPage } from '../../../Pages/ApplicationManagement/Applications/CiCdAppDetailsPage';
import { ChartStoreAppDetailsPage } from '../../../Pages/ApplicationManagement/Chart Store/ChartStoreAppDetailsPage';
// import { GlobalConfigurationPage } from '../../../Pages/GlobalConfigurations/GlobalConfigurationPage';
import { ChartStorePage } from '../../../Pages/ApplicationManagement/Chart Store/chartStorePage';
import { DeployChartPage } from '../../../Pages/ApplicationManagement/Chart Store/DeployChartPage';
import { ResourceBrowserPage } from '../../../Pages/Infrastructure Management/ResourceBrowserPage';
import { BuildConfigurationPage } from '../../../Pages/ApplicationManagement/Applications/BuildConfigurationPage';
import { ApiTokenPage } from '../../../Pages/Global Configuration/Authorization/ApiTokenPage';
import { UserPermissionPage } from '../../../Pages/Global Configuration/Authorization/UserPermissionPage';
import { AllTypes } from '../../../utilities/Types';
import { ldapSsoCreds } from '../../../utilities/clipboardyYamls.ts/customObjectsOrYaml';
import { SsoLoginServicesPage } from '../../../Pages/Global Configuration/Authorization/SsoLoginServicesPage';
import { LoginPage } from '../../../Pages/LoginPage';
import { ApiUtils } from '../../../utilities/ApiUtils';





test.use({ storageState: './LoginAuth.json' });
let providersForRbac: string[] = ['token', 'ldap'];
let rbacApitoken = "playwright-rbac-testing"
let ldapPermissionGroupName = "alltech"
let helmAppsCreated: string[] = [];
const Credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
test.describe.configure({ mode: 'serial' });

test.beforeEach('creating apiToken', async ({ AllObjects, page, browser }, testInfo) => {
    test.setTimeout(12 * 1000 * 60);
    let token: string = '';
    let permissions: AllTypes.apiTokenPage.apiTokenObject =
      process.env.clusterType?.includes(`ea`)
        ? DataSet.ApitokenData.apitokenData(
            rbacApitoken,
            "specific-user-permission",
            ["All applications"],
            ["automation"],
            ["View only"],
            "Helm Apps",
            ["devtron-demo"]
          )
        : DataSet.ApitokenData.apitokenData(
            rbacApitoken,
            "specific-user-permission"
          );

    token = await pageOpeningAndInitialSetup(AllObjects, true, rbacApitoken, permissions, true);
    // ApitokenData.apitokenData(tokenName, 'specific-user-permission','All applications','devtron-demo,'View only', 'Helm Apps','devtron-demo')
    let nonSuperdminPage = await BaseTest.createNewPageWithCustomCookies(browser, token);
    console.log('non superdmin page has been created with token ' + token);
    await nonSuperdminPage.goto(process.env.BASE_SERVER_URL!);
    await BaseTest.clickOnDarkMode(nonSuperdminPage);
    (testInfo as any).nonSuperdminPage = nonSuperdminPage;
    (testInfo as any).createAppPage = new CreateAppPage(nonSuperdminPage);
    (testInfo as any).workflowPage = new WorkflowPage(nonSuperdminPage);
    (testInfo as any).jobsPage = new JobsPage(nonSuperdminPage);
    (testInfo as any).buildHistoryPage = new BuildHistoryPage(nonSuperdminPage);
    (testInfo as any).baseDeploymentTemplatePage = new BaseDeploymentTemplatePage(nonSuperdminPage);
    (testInfo as any).appDetailsPageCiCd = new CiCdAppDetailsPage(nonSuperdminPage);
    (testInfo as any).chartStoreAppDetailsPage = new ChartStoreAppDetailsPage(nonSuperdminPage);
    // (testInfo as any).globalConfigPage = new GlobalConfigurationPage(nonSuperdminPage);
    (testInfo as any).chartStorePage = new ChartStorePage(nonSuperdminPage);
    (testInfo as any).deployChartPage = new DeployChartPage(nonSuperdminPage);
    (testInfo as any).resourceBrowserPage = new ResourceBrowserPage(nonSuperdminPage);
    (testInfo as any).buildConfigurationPage = new BuildConfigurationPage(nonSuperdminPage);
    (testInfo as any).apiTokenPage = new ApiTokenPage(nonSuperdminPage);
    (testInfo as any).userPermissionPage = new UserPermissionPage(nonSuperdminPage);
    (testInfo as any).ssoLoginServicesPage = new SsoLoginServicesPage(nonSuperdminPage);
    (testInfo as any).loginPage = new LoginPage(nonSuperdminPage);


})
let devtronApps: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)]
test.use({ devtronApps: devtronApps, devtronEnvs: ["automation", "devtron-demo"], triggerCI: [false, false] });

test('rbac for devtron apps_oss', { tag: '@rbac' }, async ({ AllObjects, page, devtronAppCreation, request, createExternalCiWorkflow }, testInfo) => {
    test.setTimeout(35 * 1000 * 60);
    for (let i = 0; i < devtronApps.length; i++) {
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'webhook', 1);
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'devtron-demo', clusterName: 'default_cluster', helmOrGitops: 'helm' });
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1);
    }
    let UserForManagertesting: string = BaseTest.generateRandomStringWithCharsOnly(5) + '@gmail.com';
    let permissionGroupForManagerTesting = BaseTest.generateRandomStringWithCharsOnly(5);
    await AllObjects.userPermissionPage.navifateToUserPermissionPage();
    await AllObjects.userPermissionPage.clickOnAddUserButtonAndFillEmail(UserForManagertesting);
    await AllObjects.userPermissionPage.saveButton.click();
    await AllObjects.permissionGroupPage.navigatetoUserPermissionPage();
    await AllObjects.permissionGroupPage.clickOnPermissionGroupOrCreateNew(permissionGroupForManagerTesting);
    await AllObjects.apiTokenPage.setSuperAdminOrSpecificPermissions({ permissionData: { permissionType: 'super-admin-permission' } });
    await AllObjects.permissionGroupPage.saveOrUpdatePermissionGroup();

    let appName1 = devtronApps[0];
    let appName2 = devtronApps[1];
    let appNamesToDelete: string[] = [];
    appNamesToDelete.push(appName1);
    appNamesToDelete.push(appName2);
    (testInfo as any).appsToDelete = appNamesToDelete;
    (testInfo as any).userToDelete = UserForManagertesting;
    (testInfo as any).permissionGroupToDelete = permissionGroupForManagerTesting;
    let count = 0;


    for (let provider of providersForRbac) {
        let nonSuperdminPage: Page = (testInfo as any).nonSuperdminPage;
        let customDataSet = DataSet.Rbac.dataSetForDevtronApps(appName1, appName2, rbacApitoken, 'project_amit', UserForManagertesting, permissionGroupForManagerTesting);
        if (provider == "ldap" && process.env.clusterType?.includes('oss') || (provider == "ldap" && process.env.isLdapServerOn == "false")) {
            continue;
        }
        if (provider == "ldap") {
            // Keep only 3rd, 5th and 8th permission sets as all are not required again 
                customDataSet = [
                    customDataSet[2],  // View only + All applications + All environments
                    customDataSet[4],  // Admin + All applications + All environments
                    customDataSet[7]   // Build and deploy + Specific app + Specific environment
                ];
            }
        for (let key of customDataSet) {
            let apiTokenOrPermissionGroup: string = provider == "ldap" ? ldapPermissionGroupName : rbacApitoken;
            let isApitoken: boolean = provider == "ldap" ? false : true;
            if(process.env.HIDE_API_TOKEN === "false")
            {
                await pageOpeningAndInitialSetup(AllObjects, isApitoken, apiTokenOrPermissionGroup, key.permissionDetails, false);
                }else{
                    let updatedToken = await pageOpeningAndInitialSetup(AllObjects, isApitoken, apiTokenOrPermissionGroup, key.permissionDetails, false);
                    if (isApitoken && updatedToken) {
                        await updatePageWithNewToken(nonSuperdminPage, updatedToken);
                    } }

            if (count == 0 && provider == "ldap") {
                await logoutAndLoginWithSso(AllObjects, (testInfo as any).ssoLoginServicesPage, (testInfo as any).loginPage);
                count++;
            }
            if (key.accessibility.appListing) {
                console.log('called');
                for (let element of key.accessibility.appListing) {
                    await nonSuperdminPage.goto(process.env.BASE_SERVER_URL!);
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName, false, 'app', element.isVisible);
                }
            }
            let appNameToCreate: string = BaseTest.generateRandomStringWithCharsOnly(5);
            if (key.accessibility.create?.isEligible) {
                let appNameToBeDeleted = appNameToCreate;
                appNamesToDelete.push(appNameToBeDeleted);
            }
            key.accessibility.create ? await (testInfo as any).createAppPage.createCustomAppJob('app', appNameToCreate, 'devtron-demo', process.env.BASE_SERVER_URL!, { isSuccessfull: key.accessibility.create.isEligible, hiddenProjectName: 'mynkproj' }) : console.log('create was not in the scope');
            if (key.accessibility.trigger) {
                for (const element of key.accessibility.trigger.passFailCriteria) {
                    let cdNodeNumber: number = element.envName == "devtron-demo" ? 1 : 0;
                    await (testInfo as any).createAppPage.navigateToDevtronAppListPage(process.env.BASE_SERVER_URL!);
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName);
                    await (testInfo as any).workflowPage.verifyImageAndTriggerDeployment(cdNodeNumber, [], [], 0, false, { isTriggerPageVisible: element.isTriggerPageVisible, isEligibleForTrigger: element.isEligible });
                }
            }
            if (key.accessibility.edit) {
                for (let element of key.accessibility.edit.passFailCriteria) {
                    await (testInfo as any).createAppPage.navigateToDevtronAppListPage(process.env.BASE_SERVER_URL!);
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName);
                    await (testInfo as any).buildHistoryPage.gotoAppConfigurationsPage();
                    await (testInfo as any).jobsPage.operBaseOrEnvOverRideResources(element.envName);
                    await (testInfo as any).baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } });
                    let messageToVerify: string = element.isEligible ? 'Updated override' : 'Error'
                    await (testInfo as any).baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate(messageToVerify);
                    if (element.isEligible) {
                        await (testInfo as any).baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'delete' } });
                    }
                }
            }
            if (key.accessibility.appDetails) {
                for (const element of key.accessibility.appDetails) {
                    await (testInfo as any).createAppPage.navigateToDevtronAppListPage(process.env.BASE_SERVER_URL!);
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName);
                    await (testInfo as any).appDetailsPageCiCd.selectEnvironment(element.envName);
                    await (testInfo as any).chartStoreAppDetailsPage.goToTerminal({ isEligible: element.terminalConnection });
                    if (process.env.clusterType?.includes('enterprise')) {
                        await (testInfo as any).chartStoreAppDetailsPage.downloadFile('/reen', element.downloadLogs);
                    }

                }
            }
            if (key.accessibility.delete) {
                for (const element of key.accessibility.delete.passFailCriteria) {
                    await (testInfo as any).createAppPage.navigateToDevtronAppListPage(process.env.BASE_SERVER_URL!);
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName);
                    await (testInfo as any).appDetailsPageCiCd.selectEnvironment(element.envName);
                    await (testInfo as any).appDetailsPageCiCd.deleteAnyResource(element.isEligible);
                }
            }
            if (key.accessibility.appLevelConfig) {
                for (let element of key.accessibility.appLevelConfig) {
                    await (testInfo as any).createAppPage.navigateToDevtronAppListPage(process.env.BASE_SERVER_URL!);
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName);
                    await (testInfo as any).buildHistoryPage.gotoAppConfigurationsPage();
                    await (testInfo as any).buildConfigurationPage.buildConfigurationTab.click();
                    let messageToVerify: string = element.isEligible ? "Success" : "Error"
                    await BaseTest.checkToast((testInfo as any).nonSuperdminPage, (testInfo as any).buildConfigurationPage.saveAndNextButton, messageToVerify);
                }
            }
            if (key.accessibility.userPermissions && provider != "ldap") {
                if (key.accessibility.userPermissions.isPageVisible) {
                    await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL! + '/global-config/auth/users');
                    await (testInfo as any).userPermissionPage.searchAanUser(UserForManagertesting, true);
                    await (testInfo as any).apiTokenPage.assignPermissionGroup(permissionGroupForManagerTesting);
                    await (testInfo as any).apiTokenPage.clickOnSaveOrUpdateTokenButton({ isEligible: false });
                    await (testInfo as any).nonSuperdminPage.reload();
                    await (testInfo as any).apiTokenPage.setSuperAdminOrSpecificPermissions(key.permissionDetails);
                    await (testInfo as any).apiTokenPage.clickOnSaveOrUpdateTokenButton();
                }
            }

        }

    }
})

test('rbac for helm apps_oss_eaMode', { tag: '@rbac' }, async ({ AllObjects, page, helmAppCreation, externalHelmAppCreation }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    let environmentsCreated: string[] = [];
    let unassginedAppName = externalHelmAppCreation.externalAppName;
    let assignedAppName = helmAppCreation.helmAppName;
    helmAppsCreated.push(assignedAppName);
    helmAppsCreated.push(unassginedAppName);
    let count = 0;
    for (let provider of providersForRbac) {
        let nonSuperdminPage: Page = (testInfo as any).nonSuperdminPage;
            if (provider == "ldap") {
                continue;
            }
        for (let key of DataSet.Rbac.dataCreationForHelmRbac(rbacApitoken, unassginedAppName, assignedAppName)) {
            await expect(async () => {
                let apiTokenOrPermissionGroup: string = provider == "ldap" ? ldapPermissionGroupName : rbacApitoken;
                let isThroughApiTokenPage: boolean = provider == "ldap" ? false : true
                if(process.env.HIDE_API_TOKEN === "false")
                {
                await pageOpeningAndInitialSetup(AllObjects, isThroughApiTokenPage, apiTokenOrPermissionGroup, key.permissionDetails, false);
                }else{
                let updatedToken =  await pageOpeningAndInitialSetup(AllObjects, isThroughApiTokenPage, apiTokenOrPermissionGroup, key.permissionDetails, false);
                await updatePageWithNewToken(nonSuperdminPage, updatedToken);
                }

            }).toPass({ timeout: 4 * 1000 * 60 });

            if (count == 0 && provider == "ldap") {
                await logoutAndLoginWithSso(AllObjects, (testInfo as any).ssoLoginServicesPage, (testInfo as any).loginPage);
                count++;
            }
            if (key.accessibility.create && (key.permissionDetails.permissionData.specificPermission?.permissions['project'][0] != "Unassigned" && provider != 'ldap') && (key.permissionDetails.permissionData.specificPermission?.permissions['project'][0] != "Unassigned" && !process.env.clusterType?.includes(`ea`))) {
                console.log('printing the stuff that i am interested in ');
                console.log(process.env.clusterType?.includes(`ea`))
                let envName: string = '';
                if (key.permissionDetails.permissionData.specificPermission?.permissions['environment'][0] == "All existing + future environments in default_cluster" && key.permissionDetails.permissionData.specificPermission.permissions['project'][0] != "Unassigned") {
                    envName = BaseTest.generateRandomStringWithCharsOnly(4);
                    await AllObjects.clusterAndEnvironmentsPage.goToClustersAndEnvironmentsPage();
                    await AllObjects.clusterAndEnvironmentsPage.createEnvironment('default_cluster', 'er', envName, 'production')
                }
                if (envName) {
                    environmentsCreated.push(envName);
                }
                let chartNameToBeCreated = BaseTest.generateRandomStringWithCharsOnly(4);
                if (key.accessibility.create.isEligible) {
                    let chartNameToBeDeleted: string = chartNameToBeCreated;
                    helmAppsCreated.push(chartNameToBeDeleted);
                }
                let envNameForCreation: string = envName ? envName : 'automation'
                await (testInfo as any).globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
                await (testInfo as any).chartStorePage.SelectingChart(Credentials.ChartName, Credentials.ChartSource);
                await (testInfo as any).deployChartPage.deployingChart(chartNameToBeCreated, Credentials.ProjectName, envNameForCreation, "helm", '', { isEligible: key.accessibility.create.isEligible });
            }
            if (key.accessibility.edit) {
                for (let element of key.accessibility.edit) {
                    await expect(async () => {
                        await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL! + '/app/list/h?cluster=1');
                        await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName, true, 'app', true);
                        await (testInfo as any).deployChartPage.configureTab.click();
                        await (testInfo as any).deployChartPage.updatingChartVersion({ isEligible: element.isEligible });
                    }).toPass({ timeout: 5 * 1000 * 60 });
                }
            }
            if (key.accessibility.appDetails) {
                for (let element of key.accessibility.appDetails) {
                    await expect(async () => {
                        await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL! + '/app/list/h?cluster=1');
                        await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName, true, 'app', true);
                        await (testInfo as any).chartStoreAppDetailsPage.goToTerminal({ isEligible: element.terminalConnection });
                        if (process.env.clusterType?.includes('enterprise')) {
                            console.log('this has been called');
                            await (testInfo as any).chartStoreAppDetailsPage.downloadFile('/reen', element.downloadLogs);
                        }
                    }).toPass({ timeout: 4 * 1000 * 60 });
                }
            }
            if (key.accessibility.delete) {
                for (let element of key.accessibility.delete) {
                    await expect(async () => {
                        await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL! + '/app/list/h?cluster=1');
                        await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.appName, true, 'app', true);
                        await (testInfo as any).appDetailsPageCiCd.deleteAnyResource(element.isEligible);
                    }).toPass({ timeout: 5 * 1000 * 60 });
                }
            }

        }
    }
    (testInfo as any).environmentsCreated = environmentsCreated;

})
test.skip('rbac for jobs', async ({ AllObjects, jobCreation }, testInfo) => {
    test.setTimeout(22 * 1000 * 60)
    let jobName1: string = jobCreation.jobsNames[0];
    let jobName2: string = jobCreation.jobsNames[1];
    let workflowName1: string = jobCreation.workflowNames[0];
    let workflowName2: string = jobCreation.workflowNames[1];
    let jobNamesToBeDeleted: string[] = [jobName1, jobName2];
    (testInfo as any).jobNames = jobNamesToBeDeleted;
    let count = 0;
    for (let provider of providersForRbac) {
        let nonSuperdminPage: Page = (testInfo as any).nonSuperdminPage;
        let dataset = DataSet.Rbac.dataCreationForJobsRbac({ jobName1: jobName1, workflowName1: workflowName1, jobName2: jobName2, workflowName2: workflowName2 }, 'playwright-jobs-rbac');
        for (let key of dataset) {
            await expect(async () => {
                let apiTokenNameOrPermissionGroupName: string = provider == "ldap" ? ldapPermissionGroupName : rbacApitoken;
                let navigationToApiTokenPage: boolean = provider == "ldap" ? false : true;
                if(process.env.HIDE_API_TOKEN === "false")
                {
                    await pageOpeningAndInitialSetup(AllObjects, navigationToApiTokenPage, apiTokenNameOrPermissionGroupName, key.permissionDetails, false);                
                }else{
                let updatedToken =  await pageOpeningAndInitialSetup(AllObjects, navigationToApiTokenPage, apiTokenNameOrPermissionGroupName, key.permissionDetails, false);
                await updatePageWithNewToken(nonSuperdminPage, updatedToken);
                }

            }).toPass({ timeout: 4 * 1000 * 60 });

            if (count == 0 && provider == "ldap") {
                await logoutAndLoginWithSso(AllObjects, (testInfo as any).ssoLoginServicesPage, (testInfo as any).loginPage);
                count++;
            }
            if (key.accessibility.edit) {
                for (let element of key.accessibility.edit) {
                    await expect(async () => {
                        await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/job/list');
                        await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.jobName, false, 'job');
                        let messageToVerify: string = element.isEligible ? 'Success' : 'Error';
                        await (testInfo as any).jobsPage.checkToastMessageOnUpateNode(messageToVerify);
                    }).toPass({ timeout: 4 * 1000 * 60 });
                }
            }
            if (key.accessibility.jobListing) {
                for (let element of key.accessibility.jobListing) {
                    await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/job/list');
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.jobName, false, 'job', element.isVisible);
                }
            }
            if (key.accessibility.run) {
                for (let element of key.accessibility.run) {
                    await expect(async () => {
                        await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/job/list');
                        console.log('veer' + element.jobName);
                        await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.jobName, false, 'job');
                        let messageToVerify: string = element.isEligible ? 'Success' : 'Not authorized'
                        await (testInfo as any).workflowPage.triggerCiModule(messageToVerify)
                    }).toPass({ timeout: 4 * 1000 * 60 });
                }
            }
            if (key.accessibility.workflowListing) {
                for (let element of key.accessibility.workflowListing) {
                    await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/job/list');
                    await (testInfo as any).createAppPage.searchAndSelectAppsFromList(element.jobName, false, 'job');
                    await (testInfo as any).jobsPage.checkWhetherWorkflowsShouldBeVisibleOrNot(element.workflowVisibility);
                }
            }
        }
    }

})

test('rbac for k8s resources_oss_eaMode', { tag: '@rbac' }, async ({ AllObjects, page }, testInfo) => {
    test.setTimeout(30 * 1000 * 60);
    //resourceData.DeploymentReplicas, resourceData.DeploymentContainerName, resourceData.DeploymentContainerImage
    let deploymentName1: string = 'deploy' + BaseTest.generateRandomStringWithCharsOnly(5);
    let deploymentName2: string = 'deploy' + BaseTest.generateRandomStringWithCharsOnly(5);
    let deploymentsToBeDeleted: string[] = [deploymentName1, deploymentName2];
    (testInfo as any).deploymentNames = deploymentsToBeDeleted;
    await page.goto(process.env.BASE_SERVER_URL! + '/resource-browser/1/node/k8sEmptyGroup');
    await AllObjects.resourceBrowserPage.createResource(deploymentName1, 'deployment', { DeploymentReplicas: 1, DeploymentContainerName: 'nginx', DeploymentContainerImage: 'nginx:latest' }, 'automation');
    await page.goto(process.env.BASE_SERVER_URL! + '/resource-browser/1/node/k8sEmptyGroup');
    await AllObjects.resourceBrowserPage.createResource(deploymentName2, 'deployment', { DeploymentReplicas: 1, DeploymentContainerName: 'nginx', DeploymentContainerImage: 'nginx:latest' }, 'default');
    let count = 0;
    for (let provider of providersForRbac) {
        if (provider == "ldap") {
            continue;
        }
        let dataForK8s = DataSet.Rbac.dataCreationFork8sResources(deploymentName1, deploymentName2, provider);
        for (let key of dataForK8s) {
            await expect(async () => {
                if (provider == 'ldap') {
                    await AllObjects.permissionGroupPage.navigatetoUserPermissionPage();
                    await AllObjects.permissionGroupPage.clickOnPermissionGroupOrCreateNew(ldapPermissionGroupName);
                    await AllObjects.apiTokenPage.setSpecificPermissionForK8sResources(key.accessDetails, true);
                    await AllObjects.permissionGroupPage.saveOrUpdatePermissionGroup();
                } else {
                    await AllObjects.apiTokenPage.goToApiTokenPage();
                    await AllObjects.apiTokenPage.clickOnTokenOrCreateNew(rbacApitoken);
                    await AllObjects.apiTokenPage.setSpecificPermissionForK8sResources(key.accessDetails);
                    await AllObjects.apiTokenPage.clickOnSaveOrUpdateTokenButton();
                }
            }).toPass({ timeout: 4 * 1000 * 60 });
            if (count == 0 && provider == "ldap") {
                await logoutAndLoginWithSso(AllObjects, (testInfo as any).ssoLoginServicesPage, (testInfo as any).loginPage);
                count++;
            }
            if (key.accessibility.nodeActions && provider != 'ldap') {
                console.log('some shit is calline');
                await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/resource-browser/1/node/k8sEmptyGroup');
                await (testInfo as any).resourceBrowserPage.checkwarningForNodeActionsForNonSuperAdmin();
            }
            if (key.accessibility.visibilityOfResources) {
                await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/resource-browser/1/node/k8sEmptyGroup');
                for (let element of key.accessibility.visibilityOfResources) {
                    await (testInfo as any).resourceBrowserPage.searchAnyResourceType(element.resourceType, element.isResourceTypeVisible);
                    if (element.resourceName) {
                        await (testInfo as any).resourceBrowserPage.searchAnyResourceName(element.resourceName, element.isResourceNameVisible);
                    }
                }
            }
            if (key.accessibility.resourceLevelOperations) {
                let messageToVerify: string = "";
                for (let element of key.accessibility.resourceLevelOperations) {
                    await expect(async () => {
                        await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/resource-browser/1/node/k8sEmptyGroup');
                        await (testInfo as any).resourceBrowserPage.searchAnyResourceType(element.resourceType);
                        await (testInfo as any).resourceBrowserPage.searchAnyResourceName(element.resourceName);
                        if (element.delete) {
                            messageToVerify = element.delete.isEligible ? "Success" : "Not authorized"
                            await (testInfo as any).resourceBrowserPage.externalDeletionOfResource(messageToVerify);
                            await (testInfo as any).nonSuperdminPage.reload();
                        }
                        if (element.manifest) {
                            messageToVerify = element.manifest.isEligible ? 'Success' : 'Not authorized'
                            await (testInfo as any).resourceBrowserPage.checkMessageOnEditingManifest(messageToVerify);
                        }
                        if (element.terminal) {
                            if (element.manifest) {
                                await (testInfo as any).nonSuperdminPage.locator(`//*[contains(@aria-label,'Close tab')]`).click();
                            }
                            await (testInfo as any).resourceBrowserPage.createdResourceList.click();
                            await (testInfo as any).resourceBrowserPage.checkPodTerminal(Credentials.TerminalScript[0], Credentials.TerminalExpectedValue[0], element.terminal.isEligible);
                        }
                    }).toPass({ timeout: 4 * 1000 * 60 });

                }
            }
            if (key.accessibility.isUnAuthorizedPageVisible) {
                await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/resource-browser/1/node/k8sEmptyGroup');
                await expect((testInfo as any).resourceBrowserPage.genericEmptyState).toBeVisible({ timeout: 8000 });
            }

        }
    }

})

test('chart groups test for rbac_oss', { tag: '@rbac' }, async ({ AllObjects }, testInfo) => {
    test.setTimeout(12 * 1000 * 60);
    let chartGroupsCreated: string[] = [];
    let count = 0;
    for (let provider of providersForRbac) {
        if (provider == "ldap") {
            continue;
        }
        let chartNameForEligibleCreation = BaseTest.generateRandomStringWithCharsOnly(5);
        let chartName: string = chartNameForEligibleCreation;
        chartGroupsCreated.push(chartName);
        (testInfo as any).chartGroupName = chartGroupsCreated;
        for (let key of DataSet.Rbac.dataCreationForChartGroups(chartName)) {
            if (provider != 'ldap') {
                await AllObjects.apiTokenPage.goToApiTokenPage();
                await AllObjects.apiTokenPage.clickOnTokenOrCreateNew(rbacApitoken);
                await AllObjects.apiTokenPage.setPermissionsForChartGroups(key.acess);
                await AllObjects.apiTokenPage.clickOnSaveOrUpdateTokenButton();
            }
            else {
                await AllObjects.permissionGroupPage.navigatetoUserPermissionPage();
                await AllObjects.permissionGroupPage.clickOnPermissionGroupOrCreateNew(ldapPermissionGroupName);
                await AllObjects.apiTokenPage.setPermissionsForChartGroups(key.acess);
                await AllObjects.permissionGroupPage.saveOrUpdatePermissionGroup();
            }
            if (count == 0 && provider == "ldap") {
                await logoutAndLoginWithSso(AllObjects, (testInfo as any).ssoLoginServicesPage, (testInfo as any).loginPage);
                count++;
            }
            await expect(async () => {
                await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/chart-store/discover');
                if (key.acessibility.createChartGroup.isEligible == false) {
                    chartName = BaseTest.generateRandomStringWithCharsOnly(5);
                    console.log('this chart should not be created' + chartName);
                }
                console.log('chart name whose creation should be successfull' + chartName);
                await (testInfo as any).chartStorePage.createChartGroup(chartName, key.acessibility.createChartGroup.isEligible);
            }).toPass({ timeout: 2 * 1000 * 60 });
            await (testInfo as any).nonSuperdminPage.goto(process.env.BASE_SERVER_URL + '/chart-store/discover');
            await (testInfo as any).chartStorePage.checkWarningOnSavingTheGroup(chartNameForEligibleCreation, key.acessibility.editChartGroup.isEligible);
        }
    }

})



test.afterEach('deleting the data for all tests', async ({ AllObjects, page, request }, testInfo) => {
    test.setTimeout(10 * 1000 * 60);

    if (testInfo.title == "rbac for helm apps_oss_eaMode") {

        console.log('array length is ' + (testInfo as any).appsCreated);
        for (let i = 0; i < helmAppsCreated.length; i++) {
            await page.goto(process.env.BASE_SERVER_URL + '/app/list/h?cluster=1');
            await AllObjects.createAppPage.searchAndSelectAppsFromList(helmAppsCreated[i], true);
            // Deleting the app
            await AllObjects.deployChartPage.deletingChartStoreApp();
        }
        await page.goto(process.env.BASE_SERVER_URL + '/global-config/cluster-env');
        await AllObjects.clusterAndEnvironmentsPage.deleteEnvironment((testInfo as any).environmentsCreated);
    }
    if (testInfo.title == "rbac for devtron apps_oss") {
        let failureCount = 0;
        try {
            await AllObjects.userPermissionPage.navifateToUserPermissionPage();
            await AllObjects.userPermissionPage.deleteAnUser((testInfo as any).userToDelete);
            await AllObjects.permissionGroupPage.navigatetoUserPermissionPage();
            await AllObjects.permissionGroupPage.deletePermissionGroup((testInfo as any).permissionGroupToDelete);
        }
        catch (error) {
            failureCount++;
            //
            console.log('was not able to delete the user and permission group');
        }
        for (let key of (testInfo as any).appsToDelete) {
            let apiUtils = new ApiUtils(request);
            await apiUtils.deleteAllNodesOfApplication(key, await apiUtils.login(process.env.PASSWORD!));
        }
        expect(failureCount).toBe(0);
    }
    if (testInfo.title == "rbac for jobs") {
        for (let key of (testInfo as any).jobNames) {
            await page.goto(process.env.BASE_SERVER_URL!);
            await AllObjects.createAppPage.searchAndSelectAppsFromList(key, false, 'job');
            await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication('job');
        }
    }
    if (testInfo.title == "rbac for k8s resources_oss_eaMode") {
        for (let key of (testInfo as any).deploymentNames) {
            await page.goto(process.env.BASE_SERVER_URL! + '/resource-browser/1/node/k8sEmptyGroup')
            await AllObjects.resourceBrowserPage.searchAnyResourceType('Deployment');
            await AllObjects.resourceBrowserPage.searchAnyResourceName(key);
            if (! await AllObjects.resourceBrowserPage.checkNullState()) {
                await AllObjects.resourceBrowserPage.externalDeletionOfResource();
            }
        }
    }
    if (testInfo.title == "chart groups test for rbac_oss") {
        for (let i = 0; i < (testInfo as any).chartGroupName.length; i++) {
            await page.goto(process.env.BASE_SERVER_URL + '/chart-store/discover');
            await AllObjects.chartStorePage.deleteChartGroups((testInfo as any).chartGroupName[i]);
        }
    }
})











async function pageOpeningAndInitialSetup(AllObjects: AllTypes.fixtures.allPageObjects, isApiToken: boolean, tokenNameOrPermissionGroup: string, permissionDetails: AllTypes.apiTokenPage.apiTokenObject, isThroughBeforeEach: boolean) {
    let tokenValue: string = '';
    if (isApiToken) {
        await AllObjects.apiTokenPage.goToApiTokenPage();
        await AllObjects.apiTokenPage.clickOnTokenOrCreateNew(tokenNameOrPermissionGroup);
        if ((isThroughBeforeEach && !await AllObjects.apiTokenPage.checkWhetherpermissionRowExistOrNot()) || !isThroughBeforeEach)
            await AllObjects.apiTokenPage.setSuperAdminOrSpecificPermissions(permissionDetails);
        await AllObjects.apiTokenPage.clickOnSaveOrUpdateTokenButton();
        tokenValue = await AllObjects.apiTokenPage.fetchTokenValue(tokenNameOrPermissionGroup);
    }
    else {
        await AllObjects.permissionGroupPage.navigatetoUserPermissionPage();
        await AllObjects.permissionGroupPage.clickOnPermissionGroupOrCreateNew(tokenNameOrPermissionGroup);
        await AllObjects.apiTokenPage.setSuperAdminOrSpecificPermissions(permissionDetails);
        await AllObjects.permissionGroupPage.saveOrUpdatePermissionGroup();
    }
    return tokenValue;
}
async function logoutAndLoginWithSso(AllObjects: AllTypes.fixtures.allPageObjects, ssoLoginServicesPage: SsoLoginServicesPage, loginPage: LoginPage) {
    await AllObjects.ssoLoginServicesPage.navigateToSsoServicesPage();
    await AllObjects.ssoLoginServicesPage.clickOnAnySsoProvider('ldap');
    await AllObjects.ssoLoginServicesPage.editSsoConfiguration(ldapSsoCreds('34.0.0.203:389', process.env.ldapConfigPass!));
    await AllObjects.ssoLoginServicesPage.clickOnSaveButton();
    await ssoLoginServicesPage.loggingOut();
    await loginPage.loginWithLdap(process.env.ldapUserEmail!, process.env.ldapUserPass!);

}

async function updatePageWithNewToken(page: Page, newToken: string) {
    console.log(`🔄 Updating page session with new token: ${newToken.substring(0, 50)}...`);
    
    // Method 1: Update cookies
    await page.context().addCookies([
        {
            name: 'argocd.token',
            value: newToken,
            domain: new URL(process.env.BASE_SERVER_URL!).hostname,
            path: '/'
        }
    ]);
     // Reload to apply new authentication
    await page.reload();
    await page.waitForLoadState('networkidle');
}