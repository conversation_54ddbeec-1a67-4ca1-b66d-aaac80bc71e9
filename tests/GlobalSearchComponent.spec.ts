import { globalSearchComponentParentEntities } from '../enums/GlobalSearchComponentEnum';
import { applicationManagementChildComponentEnum } from '../enums/Navbar/childComponentEnum';
import { sidePanelParentComponentsEnum } from '../enums/Navbar/sidePanelParentComponentsEnum';
import { test } from '../utilities/Fixtures';

test.use({ storageState: './LoginAuth.json' });
test.setTimeout(5 * 1000 * 60);
test('Global Search Component',{tag:"@globalConfigurations"}, async ({ AllObjects ,page}) => {
    await page.goto(process.env.BASE_SERVER_URL!);
    await AllObjects.globalSearchComponent.verifyKeyShortcutsOfGlobalSearchComponent();
    await AllObjects.globalSearchComponent.verifyKeyUpDownAndEnterFunctionalityIsWorking();
    await AllObjects.globalSearchComponent.openOrCloseGlobalSearchComponentThroughShortcut(true);
    await AllObjects.globalSearchComponent.searchAndClickOnAnyEntity(globalSearchComponentParentEntities.applicationManagement,applicationManagementChildComponentEnum.chartStore);
    await AllObjects.navbarPage.navigateToAnySectionInNavBar(sidePanelParentComponentsEnum.globalSearch,null);
    await AllObjects.globalSearchComponent.verifyRecentNavigationsSections([{indexNumber:0, textToVerify:'chart store'}]);
})