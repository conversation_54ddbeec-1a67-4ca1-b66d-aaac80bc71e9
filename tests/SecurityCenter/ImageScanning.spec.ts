
import { BaseTest } from '../../utilities/BaseTest';
import { test } from "../../utilities/Fixtures";
import { ApiUtils } from '../../utilities/ApiUtils';
import { dataObjectForImageScanningTestCase } from '../../utilities/DataObjectss.ts/ImageScanningDataObject';
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");

/**
 * Variable Declaration
 */
let devtronApp: string = '';
let dataForImageScanningTest = dataObjectForImageScanningTestCase();
let image: string

/**
 * Test Configuration
 */
test.use({ storageState: './LoginAuth.json', triggerCI: [false] });
test('Complete Image Scan_enterprise', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation, request }) => {
  test.setTimeout(45 * 1000 * 60);
  await test.step('Setting up all the required workflows and configure tasks', async () => {
    devtronApp = devtronAppCreation.appNames[0];
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.appConfigurationPage.turnOnImageScanToggleButton();
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'linked-cd', config: { sourceEnv: credentials.EnvNameForCD[0], destEnv: 'env1' } });
    let ciSourceName = await AllObjects.appConfigurationPage.fetchPipelineIdOfAnyCiNode();
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'linked-build', config: { linkedPipelineName: 'linked-new', sourcePipelineName: ciSourceName!, appName: devtronAppCreation.appNames[0] as string, checkValidation: false }, cdConfig: { envNameForCd: credentials.EnvNameForCD[1], helmOrGitops: "helm", clusterName: "default_cluster", autoOrManual: 'Auto', deploymentStrat: 'ROLLING' } });
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'job-ci-pipeline-button', config: { pipelineName: 'ci-jobs', branchName: 'main', script: ['echo "hello"'] }, cdConfig: { envNameForCd: 'env2', helmOrGitops: 'helm', clusterName: 'default_cluster', autoOrManual: 'Auto', deploymentStrat: 'ROLLING' } })
    for (let configurePrePostTaskObject of dataForImageScanningTest.prePostTaskConfiguration) {
      await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(configurePrePostTaskObject.workflowNumber, configurePrePostTaskObject.nodeName);
      await AllObjects.prePostCiCd.addPrePostTask(configurePrePostTaskObject.stage, configurePrePostTaskObject.pluginName);
      if (configurePrePostTaskObject.pluginName == 'IMAGE SCAN') {
        await AllObjects.jobsPage.imageScanPlugin();
      }
      else if (configurePrePostTaskObject.pluginName == 'execute') {
        await AllObjects.jobsPage.executeCustomScript('Execute custom task-pre', [], true);
      }
      else {
        await BaseTest.checkToast(page, AllObjects.prePostCiCd.updatePipelineButton, "Pipeline Updated");
      }
    }
  })

  await test.step('Triggering CI and verifying the status of all Nodes', async () => {
    await AllObjects.workflowPage.triggerCiModule("Pipeline Triggered", 3, ['externalCiArtifact:prakhr/test:6a824121-1-5']);
    await AllObjects.workflowPage.triggerCiModule();
    for (let statusVerificationObject of dataForImageScanningTest.cicdStatusVerification) {
      await AllObjects.workflowPage.verifyCiCdStatus(statusVerificationObject.workflowNumber, statusVerificationObject.nodeNumber, statusVerificationObject.status, true, statusVerificationObject.timeout);
    }
  })

  await test.step('Verify the Image Scan Modal on Build History and on cd Nodes', async () => {
    await AllObjects.workflowPage.clickOnCiDetailsButton(0);
    await AllObjects.buildHistoryPage.verifyBuildHistoryPage()
    var docker_image = await AllObjects.buildHistoryPage.verifyArtifacts('test') as string;
    image = docker_image.split(":")[1];
    await AllObjects.buildHistoryPage.goToSecurityTab();
    await AllObjects.buildHistoryPage.verifySecurityScanSummaryCardPage(true, true);
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(true, true, false, false);
    for (let verifyImageScanObject of dataForImageScanningTest.imageScanningVerificationOnCd) {
      await AllObjects.workflowPage.verifyImageScanForCd(verifyImageScanObject.workflowNumber, [image], [1], verifyImageScanObject.cdNodeNumber, false);
      await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(true, true, false, false);
      await AllObjects.workflowPage.closeSelectImageModal();
    }
  })

  await test.step('verify image scan modal On app Details Page and on resource browser', async () => {
    await AllObjects.appDetailsPageCiCd.selectEnvironment(credentials.EnvNameForCD[0]);
    await AllObjects.chartStoreAppDetailsPage.clickOnAppDetailsDeployButton();
    await AllObjects.workflowPage.verifyImageScanForCd(0, [image], [1], 0, true);
    await AllObjects.workflowPage.closeSelectImageModal();
    //Security Scan Card on app detail page.
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModalAtAppDetailsPage(credentials.securityScanData, true, true, true);
    //Resource Tree Image scanning Modal Verification
    await AllObjects.appDetailsPageCiCd.clickOnScanModalResourceTree();
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(false, false, false, true);
    //Resource Browser Image scanning Modal Verification
    await AllObjects.resourceBrowserPage.gotoSecuritySummaryCard(devtronApp);
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(false, false, false, true);
  })

  await test.step(' Verify Scan Results on Global Security Page ', async () => {
    await AllObjects.securityGlobalPage.gotoSecurityGlobalPage();
    await AllObjects.securityGlobalPage.goAndVerifyEnvForAppAtGlobalSecurityPage(devtronApp, ['automation', 'devtron-demo']);
    await AllObjects.securityGlobalPage.clickOnApplicaitonWithEnvironment(devtronApp, "automation");
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(true, true, true, true);

  })

  await test.step('Verify Security Policies Combination of Block Allow etc through api', async () => {
    //API
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.workflowPage.clickOnBuildAndDeployTab();
    await page.getByTestId(`CD-trigger-select-image-0`).click();
    let url = page.url();
    const apiUtils = new ApiUtils(request)
    await AllObjects.securityGlobalPage.verfiySecurityPoliciesAllCombination(apiUtils, url);
    // //UI
    await AllObjects.securityGlobalPage.gotoGlobalSecurityPoliciesPage();
    await AllObjects.securityGlobalPage.verifyGlobalSecurityPoliciesPage();
  })
})

test('Complete Image Scan_oss', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation, request }) => {
  test.setTimeout(45 * 1000 * 60);
  test.skip(process.env.clusterType == "enterprise");
  await test.step('setting up required worklfows', async () => {
    devtronApp = devtronAppCreation.appNames[0];
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    let ciSourceName = await AllObjects.appConfigurationPage.fetchPipelineIdOfAnyCiNode();
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'linked-build', config: { linkedPipelineName: 'linked-new', sourcePipelineName: ciSourceName!, appName: devtronAppCreation.appNames[0] as string, checkValidation: false }, cdConfig: { envNameForCd: credentials.EnvNameForCD[1], helmOrGitops: "helm", clusterName: "default_cluster", autoOrManual: 'Auto', deploymentStrat: 'ROLLING' } });
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.appConfigurationPage.turnOnImageScanToggleButton();
  })
  await test.step('Triggering CI and verifying the status of all Nodes', async () => {
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Succeeded');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Succeeded');
  })
  await test.step('Verify Image Scan Modal on Build History and on cd Nodes', async () => {
    await AllObjects.workflowPage.clickOnCiDetailsButton(0);
    await AllObjects.buildHistoryPage.verifyBuildHistoryPage()
    var docker_image = await AllObjects.buildHistoryPage.verifyArtifacts('test') as string;
    image = docker_image.split(":")[1];
    await AllObjects.buildHistoryPage.goToSecurityTab();
    await AllObjects.buildHistoryPage.verifySecurityScanSummaryCardPage(true, false);
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(true, false, false, false);
  })
  await test.step('Verify Image Scan Modal on App Details Page and on Resource Browser', async () => {
    await AllObjects.appDetailsPageCiCd.selectEnvironment(credentials.EnvNameForCD[0]);
    await AllObjects.chartStoreAppDetailsPage.clickOnAppDetailsDeployButton();
    await AllObjects.workflowPage.verifyImageScanForCd(0, [image], [1], 0, true);
    await AllObjects.workflowPage.closeSelectImageModal();
    //Security Scan Card on app detail page.
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModalAtAppDetailsPage(credentials.securityScanData, true, false, false);
  })
  await test.step(' Verify Scan Results on Global Security Page ', async () => {
    await AllObjects.securityGlobalPage.gotoSecurityGlobalPage();
    await AllObjects.securityGlobalPage.goAndVerifyEnvForAppAtGlobalSecurityPage(devtronApp, ['automation', 'devtron-demo']);
    await AllObjects.securityGlobalPage.clickOnApplicaitonWithEnvironment(devtronApp, "automation");
    await AllObjects.appDetailsPageCiCd.verifySecurityScanCardAndModal(true, false, false, true);

  })

  await test.step('Verify Security Policies Combination of Block Allow etc through api', async () => {
    //API
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.workflowPage.clickOnBuildAndDeployTab();
    await page.getByTestId(`CD-trigger-select-image-0`).click();
    let url = page.url();
    const apiUtils = new ApiUtils(request)
    await AllObjects.securityGlobalPage.verfiySecurityPoliciesAllCombination(apiUtils, url);
    // //UI
    await AllObjects.securityGlobalPage.gotoGlobalSecurityPoliciesPage();
    await AllObjects.securityGlobalPage.verifyGlobalSecurityPoliciesPage();
  })
})



test.afterEach('deletingNodes', async ({ request }, testInfo) => {
  let apiUtils = new ApiUtils(request);
  let token = await apiUtils.login(process.env.PASSWORD!);
  await apiUtils.deleteAllNodesOfApplication(devtronApp, token);
})