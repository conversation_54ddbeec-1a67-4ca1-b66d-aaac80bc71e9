import { expect } from '@playwright/test';
import { BaseTest } from '../../utilities/BaseTest';
import { test } from "../../utilities/Fixtures";
import { ApiUtils } from "../../utilities/ApiUtils";

// Define variables globally so both tests can access them
let sourceJobName: string;
let clonedJobName: string;
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
const scriptToBeExecuted = ["echo 'hello'", "echo 'world'", credentials.configMapOrSecretData[0].TerminalScript[0], credentials.configMapOrSecretData[4].TerminalScript[0]];

// Use a specific storage state for tests
test.use({ storageState: './LoginAuth.json' });

// First test: Create source job with all verifications
test("CreateJobAndVerifyOperations_oss", { tag: '@cicd' }, async ({ page, AllObjects, request }) => {
    // Set timeout for the entire test
    test.setTimeout(15 * 60 * 1000);

    // Initialize API utilities
    const apiUtils = new ApiUtils(request);
    await apiUtils.login(process.env.PASSWORD!);

    // Generate job name that will be used in both tests
    sourceJobName = "source-job-" + BaseTest.generateRandomStringWithCharsOnly(5);

    // Create a source job
    await AllObjects.createAppPage.createCustomAppJob("job", sourceJobName, credentials.ProjectName, process.env.BASE_SERVER_URL as string);

    // Add a GitHub repository
    await AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);

    // Create a workflow
    await AllObjects.jobsPage.createWorkflow("workflow-" + BaseTest.generateRandomStringWithCharsOnly(3));

    // Create a pipeline
    await AllObjects.jobsPage.createPipeline("pipeline-" + BaseTest.generateRandomStringWithCharsOnly(3), credentials.BranchName[0]);
    await AllObjects.jobsPage.clickOnJobNode();

    await expect(async () => {
        // Add a pre task
        await AllObjects.prePostCiCd.addPrePostTask("pre", "execute");
        // Execute a custom script
        await AllObjects.jobsPage.executeCustomScript("task-name-" + BaseTest.generateRandomStringWithCharsOnly(3), scriptToBeExecuted);
        await AllObjects.jobsPage.clickOnJobNode();
        await expect(page.locator('//*[text()="main"]')).toBeVisible({ timeout: 20000 });
    }).toPass({ timeout: 4 * 1000 * 60 });

    // Add a ConfigMap
    await AllObjects.jobsPage.createPipelineButton.click();
    await AllObjects.jobsPage.addConfigMapOrSecret(credentials.configMapOrSecretData[0], false);

    // Add a Secret
    await AllObjects.jobsPage.addConfigMapOrSecret(credentials.configMapOrSecretData[4], false);
    await AllObjects.jobsPage.goBackToWorkflowPage();

    // Trigger CI module
    await AllObjects.workflowPage.triggerCiModule();

    // Go to build history page via click on details link
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

    // Verify build logs and status
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus([
        credentials.configMapOrSecretData[0].TerminalExpectedValue.toString(),
        credentials.configMapOrSecretData[4].TerminalExpectedValue.toString()
    ]);

    // Verify source repo and commit details
    await AllObjects.buildHistoryPage.verifySourceRepoAndCommitDetails();

    // Verify CD status on workflow page
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Succeeded');

    // Trigger CI module again
    await AllObjects.workflowPage.triggerCiModule();

    // Go to build history page via click on details link
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

    // Abort CI build
    await AllObjects.buildHistoryPage.abortCiBuild();

    // Verify CD status on workflow page
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Aborted');
});

// Second test: Clone the job and verify basic configuration
test.skip("CloneJobAndVerifyConfiguration", { tag: '@cicd' }, async ({ page, AllObjects }) => {
    // Set timeout for the entire test
    test.setTimeout(10 * 60 * 1000);

    // Generate random name for cloned job
    clonedJobName = "cloned-job-" + BaseTest.generateRandomStringWithCharsOnly(5);

    console.log(`Cloning job from ${sourceJobName} to ${clonedJobName}`);

    // Navigate to job list page
    await page.goto(`${process.env.BASE_SERVER_URL}/job`);

    // Clone the job 
    await AllObjects.createAppPage.cloningJob(sourceJobName, clonedJobName, 'devtron-demo');

    // Verify the cloned job exists and has basic configuration
    await page.goto(`${process.env.BASE_SERVER_URL}/job/${clonedJobName}/workflow-editor`);

    // Verify workflow exists
    await expect(AllObjects.workflowPage.workflowEditorTab).toBeVisible();

    // Verify Git repository is configured correctly
    await AllObjects.gitRepositoryPage.verifyGitAccountAndRepoConfigured([
        { gitAccountName: credentials.GitAccountName, gitRepoUrl: process.env.GIT_REPO_URL?.split(',')[0] as string }
    ], false);

    // Verify pipeline exists
    await AllObjects.workflowPage.workflowEditorTab.click();
    await expect(page.locator(`//*[@class="workflow-node"]`)).toBeVisible();

    // Verify ConfigMap and Secret exist
    await AllObjects.jobsPage.goBackToWorkflowPage();
    await AllObjects.jobsPage.createPipelineButton.click();
    await expect(page.locator(`//*[contains(text(), "${credentials.configMapOrSecretData[0].resourceName}")]`)).toBeVisible();
    await expect(page.locator(`//*[contains(text(), "${credentials.configMapOrSecretData[4].resourceName}")]`)).toBeVisible();
});

// Clean up after all tests
test.afterEach("Delete Source and Cloned Jobs", async ({ page, AllObjects }) => {
    // Delete the cloned job
    if (clonedJobName) {
        await page.goto(`${process.env.BASE_SERVER_URL}/job/${clonedJobName}/workflow-editor`);
        await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication("job", false, false);
    }

    // Delete the source jobs
    if (sourceJobName) {
        await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication("job", false);
    }
});