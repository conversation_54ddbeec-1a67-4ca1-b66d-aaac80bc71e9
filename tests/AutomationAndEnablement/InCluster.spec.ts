import { test } from '../../utilities/Fixtures';
import { BaseTest } from '../../utilities/BaseTest';

/**
 * InCluster.spec.ts - Test suite for InCluster functionality
 * 
 * This file contains tests for verifying the functionality of in-cluster
 * operations, including job execution and pre/post deployment tasks.
 */

// Read test configuration from external file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");

// Test configuration variables
let jobName: string;
let appName: string;
let applicationUrl: string;

// Scripts to be executed in the tests
const scriptToBeExecuted = [
  "echo 'hello'",
  "echo 'world'",
  credentials.configMapOrSecretData[0].TerminalScript[0],
  credentials.configMapOrSecretData[4].TerminalScript[0]
];

// Use stored authentication state for all tests
test.use({ storageState: './LoginAuth.json' });

if (process.env.isPacketTesting == 'false') {
  test.skip();
}

/**
 * Test case: Verify in-cluster job functionality
 * 
 * This test creates a job, configures it with GitHub repository,
 * adds ConfigMap and Secret, and verifies execution works correctly.
 */
test("Incluster testing for jobs",{ tag: '@globalConfigurations' }, async ({ page, AllObjects }) => {
  // Set timeout for test execution
  test.setTimeout(10 * 60 * 1000);

  try {
    // Generate a unique job name with random suffix
    jobName = "ui-auto-job-" + BaseTest.generateRandomStringWithCharsOnly(6);
    console.log(`Created job with name: ${jobName}`);

    // Step 1: Create a custom job application
    await AllObjects.createAppPage.createCustomAppJob(
      "job",
      jobName,
      credentials.ProjectName,
      process.env.BASE_SERVER_URL!
    );

    // Step 2: Configure source code repository
    AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);

    // Step 3: Set up workflow and pipeline
    await AllObjects.jobsPage.createWorkflow("workflow-" + BaseTest.generateRandomStringWithCharsOnly(3));
    await AllObjects.jobsPage.createPipeline("pipeline-" + BaseTest.generateRandomStringWithCharsOnly(3), credentials.BranchName[0]);
    await AllObjects.jobsPage.clickOnJobNode();
    // Step 4: Configure pre-task execution
    await AllObjects.prePostCiCd.addPrePostTask("pre", "execute");
    await AllObjects.jobsPage.executeCustomScript(
      "task-name-" + BaseTest.generateRandomStringWithCharsOnly(3),
      scriptToBeExecuted,
      true,
      { inclusterEnv: credentials.InclusterEnv, isJob: true }
    );

    // Step 5: Add ConfigMap and Secret resources
    await AllObjects.jobsPage.operBaseOrEnvOverRideResources(credentials.InclusterEnv, false);
    await AllObjects.jobsPage.page.locator('//*[@class="env-config-selector__input"]').fill(credentials.InclusterEnv, { timeout: 6000 });
    await AllObjects.jobsPage.page.keyboard.press('Enter');
    let dataObjectForCm1= credentials.configMapOrSecretData[0].stage= credentials.InclusterEnv;
    let dataObjectForSecret1= credentials.configMapOrSecretData[4].stage= credentials.InclusterEnv;
    await AllObjects.jobsPage.addConfigMapOrSecret(dataObjectForCm1,false,false);  // ConfigMap
    await AllObjects.jobsPage.addConfigMapOrSecret(dataObjectForSecret1,false,false);  // Secret

    // Step 6: Trigger job execution and verify results
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

    // Verify execution logs contain expected values
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus([
      credentials.configMapOrSecretData[0].TerminalExpectedValue.toString(),
      credentials.configMapOrSecretData[4].TerminalExpectedValue.toString()
    ]);

    // Verify source repository and commit details
    await AllObjects.buildHistoryPage.verifySourceRepoAndCommitDetails();

    // Verify CD status on workflow page
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Succeeded");

    // Step 7: Test environment-specific execution
    await AllObjects.workflowPage.triggerCiModule("Pipeline Triggered", 0, undefined, "shared-aks-cluster");
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

    // Abort the CI build and verify status
    await AllObjects.buildHistoryPage.abortCiBuild();
    // Commented out for now as verification may be flaky
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "ABORTED");
  } catch (error) {
    console.error(`Test failure in Incluster job test: ${error}`);
    throw error;  // Re-throw to fail the test
  }
});

/**
 * Test case: Verify pre/post tasks with in-cluster execution
 * 
 * This test creates an application, configures pre/post CI/CD tasks,
 * and verifies that in-cluster execution works correctly for both.
 */
test("Test pre-post with incluster testing", { tag: '@cicd' }, async ({ page, AllObjects }) => {
  // Define pre/post CI scripts
  const preCiScript = ["echo 'pre-ci'"];
  const postCiScript = ["echo 'post-ci'"];

  // Set extended timeout for this test
  test.setTimeout(15 * 60 * 1000);

  try {
    // Step 1: Create application with unique name
    appName = "ui-automation" + BaseTest.generateRandomStringWithCharsOnly(6);
    console.log(`Created application with name: ${appName}`);

    await AllObjects.createAppPage.createCustomAppJob(
      "app",
      appName,
      credentials.ProjectName,
      process.env.BASE_SERVER_URL!
    );

    // Step 2: Configure source repository
    AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);

    // Step 3: Configure build and deployment settings
    await AllObjects.buildConfigurationPage.saveBuildConfigurations(
      credentials.ContainerRegistryName,
      credentials.ContainerRepository
    );
    await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();

    // Store application URL for later use
    applicationUrl = await page.url();

    // Step 4: Add ConfigMap and Secret resources
    for (let i = 0; i < credentials.configMapOrSecretData.length; i++) {
      await AllObjects.jobsPage.addConfigMapOrSecret(credentials.configMapOrSecretData[i]);
    }

    // Step 5: Configure pre/post deployment tasks
    await AllObjects.appConfigurationPage.goToWorkflowEditorTab();
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.InclusterEnv);

    // Configure pre-deployment task
    await AllObjects.prePostCiCd.addPrePostTask("pre", "execute", undefined, true);    // Uncomment if needed: await AllObjects.workflowPage.addCmAndSecretInPreAndPostDeployment(credentials.configMapOrSecretData[0].resourceName.toString())
    await AllObjects.jobsPage.executeCustomScript(
      "pre-cd-task",
      credentials.configMapOrSecretData[0].TerminalScript as string[],
      false,
      { inclusterEnv: credentials.InclusterEnv, isJob: false }
    );

    // Configure post-deployment task
    await AllObjects.prePostCiCd.addPrePostTask("pre", "execute", undefined, true);    // Uncomment if needed: await AllObjects.workflowPage.addCmAndSecretInPreAndPostDeployment(credentials.configMapOrSecretData[4].resourceName.toString())
    await AllObjects.jobsPage.executeCustomScript(
      "post-cd-task",
      credentials.configMapOrSecretData[4].TerminalScript as string[],
      true,
      { inclusterEnv: credentials.InclusterEnv, isJob: false }
    );

    // Step 6: Trigger workflow and verify execution
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();

    // Verify CI success

    // Verify pre-deployment task success
    await AllObjects.workflowPage.clickOnDetailsOfAnyNode(1);
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus([
      credentials.configMapOrSecretData[0].TerminalExpectedValue.toString()
    ]);

    // Verify deployment tasks succeeded
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, "Succeeded");
    await AllObjects.workflowPage.verifyCiCdStatus(0, 2, "Succeeded");

    // Verify post-deployment task success
    await AllObjects.workflowPage.clickOnDetailsOfAnyNode(3);
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus([
      credentials.configMapOrSecretData[4].TerminalExpectedValue.toString()
    ]);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 3, "Succeeded");

    // Step 7: Verify terminal output
    await AllObjects.chartStoreAppDetailsPage.goToTerminal();
    for (let i = 0; i < credentials.configMapOrSecretData.length; i++) {
      console.log(`Verifying terminal for ConfigMap/Secret index: ${i}`);
      await AllObjects.chartStoreAppDetailsPage.verifyTerminal(credentials.configMapOrSecretData[i], "#");
    }
  } catch (error) {
    console.error(`Test failure in pre-post InCluster test: ${error}`);
    throw error;  // Re-throw to fail the test
  }
});


test.afterEach("Delete Jobs and apps", async ({ page, AllObjects }) => {
  try {
    // Clean up application if it was created
    if (appName) {
      console.log(`Cleaning up application: ${appName}`);
      await page.goto(applicationUrl);
      await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication("app");
    }

    // Clean up job if it was created
    if (jobName) {
      console.log(`Cleaning up job: ${jobName}`);
      await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication("job", false);
    }
  } catch (error) {
    console.error(`Error during test cleanup: ${error}`);
    // Don't re-throw as we don't want cleanup failures to mask test failures
  }
});