import { BaseTest } from '../../../utilities/BaseTest';
import { test } from '../../../utilities/Fixtures';
import { expect } from 'playwright/test';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { dataSetForCustomPlugin } from '../../../utilities/DataObjectss.ts/CustomPluginDataObject';


/**
 * Variable Declarations
 */
let devtronAppName: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4);
let pluginName = 'testing' + BaseTest.generateRandomStringWithCharsOnly(4);
let pluginId = 'testing' + BaseTest.generateRandomStringWithCharsOnly(4);
let data = dataSetForCustomPlugin(pluginName, pluginId);


/**
 * Test Configuration
 */
test.use({ storageState: './LoginAuth.json', launchOptions: { slowMo: 1000 }, devtronApps: [devtronAppName], triggerCI: [false] })
test.setTimeout(25 * 1000 * 60);


/**
 * test case will create plugins with shell as container image task and with bash task as well
 * other functionalities like , replace task with plugin , mandat variables , category filters are also tested
 */
test('custom plugin creation', { tag: '@cicd' }, async ({ AllObjects, page, devtronAppCreation }) => {
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    for (let i = 0; i < data.length; i++) {
        await AllObjects.prePostCiCd.addPrePostTask(data[i].taskStage, 'execute');
        if (i == 0) {
            await AllObjects.jobsPage.executeCustomScript('pre', data[i].script as string[], false);
            await AllObjects.prePostCiCd.addOutputVariables(data[i].outputVariableNames!, data[i].outputVariableTypes!);
            await AllObjects.prePostCiCd.addInputVariables([{ varName: 'deep', value: 'reen', varType: 'String', valueConfiguration: { choices: ['reen'], dontAllowCustomInput: true, askValueAtRuntime: true } }, { varName: 'beep', value: '', varType: 'String' }]);
            await AllObjects.prePostCiCd.saveACustomPlugin(data[i].saveCustomPlugin);
            await expect(AllObjects.prePostCiCd.outputAndInputVariableInputField.nth(0)).toBeDisabled();
            await expect(AllObjects.prePostCiCd.outputAndInputVariableInputField.nth(1)).toBeDisabled();
            await AllObjects.prePostCiCd.inputVariablesValueDropdown.nth(0).click();
            await page.keyboard.type('seen');
            await page.keyboard.press('Enter');
            let value2 = await AllObjects.prePostCiCd.inputVariablesValueDropdown.nth(1).textContent();
            expect(value2).toBe('reen');
            await expect(AllObjects.prePostCiCd.outputAndInputVariableInputField.nth(2)).toBeDisabled();
        }
        if (i == 1) {
            await AllObjects.prePostCiCd.setConfigurationForContainerImage({ containerImage: 'alpine:latest', command: 'sh', args: 'reen.sh', mountCustomCode: { script: [`echo "deep" >> aman.txt`, 'cat aman.txt'], mountCustomCodePath: '/reen.sh' }, outputDirectoryPath: ['/reen/aman.txt'] })
            await AllObjects.prePostCiCd.saveACustomPlugin(data[i].saveCustomPlugin);
            await AllObjects.prePostCiCd.deleteTask('post');
            await AllObjects.prePostCiCd.addPrePostTask('post', pluginName);
            await AllObjects.prePostCiCd.selectVersionOfPlugin('0.2.0');
            await AllObjects.jobsPage.taskNameInput.fill('second');
            await AllObjects.prePostCiCd.updatePipelineButton.click();
        }
    }
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'automation');
    await AllObjects.appConfigurationPage.setCiCdAutoOrManual("Manual");
    await AllObjects.appConfigurationPage.updatePipelineButton.click();
    await AllObjects.appConfigurationPage.closeUpdationModal();
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['reen', 'seen', 'deep']);
    await page.goto(process.env.BASE_SERVER_URL + `/global-config/plugin-policy/profiles/create`);
    await AllObjects.mandatoryPluginPage.addPluginButton.click();
    await AllObjects.prePostCiCd.applyTagFilter('Code Review');
    await AllObjects.prePostCiCd.searchAndSelectPluginorTaskFromList(pluginName);

})

/**
 * Cleanup after test execution
 */
test.afterEach('deleting the app', async ({ request }) => {
    let apiUtils = new ApiUtils(request);
    await apiUtils.deleteAllNodesOfApplication(devtronAppName, await apiUtils.login(process.env.PASSWORD!));
})

