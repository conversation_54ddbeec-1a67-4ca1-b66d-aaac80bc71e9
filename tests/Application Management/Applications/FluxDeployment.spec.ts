import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { DeploymentType, SymbolDeploymentType } from '../../../enums/Application Management/Applications/DeploymentTriggerEnum';
import { ApiUtils } from '../../../utilities/ApiUtils';
import YAML from 'yaml';
import { expect } from '@playwright/test';
import clipboardy from 'clipboardy';
// Read test credentials from a file
const Credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronApp="";
let name="";
let appUrl=""
// Main test case
test.use({ storageState: './LoginAuth.json', triggerCI: [true],allChartVersion: ['4.21.0', Credentials.customChartVersion] });
test('Deployment-through-FluxCD_oss',{tag:'@cicd'},async({page,AllObjects,devtronAppCreation,request },testInfo)=>{
  devtronApp=devtronAppCreation.appNames[0]
  appUrl=devtronAppCreation.applicationUrl[0]
  await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
  await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, "ci", 1);
  await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: Credentials.EnvNameForCD[1], deploymentStrat: 'ROLLING',helmOrGitops: 'flux', autoOrManual: 'Auto', clusterName: 'default_cluster' });
  await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1, [], [1]);
  await AllObjects.workflowPage.verifyCiCdStatus(0, 2, "Succeeded");
  await AllObjects.createAppPage.searchAndSelectAppsFromList(devtronAppCreation.appNames[0],false,'app')
  await AllObjects.appDetailsPageCiCd.goToAppDetails();
  await AllObjects.appDetailsPageCiCd.verifyDeploymentTypeSymbolAtAppDetailsPage(SymbolDeploymentType.FLUX);
  await AllObjects.appDetailsPageCiCd.verifyArgoAndFluxTimelineForDeploymentType(DeploymentType.FLUX);
  //Creating Helm Release from Flux CD Helm Release App.
  await page.goto(`${process.env.BASE_SERVER_URL}/resource-browser/1/devtron-demo/helmrelease/helm.toolkit.fluxcd.io/${devtronApp}-${Credentials.EnvNameForCD[1]}/manifest`);
  const payloadForFetchingYaml = AllObjects.resourceBrowserPage.getK8sResourcePayload({
        request,
        keypath: 'metadata.namespace',
        newValue: 'env1',
        resourceName: `${devtronApp}-${Credentials.EnvNameForCD[1]}`,
        clusterId: 1,
        namespace: `devtron-demo`,
        kind: "HelmRelease",
        group: 'helm.toolkit.fluxcd.io',
        version: 'v2'
        });
  const manifest=await AllObjects.resourceBrowserPage.fetchK8sResourceYamlViaHittingApi(payloadForFetchingYaml);
  const  manifestJson = JSON.parse(JSON.stringify(manifest));
  await AllObjects.resourceBrowserPage.deletePropertyFromManifestJson(manifestJson,['metadata.uid','metadata.resourceVersion','metadata.creationTimestamp']);
  await AllObjects.resourceBrowserPage.setNestedProperty(manifestJson,'metadata.name',`${devtronApp}-env1`);
  await AllObjects.resourceBrowserPage.setNestedProperty(manifestJson,'metadata.namespace',`env1`);
  const updatedYaml = YAML.stringify(manifestJson, { indent: 2 });
  await clipboardy.write(updatedYaml)
  await page.goto(`${process.env.BASE_SERVER_URL}/resource-browser/1/helmrelease/helm.toolkit.fluxcd.io`);
  await AllObjects.resourceBrowserPage.createResource('HelmRelease','HelmRelease',{});
  await AllObjects.resourceBrowserPage.searchAnyResourceType('HelmRelease');
  const isResourceVisible=await AllObjects.resourceBrowserPage.searchAnyResourceName(`${devtronApp}-env1`);
  await expect(isResourceVisible).toBe(true);
  //Migrate Flux CD Application.
  await page.goto(devtronAppCreation.applicationUrl[0]);
  await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, "ci", 3);
  await AllObjects.appConfigurationPage.migrateExternalAppsToDevtron("fluxcd","default_cluster",`${devtronApp}-env1`);
  await AllObjects.appConfigurationPage.closeUpdationModal();
  await AllObjects.workflowPage.verifyImageAndTriggerDeployment(2, [], [1]);
  await AllObjects.workflowPage.verifyCiCdStatus(0, 3, "Succeeded");
  await AllObjects.createAppPage.searchAndSelectAppsFromList(devtronAppCreation.appNames[0],false,'app')
  await AllObjects.appDetailsPageCiCd.goToAppDetails();
  await AllObjects.appDetailsPageCiCd.verifyDeploymentTypeSymbolAtAppDetailsPage(SymbolDeploymentType.FLUX);
  await AllObjects.appDetailsPageCiCd.verifyArgoAndFluxTimelineForDeploymentType(DeploymentType.FLUX);

  //Verify External App

});

test('end-to-end-chart-Flux_oss', { tag: '@chartstore' }, async ({ AllObjects }, testInfo) => {
    // Setting timeout
    test.setTimeout(15 * 60 * 1000);
    // Step 1: Generating a random name for the chart
    name = BaseTest.generateRandomStringWithCharsOnly(5);
    console.log('helm app has been created with this nane ' + name);
    let appCreated: string[] = [];
    appCreated.push(name);
    (testInfo as any).apps = appCreated;
    // Step 2: Navigating to chart store and selecting a particular chart
    await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
    await AllObjects.chartStorePage.SelectingChart(Credentials.ChartName, Credentials.ChartSource);
    // Step 3: Entering details and deploying the chart
    var ChartVersion: string | null = await AllObjects.deployChartPage.deployingChart(name, Credentials.ProjectName, Credentials.envNameForCharts, "Flux");
    // Step 4: Verifying app details
    await AllObjects.chartStoreAppDetailsPage.verificationAppDetailsPage(false, ChartVersion!, [Credentials.TerminalScript[0]], Credentials.TerminalExpectedValue[0], Credentials.ChartName);
    //verify Deployment Symbol
    await AllObjects.appDetailsPageCiCd.verifyDeploymentTypeSymbolAtAppDetailsPage(SymbolDeploymentType.FLUX);
    await AllObjects.appDetailsPageCiCd.verifyArgoAndFluxTimelineForDeploymentType(DeploymentType.FLUX);
    // Step 6: Comparing manifest
    await AllObjects.deployChartPage.ComparingManifest();
    // Step 7: Updating chart version
    var updatedChartVersion: string | null = await AllObjects.deployChartPage.updatingChartVersion();

    await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
    await AllObjects.chartStoreAppDetailsPage.verifyChartVersion(updatedChartVersion!);

    });

test.afterEach('deleting apps', async ({ AllObjects, request, page }, testInfo) => {
    test.setTimeout(5 * 1000 * 60);
    console.log("");
    let apiUtils = new ApiUtils(request);
    let token = await apiUtils.login(process.env.PASSWORD!);
    let failureCount = 0;
    try {
        if (testInfo.title == "Deployment through FluxCD") {
            await page.goto(appUrl);
            await AllObjects.createAppPage.searchAndSelectAppsFromList(devtronApp,false,'app')
            await AllObjects.appConfigurationPage.appConfigurationTab.waitFor({ state: 'visible' });
            await AllObjects.appConfigurationPage.appConfigurationTab.click();
            await AllObjects.appConfigurationPage.deleteAppsFromUI(apiUtils);
            
        }
    }
    catch (error) {
        console.error('Error deleting application:', error);
        failureCount++;
    }
    if (test.name == 'end-to-end-chart-Flux_oss_eaMode') {
            let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
            await page.goto(process.env.BASE_SERVER_URL + urlTogo);
            await AllObjects.createAppPage.searchAndSelectAppsFromList(name, true);
            // Deleting the app
            await AllObjects.deployChartPage.deletingChartStoreApp();
        
    }
    expect(failureCount).toBe(0);
})