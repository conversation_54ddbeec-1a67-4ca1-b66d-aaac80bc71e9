// This test file verifies the application cloning functionality in Devtron
import { expect } from "playwright/test";
import { ApiUtils } from "../../../utilities/ApiUtils";
import { BaseTest } from "../../../utilities/BaseTest";
import { dataForDefaultAppSetup, dataSetForApplicationTemplate, updatingDefaultCreatedApp } from "../../../utilities/DataObjectss.ts/ApplicationTemplateDataObject";
import { test } from "../../../utilities/Fixtures";
import { allowOrDeleteOverrideDTO } from "../../../DTOs/Application Management/Applications/BaseDeploymentPageDTO";



// Generate unique app names for source and target applications
let devtronAppNameForFixture = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6)];
// Configure test to use existing auth and create the first app without triggering CI
test.use({ storageState: './LoginAuth.json', devtronApps: [devtronAppNameForFixture[0]], devtronEnvs: ['automation', 'devtron-demo'], triggerCI: [false] });
let apiUtils: ApiUtils;
let token: string;


// Setup before each test: Create and configure the source application with complex configuration
test.beforeEach('setting up app', async ({ AllObjects, page, devtronAppCreation, request }) => {
    // Initialize API utilities and authentication
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);

    // Navigate to the application workflow page
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await AllObjects.workflowPage.workflowEditorTab.click();

    // Get pipeline ID and create additional workflows
    let pipelineId = await AllObjects.appConfigurationPage.fetchPipelineIdOfAnyCiNode(0);
    let appcreationdata = dataForDefaultAppSetup(devtronAppNameForFixture[0], pipelineId!);
    for (let key of appcreationdata) {
        if (key.workflowType == "linked-cd" && process.env.clusterType != "enterprise") continue;
        await AllObjects.appConfigurationPage.createWorkflows(key);
    }

    // Add ConfigMaps and Secrets to the application
    let appUpdationData = updatingDefaultCreatedApp();
    for (let key of appUpdationData.cmcsCreationData) {
        await AllObjects.jobsPage.addConfigMapOrSecret(key);
    }
    await AllObjects.jobsPage.goBackToWorkflowPage();

    // Configure pre/post tasks for CI/CD nodes
    for (let key of appUpdationData.prePostCiCdData) {
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, key.nodeName);
        await AllObjects.prePostCiCd.addPrePostTask(key.stage, key.taskName, key.autoOrManul);
        if (key.nodeName == "ci") {
            // Skopeo plugin configuration is commented out
            //  await AllObjects.prePostCiCd.configureSkopeoPlugin('bains | deep10/bowl');
        }
        else {
            // Add ConfigMaps and Secrets to pre/post deployment tasks
            await AllObjects.workflowPage.addCmAndSecretInPreAndPostDeployment(['test1', 'test2']);
            await AllObjects.jobsPage.executeCustomScript(BaseTest.generateRandomStringWithCharsOnly(4), key.script);
        }
    }

    // Update ConfigMaps and Secrets for different environments
    for (let key of appUpdationData.cmcsUpdationData) {
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage);
        await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence(key.resource, key.resourceName);
        let config: allowOrDeleteOverrideDTO = key.inheritOverrideData as allowOrDeleteOverrideDTO;
        await expect(async () => {
            await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride(config);
            await AllObjects.jobsPage.editCmCs(key.resource, key.resourceName, key.stage, false, key.key, key.value);
        }).toPass({ timeout: 2 * 1000 * 60 });
    }

    // Configure deployment template settings for different environments
    for (let key of appUpdationData.dtData) {
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage);
        await AllObjects.baseDeploymentTemplatePage.deploymentTemplateDiv.click();
        let config: allowOrDeleteOverrideDTO = key.inheritOverrideData as allowOrDeleteOverrideDTO;
        await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride(config);
        if (key.inheritOverrideData.configuration.replaceMergerStrat) {
            await AllObjects.baseDeploymentTemplatePage.editAnyField([key.key], [key.value]);
        }
        else {
            await AllObjects.baseDeploymentTemplatePage.addNewField([key.key], [key.value]);
        }
        await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();
    }

    // Configure multi-git repository setup
    await AllObjects.jobsPage.goBackToWorkflowPage();
    await AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[1] as string, 'Github Public', "./second");
    await AllObjects.appConfigurationPage.configureBranchForMultiGitWithExistingWorkflows(['main']);

    // Configure CI build settings
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.appConfigurationPage.clickOnAllowOverrideOnCiNode();
    await AllObjects.buildConfigurationPage.selectRegistryFromDropDown('bains');
    await AllObjects.buildConfigurationPage.repositoryNameInput.fill('deep10/reen');
    await AllObjects.appConfigurationPage.setTargetPlatformForBuild(['linux/amd64']);
    await AllObjects.appConfigurationPage.updatePipelineButton.click();
})

// Main test: Create a clone of the source application and verify its configuration
test('creating clone application_oss', { tag: '@cicd' }, async ({ AllObjects, page }, testInfo) => {
    // Clone the source application to create a new app
    await AllObjects.workflowPage.workflowEditorTab.click();
    await AllObjects.createAppPage.cloningApp(devtronAppNameForFixture[0], devtronAppNameForFixture[1], 'devtron-demo');
    let mine = await dataSetForApplicationTemplate(AllObjects);

    // Verify Git repositories are configured correctly in the cloned app
    await AllObjects.gitRepositoryPage.verifyGitAccountAndRepoConfigured([
        { gitAccountName: 'Github Public', gitRepoUrl: 'https://github.com/deepak-devtron/sample-html.git' },
        { gitAccountName: 'Github Public', gitRepoUrl: 'https://github.com/aman10000q/sample-html.git' }
    ], false)

    // Verify build configuration is copied correctly
    console.log('paseed');
    await AllObjects.buildConfigurationPage.buildConfigurationTab.click();
    await AllObjects.buildConfigurationPage.verifySavedRegistry('devtron-test', 'test');

    // Verify workflows are copied correctly
    console.log('passed 2');
    await AllObjects.workflowPage.workflowEditorTab.click();
    console.log('passed 3');
    process.env.clusterType?.includes('oss') ? await expect(AllObjects.appConfigurationPage.workflowHeader).toHaveCount(7) : await expect(AllObjects.appConfigurationPage.workflowHeader).toHaveCount(8);
    await AllObjects.workflowPage.buildAndDeployTab.click();
    process.env.clusterType?.includes('oss') ? await expect(page.locator(`//*[@class="workflow-node"]`)).toHaveCount(16) : await expect(page.locator(`//*[@class="workflow-node"]`)).toHaveCount(17);

    // Verify deployment templates, ConfigMaps and Secrets are copied correctly
    await AllObjects.appConfigurationPage.appConfigurationTab.click();
    for (let key of mine.dtConfiguration) {
        let cmValueName: string = key.stage == "automation" ? 'test' : 'rest';
        let secretValueName: string = key.stage == "automation" ? 'best' : 'nest';
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage, true);
        await AllObjects.baseDeploymentTemplatePage.deploymentTemplateDiv.click();
        await AllObjects.baseDeploymentTemplatePage.verifyTheMergeStrategyApplied(key.mergeStrat);
        await AllObjects.baseDeploymentTemplatePage.verfiyFieldValues([key.key], [key.value]);
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage, true);
        await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence('ConfigMaps', 'test1');
        await AllObjects.jobsPage.verifyCmSecrets({ keyName: ['key1'], valueName: [cmValueName] });
        await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence('Secrets', 'test2');
        await AllObjects.jobsPage.verifyCmSecrets({ keyName: ['key2'], valueName: [secretValueName] });
    }
})

//Clean up: Delete all created applications after tests
test.afterEach('deleting apps', async ({ AllObjects, page }) => {
    let failureCount = 0;
    for (let i = devtronAppNameForFixture.length - 1; i >= 0; i--) {
        try {
            await apiUtils.deleteAllNodesOfApplication(devtronAppNameForFixture[i], token);
        }
        catch (error) {
            console.error('Error deleting application in clone app test case', error);
            failureCount++;
        }
    }
})
