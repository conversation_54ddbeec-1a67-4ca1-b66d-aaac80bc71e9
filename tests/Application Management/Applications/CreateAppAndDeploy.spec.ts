import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { DataSet } from '../../../utilities/DataObjectss.ts/multipleTestDataObject';
import { expect } from '@playwright/test';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { OctokitClient } from '../../../utilities/Third Party Clients/OctokitClient';
import { ResourceBrowserPage } from '../../../Pages/Infrastructure Management/ResourceBrowserPage';

// Read test credentials from a file
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppFixtureNames: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6);
let appsCreated: string[] = [devtronAppFixtureNames];
// Main test case
let imageBuilt: string;
let filterName: string;
let cdFilterDeletion: boolean = false;
let cdFilterUrl: string;
let applicationUrl: string;
let containerRegistryArray: string[] = [];


test.describe('test cases without ci trigger and single app', async () => {
  test.skip(process.env.clusterType == "eaoss");
  test.use({ storageState: './LoginAuth.json', triggerCI: [false], devtronApps: [devtronAppFixtureNames] });
  test.beforeEach('end-to-end test', async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
    // Set a timeout for the entire test
    test.setTimeout(15 * 60 * 1000);
    applicationUrl = devtronAppCreation.applicationUrl[0];
    imageBuilt = devtronAppCreation.imageBuilt![0];

  });
  test('customCiTag_oss', { tag: '@cicd' }, async ({ AllObjects }) => {
    test.setTimeout(20 * 1000 * 60);

    /* === Section 1: verify wrong values for tag and verify abort ci build case === */

    // Step 1: Select CI node in App Configuration
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    // Step 2: Generate a custom CI tag
    var customTag: string = BaseTest.generateRandomString(6);
    // Step 3: Set up custom CI tag in Build Configuration
    await AllObjects.buildConfigurationPage.cutomCiTagSetup(credentials.customCiTag, '{x}' + customTag);

    // Step 4: Trigger CI module
    await AllObjects.workflowPage.triggerCiModule();
    // Step 5: Go to Build History page via Details link
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
    // Step 6: Abort CI build in Build History page
    await AllObjects.buildHistoryPage.abortCiBuild();

    //---------------------------------------------

    /* === Section 2: verify normal tag creating and verify that counter get increased automatically === */

    // Step 7: Select CI node in App Configuration
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    // Step 8: Toggle custom CI tag in Build Configuration
    await AllObjects.buildConfigurationPage.togglingCustomCiTag();
    // Step 9: Enter and verify CI tag counter value
    await AllObjects.buildConfigurationPage.enterAndVerifyCiTagCounterValue('0', '1');
    // Step 10: Trigger CI module
    await AllObjects.workflowPage.triggerCiModule();
    // Step 11: Go to Build History page via Details link
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
    // Step 12: Verify Build History page details
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(["Login Succeeded"]);
    await AllObjects.buildHistoryPage.verifySourceRepoAndCommitDetails();
    await AllObjects.buildHistoryPage.verifyArtifacts(customTag);
    await AllObjects.buildHistoryPage.verifyBuildHistoryPage();

    //---------------------------------------------

    /* === Section 3:- verify that on using same tag we get warning and ci should get failed  === */

    // Step 13: Go to App Configurations page
    await AllObjects.buildHistoryPage.gotoAppConfigurationsPage();
    // Step 14: Select CI node in App Configuration
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    // Step 15: Toggle custom CI tag in Build Configuration
    await AllObjects.buildConfigurationPage.togglingCustomCiTag();
    // Step 16: Enter and verify CI tag counter value
    await AllObjects.buildConfigurationPage.enterAndVerifyCiTagCounterValue('0', '1');
    // Step 17: Verify desired image tag already exist warning
    await AllObjects.workflowPage.triggerCiModule('[{Desired image tag already exists}]');
    //await AllObjects.workflowPage.verifyDuplicateImageWarning("[{Desired image tag already exists}]",async()=>await AllObjects.workflowPage.triggerCiModule());
    // Step 18: Verify CI status on Workflow page
    await AllObjects.workflowPage.ciModalCloseIcon.click();
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Failed");

    //---------------------------------------------

  })


  test("verifycmcs_oss ", { tag: '@cicd' }, async ({ page, AllObjects, createExternalCiWorkflow }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    await page.goto(applicationUrl);
    for (let i = 0; i < credentials.configMapOrSecretData.length; i++) {
      await AllObjects.jobsPage.addConfigMapOrSecret(credentials.configMapOrSecretData[i]);
    }
    await AllObjects.jobsPage.goBackToWorkflowPage();
    await AllObjects.appConfigurationPage.goToWorkflowEditorTab();
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.EnvNameForCD[0]);
    await AllObjects.prePostCiCd.addPrePostTask("pre", "execute");
    await AllObjects.workflowPage.addCmAndSecretInPreAndPostDeployment([credentials.configMapOrSecretData[4].resourceName.toString()])
    await AllObjects.jobsPage.executeCustomScript("pre-cd-task", credentials.configMapOrSecretData[4].TerminalScript as string[]);
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Running");
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, "Progressing");
    await AllObjects.workflowPage.clickOnDetailsOfAnyNode(0);
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus([credentials.configMapOrSecretData[4].TerminalExpectedValue.toString()])
    await AllObjects.chartStoreAppDetailsPage.goToTerminal();
    for (let i = 0; i < credentials.configMapOrSecretData.length; i++) {
      await AllObjects.chartStoreAppDetailsPage.verifyTerminal(credentials.configMapOrSecretData[i].TerminalScript, credentials.configMapOrSecretData[i].TerminalExpectedValue);
    }
  });

  test("Push Charts using OCI-registry and pull using chart_eaMode", { tag: '@cicd' }, async ({ AllObjects, createExternalCiWorkflow }) => {
    /**
     * Variable Declaration
     */
    let dockerPrivateRepoListValue = process.env.clusterType?.includes('ea') ? process.env.DOCKER_REGISTRY_USERNAME + '/' + 'es1y1' : process.env.DOCKER_REGISTRY_USERNAME + "/" + BaseTest.generateRandomString(5)
    test.setTimeout(30 * 1000 * 60)
    //Adding Docker Containter registry if not added and if added only adding repository that needs to be pushed.
    await AllObjects.containerRegistryPage.goToContainerRegistry();
    credentials.registryData.DockerRegistry.privateRepoList.push(dockerPrivateRepoListValue);

    credentials.registryData.DockerRegistry.registryName = "docker-" + BaseTest.generateRandomString(5);
    containerRegistryArray.push(credentials.registryData.DockerRegistry.registryName);
    //Create Docker Container Registry
    await AllObjects.containerRegistryPage.selectAndAddRegistry(credentials.registryData.DockerRegistry.registryName, "Docker", "private", credentials.registryData);

    credentials.registryData.EcrRegistry.registryName = "ecr-" + BaseTest.generateRandomString(5);
    containerRegistryArray.push(credentials.registryData.EcrRegistry.registryName);
    //Create Ecr Container Registry.
    await AllObjects.containerRegistryPage.selectAndAddRegistry(credentials.registryData.EcrRegistry.registryName, "Ecr", "private", credentials.registryData);
    //Adding Virtual CD and pushing repo to Docker regsitry.
    var counter = 0;
    for (let key in credentials.registryData) {
      if (++counter > 2) break;
      if (process.env.clusterType == "enterprise") {
        await AllObjects.appConfigurationPage.goToAppConfigurationPage(applicationUrl);
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, "webhook", 1);
        //await AllObjects.appConfigurationPage.clickOnAddCdIcon(0, "ci", true, "parallel");
        console.log('refistry name is' + credentials.registryData[key].registryName);
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: credentials.VirtualEnvironment, clusterName: credentials.VirtualClusterName, virtualEnvConfig: { pushOrNot: 'Push to registry', regName: credentials.registryData[key].registryName, repoName: credentials.registryData[key].privateRepoList[0] } })
        //Triggerd Image in order to push repo.
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1);
      }
      //Go to chartStore and Fetching repo from Docker Container Registry.
      await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
      await AllObjects.chartStorePage.fetchCharts(credentials.registryData[key].privateRepoList[0], credentials.registryData[key].registryName);
      await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
      //Verifing Pulled repo from Docker contaier Registry.
      await AllObjects.chartStorePage.SelectingChart(credentials.registryData[key].privateRepoList[0], credentials.registryData[key].registryName);
      if (key == "DockerRegistry") {
        const name: string = "oci-chart-" + BaseTest.generateRandomString(5);
        await AllObjects.deployChartPage.deployingChart(name, credentials.ProjectName, credentials.envNameForCharts, "helm");
        // Verifying app details
        await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
        //Delete Chart Store App
        await AllObjects.createAppPage.navigateToHelmAppListPage(process.env.BASE_SERVER_URL as string);
        await AllObjects.createAppPage.searchAndSelectAppsFromList(name, true);
        // Deleting the app
        await AllObjects.deployChartPage.deletingChartStoreApp();
      }
      //Removing charts that is being added.
      if (process.env.clusterType == "enterprise") {
        await AllObjects.containerRegistryPage.removeRepoContainerRegistry(credentials.registryData[key].privateRepoList[0], credentials.registryData[key].registryName);
        await AllObjects.appConfigurationPage.goToAppConfigurationPage(applicationUrl);
        await AllObjects.appConfigurationPage.deleteParticularCd(credentials.VirtualEnvironment);
      }
    }
  });


})


test.describe('2 applications without ci trigger', async () => {
  test.use({ storageState: './LoginAuth.json', devtronApps: [devtronAppFixtureNames, 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)], triggerCI: [false, false] })
  test.beforeEach('end-to-end test', async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
    // Set a timeout for the entire test
    test.setTimeout(15 * 60 * 1000);
    applicationUrl = devtronAppCreation.applicationUrl[0];
    imageBuilt = devtronAppCreation.imageBuilt![0];
  });
  test('Application Group Sanity_oss', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation, request }, testInfo) => {
    //Create 2nd App
    appsCreated.push(devtronAppCreation.appNames[1]);
    test.setTimeout(30 * 60 * 1000);
    let appGroupPageUrl: string;
    let filterName: string;
    await test.step('bulk deploy brnach change checking', async () => {
      await AllObjects.globalConfigPage.clickingOnApplicationGroup();

      // Ensure that the application group page is loaded and verify its elements.
      await AllObjects.applicationGroupPage.VerifyApplicationGroupPage(credentials.applicationGroup);

      // Search for the specified environment (using the first environment name from the credentials)
      await AllObjects.applicationGroupPage.searchEnvironment(credentials.EnvNameForCD[0]);

      // Generate a random filter name for selecting applications
      filterName = "appgroup-filter" + BaseTest.generateRandomStringWithCharsOnly(6);

      // Select the specified applications and apply the generated filter name
      await AllObjects.applicationGroupPage.selectApplcations(appsCreated[0], appsCreated[1]);

      //Crete Filter of selected Application.
      await AllObjects.applicationGroupPage.createOrApplyFilter(filterName);

      // Select pipelines on the application group page
      await AllObjects.applicationGroupPage.selectPipelines(credentials.BranchName[0], "");
      await AllObjects.applicationGroupPage.bulkDeploy(false, 'test', undefined);
      await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Progressing", false);
      await AllObjects.overViewPage.clickOnOverviewTab();
      appGroupPageUrl = page.url();

    });

    // await test.step('app details page sanity', async () => {
    //   console.log('Starting App Details Page sanity testing');
    //   test.skip()
    //   // Navigate to the application details page
    //   await page.goto(appGroupPageUrl);
    //   await AllObjects.applicationGroupPage.createOrApplyFilter(filterName, true);
    //   await AllObjects.appDetailsPageCiCd.selectEnvironment(appsCreated[0]);
    //   await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
    //   let commitHash1 = imageBuilt!.split(':')[1].split(`-`)[0];
    //   let commitHash2 = imageBuilt!.split(':')[1].split(`-`)[1];
    //   console.log('commit hash 1 is', commitHash1);
    //   console.log('commit hash 2 is', commitHash2);
    //   await AllObjects.appDetailsPageCiCd.verifyCommitInfo([{ branchName: 'main', commitHash: commitHash1.slice(0, 7), commitCardNumber: 1 }, { branchName: 'main', commitHash: commitHash2.slice(0, 7), commitCardNumber: 0 }]);
    //   await AllObjects.chartStoreAppDetailsPage.openResourcesAndClickOnSpecificResource('workloads', 'Deployment');
    //   await AllObjects.appDetailsPageCiCd.deleteAnyResource(true);
    //   await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus("Missing");
    // });

    await test.step('pre-requisite-creation', async () => {
      const apiUtils = new ApiUtils(request);
      await AllObjects.createAppPage.searchAndSelectAppsFromList(devtronAppCreation.appNames[0]);
      let appId = page.url().split('/')[5];
      let token: string;
      if (process.env.isStaging == "true") {
        token = process.env.stagingSuperAdminToken!;
      }
      else {
        token = await apiUtils.login(process.env.PASSWORD!)
      }
      let envObject = await apiUtils.getEnvObject(token, "automation");
      await apiUtils.addConfigOrSecretInEnv([{
        appId: Number(appId),
        token: token,
        key: 'custom-key',
        value: 'custom-value',
        name: 'custom-test',
        isSecret: false,
        envId: Number(envObject.id),
      }]);
      await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'automation');
      await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
      await AllObjects.appConfigurationPage.updatePipelineButton.click();
    });
    await test.step('hibernate/unhibernate/clonePipeline/restartworkloads', async () => {
      const testData = DataSet.ApplicationGroupTest.appGroupDataSet();
      for (const key of testData) {
        if (key.processName == "clonePipeline" && process.env.clusterType == "oss") {
          continue;
        }
        else {
          await page.goto(appGroupPageUrl);
          await AllObjects.applicationGroupPage.createOrApplyFilter(filterName, true);
          await AllObjects.overViewPage.selectApplicationsOnAppGroupOverViewPage([devtronAppCreation.appNames[0]]);
          await key.functionToCall(key.processName, key.isSuccedded, AllObjects, devtronAppCreation.appNames[0]);
        }
      }
    })
  });
  test('Test linked pipeline by creating pipeline in the same app_oss', { tag: '@cicd' }, async ({ AllObjects, request, page, devtronAppCreation }, testInfo) => {
    // Set a timeout for the entire test
    let apiutils = new ApiUtils(request);
    let parentAppId = await apiutils.getAppIdFromAppName(devtronAppCreation.appNames[0], await apiutils.login(process.env.PASSWORD!));
    let appUrls: string[] = [devtronAppCreation.applicationUrl[0], devtronAppCreation.applicationUrl[1]];
    await page.goto(appUrls[0]);
    let ciId = await AllObjects.appConfigurationPage.fetchPipelineIdOfAnyCiNode();
    for (let i = 0; i <= 1; i++) {
      await page.goto(appUrls[i]);
      await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'automation');
      await AllObjects.appConfigurationPage.setCiCdAutoOrManual('Manual');
      await AllObjects.appConfigurationPage.updatePipelineButton.click();
      await AllObjects.appConfigurationPage.closeUpdationModal();
      await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'linked-build', config: { linkedPipelineName: 'test', sourcePipelineName: ciId!, appName: devtronAppCreation.appNames[0], checkValidation: true } })
      await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(1, 'ci', 0);
      if (process.env.clusterType == "enterprise") {
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: credentials.VirtualEnvironment, autoOrManual: 'Auto', clusterName: credentials.VirtualClusterName, virtualEnvConfig: { pushOrNot: 'Push to registry', regName: 'bains', repoName: 'deep10/' + BaseTest.generateRandomStringWithCharsOnly(5) } });
      }
      else {
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'devtron-demo', helmOrGitops: 'helm', clusterName: 'default_cluster' });
      }
    }
    for (let j = 0; j <= 1; j++) {
      await page.goto(appUrls[0]);
      if (j == 1) {
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
        await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
        await AllObjects.jobsPage.executeCustomScript('failing', ['exit 1']);
      }
      let statusToVerify: ("Running" | "Succeeded" | "Failed")[] = j == 0 ? ["Running", "Succeeded"] : ["Failed"];
      await AllObjects.workflowPage.triggerCiModule();
      for (let status of statusToVerify) {
        for (let apps of appUrls) {
          await page.goto(apps);
          await AllObjects.workflowPage.clickOnBuildAndDeployTab();
          await expect(AllObjects.workflowPage.ciLinkedStatus).toContainText(status, { timeout: 8 * 60 * 1000 });
        }
      }
      if (j == 0) {
        for (let apps of appUrls) {
          await page.goto(apps);
          await AllObjects.workflowPage.verifyImageOnSelectImageModal(0, ['test'], 1, [1]);
          await AllObjects.workflowPage.ciModalCloseIcon.click();
          await AllObjects.workflowPage.verifyCiCdStatus(1, 1, 'Succeeded')
          await AllObjects.workflowPage.clickOnDetailsOfAnyNode(0, 1);
          await AllObjects.buildHistoryPage.verifyNavigationToViewSourcePipeline(String(parentAppId), 'Succeeded');
        }
      }
    }
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await expect(AllObjects.appConfigurationPage.ciDeletePipelineButton).toBeDisabled();
    await apiutils.deleteAllNodesOfApplication(devtronAppCreation.appNames[1], await apiutils.login(process.env.PASSWORD!));
  });

})

test.describe('test cases with ci trigger', async () => {

  test.use({ storageState: './LoginAuth.json', triggerCI: [true], devtronApps: [devtronAppFixtureNames], repoNumber: 1 });
  test.beforeEach('end-to-end test', async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
    // Set a timeout for the entire test
    test.setTimeout(15 * 60 * 1000);
    applicationUrl = devtronAppCreation.applicationUrl[0];
    imageBuilt = devtronAppCreation.imageBuilt![0];
  });

  test(' Helm App Details Validations and operations_oss', { tag: '@cicd' }, async ({ AllObjects, request }) => {
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, "Progressing");
    // Select the environment on Deployment History Page.
    await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[0]);
    // Verify Deployment Status on Deployment History Page.
    await AllObjects.deploymentHistory.verifyDeploymentStatus();
    // Verify Artifacts on Deployment History Page.
    await AllObjects.deploymentHistory.verifyArtifactsOnDeploymentHistory(imageBuilt);
    // Verify Source Info on Deployment History Page.
    await AllObjects.deploymentHistory.verifySourceInfo(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.BranchName[0]);
    await AllObjects.appDetailsPageCiCd.selectEnvironment(credentials.EnvNameForCD[0]);
    await AllObjects.chartStoreAppDetailsPage.verifyStatusOfParticularResource([{ resourceType: 'workloads', resourceName: 'Pod', status: 'Running' }]);
    await AllObjects.chartStoreAppDetailsPage.goToTerminal();
    await test.step('Ephemeral Containers ', async () => {
      for (let i = 0; i <= 1; i++) {
        for (let j = 0; j < credentials.ephimeralContainer.length; j++) {
          if (i == 1 && j == credentials.ephimeralContainer.length - 1) {
            continue;
          }
          let targetContainerName: string = credentials.ephimeralContainer[j].targetContainerName && i == 0 ? credentials.ephimeralContainer[j].targetContainerName : devtronAppFixtureNames;
          await AllObjects.chartStoreAppDetailsPage.launchEphimeralContainer(targetContainerName, credentials.ephimeralContainer[j].containerNamePrefix, Boolean(i), credentials.ephimeralContainer[j].isSuccessfull, credentials.ephimeralContainer[j].containerImage);
          await AllObjects.chartStoreAppDetailsPage.verifyTerminal([credentials.TerminalScript[0]], credentials.TerminalExpectedValue[0]);
          if (credentials.ephimeralContainer[j].isSuccessfull) {
            await AllObjects.chartStoreAppDetailsPage.removeEphimeralContainer(credentials.ephimeralContainer[j].containerNamePrefix);
          }
          await AllObjects.chartStoreAppDetailsPage.verifyContainerListing('Ephemeral', false, credentials.ephimeralContainer[j].containerNamePrefix);
        }
      }
    });
    await test.step('Download logs', async () => {
      let data = DataSet.DownloadLogs.dataObjectForDownloadLogs();
      for (let key of data) {
        await AllObjects.chartStoreAppDetailsPage.downloadLogs({ valueToSelectFromDropdown: key.valueToSelectFromDropdown, customDataSet: key.customDataSet });
      }
    })
    if (!process.env.clusterType?.includes('oss')) {
      await test.step('download files ', async () => {
        await AllObjects.chartStoreAppDetailsPage.downloadFile('/myfile.txt', true);
      })
    }
    await test.step('navigation to resource browser', async () => {
      for (let i = 0; i <= 1; i++) {
        let valueToSelect: "default cluster" | "all resources" = i == 0 ? 'default cluster' : 'all resources';
        let isDefaultCluster = i == 0 ? true : false;
        let navigatedPage = await AllObjects.chartStoreAppDetailsPage.verifyNavigationToResourceBrowserFromAppDetailsPage(valueToSelect);
        let resourceBrowser = new ResourceBrowserPage(navigatedPage);
        await resourceBrowser.verifyExistingNamespaceApplied(isDefaultCluster, 'automation');
      }
    })
    await test.step('verify service ', async () => {
      await AllObjects.chartStoreAppDetailsPage.verifyStatusOfParticularResource([{ resourceName: 'Service', resourceType: 'networking', status: 'Healthy' }]);
      await AllObjects.chartStoreAppDetailsPage.verifyServiceUrlAndManifest(devtronAppFixtureNames, 'automation', 'ClusterIP', '80');
    })
    await test.step('helm hooks', async () => {
      let apiUtils = new ApiUtils(request);
      let apiToken = await apiUtils.login(process.env.PASSWORD!);
      let appId = await apiUtils.getAppIdFromAppName(devtronAppFixtureNames, apiToken);
      await apiUtils.updateTemplate(apiToken, 'Deployment', '4.20.0', appId);
      await AllObjects.chartStoreAppDetailsPage.clickOnAppDetailsDeployButton();
      await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0, [imageBuilt.split(':')[1]], [1], 0, true);
      await AllObjects.chartStoreAppDetailsPage.verifyDeploymentStatus('In progress');
      await AllObjects.chartStoreAppDetailsPage.verifyStatusOfParticularResource([{ resourceType: 'workloads', resourceName: 'Job', status: 'Missing' }]);
      await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus("Healthy");
    })
    await test.step('verify missign and failed status', async () => {
      await AllObjects.chartStoreAppDetailsPage.openResourcesAndClickOnSpecificResource('workloads', 'Deployment');
      await AllObjects.appDetailsPageCiCd.deleteAnyResource(true);
      await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus("Missing");
    })
  })

  test('cd-filter-condition', { tag: '@globalConfigurations' }, async ({ page, AllObjects }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    filterName = BaseTest.generateRandomString(7);
    let octokitclinetPage = new OctokitClient(process.env.GITHUB_OCTOKIT_TOKEN!);
    let latestShaId = await octokitclinetPage.getLatestCommit('aman10000q', 'sample-html');
    let dataForConditions = credentials.cdFilterConditions;
    dataForConditions[2] = `containerImageTag.startsWith('${latestShaId.substring(0, 4)}')`;

    var isBlockedTested = false;
    cdFilterDeletion = true;
    await page.goto(applicationUrl);
    await AllObjects.workflowPage.addReleaseTagsOnImage(0, "reen")
    let navigationPageUrl = page.url();
    await AllObjects.globalConfigPage.goToGlobalConfigurations(process.env.BASE_SERVER_URL as string);
    await AllObjects.globalConfigPage.clickOnCdFilterIcon();
    cdFilterUrl = page.url();
    var isNewFilterCondition: boolean = true;
    for (var conditions of dataForConditions) {
      for (var i = 0; i <= 1; i++) {
        if (i == 0 && conditions == "'reen' in imageLabels") {
          continue;
        }
        for (var env of credentials.cdFilterEnv) {
          if (env == 'automation' && conditions == "'reen' in imageLabels" || env == "All existing + future prod environments" && conditions == "'reen' in imageLabels") {
            continue;
          }
          await AllObjects.filterConditionPage.createNewFilterCondition(isNewFilterCondition, filterName, conditions, i, appsCreated[0], env);
          isNewFilterCondition = false;
          await page.goto(navigationPageUrl);
          await page.waitForLoadState('load');
          if (i == 0 && (env == "automation" || env == "All existing + future prod environments")) {
            console.log(env);
            await AllObjects.workflowPage.verifyTagsOnImage('Excluded', true, 0);
            await AllObjects.workflowPage.verifyNumberOfEligibleImages(0, 0, true);
          }
          else {
            await AllObjects.workflowPage.verifyTagsOnImage('Excluded', false, 0);
          }
          if (isBlockedTested == false && conditions != "'reen' in imageLabels") {
            await page.goto(navigationPageUrl);
            await page.waitForLoadState('load');
            await AllObjects.workflowPage.triggerCiModule();
            await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Running");
            await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Succeeded");
            await AllObjects.workflowPage.verifyBlockedAutoTriggerAndAppliedFilter(i, conditions, 0);
            isBlockedTested = true;
          }
          await page.goto(cdFilterUrl);
          await page.waitForLoadState('load');
        }
      }
    }
  })

  test('skopeo_oss', { tag: '@cicd' }, async ({ AllObjects }) => {
    test.setTimeout(30 * 1000 * 60);
    await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, credentials.EnvNameForCD[0], 0)
    await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: credentials.EnvNameForCD[1], helmOrGitops: 'helm', clusterName: 'default_cluster' });
    var firstImage: string | null;
    var iterationCount: number = 0;
    var imageCountVerificationCd: number = 2;
    var firstImageTag: string = "";
    var customTag: string = "";
    for (var i = 0; i < credentials.skopeoData.length; i++) {
      await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.skopeoData[i].parentNode.toString());
      await AllObjects.prePostCiCd.addPrePostTask(credentials.skopeoData[i].stage.toString(), "Copy container image");
      await AllObjects.prePostCiCd.configureSkopeoPlugin(credentials.skopeoData[i].destination_path.toString());
      while (iterationCount < Number(credentials.skopeoData[i].iterationCount)) {
        if (credentials.skopeoData[i].parentNode == "ci") {
          await AllObjects.workflowPage.triggerCiModule();
          await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
          await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(["Login Succeeded"]);
          firstImage = await AllObjects.buildHistoryPage.verifyArtifacts('test');
          firstImageTag = firstImage!.split(':')[1];
        }
        else {
          if (iterationCount == 1) {
            customTag = BaseTest.generateRandomString(6) + '{X}';
            await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.skopeoData[i].parentNode.toString());
            await AllObjects.buildConfigurationPage.togglingCustomCiTag();
            await AllObjects.appConfigurationPage.setupCustomTagForCd(credentials.skopeoData[i].stage.toString(), customTag);
          }
          await AllObjects.appConfigurationPage.closeUpdationModal();
          await AllObjects.workflowPage.verifyImageAndTriggerDeployment(Number(credentials.skopeoData[i].prePostNodeNumber), [firstImageTag], [Number(credentials.skopeoData[i].imageCountTobeVisible)]);
          await AllObjects.workflowPage.verifyCiCdStatus(0, Number(credentials.skopeoData[i].nodeNumber), "Succeeded");
          if (iterationCount == 0) {
            await AllObjects.workflowPage.verifyImageAndTriggerDeployment(Number(credentials.skopeoData[i].prePostNodeNumber), [firstImageTag], [Number(credentials.skopeoData[i].imageCountTobeVisible)], 0, false, { isEligibleForTrigger: false, isTriggerPageVisible: true });
            await AllObjects.workflowPage.prePostCdCloseIconButton.click();
            imageCountVerificationCd = 2;
          }
          else {
            firstImageTag = customTag.split('{')[0] + "0";
            imageCountVerificationCd = 1;
          }
        }
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(Number(credentials.skopeoData[i].nodeNumber), [firstImageTag], [imageCountVerificationCd]);
        if (credentials.skopeoData[i].stage == "pre" && iterationCount == 1) {
          await AllObjects.workflowPage.verifyCiCdStatus(0, 2, "Succeeded");
        }
        await AllObjects.deploymentHistory.selectEnvironment(credentials.skopeoData[i].environment.toString());
        await AllObjects.deploymentHistory.verifyArtifactsOnDeploymentHistory(`${credentials.skopeoData[i].imageRegistryAndRepo}:${firstImageTag}`);
        iterationCount++;
      }
      iterationCount = 0;
    }
  })


})


test.afterEach('deletingNodes', async ({ page, AllObjects, context, request }, testInfo) => {
  test.setTimeout(8 * 1000 * 60);
  let apiUtils = new ApiUtils(request);
  try {
    if (!process.env.clusterType?.includes('ea')!) {
      for (var i = 0; i < appsCreated.length; i++) {
        console.log('app we are going to delete is' + appsCreated[i]);
        await apiUtils.deleteAllNodesOfApplication(appsCreated[i], await apiUtils.login(process.env.PASSWORD!));
        if (cdFilterDeletion) {
          await page.goto(cdFilterUrl);
          await AllObjects.filterConditionPage.deleteFilterCondition(filterName);
          cdFilterDeletion = false;
        }
      }
    }
  }
  catch (error) {
    console.log('issue in deleting the app');
  }
  if ((testInfo.title) == "Push Charts using OCI-registry and pull using chart") {
    await expect(async () => {
      await AllObjects.containerRegistryPage.goToContainerRegistry();
      await AllObjects.containerRegistryPage.deleteRegistry(containerRegistryArray);
    }).toPass({ timeout: 4 * 1000 * 60 });
  }
})
