import { expect, Page } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { test } from "../../../utilities/Fixtures";
import { ApiUtils } from '../../../utilities/ApiUtils';


let devtronAppsToBeCreated: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
test.use({ storageState: './LoginAuth.json', devtronApps: devtronAppsToBeCreated, triggerCI: [false, false, false] });
test('recently visited devtron apps', { tag: '@cicd' }, async ({ AllObjects, page, devtronAppCreation }) => {
    test.setTimeout(10 * 1000 * 60);
    for (let i = 0; i < devtronAppsToBeCreated.length; i++) {
        await page.goto(devtronAppCreation.applicationUrl[i]);
        await expect(AllObjects.appConfigurationPage.newWorkflowButton).toBeVisible({ timeout: 25000 });
    }
    for (let i = 0; i < devtronAppsToBeCreated.length; i++) {
        await AllObjects.createAppPage.clickOnAnyAppInRecentlyVisitedAppsDropdown(devtronAppsToBeCreated[i]);
        await expect(page).toHaveURL(devtronAppCreation.applicationUrl[i]);
        await AllObjects.createAppPage.verifyAppVisibilityInRecentlyVisitedAppsDropdown(devtronAppsToBeCreated[i], false);
        i != 0 ? await AllObjects.createAppPage.verifyAppVisibilityInRecentlyVisitedAppsDropdown(devtronAppsToBeCreated[i - 1], true) : '';
    }

})
test.afterEach('deleting the data ', async ({ AllObjects, page, request }) => {
    test.setTimeout(5 * 1000 * 60);
    let apiUtils = new ApiUtils(request);
    for (let i = 0; i < devtronAppsToBeCreated.length; i++) {
        await apiUtils.deleteAllNodesOfApplication(devtronAppsToBeCreated[i], await apiUtils.login(process.env.PASSWORD!));
    }
})