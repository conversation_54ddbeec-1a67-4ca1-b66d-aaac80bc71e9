
import { test } from "../../../utilities/Fixtures";
import { BaseTest } from "../../../utilities/BaseTest";
import { ApiUtils } from "../../../utilities/ApiUtils";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
// Setting storage state for tests
let appNames: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
test.use({ storageState: './LoginAuth.json', repoNumber: 2, devtronApps: appNames, triggerCI: [false] });
test('buildx_oss', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
   test.setTimeout(35 * 1000 * 60);
   (testInfo as any).appCreated = appNames[0];
   await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'automation');
   await AllObjects.appConfigurationPage.setCiCdAutoOrManual('Manual');
   await AllObjects.appConfigurationPage.updatePipelineButton.click();
   await AllObjects.appConfigurationPage.closeUpdationModal();
   for (var i = 0; i < credentials.buildxData.length; i++) {
      
      await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
      await AllObjects.appConfigurationPage.configureBuildx(credentials.buildxData[i]);
      await AllObjects.workflowPage.triggerCiModule();
      await AllObjects.workflowPage.clickOnDetailsOfAnyNode(0);
      await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(credentials.buildxData[i].logsVerificationText!);
   }
})
test.afterEach('deleting the data', async ({ AllObjects, page, request }, testInfo) => {
   test.setTimeout(5 * 1000 * 60);
   let apiUtils = new ApiUtils(request);
   await apiUtils.deleteAllNodesOfApplication(appNames[0], await apiUtils.login(process.env.PASSWORD!));
})

