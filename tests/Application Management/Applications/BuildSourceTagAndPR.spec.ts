import { ApiUtils } from '../../../utilities/ApiUtils';
import { BaseTest } from '../../../utilities/BaseTest';
import { test } from "../../../utilities/Fixtures";
import { OctokitClient } from "../../../utilities/Third Party Clients/OctokitClient";


/**
 * Variables Declarations and initializations
 */
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
const octokitClient = new OctokitClient(process.env.GITHUB_OCTOKIT_TOKEN as string);
let apiUtils: ApiUtils;
let token: string;
let webhookId: any, pullRequestNumber: any;
let releaseId: number;
let devtronAppName: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
let devtronAppUrls: string[];


/**
 * Test Configurations
 */
test.use({ storageState: './LoginAuth.json', triggerCI: [false, false], devtronApps: devtronAppName, repoNumber: 2 });
test.describe.configure({ mode: 'serial' });


/**
 * setting up the request Object for api
 */
test.beforeEach('creating the devtron apps ', async ({ request }) => {
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
})


/**
 * Test the functionality of auto trigger in case of pull request
 */
test('Test build and deploy for trigger with PR creation_oss', { tag: '@cicd' }, async ({ AllObjects, devtronAppCreation }) => {
  test.setTimeout(15 * 60 * 1000);
  devtronAppUrls = devtronAppCreation.applicationUrl;
  await apiUtils.deleteAllNodesOfApplication(devtronAppName[0], token, false);
  // step 6: create workflow with branch type as PR
  await AllObjects.appConfigurationPage.createWorkflows({ workflowType: "build-from", config: { sourceType: 'Pull Request' } })
  // step 7: get webhook credentials
  await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
  const webhookCredentials = await AllObjects.buildSourceTagAndPR.getPRWebhookCredentials();
  webhookId = await octokitClient.createWebhook(credentials.githubAccountOwner, credentials.gitRepoName, webhookCredentials.webhookURL, webhookCredentials.secretKey);
  pullRequestNumber = await octokitClient.createPullRequest(credentials.githubAccountOwner, credentials.gitRepoName);
  await AllObjects.workflowPage.clickOnBuildAndDeployTab();
  await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
  // Step 7: Verify BuildHistory Page
  await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(["Login Succeeded"], "Running");
  await AllObjects.buildHistoryPage.abortCiBuild();
});



/**
 * Test the functionality of auto trigger in case of tag creation
 */
test('Test build and deploy for trigger with Tag creation_oss', { tag: '@cicd' }, async ({ page, AllObjects }, testInfo) => {

  test.setTimeout(15 * 60 * 1000);
  await page.goto(devtronAppUrls[0]);
  await AllObjects.appConfigurationPage.createWorkflows({ workflowType: "build-from", config: { sourceType: 'Tag Creation' } })
  releaseId = await octokitClient.createReleaseWithTag(credentials.githubAccountOwner, credentials.gitRepoName, "0.0.1");
  await AllObjects.workflowPage.clickOnBuildAndDeployTab();
  await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink(1);
  // Step 7: Verify BuildHistory Page
  await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(["Login Succeeded"], "Running");


});



/**
 * Cleanup after test execution
 */
test.afterEach('tag and PR after each hook for clearing the created data', async ({}, testInfo) => {
  // calling the  method to delete all nodes and delete the app 
  if (testInfo.status == "failed" || testInfo.title == 'Test build and deploy for trigger with Tag creation_oss') {
    if (webhookId) {
      await octokitClient.deleteWebhook(credentials.githubAccountOwner, credentials.gitRepoName, webhookId);
    }
    if (pullRequestNumber) {
      await octokitClient.closePullRequest(pullRequestNumber, credentials.githubAccountOwner, credentials.gitRepoName);
    }
    if (releaseId) {
      await octokitClient.deleteRelease(credentials.githubAccountOwner, credentials.gitRepoName, releaseId);
      await octokitClient.deleteTag(credentials.githubAccountOwner, credentials.gitRepoName, "0.0.1");
    }
    await apiUtils.deleteAllNodesOfApplication(devtronAppName[0], await apiUtils.login(process.env.PASSWORD!));
  }

}) 