import { BaseTest } from "../../../utilities/BaseTest";
import { test } from "../../../utilities/Fixtures";
import { ResourceTypeEnum } from "../../../enums/Application Management/Applications/CiCdAppDetailsPageEnum";
import { ApiUtils } from "../../../utilities/ApiUtils";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let applicationUrl: string;
const devtronAppFixtureNames = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6);
let configName: string;
let secretName: string;

if (process.env.isPacketTesting == 'false') {
  test.skip();
}

test.use({
  storageState: './LoginAuth.json',
  triggerCI: [true],
  devtronApps: [devtronAppFixtureNames],
  repoNumber: 1,
});

test.beforeEach('Setup application URL', async ({ devtronAppCreation }) => {
  test.setTimeout(7 * 60 * 1000);
  applicationUrl = devtronAppCreation.applicationUrl[0];
});

test("verify deployment and configmap drift sequentially", { tag: '@cicd' }, async ({ page, AllObjects, request }) => {
  test.setTimeout(15 * 60 * 1000);

  // Step 1: Deployment Drift
  await test.step("Step 1: Deployment Drift Check", async () => {
    await page.goto(applicationUrl);

    // Add ConfigMaps/Secrets
    for (const item of credentials.configMapOrSecretData) {
      await AllObjects.jobsPage.addConfigMapOrSecret(item);
    }
    await AllObjects.jobsPage.goBackToWorkflowPage();
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, "Progressing");

    // Edit Deployment live
    await AllObjects.resourceBrowserPage.goToResourceBrowser(process.env.BASE_SERVER_URL!);
    await AllObjects.resourceBrowserPage.goToCluster('default_cluster');
    await AllObjects.resourceBrowserPage.searchAnyResourceType('Deployment');
    await AllObjects.resourceBrowserPage.searchAnyResourceName(`${devtronAppFixtureNames}-automation`);
    await AllObjects.resourceBrowserPage.openManifestOfSpecificResource(`${devtronAppFixtureNames}-automation`);
    await AllObjects.resourceBrowserPage.clickOnEditLiveManifestButton();

    const payloadForUpdatingDeploymentYaml = AllObjects.resourceBrowserPage.getK8sResourcePayload({
      request,
      keypath: 'spec.replicas',
      newValue: 2,
      resourceName: `${devtronAppFixtureNames}-automation`,
      clusterId: 1,
      namespace: 'automation',
      kind: ResourceTypeEnum.Deployment,
      group: 'apps',
      version: 'v1'
    });

    await AllObjects.resourceBrowserPage.updateK8sResourceYaml(payloadForUpdatingDeploymentYaml);

    // Verify drift in UI
    await page.goto(applicationUrl);
    await AllObjects.appDetailsPageCiCd.goToAppDetails();
    await AllObjects.appDetailsPageCiCd.verifyDriftForSpecificK8sResourceInLiveManifest(
      ResourceTypeEnum.Deployment,
      `${devtronAppFixtureNames}-automation`
    );
  });

  // Step 3: ConfigMap Drift
  await test.step("Step 2: ConfigMap Drift Check", async () => {
    await page.goto(applicationUrl);
    await AllObjects.appDetailsPageCiCd.goToAppDetails();
    configName = await AllObjects.appDetailsPageCiCd.getAttachedCmOrCsNameViaAppDetailsPage('cm');
    secretName = await AllObjects.appDetailsPageCiCd.getAttachedCmOrCsNameViaAppDetailsPage('secret');
    await AllObjects.resourceBrowserPage.goToResourceBrowser(process.env.BASE_SERVER_URL!);
    await AllObjects.resourceBrowserPage.goToCluster('default_cluster');
    await AllObjects.resourceBrowserPage.searchAnyResourceType('ConfigMap');

    await AllObjects.resourceBrowserPage.searchAnyResourceName(configName);
    await AllObjects.resourceBrowserPage.openManifestOfSpecificResource(configName);
    await AllObjects.resourceBrowserPage.clickOnEditLiveManifestButton();

    const payloadForUpdatingConfigMap = AllObjects.resourceBrowserPage.getK8sResourcePayload({
      request,
      keypath: 'data.checkCmKeyenv',
      newValue: 'checkCmValuedatavolEdited',
      resourceName: configName,
      clusterId: 1,
      namespace: 'automation',
      kind: ResourceTypeEnum.ConfigMap,
      group: '',
      version: 'v1'
    });

    await AllObjects.resourceBrowserPage.updateK8sResourceYaml(payloadForUpdatingConfigMap);

    await page.goto(applicationUrl);
    await AllObjects.appDetailsPageCiCd.goToAppDetails();

    await AllObjects.appDetailsPageCiCd.verifyDriftForSpecificK8sResourceInLiveManifest(
      ResourceTypeEnum.ConfigMap,
      configName
    );
  });

  await test.step("Step 3: SecretMap Drift Check", async () => {
    await AllObjects.resourceBrowserPage.goToResourceBrowser(process.env.BASE_SERVER_URL!);
    await AllObjects.resourceBrowserPage.goToCluster('default_cluster');
    await AllObjects.resourceBrowserPage.searchAnyResourceType('Secret');
    await AllObjects.resourceBrowserPage.searchAnyResourceName(secretName);
    await AllObjects.resourceBrowserPage.openManifestOfSpecificResource(secretName);
    await AllObjects.resourceBrowserPage.clickOnEditLiveManifestButton();

    const payloadForUpdatingConfigMap = AllObjects.resourceBrowserPage.getK8sResourcePayload({
      request,
      keypath: 'data.checkSecretKeyenv',
      newValue: 'ZGVlcGFrY20tdmFsdWU=',
      resourceName: secretName,
      clusterId: 1,
      namespace: 'automation',
      kind: ResourceTypeEnum.Secret,
      group: '',
      version: 'v1'
    });

    await AllObjects.resourceBrowserPage.updateK8sResourceYaml(payloadForUpdatingConfigMap);
    await page.goto(applicationUrl);
    await AllObjects.appDetailsPageCiCd.goToAppDetails();
    await AllObjects.appDetailsPageCiCd.verifyDriftForSpecificK8sResourceInLiveManifest(
      ResourceTypeEnum.Secret,
      secretName
    );
  });
});


test.afterEach('Cleanup test resources', async ({ request }) => {
  test.setTimeout(5 * 60 * 1000);
  const apiUtils = new ApiUtils(request);
  console.log('Deleting the app:', devtronAppFixtureNames);
  await apiUtils.deleteAllNodesOfApplication(devtronAppFixtureNames, await apiUtils.login(process.env.PASSWORD!));
});
