import { test } from '../../utilities/Fixtures';
import { BaseTest } from '../../utilities/BaseTest';
import { ApiUtils } from '../../utilities/ApiUtils';
import { createValidApplicationYamlToClipboard } from '../../utilities/clipboardyYamls.ts/YamlForBulkEdit';
import { DataSet } from '../../utilities/DataObjectss.ts/multipleTestDataObject';

/**
 * variable declarations and initiazlizations
 */
let appNames: string[] = ["ui-autom" + BaseTest.generateRandomStringWithCharsOnly(3), "ui-autom" + BaseTest.generateRandomStringWithCharsOnly(3)];
let envIds: number[] = [];
let applicationUrls: string[] = [];
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let cmcsConfigDetails = [{ key: 'custom-key', value: 'custom-value', name: 'custom-test', isSecret: false }, { key: 'custom-key', value: 'custom-value', name: 'secretnew', isSecret: true }];
let apiUtils: ApiUtils
let token: string;
let cmcsVerificationData = [{ rName: 'custom-test', rType: 'ConfigMaps', keys: ['custom-key', 'newadded'], values: ['cmvalue', 'cmvalue'] }, { rName: 'secretnew', rType: 'Secrets', keys: ['custom-key', 'newadd'], values: ['mayank', 'mayank'] }]
//

test.use({ storageState: './LoginAuth.json', triggerCI: [false, false], devtronApps: appNames, devtronEnvs: ['automation', 'devtron-demo'] });

test.beforeEach('Create two application with overridden cm secrets', async ({ page, AllObjects, request, devtronAppCreation }) => {
  applicationUrls = devtronAppCreation.applicationUrl;
  test.setTimeout(10 * 60 * 1000);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
  for (let i = 0; i < appNames.length; i++) {
    let appId = await apiUtils.getAppIdFromAppName(appNames[i], token);
    await page.goto(applicationUrls[i]);
    for (let element of credentials.EnvNameForCD) {
      await AllObjects.appConfigurationPage.appConfigurationTab.click({ delay: 300 });
      await AllObjects.jobsPage.operBaseOrEnvOverRideResources(element);
      await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } });
      await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();
      let envObject = await apiUtils.getEnvObject(token, element);
      envIds.push(envObject.id);
      for (let config of cmcsConfigDetails) {
        await apiUtils.addConfigOrSecretInEnv([{ appId: Number(appId), token: token, key: config.key, value: config.value, name: config.name, isSecret: config.isSecret, envId: Number(envObject.id) }]);
      }
    }
  }
});
// Bulk edit configuration
const bulkEditConfig = DataSet.bulkEdit.dataObjectForBulkEdit(appNames, envIds);
test('BulkEdit For Multiple Env Multiple Apps_oss', { tag: '@globalConfigurations' }, async ({ AllObjects, page }) => {
  console.log(' this is the testing' + bulkEditConfig.envIds[0] + 'second env id is' + bulkEditConfig.envIds[1]);
  test.setTimeout(5 * 1000 * 60);

  console.log(appNames[0]);
  console.log(appNames[1]);

  // Navigate to bulk edit and perform edit
  await page.goto(process.env.BASE_SERVER_URL + '/bulk-edits');
  // revert new code editor
  await page.click('//*[@role="textbox"]');


  await (async () => {
    await createValidApplicationYamlToClipboard(bulkEditConfig);
  })();

  await page.keyboard.press('Shift+Insert');
  // await page.keyboard.press('Meta+V');
  await page.waitForTimeout(500);
  await page.getByTestId('show-impacted-objects-button').click({ delay: 500 });
  await page.getByTestId('run-button').click({ delay: 500 });

  // Environments to verify
  const environments = ['automation', 'devtron-demo'];

  // Verification function 
  const verifyApplicationConfig = async (applicationUrl: string) => {
    for (const env of environments) {
      await page.goto(applicationUrl);
      await AllObjects.appConfigurationPage.appConfigurationTab.click({ delay: 300 });
      await AllObjects.jobsPage.operBaseOrEnvOverRideResources(env, true);

      // Verify deployment config
      await AllObjects.baseDeploymentTemplatePage.verifyConfigDifference([
        { field: "GracePeriod", value: "47", isEditable: true },
      ]);

      // Verify ConfigMaps
      for (let cmcsData of cmcsVerificationData) {
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(env);
        await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence(cmcsData.rType, cmcsData.rName);
        await AllObjects.jobsPage.verifyCmSecrets({ keyName: cmcsData.keys, valueName: cmcsData.values });
      }
      // await AllObjects.jobsPage.verifyCmSecrets(env, {
      //   resourceType: 'ConfigMaps',
      //   resourceName: 'custom-test',
      //   keyName: ['custom-key', 'newadded'],
      //   valueName: ['cmvalue', 'cmvalue'],
      //   isDevtronapp: true
      // });

      // // Verify Secrets
      // await AllObjects.jobsPage.verifyCmSecrets(env, {
      //   resourceType: 'Secrets',
      //   resourceName: 'secretnew',
      //   keyName: ['custom-key', 'newadd'],
      //   valueName: ['mayank', 'mayank'],
      //   isDevtronapp: true
      // });
    }
  };

  // Verify configurations for both applications
  for (const applicationUrl of applicationUrls) {
    await verifyApplicationConfig(applicationUrl);
  }
});

test.afterEach('deleting the apps', async ({ AllObjects, page, request }) => {
  test.setTimeout(5 * 1000 * 60);
  for (let app of appNames) {
    await apiUtils.deleteAllNodesOfApplication(app, await apiUtils.login(process.env.PASSWORD!));
  }
})


