// Importing necessary modules
import { test } from "../../utilities/Fixtures";
import { BaseTest } from "../../utilities/BaseTest";
import { DataSet } from "../../utilities/DataObjectss.ts/multipleTestDataObject";
import { ApiUtils } from "../../utilities/ApiUtils";
import { DevtronDeploymentChart, DevtronAllChartVersion } from "../../enums/Application Management/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum";
import { GetAllInboxForwarderEventsSortEnum } from "mailslurp-client";
import GmailConfig from "../../utilities/Third Party Clients/GmailNotifications/GmailConfig";
import { DeploymentType, SymbolDeploymentType } from "../../enums/Application Management/Applications/DeploymentTriggerEnum";
import { yamlForCustomGuiSchema } from "../../utilities/clipboardyYamls.ts/GuiSchemaYaml";
import { expect } from "playwright/test";

// Reading credentials from a JSON file
const Credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
// Setting storage state for tests
test.use({ storageState: './LoginAuth.json' });


// Describing test suite
test.describe('Chart Store Deployments', () => {
    let name: string;
    // Test case for end-to-end chart deployment using Helm
    test.skip('end-to-end-chart-helm_oss_eaMode', { tag: '@chartstore' }, async ({ AllObjects }, testInfo) => {
        // Setting timeout
        test.setTimeout(15 * 60 * 1000);
        // Step 1: Generating a random name for the chart
        name = BaseTest.generateRandomStringWithCharsOnly(5);
        console.log('helm app has been created with this nane ' + name);
        let appCreated: string[] = [];
        appCreated.push(name);
        (testInfo as any).apps = appCreated;
        // Step 2: Navigating to chart store and selecting a particular chart
        await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
        await AllObjects.chartStorePage.SelectingChart(Credentials.ChartName, Credentials.ChartSource);
        // Step 3: Entering details and deploying the chart
        var ChartVersion: string | null = await AllObjects.deployChartPage.deployingChart(name, Credentials.ProjectName, Credentials.envNameForCharts, "helm");
        // Step 4: Verifying app details
        await AllObjects.chartStoreAppDetailsPage.verificationAppDetailsPage(true, ChartVersion!, [Credentials.TerminalScript[0]], Credentials.TerminalExpectedValue[0], Credentials.ChartName);
        // Step 5: Scaling workloads
        await AllObjects.chartStoreAppDetailsPage.scaleUpAndDownWorkloadsChartStoreApps(false);
        await AllObjects.chartStoreAppDetailsPage.scaleUpAndDownWorkloadsChartStoreApps(true);

        // Step 6: Comparing manifest
        await AllObjects.deployChartPage.ComparingManifest();
        // Step 7: Updating chart version
        var updatedChartVersion: string | null = await AllObjects.deployChartPage.updatingChartVersion();
        // await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus('Progressing');
        await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
        await AllObjects.chartStoreAppDetailsPage.verifyConfigApplyStatus('deployed');
        await AllObjects.chartStoreAppDetailsPage.verifyChartVersion(updatedChartVersion!);
        await AllObjects.chartStoreDeploymentHistoryy.verifyNumberofSucceddedDeployments(2);


    });
    test('preset-values and deployments of a chart', { tag: '@chartstore' }, async ({ AllObjects, helmAppCreation, page }, testInfo) => {
        let appCreated: string[] = [];
        
        let presetValueName: string = BaseTest.generateRandomStringWithCharsOnly(4);
        appCreated.push(helmAppCreation.helmAppName);
        (testInfo as any).apps = appCreated;
        await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
        await AllObjects.chartStorePage.SelectingChart(Credentials.ChartName, Credentials.ChartSource, false);
        await AllObjects.chartStorePage.clickOnTabOfPresetValuesOrDeployments(false);
        await AllObjects.chartStorePage.verifyDetailsOfAPresetValueOrDeployment(helmAppCreation.helmAppName, ['automation', "admin"]);
        await AllObjects.chartStorePage.deleteDeployment(helmAppCreation.helmAppName);
        await AllObjects.chartStorePage.searchPresetValueOrDeployments(helmAppCreation.helmAppName, false);
        await AllObjects.chartStorePage.clickOnTabOfPresetValuesOrDeployments(true);
        await AllObjects.chartStorePage.createPresetValue(presetValueName);
        await AllObjects.chartStorePage.searchPresetValueOrDeployments(presetValueName);
        let presetValuePageUrl = page.url();
        await AllObjects.chartStorePage.clickOneditOrUseOrDeletePresetValueButton(presetValueName, 'use');
        await AllObjects.deployChartPage.verifyTheValueSelectedInChartValueDropdown(presetValueName);
        await page.goto(presetValuePageUrl);
        await AllObjects.chartStorePage.clickOneditOrUseOrDeletePresetValueButton(presetValueName, 'delete');
        await AllObjects.chartStorePage.searchPresetValueOrDeployments(presetValueName, false);
        await page.goto(process.env.BASE_SERVER_URL!);
        await AllObjects.createAppPage.searchAndSelectAppsFromList(helmAppCreation.helmAppName, true, 'app', false);
    })


    // Test case for verify security scanning modal for helm apps
    test('external-helm-chart_oss_eaMode', { tag: '@chartstore' }, async ({ AllObjects, page }, testInfo) => {
        test.setTimeout(12 * 1000 * 60);
        let dataset = DataSet.externalHelmInstallation.dataSetForExternalHelm();
        let chartName: string;
        let chartCreated: string[] = [];
        for (let key of dataset) {
            await AllObjects.resourceBrowserPage.goToResourceBrowser(process.env.BASE_SERVER_URL as string);
            await AllObjects.resourceBrowserPage.goToCluster('default_cluster');
            chartName = BaseTest.generateRandomStringWithCharsOnly(4);
            chartCreated.push(chartName);
            (testInfo as any).apps = chartCreated;
            await AllObjects.resourceBrowserPage.executeCommandsInDefaultTerminal([{ commandToExecute: 'helm repo add bitnami https://charts.bitnami.com/bitnami', resultOfCommandToVerify: '"bitnami" has been added to your repositories', count: 1 },
            { commandToExecute: `helm install ${chartName} bitnami/${key.chartName} --version ${key.chartVersion} -n ${key.envName}`, resultOfCommandToVerify: "REVISION: 1", count: 1 }
            ]);
            await page.goto(process.env.BASE_SERVER_URL! + '/app/list/h?cluster=1');
            await AllObjects.createAppPage.searchAndSelectAppsFromList(chartName, true);
            await AllObjects.chartStoreAppDetailsPage.verificationAppDetailsPage(true, key.chartVersion, [Credentials.TerminalScript[0]], Credentials.TerminalExpectedValue[0], chartName, true);
            await AllObjects.deployChartPage.linkExternalHelmChartAndTrigger({ projectName: key.projectName }, Credentials.ChartSource);
            await AllObjects.chartStoreAppDetailsPage.verificationAppDetailsPage(true, key.chartVersion, [Credentials.TerminalScript[0]], Credentials.TerminalExpectedValue[0], chartName, true);
            await AllObjects.chartStoreDeploymentHistoryy.verifyNumberofSucceddedDeployments(1);
        }
    })


    // Action to perform after each test
    test.afterEach('deleting', async ({ AllObjects, page }, testInfo) => {
        test.setTimeout(5 * 1000 * 60);
        // Searching and selecting the app
        if (testInfo.title != "preset-values and deployments of a chart" || testInfo.status == "failed") {
            for (var i = 0; i < (testInfo as any).apps.length; i++) {
                let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
                await page.goto(process.env.BASE_SERVER_URL + urlTogo);
                await AllObjects.createAppPage.searchAndSelectAppsFromList((testInfo as any).apps[i], true);
                // Deleting the app
                await AllObjects.deployChartPage.deletingChartStoreApp();
            }
        }
    });
})



