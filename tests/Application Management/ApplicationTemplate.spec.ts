import { expect, request } from "playwright/test";
import { ApiUtils } from "../../utilities/ApiUtils";
import { BaseTest } from "../../utilities/BaseTest";
import { dataForDefaultAppSetup, dataSetForApplicationTemplate, updatingDefaultCreatedApp, verifyConfigurationOfTemplateAndApp } from "../../utilities/DataObjectss.ts/ApplicationTemplateDataObject";
import { test } from "../../utilities/Fixtures";
import { DataSet } from "../../utilities/DataObjectss.ts/multipleTestDataObject";
import { allowOrDeleteOverrideDTO } from "../../DTOs/Application Management/Applications/BaseDeploymentPageDTO";
import { createAllObjects } from "../../utilities/CreateObjectsForAllPomClasses";



/**
 * Variable Declarations
 */

let devtronAppNameForFixture = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
let templateName: string = 'playwright-auto-template';
let preDefinedAppName: string = 'playwright-auto-apptemplate';
let createdTemplateUrl: string;
let apiUtils: ApiUtils;
let token: string;
let credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");



/**
 * Test Configuration
 */
test.use({ storageState: './LoginAuth.json', devtronApps: [devtronAppNameForFixture[0]], devtronEnvs: ['automation', 'devtron-demo'], triggerCI: [false] });
test.describe.configure({ mode: 'serial' });



/**
 * setting up the required app
 * we are only creating the parent app with all required setup if it is already not present inside the cluster
 * saving a lot of time on multiple runs
 */
test.beforeAll('setting up app', async ({ browser }) => {
    test.setTimeout(10 * 60 * 1000);
    let requestObject = await request.newContext();
    apiUtils = new ApiUtils(requestObject);
    token = await apiUtils.login(process.env.PASSWORD!);
    let page = await browser.newPage();
    let AllObjects = createAllObjects(page);
    if (!await apiUtils.getAppIdFromAppName(preDefinedAppName, token)) {
        await page.goto(process.env.BASE_SERVER_URL!);
        await AllObjects.createAppPage.createCustomAppJob("app", preDefinedAppName, credentials.ProjectName, process.env.BASE_SERVER_URL as string);
        AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
        await AllObjects.buildConfigurationPage.saveBuildConfigurations(credentials.ContainerRegistryName, credentials.ContainerRepository);
        await AllObjects.baseDeploymentTemplatePage.saveDeploymentTemplateWhileCreatingNewApp();
        await AllObjects.workflowPage.getCountOfWorkFlow();
        await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
        await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] }, cdConfig: { envNameForCd: 'automation', clusterName: 'default_cluster', deploymentStrat: 'ROLLING', helmOrGitops: 'helm', autoOrManual: 'Auto' } });
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 1);
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'devtron-demo', clusterName: 'default_cluster', helmOrGitops: 'helm', deploymentStrat: 'ROLLING', autoOrManual: 'Auto' });
        let pipelineId = await AllObjects.appConfigurationPage.fetchPipelineIdOfAnyCiNode(0);
        let appcreationdata = dataForDefaultAppSetup(preDefinedAppName, pipelineId!);
        for (let key of appcreationdata) {
            await AllObjects.appConfigurationPage.createWorkflows(key);
        }
        let appUpdationData = updatingDefaultCreatedApp();
        for (let key of appUpdationData.cmcsCreationData) {
            await AllObjects.jobsPage.addConfigMapOrSecret(key);
        }
        await AllObjects.jobsPage.goBackToWorkflowPage();
        for (let key of appUpdationData.prePostCiCdData) {
            await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, key.nodeName);
            await AllObjects.prePostCiCd.addPrePostTask(key.stage, key.taskName, key.autoOrManul);
            if (key.nodeName == "ci") {
                //   await AllObjects.prePostCiCd.configureSkopeoPlugin('bains | deep10/bowl');
            }
            else {
                await AllObjects.workflowPage.addCmAndSecretInPreAndPostDeployment(['test1', 'test2']);
                await AllObjects.jobsPage.executeCustomScript(BaseTest.generateRandomStringWithCharsOnly(4), key.script);
            }
        }
        for (let key of appUpdationData.cmcsUpdationData) {
            await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage);
            await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence(key.resource, key.resourceName);
            let config: allowOrDeleteOverrideDTO = key.inheritOverrideData as allowOrDeleteOverrideDTO;
            await expect(async () => {
                await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride(config);
                await AllObjects.jobsPage.editCmCs(key.resource, key.resourceName, key.stage, false, key.key, key.value);
            }).toPass({ timeout: 2 * 1000 * 60 });
        }
        for (let key of appUpdationData.dtData) {
            await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage);
            await AllObjects.baseDeploymentTemplatePage.deploymentTemplateDiv.click();
            let config: allowOrDeleteOverrideDTO = key.inheritOverrideData as allowOrDeleteOverrideDTO;
            await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride(config);
            if (key.inheritOverrideData.configuration.replaceMergerStrat) {
                await AllObjects.baseDeploymentTemplatePage.editAnyField([key.key], [key.value]);
            }
            else {
                await AllObjects.baseDeploymentTemplatePage.addNewField([key.key], [key.value]);
            }
            await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();
        }
        await AllObjects.jobsPage.goBackToWorkflowPage();
        await AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[1] as string, 'Github Public', "./second");
        await AllObjects.appConfigurationPage.configureBranchForMultiGitWithExistingWorkflows(['main']);
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
        await AllObjects.appConfigurationPage.clickOnAllowOverrideOnCiNode();
        await AllObjects.buildConfigurationPage.selectRegistryFromDropDown('bains');
        await AllObjects.buildConfigurationPage.repositoryNameInput.fill('deep10/reen');
        await AllObjects.appConfigurationPage.setTargetPlatformForBuild(['linux/amd64']);
        await AllObjects.appConfigurationPage.updatePipelineButton.click();
    }
    await page.close();
})

/**
 * This test case will verify the application template creation and validations
 * this test case will create the requied template for other test cases 
 */
test('application template creation and validations', { tag: '@globalConfigurations' }, async ({ AllObjects, page, request }, testInfo) => {
    test.setTimeout(25 * 60 * 1000);
    await AllObjects.globalConfigPage.navigateToApplicationTemplate();
    await AllObjects.applicationTemplatePage.createTemplateFromApp({ templateRelatedData: { name: templateName, id: templateName, desc: '', checkValidation: false }, applicationRelatedData: { appName: preDefinedAppName } });
    let datasetForTestCase = await dataSetForApplicationTemplate(AllObjects);
    await verifyConfigurationOfTemplateAndApp(datasetForTestCase, AllObjects);
    createdTemplateUrl = page.url();
})

/**
 * This test case will verify the app creation from template
 * we will verify the configuration of app and will trigger the ci/cd and verify the status
 */
test('devtron app creation from template with cicd and changes on configuration page', { tag: '@globalConfigurations' }, async ({ AllObjects, page }, testInfo) => {
    test.setTimeout(25 * 60 * 1000);
    await page.goto(createdTemplateUrl);
    let datasetForTestCase = await dataSetForApplicationTemplate(AllObjects);
    await AllObjects.createAppPage.createAppFromTemplate({ appName: devtronAppNameForFixture[0], projectName: 'devtron-demo', templateName: templateName, verificationOfPreDefinedData: { codeSource: datasetForTestCase.createAppPageValidation.codeSource, buildConfiguration: datasetForTestCase.createAppPageValidation.buildConfiguration, workflow: datasetForTestCase.createAppPageValidation.workflow }, tags: datasetForTestCase.tags, configurationChange: datasetForTestCase.createAppPageWorkflowChanges }, AllObjects);
    await AllObjects.appConfigurationPage.appConfigurationTab.click();
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await AllObjects.workflowPage.workflowEditorTab.click();
    datasetForTestCase = await dataSetForApplicationTemplate(AllObjects);
    await verifyConfigurationOfTemplateAndApp(datasetForTestCase, AllObjects);
    let imageBuilt: string;
    for (let key of datasetForTestCase.triggerRelatedData) {
        if (key.triggerType == "webhook") {
            await AllObjects.appConfigurationPage.appConfigurationTab.click();
            await AllObjects.externalCIPage.clickOnExternalSourceConfigButton();
            await AllObjects.externalCIPage.externalCITriggerWithTryItOut();
        }
        else if (key.triggerType == "ci") {     
       await AllObjects.workflowPage.triggerCiModule('Success', key.workflowNumber, key.runtimeParams);
        }
    }
    for (let key of datasetForTestCase.statusverificationData) {
        await AllObjects.workflowPage.verifyCiCdStatus(key.workflowNumber, key.nodeNumber, key.status);
    }
    for (let key of datasetForTestCase.logVerifications) {
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.clickOnDetailsOfAnyNode(key.nodeNumber, key.workflowNumber);
        await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(key.logs);
        if (key.workflowNumber == 0 && key.nodeNumber == 0) {
            imageBuilt = await AllObjects.buildHistoryPage.verifyArtifacts('deep10');
        }
    }
    await AllObjects.appDetailsPageCiCd.selectEnvironment('automation');
    await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
    let commitHash1 = imageBuilt!.split(':')[1].split(`-`)[0];
    let commitHash2 = imageBuilt!.split(':')[1].split(`-`)[1];
    await AllObjects.appDetailsPageCiCd.verifyCommitInfo([{ branchName: 'main', commitHash: commitHash1.slice(0, 7), commitCardNumber: 1 }, { branchName: 'main', commitHash: commitHash2.slice(0, 7), commitCardNumber: 0 }]);
})

/**
 * This test case will verify the application template creation with changes in runtime and config as well
 * we will verify the configuration of app and will trigger the ci/cd and verify the status
 */
test('application temoplate with changes in runtime and config as well', { tag: '@globalConfigurations' }, async ({ AllObjects, page }, testInfo) => {
    test.setTimeout(25 * 60 * 1000);
    await AllObjects.globalConfigPage.navigateToApplicationTemplate();
    console.log('workflow name we are getting is ' + templateName);
    await AllObjects.applicationTemplatePage.searchAndClickOnAnyTemplate(templateName);
    await AllObjects.appConfigurationPage.clickOnChangeImageSourceButtonOfWorkflow(4);
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main', 'main'] }, cdConfig: { envNameForCd: 'env10', clusterName: 'default_cluster', deploymentStrat: 'ROLLING', helmOrGitops: 'gitops', autoOrManual: 'Auto' } }, true);
    await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 0);
    await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'env10', clusterName: 'default_cluster', helmOrGitops: 'gitops', deploymentStrat: 'ROLLING', autoOrManual: 'Auto' });
    await AllObjects.jobsPage.operBaseOrEnvOverRideResources('devtron-demo', true);
    await AllObjects.baseDeploymentTemplatePage.editAnyField(['GracePeriod'], ['100']);
    await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();
    await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence('ConfigMaps', 'test1');
    await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'delete' } });
    await AllObjects.jobsPage.goBackToWorkflowPage()
    let datasetForTestCase = await dataSetForApplicationTemplate(AllObjects);
    datasetForTestCase.configurationValidationAppTemplate[4] = { workflowNumber: await AllObjects.appConfigurationPage.findIndexOfWorkflow('devtron-demo'), nodeNumber: 4, textToVerify: ['env9', 'Pre-deploy'] };
    datasetForTestCase.configurationValidationAppTemplate[3] = { workflowNumber: await AllObjects.appConfigurationPage.findIndexOfWorkflow('env10'), nodeNumber: 3, textToVerify: ['env10', 'Deploy'] };
    datasetForTestCase.configurationValidationAppTemplate[5] = { workflowNumber: await AllObjects.appConfigurationPage.findIndexOfWorkflow('devtron-demo'), nodeNumber: 5, textToVerify: ['devtron-demo', 'Pre-deploy'] };
    datasetForTestCase.configurationValidationAppTemplate[11] = { workflowNumber: await AllObjects.appConfigurationPage.findIndexOfWorkflow('env4'), nodeNumber: 2, textToVerify: ['Build', 'ci'] };
    datasetForTestCase.createAppPageValidation.workflow[0] = { workflowNumber: await AllObjects.appConfigurationPage.findIndexOfWorkflow('devtron-demo'), envNames: ['automation', 'devtron-demo', 'env10'] }
    datasetForTestCase.createAppPageWorkflowChanges = { workflowNumber: await AllObjects.appConfigurationPage.findIndexOfWorkflow('devtron-demo'), existingEnvName: 'automation', envNameToChange: 'env9' }
    datasetForTestCase.dtConfiguration[0].stage = 'env9';
    datasetForTestCase.dtConfiguration[1].value = "100";
    await page.goto(process.env.BASE_SERVER_URL!);
    await AllObjects.createAppPage.createAppFromTemplate({ appName: devtronAppNameForFixture[1], projectName: 'devtron-demo', templateName: templateName, verificationOfPreDefinedData: { codeSource: datasetForTestCase.createAppPageValidation.codeSource, buildConfiguration: datasetForTestCase.createAppPageValidation.buildConfiguration, workflow: datasetForTestCase.createAppPageValidation.workflow }, tags: datasetForTestCase.tags, configurationChange: datasetForTestCase.createAppPageWorkflowChanges }, AllObjects);
    await AllObjects.appConfigurationPage.appConfigurationTab.click();
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await AllObjects.workflowPage.workflowEditorTab.click();
    await verifyConfigurationOfTemplateAndApp(datasetForTestCase, AllObjects, false);

})


/**
 * This test case will delete the template and the apps created
 * we have done conditional setup here , as test cases will run in sequential order
 * we are deleting the template only if the test case is failed or the last test case is run
 */
test.afterEach('deleting the data', async ({ AllObjects, page, request }, testInfo) => {
    if (testInfo.status == "failed" || testInfo.title == "application temoplate with changes in runtime and config as well") {
        try {
            await AllObjects.globalConfigPage.navigateToApplicationTemplate();
            await AllObjects.applicationTemplatePage.deleteTemplate(templateName);
        }
        catch (error) {
            console.log('not able to delete the template');
        }
    }
    if (testInfo.title == "application temoplate with changes in runtime and config as well") {
        await apiUtils.deleteAllNodesOfApplication(devtronAppNameForFixture[1], token);
    }
    if (testInfo.title == "devtron app creation from template with cicd and changes on configuration page") {
        await apiUtils.deleteAllNodesOfApplication(devtronAppNameForFixture[0], token);
    }
})

