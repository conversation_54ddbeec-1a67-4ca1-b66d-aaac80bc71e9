import { DataSet } from '../../../utilities/DataObjectss.ts/multipleTestDataObject.ts';
import { BaseTest } from '../../../utilities/BaseTest.ts'
import { test } from "../../../utilities/Fixtures.ts";
import { LockConfiguration } from '../../../Pages/ApplicationManagement/Policies/LockConfiguration.ts';
import { AppConfigurationPage } from '../../../Pages/ApplicationManagement/Applications/AppConfigurationsPage.ts';
import { expect } from '@playwright/test';
import { BuildHistoryPage } from '../../../Pages/ApplicationManagement/Applications/BuildHistoryPage.ts';
import { JobsPage } from '../../../Pages/Automation and Enablement/Jobs.ts';
import { BaseDeploymentTemplatePage } from '../../../Pages/ApplicationManagement/Applications/BaseDeploymentTemplatePage.ts';
import { ApiUtils } from '../../../utilities/ApiUtils.ts';


const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronApps: string[] = [BaseTest.generateRandomStringWithCharsOnly(4)];
let data: any;
let apiUtils: ApiUtils;
let token: string;

test.use({ storageState: "./LoginAuth.json", devtronApps: devtronApps, triggerCI: [false] });

test.beforeEach('Creating an app and generating a token', async ({ devtronAppCreation, page, AllObjects, request }, testInfo) => {
  test.setTimeout(10 * 1000 * 60);
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
  // Store the token and application URL in testInfo
  (testInfo as any).token = await apiUtils.getApiTokenObject(token, "playwright-non-super").then(key => key?.tokenValue);
  (testInfo as any).applicationUrl = devtronAppCreation.applicationUrl[0];
  await page.goto(process.env.BASE_SERVER_URL!);
  await AllObjects.createAppPage.searchAndSelectAppsFromList(devtronApps[0]);
  await AllObjects.buildHistoryPage.gotoAppConfigurationsPage();
  await AllObjects.jobsPage.operBaseOrEnvOverRideResources('automation');
  await AllObjects.baseDeploymentTemplatePage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } });
  await AllObjects.baseDeploymentTemplatePage.SaveAndUpdateDeploymentTemplate();

  // Fetch lock configuration data for the created application
  data = DataSet.LockConfigData.lockConfigDataObject(devtronApps[0]);
});

test('Lock Configuration Test', { tag: '@globalConfigurations' }, async ({ page, AllObjects, browser }, testInfo) => {
  test.setTimeout(20 * 1000 * 60);
  // Navigate to the Lock Configuration Page
  await AllObjects.lockConfig.goToLockConfiguration();

  // Loop through the profiles and apply configurations
  for (const key of data.profiles) {
    await AllObjects.lockConfig.createOrEditProfile(key.profileName, key.lockValue);
    await AllObjects.lockConfig.applyProfile(key);
  }

  // Create a new page with custom cookies using the generated token
  const page2 = await BaseTest.createNewPageWithCustomCookies(browser, (testInfo as any).token);
  await page2.goto((testInfo as any).applicationUrl);
  await BaseTest.clickOnToolTipOkayButton(page2);
  await BaseTest.clickOnDarkMode(page2);

  // Initialize Page Objects for further interactions
  const appConfiguration = new AppConfigurationPage(page2);
  const lockConfig = new LockConfiguration(page2);
  const baseDeploymentPage = new BaseDeploymentTemplatePage(page2);
  const buildHistory = new BuildHistoryPage(page2);
  const jobPage = new JobsPage(page2);

  // Navigate to the App Configurations Page via the Build History Page
  await buildHistory.gotoAppConfigurationsPage();
  let activePageUrl: string = "";

  // Loop through fields related data and perform configuration actions
  for (const key of data.fieldsRelatedData) {
    await expect(async () => {
      await jobPage.operBaseOrEnvOverRideResources(key.stage);
      activePageUrl = page2.url();

      if (key.stage != 'base-configurations') {
        // await baseDeploymentPage.clickOnAdvancedYaml();
        await baseDeploymentPage.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } });
        console.log('Allow override or delete override action performed');
      }

      // Verify changes after editing fields

      console.log('Function called to edit and verify lock configurations');
      await baseDeploymentPage.editAnyField(key.fieldsToEdit, key.valuesToEdit);
      await baseDeploymentPage.verifyChangesForUpdateAndSaveLockConfig(key.eligibleNotEligible, key.stage);
    }).toPass({ timeout: 5 * 1000 * 60 });

    // Switch between pages to verify configurations
    await page.bringToFront();
    await page.goto(activePageUrl);
    await AllObjects.baseDeploymentTemplatePage.verifyHideLockedKeys(key.hideLockedKeys);
    await AllObjects.baseDeploymentTemplatePage.verifyConfigDifference(key.verifyFieldsAfterSaving);
    await page2.bringToFront();
    await page2.goto(activePageUrl);
  }
  await page.bringToFront();
  await AllObjects.lockConfig.goToLockConfiguration();
  await AllObjects.lockConfig.applyFilterAndVerifyOutput(data.filterRelatedData);
  await AllObjects.lockConfig.RemoveAllAppliedFilters();
  await AllObjects.lockConfig.bulkAdditionOrRemovalOFprofiles([{ stage: 'Remove', profileName: 'all' }]);
});

test.afterEach('Deleting the application', async ({ AllObjects, page, request }, testInfo) => {
  test.setTimeout(5 * 1000 * 60);
  // Navigate to Lock Configuration and delete specific profiles
  try {
    await AllObjects.lockConfig.goToLockConfiguration();
    await AllObjects.lockConfig.deleteProfile(['specific-automation', 'match-criteria-automation', 'global-autmation']);
  }
  catch (error) {
    console.log('was not able to delete the lock profiles');
  }
  await apiUtils.deleteAllNodesOfApplication(devtronApps[0], token);
});


