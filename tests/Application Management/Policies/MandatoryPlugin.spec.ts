import { ALL } from "dns";
import { BaseTest } from "../../../utilities/BaseTest";
import { test } from "../../../utilities/Fixtures"
import { dataCreationForMandatoryPlugins } from "../../../utilities/DataObjectss.ts/MandatoryPluginDataObjects";
import { PrePostCiCd } from "../../../Pages/ApplicationManagement/Applications/PrePostCiCd";
import { ApiUtils } from "../../../utilities/ApiUtils";
let devtronAppNames: string[] = [BaseTest.generateRandomStringWithCharsOnly(4), BaseTest.generateRandomStringWithCharsOnly(4)];
let devtronAppUrls: string[] = [];
let apiUtils: ApiUtils;
let token: string;
test.use({ storageState: './LoginAuth.json', devtronApps: devtronAppNames, launchOptions: { slowMo: 500 }, triggerCI: [false, false], externalCiWorkflowEnvName: 'env9' });
test('mandatory-plugin-test', { tag: '@globalConfigurations' }, async ({ AllObjects, page, devtronAppCreation, request, createExternalCiWorkflow }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    let profileName: string = BaseTest.generateRandomStringWithCharsOnly(6);
    let app1 = devtronAppNames[0];
    let app2 = devtronAppNames[1];
    devtronAppUrls = devtronAppCreation.applicationUrl;
    (testInfo as any).profileCreated = profileName;
    (testInfo as any).appsCreated = [app1, app2];
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);

    for (let key of devtronAppCreation.applicationUrl) {
        let i = 0;
        await page.goto(key);
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'env9');
        await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
        await AllObjects.prePostCiCd.addPrePostTask('post', 'execute');
        await AllObjects.appConfigurationPage.updatePipelineButton.click();
        i++;
    }


    for (let key of dataCreationForMandatoryPlugins(profileName, app1, app2)) {
        let workflowChanged = false;
        if (key.profileCreationData.isCi && workflowChanged == false) {
            for (let key of devtronAppUrls) {
                await page.goto(key);
                await AllObjects.appConfigurationPage.clickOnChangeImageSourceButtonOfWorkflow(0);
                await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed', branchName: ['main'] } }, true);
                workflowChanged = true;
            }
        }

        let checkCiTrigger: boolean = key.applyingProfile.applyingType == "Global" && key.profileCreationData.isCi ? true : false
        await AllObjects.mandatoryPluginPage.navigateToMandatoryPluginPage();
        await AllObjects.lockConfig.clickOnProfileOrCreateNew(profileName);
        await AllObjects.mandatoryPluginPage.editConfigurationForPluginPolicy(key.profileCreationData);
        await AllObjects.mandatoryPluginPage.settingConfigurationToApplyAProfile(profileName, key.applyingProfile);

        for (let i = 0; i < key.affectedPipelinesDetails.length; i++) {
            if (key.affectedPipelinesDetails[i].impactedCiCdNumber != -1) {
                await page.goto(process.env.BASE_SERVER_URL!);
                await AllObjects.createAppPage.searchAndSelectAppsFromList(key.affectedPipelinesDetails[i].appName);
                let isBlocked = key.profileCreationData.consequences == "Allow respective triggers with warning" ? false : true
                await AllObjects.workflowPage.checkTriggerWithMandatoryPlugins(isBlocked, checkCiTrigger && i == 0, key.affectedPipelinesDetails[i].impactedCiCdNumber);
            }
            if (key.profileCreationData.isCi && i == 1 && key.applyingProfile.applyingType != "Global") {
                await page.goto(process.env.BASE_SERVER_URL!);
                await AllObjects.createAppPage.searchAndSelectAppsFromList(key.affectedPipelinesDetails[i].appName);
                await AllObjects.workflowPage.triggerCiModule();
            }
        }
        if (key.applyingProfile.applyingType != "Global") {
            await AllObjects.mandatoryPluginPage.navigateToMandatoryPluginPage();
            let newPage = await AllObjects.mandatoryPluginPage.checkNonCompliancePipelinesAndNavigateToPipelines(profileName, true);
            let prePostCiCd = new PrePostCiCd(newPage!);
            await prePostCiCd.configureMandatPlugins();
            await AllObjects.mandatoryPluginPage.navigateToMandatoryPluginPage();
            await AllObjects.mandatoryPluginPage.checkNonCompliancePipelinesAndNavigateToPipelines(profileName, false);
        }
        await AllObjects.mandatoryPluginPage.navigateToMandatoryPluginPage();
        await AllObjects.mandatoryPluginPage.removeAppliedProfile({ filterType: 'profile', filterValue: [profileName], clearPreviousFilter: false })

    }
})
test.afterEach('deletin the data created ', async ({ page, AllObjects, request }, testInfo) => {
    test.setTimeout(5 * 1000 * 60);
    let apiUtils = new ApiUtils(request);
    try {
        for (let i = 0; i < devtronAppNames.length; i++) {
            await apiUtils.deleteAllNodesOfApplication(devtronAppNames[i], await apiUtils.login(process.env.PASSWORD!));
        }
    }
    catch (error) {
        console.log(' not able to delete the apps');
    }
    await AllObjects.mandatoryPluginPage.navigateToMandatoryPluginPage();
    await AllObjects.mandatoryPluginPage.delteProfileCreated((testInfo as any).profileCreated);
})
