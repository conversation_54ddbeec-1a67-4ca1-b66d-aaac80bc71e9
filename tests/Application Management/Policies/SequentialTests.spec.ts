import { test } from '../../../utilities/Fixtures';
import { BaseTest } from '../../../utilities/BaseTest';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { expect } from 'playwright/test';
import { mine } from '../../../utilities/DataObjectss.ts/SequentialTestsDataObject';

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let devtronAppNames: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
let apiUtils: ApiUtils;
let token: string;
test.describe.configure({ mode: 'default' })


test.describe('external ci-section', async () => {
    test.use({ storageState: './LoginAuth.json', devtronApps: devtronAppNames, triggerCI: [false] });
    test.beforeEach('end-to-end test', async ({ AllObjects, page, request }, testInfo) => {
        apiUtils = new ApiUtils(request);
        token = await apiUtils.login(process.env.PASSWORD!);
    })
    test('External CI app deployment_oss', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
        //create new workflow for external ci.

        await page.goto(devtronAppCreation.applicationUrl[0]);
        (testInfo as any).appUrl = devtronAppCreation.applicationUrl[0];
        let appId = await apiUtils.getAppIdFromAppName(devtronAppNames[0], token);
        let envObject = await apiUtils.getEnvObject(token, mine.envNameForWorkflowCreation);
        await apiUtils.deleteAllNodesOfApplication(devtronAppNames[0], token, false);
        await apiUtils.createNewWorkflowOfExternalCi(token, appId, envObject.id, mine.envNameForWorkflowCreation);
        await page.reload();
        if (process.env.clusterType == "oss") {
            await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'devtron-demo');
            await AllObjects.appConfigurationPage.setCiCdAutoOrManual('Manual');
            await AllObjects.appConfigurationPage.updatePipelineButton.click();
            await AllObjects.appConfigurationPage.closeUpdationModal();
        }
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'webhook', 1);
        await AllObjects.appConfigurationPage.addCdModule(mine.addCdModuleConfig as any);
        await AllObjects.externalCIPage.closeWebhookTippy.click();
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, mine.envNameForWorkflowCreation);
        process.env.clusterType == "enterprise" ? await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute') : await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute', "Manual")
        await AllObjects.appConfigurationPage.updatePipelineButton.click();
        await AllObjects.appConfigurationPage.closeUpdationModal();
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, mine.addCdModuleConfig.envNameForCd, 0)
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'automation', clusterName: 'default_cluster', helmOrGitops: 'helm' });

        //click on external cd
        for (let i = 0; i < 2; i++) {
            let autoGeneratoToken: boolean = i == 1 ? true : false;
            let checkUnathorized: boolean = i == 1 ? true : false;
            await AllObjects.externalCIPage.clickOnExternalSourceConfigButton();
            //click on try it out and trigger the external cd with token named as dontdeleteit
            await AllObjects.externalCIPage.externalCITriggerWithTryItOut(autoGeneratoToken, checkUnathorized);
        }

        //verify the ci and cd status.
        for (let i = 0; i < mine.statusToVerify.length; i++) {
            console.log('i we are getting ' + i);
            i == 1 || i == 2 ? await AllObjects.workflowPage.verifyImageAndTriggerDeployment(i - 1) : '';
            if (i == 5) {
                process.env.clusterType == "enterprise" ? await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.VirtualEnvironment) : await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'env1');
                await AllObjects.prePostCiCd.addPrePostTask('post', 'execute', 'Manual');
                await AllObjects.appConfigurationPage.updatePipelineButton.click();
                await AllObjects.appConfigurationPage.closeUpdationModal();
                await AllObjects.workflowPage.verifyImageAndTriggerDeployment(2);
            }
            await AllObjects.workflowPage.verifyCiCdStatus(0, mine.statusToVerify[i].cdNodeNumber, mine.statusToVerify[i].statusToCheck);
        }
    })

})
test.describe('image pull digest', async () => {
    test.use({ storageState: "./LoginAuth.json", devtronApps: devtronAppNames })
    test("Create Image Pull Digest", { tag: '@globalConfigurations' }, async ({ AllObjects, devtronAppCreation, page }, testInfo) => {
        var appUrl: string;
        // Set a timeout for the entire test.
        var imageBuilt = devtronAppCreation.imageBuilt;
        (testInfo as any).appCreated = devtronAppNames[0];
        var manifestVerificationText = imageBuilt + '@sha';
        test.setTimeout(15 * 60 * 1000);

        await AllObjects.globalConfigPage.goToGlobalConfigurations(process.env.BASE_SERVER_URL as string)
        await AllObjects.imagePullDigest.openPullImageDigest()
        await AllObjects.imagePullDigest.selectEnvironment(false, "env12");

        await page.goto(devtronAppCreation.applicationUrl[0]);
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 1);
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'env12', clusterName: 'default_cluster', helmOrGitops: 'helm' });
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'env12')
        await AllObjects.appConfigurationPage.verifyImagePullToggleIsLocked();

        await AllObjects.workflowPage.closeSelectImageModal();
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1)
        await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Progressing');
        await AllObjects.appDetailsPageCiCd.goToAppDetails()
        await AllObjects.appDetailsPageCiCd.selectEnvironment('env12');
        await AllObjects.chartStoreAppDetailsPage.verifyManifest(manifestVerificationText)
    })

})
 test.afterEach('deleting-apps', async ({ AllObjects, page, request }, testInfo) => {

    let apiUtils: ApiUtils = new ApiUtils(request);
    let failureCount = 0;
    try {

        await apiUtils.deleteAllNodesOfApplication(devtronAppNames[0], await apiUtils.login(process.env.PASSWORD!));

    }
    catch (error) {
        console.error('Error deleting application:', error);
        failureCount++;
    }
    expect(failureCount).toBe(0);

 })

