
import { test } from '../../../utilities/Fixtures';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { BaseTest } from '../../../utilities/BaseTest';
import { WorkflowPage } from '../../../Pages/ApplicationManagement/Applications/WorkflowPage';

if (process.env.isStaging == 'true') {
    test.skip();
}

let devtronappName = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(6);
let profileName: string = 'playwright' + BaseTest.generateRandomStringWithCharsOnly(4);
let secondPageToken: string;
let apiUtils: ApiUtils;
let token: string;
let nodeId=0;
test.use({ storageState: './LoginAuth.json', devtronApps: [devtronappName], triggerCI: [false] })
test.beforeEach('setting up pre-requisite', async ({ request }) => {
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);
    secondPageToken = await apiUtils.getApiTokenObject(token, 'playwright-super-admin').then(key => key?.tokenValue) as string;
})
test('blackout window', { tag: '@globalConfigurations' }, async ({ AllObjects, page, devtronAppCreation, createExternalCiWorkflow, browser }) => {
    test.setTimeout(25 * 1000 * 60);
    await page.clock.install();
    await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
    const data = await BaseTest.fetchTimeFromBrowser(page);
    const preTime = BaseTest.mapTimeToNearestHalfHour(data.totalMinutes, 'pre');
    const postTime = BaseTest.mapTimeToNearestHalfHour(data.totalMinutes + 30, 'post');
    let object = {
        0: {
            type: 'fixed',
            fromTime: preTime,
            toTime: postTime,
            fromDate: { year: String(data.year), month: String(data.month), date: String(data.date) },
            toDate: { year: String(data.year), month: String(data.month), date: String(data.date) },
            userDetails: { superAdmin: true },
            triggerDeploymentEligibility: { user1: true, user2: true },
            isBlackoutWindowActive: true,
            checkTimelineOnAppDetailsPage: true,
            locatorForTimeline: `//span[text()="Ends in"]/following-sibling::span`,
            maxTimeInSecondsToCheck: 3600
        },
        1: {
            type: 'fixed',
            fromTime: BaseTest.mapTimeToNearestHalfHour(data.totalMinutes + 30, 'post'),
            toTime: BaseTest.mapTimeToNearestHalfHour(data.totalMinutes + 30, 'post'),
            fromDate: undefined,
            toDate: undefined,
            userDetails: { superAdmin: false, specificUser: ['admin'] },
            triggerDeploymentEligibility: { user1: true, user2: false },
            isBlackoutWindowActive: false,
            checkTimelineOnAppDetailsPage: true,
            locatorForTimeline: '//span[text()="Starts in"]/following-sibling::span',
            maxTimeInSecondsToCheck: 3600
        },
        2: {
            type: 'daily',
            fromTime: preTime,
            toTime: postTime,
            fromDate: undefined,
            toDate: undefined,
            userDetails: { superAdmin: false, specificUser: ['admin'] },
            triggerDeploymentEligibility: { user1: true, user2: false },
            isBlackoutWindowActive: true,
            checkTimelineOnAppDetailsPage: false,
            locatorForTimeline: '//span[text()="Ends in"]/following-sibling::span',
            maxTimeInSecondsToCheck: 3600
        }
    }
    for (let i = 0; i <= 2; i++) {
        if (i == 0) {
            await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
            await AllObjects.deploymentWindowPage.createAProfile({ profileName: profileName, durationConfiguration: { type: object[i].type as 'fixed', fromTime: object[i].fromTime, toTime: object[i].toTime, fromDate: object[i].fromDate, toDate: object[i].toDate }, isMaintenanceWindow: false, userData: object[i].userDetails });
            await AllObjects.deploymentWindowPage.applyDWProfileToAnApp({ appName: devtronappName, envName: 'automation', profileName: profileName });
        }
        else {
            await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
            await AllObjects.deploymentWindowPage.searchAndClickOnAprofile(profileName);
            await AllObjects.deploymentWindowPage.clearPreviousAppliedDurationOnAProfile();
            await AllObjects.deploymentWindowPage.addDurationToWindow({ type: object[i].type as 'fixed', fromTime: object[i].fromTime, toTime: object[i].toTime, fromDate: object[i].fromDate, toDate: object[i].toDate });
            await AllObjects.deploymentWindowPage.setSuperAdminOrSpecificUserForDW(object[i].userDetails);
            await AllObjects.deploymentWindowPage.saveChangesButton.click();
        }
        await page.goto(devtronAppCreation.applicationUrl[0]);
        await AllObjects.workflowPage.checkWhetherBlackoutIsAppliedOrNot('automation', object[i].isBlackoutWindowActive);
        if (object[i].isBlackoutWindowActive) {
            await AllObjects.workflowPage.verifyBlackoutWindowAndCheckTriggerEligibility(0, 0, true);
            await AllObjects.workflowPage.clickOnSelectImageButton(0);
            await AllObjects.workflowPage.cdTriggerDeploymentButton.click();
            await AllObjects.deploymentWindowPage.verifyDeploymentWindowTimeline(page.locator(object[i].locatorForTimeline), object[i].maxTimeInSecondsToCheck);
            await page.reload();
            await AllObjects.workflowPage.ciModalCloseIcon.click();

        }
        if (i == 2) {
            await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Progressing');
            // triggerExternalCi is the API call is expected to fail
            await apiUtils.triggerExternalCi(token, nodeId, false);
            await page.reload();
            let secondPage = await BaseTest.createNewPageWithCustomCookies(browser, secondPageToken);
            let secondPageWorkflowPage = new WorkflowPage(secondPage);
            await BaseTest.clickOnDarkMode(secondPage);
            await BaseTest.clickOnToolTipOkayButton(secondPage);
            await secondPage.goto(devtronAppCreation.applicationUrl[0]);
            await secondPageWorkflowPage.verifyBlackoutWindowAndCheckTriggerEligibility(0, 0, false);
        }
        if (object[i].checkTimelineOnAppDetailsPage) {
            await AllObjects.appDetailsPageCiCd.appDetailsTabButton.click();
            await AllObjects.deploymentWindowPage.verifyDeploymentWindowTimeline(page.locator(object[i].locatorForTimeline), object[i].maxTimeInSecondsToCheck);
            if (object[i].isBlackoutWindowActive) {
                await AllObjects.appDetailsPageCiCd.triggerHibernationOrUnhibernation(true, { isEligible: true });
            }
        }

    }

})

test('maintenance window',{ tag: '@globalConfigurations' }, async ({ AllObjects, page, devtronAppCreation, createExternalCiWorkflow }) => {
    test.setTimeout(25 * 1000 * 60);
    await page.clock.install();
    await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
    const data = await BaseTest.fetchTimeFromBrowser(page);
    let object = {
        0: {
            type: 'fixed',
            fromTime: BaseTest.mapTimeToNearestHalfHour(data.totalMinutes + 30, 'post'),
            toTime: BaseTest.mapTimeToNearestHalfHour(data.totalMinutes + 30, 'post'),
            fromDate: { year: String(data.year), month: String(data.month), date: String(data.date) },
            day: undefined,
            toDate: { year: String(data.year), month: String(data.month), date: String(data.date) },
            userDetails: { superAdmin: true },
            triggerDeploymentEligibility: { user1: true, user2: true },
            isBlackoutWindowActive: true,
            checkTimelineOnAppDetailsPage: true,
            locatorForTimeline: `//span[text()="Ends in"]/following-sibling::span`,
            maxTimeInSecondsToCheck: 3600
        },
        1: {
            type: 'weekly',
            fromTime: BaseTest.mapTimeToNearestHalfHour(data.totalMinutes, 'pre'),
            toTime: BaseTest.mapTimeToNearestHalfHour(data.totalMinutes + 30, 'post'),
            day: data.day,
            fromDate: undefined,
            toDate: undefined,
            userDetails: { superAdmin: false, specificUser: ['admin'] },
            triggerDeploymentEligibility: { user1: true, user2: false },
            isBlackoutWindowActive: false,
            checkTimelineOnAppDetailsPage: true,
            locatorForTimeline: '//span[text()="Ends in"]/following-sibling::span',
            maxTimeInSecondsToCheck: 3600
        }
    }
    for (let i = 0; i <= 1; i++) {
        if (i == 0) {
            await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
            await AllObjects.deploymentWindowPage.createAProfile({ profileName: profileName, durationConfiguration: { type: object[i].type as 'fixed', fromTime: object[i].fromTime, toTime: object[i].toTime, fromDate: object[i].fromDate, toDate: object[i].toDate }, isMaintenanceWindow: true, userData: object[i].userDetails });
            await AllObjects.deploymentWindowPage.applyDWProfileToAnApp({ appName: devtronappName, envName: 'automation', profileName: profileName });
        }
        else {
            await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
            await AllObjects.deploymentWindowPage.searchAndClickOnAprofile(profileName);
            await AllObjects.deploymentWindowPage.clearPreviousAppliedDurationOnAProfile();
            await AllObjects.deploymentWindowPage.addDurationToWindow({ type: object[i].type as 'weekly', fromTime: object[i].fromTime, toTime: object[i].toTime, fromDate: object[i].fromDate, toDate: object[i].toDate, dayOfWeek: object[i].day });
            await AllObjects.deploymentWindowPage.setSuperAdminOrSpecificUserForDW(object[i].userDetails);
            await AllObjects.deploymentWindowPage.saveChangesButton.click();
        }
        await page.goto(devtronAppCreation.applicationUrl[0]);
        await AllObjects.workflowPage.checkWhetherBlackoutIsAppliedOrNot('automation', object[i].isBlackoutWindowActive);
        if (object[i].isBlackoutWindowActive) {
            await AllObjects.workflowPage.verifyBlackoutWindowAndCheckTriggerEligibility(0, 0, true);
            await AllObjects.workflowPage.clickOnSelectImageButton(0);
            await AllObjects.workflowPage.cdTriggerDeploymentButton.click();
            await AllObjects.deploymentWindowPage.verifyDeploymentWindowTimeline(page.locator(object[i].locatorForTimeline), object[i].maxTimeInSecondsToCheck);
            await page.reload();
            await AllObjects.workflowPage.ciModalCloseIcon.click();
        }
        else {
            await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0, [], [], 0, false, { isEligibleForTrigger: true, isTriggerPageVisible: true });
            await AllObjects.workflowPage.clickOnSelectImageButton(0);
            await AllObjects.workflowPage.cdTriggerDeploymentButton.click();
            await AllObjects.deploymentWindowPage.verifyDeploymentWindowTimeline(page.locator(object[i].locatorForTimeline), object[i].maxTimeInSecondsToCheck);
        }
    }
})
test.afterEach('deleting the data', async ({ AllObjects, page, request }) => {
    let apiUtils = new ApiUtils(request);
    await apiUtils.deleteAllNodesOfApplication(devtronappName, await apiUtils.login(process.env.PASSWORD!));
    await AllObjects.deploymentWindowPage.navigateToDeploymentWindow();
    await AllObjects.deploymentWindowPage.deleteDWProfile(profileName);
})