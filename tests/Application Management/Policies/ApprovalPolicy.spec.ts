import { APIRequestContext, request } from "playwright";
import { editDTCMCSDTO } from "../../../DTOs/DeevtronAppsDto/BaseDeploymentPageDTO";
import { exceptionCategoryEnum } from "../../../enums/Application Management/Policies/ApprovalPolicyPageEnums";
import { ApplyProfileFilterByEnum, ApplyProfileScopeCategoryEnum } from "../../../enums/Application Management/Policies/LockConfigurationPageEnums";
import { ApiUtils } from "../../../utilities/ApiUtils";
import { BaseTest } from "../../../utilities/BaseTest";
import { createAllObjects } from "../../../utilities/CreateObjectsForAllPomClasses";
import { dataSetForApprovalPolicyConfigPart, dataSetForImageApprovalPolicy, verifyExpressEditFeatureForDeploymentAppDetailsAppGrp } from "../../../utilities/DataObjectss.ts/approvalPolicies";
import { test } from "../../../utilities/Fixtures";
import { approveTheRequestInGmailAndVerifyMessage } from "../../../utilities/Third Party Clients/GmailNotifications/GmailConfig";
import { isFirstRun } from "../../../utilities/Third Party Clients/isFirstRun";
import { table } from "console";
test.use({ storageState: './LoginAuth.json' });

/**
 * variable Declarations
 */
let apiUtils: ApiUtils;
let devtronAppName: string = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4);
let devtronAppUrl: string;
let token: string;
let profileName: string = BaseTest.generateRandomStringWithCharsOnly(5);
let pageUrlWhereApproverWillNavigateToApprove: string;

/**
 * test configuration
 */

test.use({ storageState: './LoginAuth.json', devtronApps: [devtronAppName], triggerCI: [false] })

test.beforeEach('setting up the user and notifier', async ({ request, AllObjects }) => {
    test.setTimeout(5 * 1000 * 60);
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);
    if (!await AllObjects.notificationsPage.isValidSESConfigPresent('playwright-test', process.env.SES_CONFIG_ACCESS_KEY!)) {
        await AllObjects.notificationsPage.addNewSESConfig('playwright-test', true);
    }
    
        console.log('user details we are getting is ');
        const userDetails = await apiUtils.getObjectDetailsOfUser(token, '<EMAIL>');
        console.log(userDetails);
        
        if (!userDetails || !userDetails.id) {
            console.log('User not found, creating new user...');
            await apiUtils.addUser('<EMAIL>', token);
        } else {
            console.log('User already exists with ID:', userDetails.id);
        }
    
    
    await AllObjects.userPermissionPage.navifateToUserPermissionPage();
    await AllObjects.userPermissionPage.searchAanUser('<EMAIL>', true);
    await AllObjects.userPermissionPage.assignAPermissionGroupToAUser(apiUtils, 'test');
});



test('config approver testing',{tag: '@globalConfigurations'}, async ({ AllObjects, page, devtronAppCreation, instantiatePomObjectsForNonSuperAdminPage, browser }) => {
    test.setTimeout(30 * 1000 * 60);
    let appId = await apiUtils.getAppIdFromAppName(devtronAppName, token);
    devtronAppUrl = devtronAppCreation.applicationUrl[0];
    await apiUtils.addConfigOrSecretInEnv([{ appId: appId, token: token, key: "GracePeriod", value: "32", name: "testcm", isSecret: false }, { appId: appId, token: token, key: "GracePeriod", value: "32", name: "testsec", isSecret: true }]);
    let dataSet = dataSetForApprovalPolicyConfigPart(profileName, AllObjects, instantiatePomObjectsForNonSuperAdminPage.objects, devtronAppName);
    for (let approvalPolicyObejct of dataSet) {
        await page.bringToFront();
        await AllObjects.approvalPolicyPage.createApprovalPolicyProfile(approvalPolicyObejct.createProfileConfig);
        await AllObjects.approvalPolicyPage.applyApprovalPolicyProfile(approvalPolicyObejct.applyProfileConfig);
        await AllObjects.approvalPolicyPage.setOrRemoveExceptionUsersForProtectConfig(approvalPolicyObejct.exceptionConfig);
        await page.goto(devtronAppUrl);
        for (let i = 0; i < approvalPolicyObejct.editResourceRelatedData.length; i++) {
            await AllObjects.baseDeploymentTemplatePage.editAndSaveFieldsForDtCmCs(approvalPolicyObejct.editResourceRelatedData[i].editRelatedData);
            await AllObjects.baseDeploymentTemplatePage.raiseDraftOrProposeChanges(approvalPolicyObejct.raiseDraftOrProposeChanges as any);
            pageUrlWhereApproverWillNavigateToApprove = page.url();
            instantiatePomObjectsForNonSuperAdminPage.nonSuperAdminPage.bringToFront();
            await instantiatePomObjectsForNonSuperAdminPage.nonSuperAdminPage.goto(pageUrlWhereApproverWillNavigateToApprove);
            await BaseTest.clickOnDarkMode(instantiatePomObjectsForNonSuperAdminPage.nonSuperAdminPage);
            await BaseTest.clickOnToolTipOkayButton(instantiatePomObjectsForNonSuperAdminPage.nonSuperAdminPage);
            await instantiatePomObjectsForNonSuperAdminPage.objects.baseDeploymentTemplatePage.approveChanges(approvalPolicyObejct.editResourceRelatedData[i].approvalRelatedData.toastMessageWhileApproving);
            await approveTheRequestInGmailAndVerifyMessage(approvalPolicyObejct.editResourceRelatedData[i].approvalRelatedData.mailSubjectToFilterOut, browser, approvalPolicyObejct.editResourceRelatedData[i].approvalRelatedData.mailMessageWhileApproving, approvalPolicyObejct.editResourceRelatedData[i].approvalRelatedData.mailNumberToVerify);
            await page.bringToFront();
            await page.reload();
            await instantiatePomObjectsForNonSuperAdminPage.nonSuperAdminPage.reload();
            await AllObjects.baseDeploymentTemplatePage.verifyFieldsValuesInDtCmCS(['GracePeriod'], ['33'], approvalPolicyObejct.editResourceRelatedData[i].editRelatedData.resType, AllObjects.jobsPage);
            for (let { pageObject, expressEditVerification } of approvalPolicyObejct.editResourceRelatedData[i].expressEditData) {
                await pageObject.baseDeploymentTemplatePage.expressEditForDtCmCs(expressEditVerification);
                expressEditVerification.configuration.isEligible ? await pageObject.baseDeploymentTemplatePage.verifyFieldsValuesInDtCmCS(['GracePeriod'], ['34'], approvalPolicyObejct.editResourceRelatedData[i].editRelatedData.resType, pageObject.jobsPage) : null;
            }
        }
    }
})

test.describe('image approver test', async () => {
    test.use({ storageState: './LoginAuth.json', devtronApps: [devtronAppName], triggerCI: [false] })
    test('image approver test',{tag: '@globalConfigurations'}, async ({ AllObjects, devtronAppCreation, createExternalCiWorkflow, page, instantiatePomObjectsForSuperAdminPage, browser }) => {
        test.setTimeout(30 * 1000 * 60);
        let dataSet = dataSetForImageApprovalPolicy(profileName, AllObjects, devtronAppName);
        await page.bringToFront();
        await page.goto(devtronAppCreation.applicationUrl[0]);
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'webhook', 1);
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'devtron-demo', clusterName: 'default_cluster', helmOrGitops: 'helm' });
        for (let { createProfileConfig, applyProfileConfig, mailMessageToVerify, eligibleApproversAndCountOfApprovalsDone, sendApprovalRequest, imageApproverIsAppliedOrNot, mailSubjectToFilterMails, mailCountToVerify } of dataSet) {
            await AllObjects.approvalPolicyPage.createApprovalPolicyProfile(createProfileConfig);
            await AllObjects.approvalPolicyPage.applyApprovalPolicyProfile(applyProfileConfig);
            await AllObjects.approvalPolicyPage.setOrRemoveExceptionUsersForProtectConfig({ exceptionFor: exceptionCategoryEnum.deployment, allowSuperAdmins: false });
            await page.goto(devtronAppCreation.applicationUrl[0]);
            await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Succeeded');
            let approverPageUrl = await AllObjects.workflowPage.sendApprovalRequestOrCancelRequest(sendApprovalRequest);
            await instantiatePomObjectsForSuperAdminPage.SuperAdminPage.goto(approverPageUrl);
            await BaseTest.clickOnDarkMode(instantiatePomObjectsForSuperAdminPage.SuperAdminPage);
            await instantiatePomObjectsForSuperAdminPage.objects.workflowPage.verifyEligibleApproversListAndSucessfullApprovals(eligibleApproversAndCountOfApprovalsDone.eligibleApprovers, eligibleApproversAndCountOfApprovalsDone.count);
            await instantiatePomObjectsForSuperAdminPage.objects.workflowPage.approveRequest();
            await approveTheRequestInGmailAndVerifyMessage(mailSubjectToFilterMails, browser, mailMessageToVerify, mailCountToVerify);
            await page.reload();
            await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Progressing');
            await AllObjects.workflowPage.verifyCountOfApprovedImagesAndPerformAction(0);
        }
        await AllObjects.approvalPolicyPage.setOrRemoveExceptionUsersForProtectConfig({ exceptionFor: exceptionCategoryEnum.deployment, allowSuperAdmins: true });
        // await verifyExpressEditFeatureForDeploymentAppDetailsAppGrp(devtronAppCreation.applicationUrl[0], devtronAppName, AllObjects, page, 'deployment');
        await verifyExpressEditFeatureForDeploymentAppDetailsAppGrp(devtronAppCreation.applicationUrl[0], devtronAppName, AllObjects, page, 'app-details');
        await verifyExpressEditFeatureForDeploymentAppDetailsAppGrp(devtronAppCreation.applicationUrl[0], devtronAppName, AllObjects, page, 'app-group');
    })

})

test.afterEach('deleting the data', async ({ AllObjects, page, request }) => {
    test.setTimeout(5 * 1000 * 60);
    await apiUtils.deleteApprovalPolicy(profileName, await apiUtils.login(process.env.PASSWORD!));
    await apiUtils.deleteAllNodesOfApplication(devtronAppName, await apiUtils.login(process.env.PASSWORD!));
})

//Image approval requested | Application: ui-automdfsk | Environment: automation
//Image approved successfully
//Image is already approved