import { removeAllListeners } from "process";
import { ApiUtils } from "../../../../utilities/ApiUtils";
import { BaseTest } from "../../../../utilities/BaseTest";
import { DataObjectForHibernationPatch } from "../../../../utilities/DataObjectss.ts/HibernationPatchDataObjec";
import { test } from "../../../../utilities/Fixtures";
import { expect } from "playwright/test";
import { DevtronDeploymentChart } from "../../../../enums/Application Management/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum";


// variables Decalaration
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
let apiUtils: ApiUtils;
let token: string;
let filePathOfCustomChart: string = 'utilities/playwright-automation-test-0.4.0.tgz';
let applicationNames: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
// variable declaration ends here

test.use({ storageState: "./LoginAuth.json", devtronApps: applicationNames, deploymentTemplateChart: ['Deployment', credentials.customChartName], allChartVersion: ['4.20.0', credentials.customChartVersion], triggerCI: [false, false], triggerExternalCi: true });

/**
 * Setting up the custom chart before running the tests.
 *
 */
test.beforeEach('uploading required chart', async ({ AllObjects, page, request }, testInfo) => {
    if (testInfo.title != 'Hibernation patch visibility check for all charts ') {
        apiUtils = new ApiUtils(request);
        token = await apiUtils.login(process.env.PASSWORD!);
        await AllObjects.deploymentChartPage.ensureCustomChartIsUploaded(credentials.customChartName, credentials.customChartVersion, filePathOfCustomChart, apiUtils, token);
    }
})


/**
 * This test case checks the visibility of the hibernation patch feature for all charts.
 * It navigates to the deployment charts page and verifies that the hibernation patch
 * button is visible for each chart type (Job/Cronjob, Rollout Deployment, Stateful Set).
 *
 */
test('Hibernation patch visibility check for all charts ',{ tag: '@globalConfigurations' }, async ({ AllObjects }) => {
    test.setTimeout(5 * 1000 * 60);
    let chartsToCheck: string[] = [DevtronDeploymentChart.JobCronjob, DevtronDeploymentChart.RolloutDeployment, DevtronDeploymentChart.StatefulSet];
    await AllObjects.deploymentChartPage.navigateToDeploymentChartsPage();
    for (let chart of chartsToCheck) {
        await AllObjects.deploymentChartPage.clickOnEditGuiSchemaOrEditHibernationPatchButton(chart, true);
        await AllObjects.deploymentChartPage.saveTheChartConfigurationAndVerifyToastMessage(true);
    }
})

/**
 * This test case checks the functionality of the hibernation patch feature for all charts.
 * It navigates to the deployment charts page, configures the hibernation patch, and verifies
 * that the hibernation is successful for custom chart and devtron deployment chart.
 *
 */

test('Hibernation patch deployment check', { tag: '@globalConfigurations' }, async ({ AllObjects, page, devtronAppCreation, createExternalCiWorkflow }) => {
    test.setTimeout(10 * 1000 * 60);
    let hibernationPatchTestDataObjectArray = DataObjectForHibernationPatch(devtronAppCreation.applicationUrl);
    for (let hibernationPatchObjectEntity of hibernationPatchTestDataObjectArray) {
        await AllObjects.deploymentChartPage.navigateToDeploymentChartsPage();
        await AllObjects.deploymentChartPage.configureHibernationPatch({ chartName: hibernationPatchObjectEntity.chartName, resetToDefault: hibernationPatchObjectEntity.resetToDefault, configuration: hibernationPatchObjectEntity.yamlConfigurationToEnter, expectSuccess: true });
        await page.goto(hibernationPatchObjectEntity.appUrl);
        await AllObjects.appDetailsPageCiCd.triggerHibernationOrUnhibernation(true, undefined, hibernationPatchObjectEntity.isHibernationSuccessfull);
        if (hibernationPatchObjectEntity.isHibernationSuccessfull) {
            await AllObjects.chartStoreAppDetailsPage.verifyScaleUpAndDownWorkloads(false);
            await AllObjects.chartStoreAppDetailsPage.verifyManifestOfService(hibernationPatchObjectEntity.serviceTypeToVerify);
            await AllObjects.appDetailsPageCiCd.triggerHibernationOrUnhibernation(false);
            await AllObjects.chartStoreAppDetailsPage.verifyScaleUpAndDownWorkloads(true, "Healthy");
        }
        else {
            await expect(AllObjects.deploymentChartPage.codeMirrorEditorTextArea).toBeVisible({ timeout: 10000 });
        }
    }


})
test.afterEach('deleting the applications', async ({ }, testInfo) => {
    if (testInfo.title != 'Hibernation patch visibility check for all charts ')
        for (let application of applicationNames) {
            await apiUtils.deleteAllNodesOfApplication(application, token, true);
        }

})
