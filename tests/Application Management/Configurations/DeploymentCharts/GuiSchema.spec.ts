import { expect, Locator } from "playwright/test";
import { DevtronDeploymentChart } from "../../../../enums/Application Management/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum";
import { BasePage } from "../../../../Pages/BasePage";
import { yamlForCustomGuiSchema } from "../../../../utilities/clipboardyYamls.ts/GuiSchemaYaml";
import { test } from "../../../../utilities/Fixtures";
import { BaseTest } from "../../../../utilities/BaseTest";
import { ApiUtils } from "../../../../utilities/ApiUtils";


test.setTimeout(10 * 1000 * 60);
let devtornAppNames: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4), 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
let credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
test.use({ storageState: './LoginAuth.json', devtronApps: devtornAppNames, triggerCI: [false, false], deploymentTemplateChart: ['Deployment', credentials.customChartName], allChartVersion: ['4.20.0', credentials.customChartVersion] });
let locatorToVerifyScreenshot: Locator;
test('Gui Schema visibility check For All devtron charts ',{tag: '@globalConfigurations'}, async ({ AllObjects }) => {
    let chartsNameToCheck: string[] = [DevtronDeploymentChart.JobCronjob, DevtronDeploymentChart.RolloutDeployment, DevtronDeploymentChart.StatefulSet];
    for (let chart of chartsNameToCheck) {
        await AllObjects.deploymentChartPage.navigateToDeploymentChartsPage();
        await AllObjects.deploymentChartPage.clickOnEditGuiSchemaOrEditHibernationPatchButton(chart, false);
        await AllObjects.deploymentChartPage.verifyThatCustomGuiSchemaTextAreaIsEmpty(false);
    }
})

export function dataSetForGuiSchema(appUrl1: string, appUrl2: string) {
    return [{
        chartName: 'Deployment',
        yamlToEnter: yamlForCustomGuiSchema(),
        resetToDefault: false,
        checkValidations: true,
        guiSchemaConfiguredPerfectly: true,
        appUrl: appUrl1
    }, {
        chartName: 'Deployment',
        yamlToEnter: '{}',
        resetToDefault: false,
        checkValidations: false,
        guiSchemaConfiguredPerfectly: false,
        appUrl: appUrl1
    }, {
        chartName: 'Deployment',
        yamlToEnter: yamlForCustomGuiSchema(),
        resetToDefault: true,
        checkValidations: false,
        guiSchemaConfiguredPerfectly: true,
        appUrl: appUrl1
    }, {
        chartName: credentials.customChartName,
        yamlToEnter: yamlForCustomGuiSchema(),
        resetToDefault: false,
        checkValidations: false,
        guiSchemaConfiguredPerfectly: true,
        appUrl: appUrl2
    }]
}


test('Edit Gui Schema And Verify functionality',{tag: '@globalConfigurations'}, async ({ AllObjects, page, devtronAppCreation }) => {
    for (let data of dataSetForGuiSchema(devtronAppCreation.applicationUrl[0], devtronAppCreation.applicationUrl[1])) {
        await AllObjects.deploymentChartPage.navigateToDeploymentChartsPage();
        await AllObjects.deploymentChartPage.clickOnEditGuiSchemaOrEditHibernationPatchButton(data.chartName, false);
        if (data.checkValidations) {
            await AllObjects.deploymentChartPage.clearHibernationPatchTextArea();
            await AllObjects.deploymentChartPage.saveTheChartConfigurationAndVerifyToastMessage(false);
        }
        if (data.resetToDefault) {
            await AllObjects.deploymentChartPage.resetToDefaultButton.click();
        }
        else {
            await AllObjects.deploymentChartPage.clearHibernationPatchTextArea();
            await AllObjects.deploymentChartPage.enterHibernationPatchConfiguration(data.yamlToEnter);
        }
        await AllObjects.deploymentChartPage.preViewGuiSchemaButton.click();
        locatorToVerifyScreenshot = AllObjects.deploymentChartPage.setGuiSchemaLocatorToVerifyScreenshot(true, true);
        await expect(locatorToVerifyScreenshot).toHaveScreenshot({
            maxDiffPixelRatio: 0.033,
            threshold: 0.02
        });
        await AllObjects.deploymentChartPage.saveTheChartConfigurationAndVerifyToastMessage(true);
        await page.goto(data.appUrl);
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources('base-configurations');
        await AllObjects.baseDeploymentTemplatePage.clickOnAdvancedYamlOrGui(true);
        locatorToVerifyScreenshot = AllObjects.deploymentChartPage.setGuiSchemaLocatorToVerifyScreenshot(data.guiSchemaConfiguredPerfectly, false);
        await expect(locatorToVerifyScreenshot).toHaveScreenshot({ maxDiffPixelRatio: 0.033 });
    }

})

test.afterEach('delete all applications', async ({ AllObjects, request }, testInfo) => {
    if (testInfo.title == "Edit Gui Schema And Verify functionality") {
        let apiUtls = new ApiUtils(request);
        let token = await apiUtls.login(process.env.PASSWORD!);
        for (let app of devtornAppNames) {
            await apiUtls.deleteAllNodesOfApplication(app, token);
        }
    }
})

