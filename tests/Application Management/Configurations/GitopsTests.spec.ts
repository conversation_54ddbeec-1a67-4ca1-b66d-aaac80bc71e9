import { expect } from "playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { DataSet } from "../../../utilities/DataObjectss.ts/multipleTestDataObject";
import { test } from "../../../utilities/Fixtures";
import { ApiUtils } from "../../../utilities/ApiUtils";

test.describe.configure({ mode: 'default' });
test.use({ storageState: './LoginAuth.json' });
let credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");


test('gitops deployment_oss', { tag: '@cicd' }, async ({ AllObjects, page, devtronAppCreation }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    let imageBuilt = devtronAppCreation.imageBuilt![0];
    let devtronApps: string[] = [];
    devtronApps.push(devtronAppCreation.appNames![0]);
    (testInfo as any).devtronAppsCreated = devtronApps;
    // Click on Add CD Icon on Application Configuration Page
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, "ci", 1);
    // Add CD Module on Workflow Page
    await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: credentials.EnvNameForCD[1], helmOrGitops: 'gitops', clusterName: 'default_cluster' });
    // Verify Image and Trigger Deployment on Workflow Page
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1, [imageBuilt.split(':')[1]], [1]);
    await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'devtron-demo', 0);
    await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: 'env1', clusterName: 'default_cluster', helmOrGitops: 'helm' });
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.EnvNameForCD[1]);
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute', 'Manual');
    await AllObjects.jobsPage.executeCustomScript('pre', ['echo $testcm', 'exit 1 '], false);
    await AllObjects.prePostCiCd.addPrePostTask('post', 'execute', 'Auto');
    await AllObjects.jobsPage.executeCustomScript('pre', ['echo $testcm', 'exit 1 '], true);
    // Verify Continuous Deployment (CD) status on Workflow Page
    await AllObjects.workflowPage.verifyCiCdStatus(0, 3, "Succeeded");
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(1, [], [], 0, false);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 2, 'Failed');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 4, 'Failed');
    await page.reload();
    await AllObjects.workflowPage.verifyCiCdStatus(0, 3, "Succeeded");
    await AllObjects.workflowPage.verifyCiCdStatus(0, 5, "Not Deployed");
    if (process.env.clusterType == "enterprise") {
        await AllObjects.workflowPage.clickOnDetailsOfAnyNode(2);
        await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['testvalue'], 'Failed');
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
        await AllObjects.workflowPage.clickOnDetailsOfAnyNode(4);
        await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['testvalue'], 'Failed');
        await AllObjects.workflowPage.clickOnBuildAndDeployTab();
    }
    // Verify Tags on Image on Workflow Page
    await AllObjects.workflowPage.verifyTagsOnImage('deploymentTime', true, 1);
    await AllObjects.workflowPage.verifyTagsOnImage('Latest', true, 1)
    // Select the environment on Deployment History Page
    await AllObjects.deploymentHistory.selectEnvironment(credentials.EnvNameForCD[1]);
    // Verify GitOps Deployment on Deployment History Page
    await AllObjects.deploymentHistory.verifyGitopsDeployment('2');
    // Verify Deployment Status on Deployment History Page
    await AllObjects.deploymentHistory.verifyDeploymentStatus();
    // Verify Artifacts on Deployment History Page
    await AllObjects.deploymentHistory.verifyArtifactsOnDeploymentHistory(imageBuilt);
    // Verify Source Info on Deployment History Page
    await AllObjects.deploymentHistory.verifySourceInfo(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.BranchName[0]);
    // Verify Deployment Popup Card
    await AllObjects.deploymentHistory.verifyDeploymentPopupCard();
    // Select environment on Application Details Page - CI/CD section
    await AllObjects.appDetailsPageCiCd.selectEnvironment(credentials.EnvNameForCD[1]);
    // Verify Application and Deployment Status on Application Details Page - CI/CD section
    await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
    await AllObjects.chartStoreAppDetailsPage.verifyDeploymentStatus();
    await page.goto(process.env.BASE_SERVER_URL!);
    await AllObjects.createAppPage.applyFilter(DataSet.appListPage.dataSetForFiltering());
    await AllObjects.createAppPage.verifySorting();
});


test.describe('without ci trigger', async () => {
    test.use({ storageState: './LoginAuth.json', triggerCI: [false] });
    test('User-defined-git-repo Verifing features->Cloning_oss', { tag: '@cicd' }, async ({ page, AllObjects, devtronAppCreation }, testInfo) => {
        test.setTimeout(25 * 1000 * 60);
        let devtronApps: string[] = [];
        devtronApps.push(devtronAppCreation.appNames![0]);
        (testInfo as any).devtronAppsCreated = devtronApps;
        // Click on Add CD Icon on Application Configuration Page
        await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
        await AllObjects.appConfigurationPage.clickOnAddIconToAddCd(0, 'ci', 1);
        // Add CD Module on Workflow Page
        await AllObjects.appConfigurationPage.addCdModule({ envNameForCd: credentials.EnvNameForCD[1], helmOrGitops: 'gitops', clusterName: 'default_cluster' });
        //Go to Gitops Configure page.
        await AllObjects.globalConfigPage.goToGitopsConfiguration(process.env.BASE_SERVER_URL as string);
        //Validate and Configure Gitops Configuration page.
        //Verify User defined git repo Buttons is present.
        await AllObjects.gitopsConfigurationPage.VerifyUserDefinedGitRepoIsVisible();
        //Switch between Allow changing git and Default Git.
        await AllObjects.gitopsConfigurationPage.switchRadioButtonBetweenDefaultAndAllowChangingGit(credentials.userDefinedGitRepo[1]);
        // Clicking on the application in the global configuration page
        await AllObjects.globalConfigPage.clickingOnApplicaiton();
        // Generating a new random application name for cloning
        var newAppName = "clone-app-" + BaseTest.generateRandomStringWithCharsOnly(6);
        // Cloning the original app with the new generated name
        await AllObjects.createAppPage.cloningApp(devtronAppCreation.appNames[0], newAppName, credentials.ProjectName);
        devtronApps.push(newAppName);
        (testInfo as any).devtronAppsCreated = devtronApps;
        // Verifying if GitOps repository is not configured
        await AllObjects.workflowPage.VerifyGitopsRepoNotConfigureModal();
        await expect(async () => {
            await AllObjects.deployChartPage.setGitRepository(newAppName, credentials.userDefinedGitRepo[3]);
            //wait until worklfow editor page is visibel.
            await AllObjects.workflowPage.waitUntilWorkflowEditorPageIsVisible();
        }).toPass({ timeout: 3 * 1000 * 60 });
    });
})


// 0-> Test case for end-to-end chart deployment using Gitops.
// 1->Test case for end-to-end chart deployment using Gitops with Autocreate repo.
// 2->Test case for end-to-end chart deployment using Gitops with User defined repo.
test('end-to-end-chart-gitops-user-defined-git-repo_oss', { tag: '@chartstore' }, async ({ page, AllObjects }, testInfo) => {
    test.setTimeout(25 * 60 * 1000);
    let count = 0;
    let appCreated: string[] = [];
    for (let i = 0; i < 3; i++) {
        if (i == 0) {
            await AllObjects.globalConfigPage.goToGitopsConfiguration(process.env.BASE_SERVER_URL as string);
            await AllObjects.gitopsConfigurationPage.switchRadioButtonBetweenDefaultAndAllowChangingGit(credentials.userDefinedGitRepo[0]);
        } else {
            await AllObjects.globalConfigPage.goToGitopsConfiguration(process.env.BASE_SERVER_URL as string);
            await AllObjects.gitopsConfigurationPage.switchRadioButtonBetweenDefaultAndAllowChangingGit(credentials.userDefinedGitRepo[1]);
        }
        // Setting timeout

        // Generating a random name for the chart
        let name = BaseTest.generateRandomStringWithCharsOnly(5);
        appCreated.push(name);
        (testInfo as any).apps = appCreated;
        // Navigating to chart store and selecting a particular chart
        await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
        await AllObjects.chartStorePage.SelectingChart(credentials.ChartName, credentials.ChartSource);
        // Deploying the chart
        let userDefinedGitRepo = credentials.userDefinedGitRepo[i + 1];
        var ChartVersion = await AllObjects.deployChartPage.deployingChart(name, credentials.ProjectName, credentials.envNameForCharts, "gitops", userDefinedGitRepo);
        await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
        if (count == 0) {
            await AllObjects.chartStoreAppDetailsPage.verificationAppDetailsPage(false, ChartVersion!, [credentials.TerminalScript[0]], credentials.TerminalExpectedValue[0], credentials.ChartName);
            await AllObjects.chartStoreAppDetailsPage.scaleUpAndDownWorkloadsChartStoreApps(false);
            await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus('Hibernating');
            await AllObjects.chartStoreAppDetailsPage.scaleUpAndDownWorkloadsChartStoreApps(true);
            await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
            // Comparing manifest
            await AllObjects.deployChartPage.ComparingManifest();
            var updatedChartVersion = await AllObjects.deployChartPage.updatingChartVersion();
            await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
            await AllObjects.chartStoreAppDetailsPage.verifyDeploymentStatus();
            await AllObjects.chartStoreAppDetailsPage.verifyChartVersion(updatedChartVersion!);
            await AllObjects.chartStoreDeploymentHistoryy.verifyNumberofSucceddedDeployments(2);
        }
        // Updating chart version
        count++;
    }
});
test.afterEach('deleting apps', async ({ AllObjects, request, page }, testInfo) => {
    test.setTimeout(5 * 1000 * 60);
    let apiUtils = new ApiUtils(request);
    let token = await apiUtils.login(process.env.PASSWORD!);
    let failureCount = 0;
    try {
        if (testInfo.title != "end-to-end-chart-gitops-user-defined-git-repo_oss") {
            for (let key of (testInfo as any).devtronAppsCreated) {
                await apiUtils.deleteAllNodesOfApplication(key, token);
            }
        }
    }
    catch (error) {
        console.error('Error deleting application:', error);
        failureCount++;
    }
    if (test.name == 'end-to-end-chart-gitops-user-defined-git-repo_oss') {
        for (var i = 0; i < (testInfo as any).apps.length; i++) {
            let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
            await page.goto(process.env.BASE_SERVER_URL + urlTogo);
            await AllObjects.createAppPage.searchAndSelectAppsFromList((testInfo as any).apps[i], true);
            // Deleting the app
            await AllObjects.deployChartPage.deletingChartStoreApp();
        }
    }
    expect(failureCount).toBe(0);
})
