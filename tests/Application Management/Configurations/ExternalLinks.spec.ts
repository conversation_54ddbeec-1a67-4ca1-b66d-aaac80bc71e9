import { expect } from "playwright/test";
import { ApiUtils } from "../../../utilities/ApiUtils";
import { BaseTest } from "../../../utilities/BaseTest"
import { externalLinkDataObject } from "../../../utilities/DataObjectss.ts/ExternalLinksDataObject"
import { test } from "../../../utilities/Fixtures"
/**
 * Variable Declarations
 */
let devtronAppNames = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(5)];
let externalLinkName: string = BaseTest.generateRandomStringWithCharsOnly(6);
let helmAppCreated: string;
let apiUtils: ApiUtils;
let token: string;
let externalCiWorkflowEnv: string = process.env.clusterType == "enterprise" ? 'playwright-vir' : 'automation';
let data;
let appUrls: string[];
let virtualEnvObjectDetails: any;
let automationEnvObjectDetails: any;


/**
 * Test Configurations
 */
test.use({ storageState: './LoginAuth.json', devtronApps: devtronAppNames, triggerCI: [false], externalCiWorkflowEnvName: externalCiWorkflowEnv });
test.setTimeout(15 * 1000 * 60);


test.beforeEach('creating apps and initizlixing variables', async ({ AllObjects, devtronAppCreation, createExternalCiWorkflow, helmAppCreation, page, request }) => {
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);
    appUrls = [devtronAppCreation.applicationUrl[0], helmAppCreation.helmAppUrl];
    if (process.env.clusterType?.includes('enterprise')!) {
        await page.goto(appUrls[0]);
        await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
        virtualEnvObjectDetails = await apiUtils.getEnvObject(token, 'playwright-vir');
    }
    data = externalLinkDataObject(externalLinkName, [devtronAppNames[0], helmAppCreation.helmAppName]);
    helmAppCreated = helmAppCreation.helmAppName;
    automationEnvObjectDetails = await apiUtils.getEnvObject(token, 'automation');
})


test('external links_oss_eaMode', { tag: '@globalConfigurations' }, async ({ AllObjects, devtronAppCreation, createExternalCiWorkflow, helmAppCreation, page, request }) => {

    for (let i = 0; i < data.length; i++) {
        await AllObjects.externalLinksPage.navigateToExternalLinksPage();
        await AllObjects.externalLinksPage.configureExternalLinks(data[i]);
        if (i == 0 && !process.env.clusterType?.includes('ea')!) {
            await page.goto(appUrls[0]);
            await AllObjects.appConfigurationPage.externalLinksButton.click();
            data[i].linkName = 'appLinkTest';
            data[i].appAdminsCanEdit = false;
            await AllObjects.externalLinksPage.configureExternalLinks(data[i], false);
        }
        for (let j = 0; j < appUrls.length; j++) {
            if (process.env.clusterType?.includes('ea')! && j == 0) {
                continue;
            }
            let isLinkVisible: boolean = j == 0 || i == 2 || (process.env.clusterType?.includes('ea')! && j == 1) ? true : false;
            let appNames: string[] = [devtronAppNames[0], helmAppCreation.helmAppName];
            await page.goto(appUrls[j]);
            let idTouseInUrl = j == 1 || process.env.clusterType != "enterprise" ? automationEnvObjectDetails.id : virtualEnvObjectDetails.id;
            await AllObjects.appDetailsPageCiCd.clickOnExternalLinkAndVerifyLink(data[i].linkName, true, `https://deep/${appNames[j]}/${idTouseInUrl}`, isLinkVisible);
        }
        await AllObjects.externalLinksPage.navigateToExternalLinksPage();
        if (i == 0) {
            let appNameToVerify = process.env.clusterType?.includes('ea')! ? helmAppCreated : devtronAppNames[0];
            await AllObjects.externalLinksPage.applyFilterAndVerifyResult(['Application'], [appNameToVerify], ['1 application', externalLinkName]);
        }
        await AllObjects.externalLinksPage.deleteExternalLink(externalLinkName);
    }

})
test.afterEach('deleting the data', async ({ request, AllObjects, page }) => {
    apiUtils = new ApiUtils(request);
    token = await apiUtils.login(process.env.PASSWORD!);
    test.setTimeout(5 * 1000 * 60);
    let failCount = 0;
    console.log("------------"+devtronAppNames[0]+"--------------------------");
    try {
        process.env.clusterType?.includes('ea')! ? '' : await apiUtils.deleteAllNodesOfApplication(devtronAppNames[0], token);
    }
    catch (error) {
        console.log('was nOT ABLE TO delete apps');
        failCount++;
    }
    try {
        let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
        await page.goto(process.env.BASE_SERVER_URL + urlTogo);
        await AllObjects.createAppPage.searchAndSelectAppsFromList(helmAppCreated, true);
        // Deleting the app
        await AllObjects.deployChartPage.deletingChartStoreApp();
    }
    catch (error) {
        console.log('not able to delete the helm appos');
        failCount++;
    }
    try {
        await AllObjects.externalLinksPage.navigateToExternalLinksPage();
        await AllObjects.externalLinksPage.deleteExternalLink('appLinkTest');
    }
    catch (error) {
        console.log('not able to delete the helm app');
    }
    expect(failCount).toBe(0);
})