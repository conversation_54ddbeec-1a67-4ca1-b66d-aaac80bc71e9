import { expect } from "playwright/test";
import { test } from "../../../utilities/Fixtures";
import { AllTypes } from "../../../utilities/Types";
import { ChartStoreAppDetailsPage } from "../../../Pages/ApplicationManagement/Chart Store/ChartStoreAppDetailsPage";
import { BaseTest } from "../../../utilities/BaseTest";
import { dataObjectForApplyProfileFilterAndVerifyConfig, dataObjectForOssBuildInfraProfileConfig, datasetForBuildInfra } from "../../../utilities/DataObjectss.ts/BuildInfraDataObject";
import { ApiUtils } from "../../../utilities/ApiUtils";


/**
 * Variable Declarations
 */
let appname = 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4);
let applicationUrl: string;
test.use({ storageState: './LoginAuth.json', repoNumber: 2, devtronApps: [appname], triggerCI: [false] });
test.setTimeout(35 * 1000 * 60);
let apiutils: ApiUtils;
let token: string;
let arrayOfObjectForBuildInfra: AllTypes.BuildInfra.dataSetForBuildInfra[] = datasetForBuildInfra;
let k8sDriverConfiguration: AllTypes.BuildInfra.k8sDriverConfiguration[];



/**
 * Creation of Profiles and target platforms inside target platforms..
 * apply the profile filter and verify that configuration should be changed acc to filter 
 * Applying the profile to an application
 */
test.beforeEach(async ({ AllObjects, devtronAppCreation, request }, testInfo) => {
    apiutils = new ApiUtils(request);
    token = await apiutils.login(process.env.PASSWORD!);
    applicationUrl = devtronAppCreation.applicationUrl[0];
    if (process.env.clusterType == "enterprise") {
        await AllObjects.buildInfraPage.goToBuildInfraPage();
        let profileNames = ['global', 'automation-test'];
        for (let i = 0; i < profileNames.length; i++) {
            i == 0 ? k8sDriverConfiguration = [{ turnOn: true, targetPlatform: 'linux/amd64' }] : k8sDriverConfiguration = [{ turnOn: true, targetPlatform: 'linux/arm64' }, { turnOn: true, targetPlatform: 'linux/amd64' }];
            await AllObjects.buildInfraPage.createOrEditBuildInfraProfile({ profileName: profileNames[i], k8sDriverConfiguration: k8sDriverConfiguration });
            await AllObjects.buildInfraPage.goToBuildInfraPage();
        }
        for (let profileFilterObject of await dataObjectForApplyProfileFilterAndVerifyConfig(AllObjects)) {
            await AllObjects.buildInfraPage.fetchConfigurationOfAProfileAndVerify(profileFilterObject.profileName, profileFilterObject.filterValue, profileFilterObject.value);
        }
        await AllObjects.buildInfraPage.applyGivenProfilesToGivenApplications(['automation-test'], [appname]);
        await AllObjects.buildInfraPage.verifyApplicationsAssignedToAProfile('automation-test', appname);
    }

});

/**
 * build infra test for enterprise
 */
test('build infratest', { tag: '@globalConfigurations' }, async ({ AllObjects, page, request }) => {
    for (let i = 0; i < arrayOfObjectForBuildInfra.length; i++) {
        //editing the 2 profiles based on data set
        for (let profileConfiguration of arrayOfObjectForBuildInfra[i].editProfiles) {
            await AllObjects.buildInfraPage.goToBuildInfraPage();
            await AllObjects.buildInfraPage.createOrEditBuildInfraProfile(profileConfiguration);
        }
        // apply the filter of runner configuration and target platform and verify the result of the filter applied 
        await AllObjects.buildInfraPage.goToBuildInfraPage();
        for (let ele of arrayOfObjectForBuildInfra[i].validationCheck) {
            await AllObjects.buildInfraPage.fetchConfigurationOfAProfileAndVerify('automation-test', ele.filterType, ele.config);
        }
        // upading the ci node of the application
        await page.goto(applicationUrl);
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
        await AllObjects.appConfigurationPage.clickOnAllowOverrideOnCiNode();
        await AllObjects.appConfigurationPage.setTargetPlatformForBuild(arrayOfObjectForBuildInfra[i].platformDuringBuild!);
        await AllObjects.appConfigurationPage.updatePipelineButton.click();
        if (i == 0) {
            await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
            await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
            await AllObjects.jobsPage.executeCustomScript('printing values', ['echo $key1', 'echo $key2']);
        }
        // trigger the build , and wait for the required count of pods to come in resource browser
        await AllObjects.workflowPage.triggerCiModule();
        await AllObjects.workflowPage.clickOnDetailsOfAnyNode(0);
        await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(arrayOfObjectForBuildInfra[i].valuesToCheckInLogs, 'Running');
        if (i == 0 || i == 1) {
            let requiredpodCount = i == 0 ? 4 : 3
            let count = 0;
            let triggerNumber = page.url().split('/');
            await page.goto(process.env.BASE_SERVER_URL! + `/resource-browser/1/pod/k8sEmptyGroup?searchKey=${triggerNumber[triggerNumber.length - 2]}&namespace=devtron-ci`);
            while (await AllObjects.resourceBrowserPage.podsList.count() != requiredpodCount && count <= 25) {
                count++;
                await AllObjects.resourceBrowserPage.pageSyncButton.click();
                await page.waitForTimeout(5000);
            }
            expect(count).toBeLessThan(25);
            for (let j = 0; j < requiredpodCount; j++) {
                await AllObjects.resourceBrowserPage.podsList.nth(j).click();
                let platform = await AllObjects.resourceBrowserPage.checPodTargetPlatform();
                for (let key of arrayOfObjectForBuildInfra[i].valuesToCheckInManifest[platform!]) {
                    await AllObjects.chartStoreAppDetailsPage.verifyManifest(key);
                }
                await page.goBack();
            }

        }
        if (i == 2) {
            let manifestPage = await AllObjects.buildHistoryPage.ViewCiWorkerPod();
            let chartStorePage = new ChartStoreAppDetailsPage(manifestPage);
            for (let key of arrayOfObjectForBuildInfra[i].valuesToCheckInManifest.runnerManifest!) {
                await chartStorePage.verifyManifest(key);
            }
        }
        if (arrayOfObjectForBuildInfra[i].isCiFailed) {
            await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['checkout commit'], 'Failed');
        }
    }
    // checking that we should not be able to create a profile with same name
    await apiutils.createBuildInfraProfileWithDefaultConfig(token, 'global', 400);
    await apiutils.createBuildInfraProfileWithDefaultConfig(token, 'automation-test', 400);

})

/**
 * build infra test for oss
 */
test.describe('build infra test for oss', () => {
    test.skip(process.env.clusterType == "enterprise");
    test('buildInfra_oss', async ({ AllObjects, page }) => {
        await AllObjects.buildInfraPage.goToBuildInfraPage()
        await AllObjects.buildInfraPage.createOrEditBuildInfraProfile(dataObjectForOssBuildInfraProfileConfig())
        await page.goto(applicationUrl);
        await AllObjects.workflowPage.triggerCiModule();
        await AllObjects.workflowPage.clickOnDetailsOfAnyNode(0);
        await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(['Login Succeeded'], 'Running');
        let otherPage = await AllObjects.buildHistoryPage.ViewCiWorkerPod();
        let chartStorePage = new ChartStoreAppDetailsPage(otherPage);
        await chartStorePage.verifyManifest('130');
        await chartStorePage.verifyManifest('200');

    })

})

/**
 * deletion of app created and build infra profile created
 */
test.afterEach('deleting the data', async ({ AllObjects }) => {
    let failureCount = 0;
    if (process.env.clusterType == "enterprise") {
        await AllObjects.buildInfraPage.goToBuildInfraPage();
        try {
            await AllObjects.buildInfraPage.deleteGivenProfile('automation-test');
        }
        catch (error) {
            console.log('cant delete the profile');
            failureCount++;
        }
        try {
            await AllObjects.buildInfraPage.clickOnEditIconOfAProfile('global');
            await AllObjects.buildInfraPage.turnOnOffK8sProvider(false);
            await AllObjects.buildInfraPage.removeCmCsOfAProfile('cm-test', 'ConfigMaps');
        }
        catch (error) {
            console.log('cant delete the profile');
            failureCount++;
        }
    }

    try {
        await apiutils.deleteAllNodesOfApplication(appname, await apiutils.login(process.env.PASSWORD as string));
    }
    catch (error) {
        console.error('cant delete the application' + error);
        failureCount++;
    }

    expect(failureCount).toBe(0);
})