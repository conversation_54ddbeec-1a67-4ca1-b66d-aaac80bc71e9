import { test } from '../../../utilities/Fixtures';
import { isFirstRun } from '../../../utilities/Third Party Clients/isFirstRun';
import { ScopedVariablePage } from '../../../Pages/ApplicationManagement/Configurations/ScopedVariablePage';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { dataObjectForDevtronAppDtCmTestCase, scopeVarTestCaseConstants, verifyScopeVariableValueForSuperAndNonSuperAdmin, scopeVariableObjectCreationForOss } from '../../../utilities/DataObjectss.ts/ScopeVariableDataObject';


/**
 * Variable Declarations and test setup
 */
let scopeVarConstants = scopeVarTestCaseConstants();
test.describe.configure({ mode: 'default' });
test.use({ storageState: './LoginAuth.json' });


/**
 * Setting up the scoped variable configuration before running the tests.
 * we will upload the yaml configuration for scoped variables
 * and then we will replace it with another yaml configuration
 */
test.beforeAll('uploading/replacing the scoped variable configuration', async ({ browser }) => {
  test.setTimeout(4 * 1000 * 60);
  if (!await isFirstRun()) return;
  let page = await browser.newPage();
  let scopeVariablePage = new ScopedVariablePage(page);
  await scopeVariablePage.navigateToScopedVariablePage();
  await scopeVariablePage.uploadScopeVariableYamlConfiguration(scopeVarConstants.scopeVariableConfigFilePath);
  await scopeVariablePage.reviewAndSaveChangesOfScopedVariableConfiguration();
  await scopeVariablePage.editScopeVariableConfigurationAndReplaceItWithAnotherYaml(scopeVarConstants.updatedScopeVariableConfiguration!);
  await scopeVariablePage.reviewAndSaveChangesOfScopedVariableConfiguration(scopeVarConstants.objectForReviewTheChangesInYaml);
  await page.close();
})

/**
 * This test case will verify the following:
 * 1. Download template for both saved and default template
 * 2. Sensitive variables
 */
test('verify downloadTemplate/sensitiveVariables_oss', { tag: '@globalConfigurations' }, async ({ AllObjects }) => {
  await AllObjects.scopedVariablePage.navigateToScopedVariablePage();
  await AllObjects.scopedVariablePage.verifyTemplateDownload(true, scopeVarConstants.valueToVerifyInDownloadedDefaultTemplate);
  await AllObjects.scopedVariablePage.verifyTemplateDownload(false, scopeVarConstants.valueToVerifyInDownloadedLastSavedTemplate);
  await AllObjects.scopedVariablePage.verifyValueOfVariableIsSensitiveOrNot(scopeVarConstants.objectForCheckingSensitiveVariable);
});



/**
 * 1. Devtron app dt/cm/cs/deploymentHistoryVerification
 * 2. Compare values verification
 * 3: config diff while deploying
 */
test.describe('scope variable test for dt cm cs compare values', { tag: '@globalConfigurations' }, async () => {
  test.setTimeout(12 * 1000 * 60);
  test.use({ storageState: './LoginAuth.json', devtronApps: scopeVarConstants.devtronAppName, triggerCI: [true], devtronEnvs: scopeVarConstants.devtronEnvNames });
  test.beforeEach('setting up api utils', async ({ request }) => {
    scopeVarConstants.apiUtils = new ApiUtils(request);
    scopeVarConstants.token = await scopeVarConstants.apiUtils.login(process.env.PASSWORD!);

  })

  // verifying the scope variable in dt cm cs and compare values
  test('devtron app dt/cm/cs/deploymentHistoryVerification_oss', async ({ devtronAppCreation, page, AllObjects, instantiatePomObjectsForNonSuperAdminPage }) => {
    let scopeVarData = process.env.clusterType == "oss" ? scopeVariableObjectCreationForOss('cmtest', 'sectest', AllObjects, instantiatePomObjectsForNonSuperAdminPage.objects) : dataObjectForDevtronAppDtCmTestCase('cmtest', 'sectest', AllObjects, instantiatePomObjectsForNonSuperAdminPage.objects);
    let appId = await scopeVarConstants.apiUtils.getAppIdFromAppName(scopeVarConstants.devtronAppName[0], scopeVarConstants.token);
    await scopeVarConstants.apiUtils.addConfigOrSecretInEnv([{ appId: appId, token: scopeVarConstants.token, key: 'GracePeriod', value: 'testcm', name: 'cmtest', isSecret: false }, { appId: appId, token: scopeVarConstants.token, key: 'GracePeriod', value: 'testsec', name: 'sectest', isSecret: true }]);

    await test.step('verifying the scope variable in dt cm cs', async () => {
      for (let object of scopeVarData.dtcmcsVerificationObject) {
        await page.bringToFront();
        await AllObjects.baseDeploymentTemplatePage.editAndSaveFieldsForDtCmCs({ resType: object.resourceTypeConfiguration.rType, resName: object.resourceTypeConfiguration.rName, fieldsToEdit: object.fieldsToEdit, valuesToEdit: object.valuesToEdit, stage: object.stage, jobsPage: AllObjects.jobsPage, mergeStrat: object.strat as any })
        await verifyScopeVariableValueForSuperAndNonSuperAdmin(object.scopeVarVerificationData.dtcmcsVerificationData.userLevelConfig, object.resourceTypeConfiguration.rType, object.scopeVarVerificationData.dtcmcsVerificationData.fields, page, instantiatePomObjectsForNonSuperAdminPage.nonSuperAdminPage);
        await AllObjects.baseDeploymentTemplatePage.verifyScopeVariablesValueAndVisibilityInScopeVarComponent(object.scopeVarVerificationData.tooltipVerificationData);
      }
    })

    await test.step('verifying the scope variable in compare values', async () => {
      for (let comparisonObject of scopeVarData.compareValueOrManifestObject) {
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(comparisonObject.stage);
        await AllObjects.baseDeploymentTemplatePage.clickOnCompareWithConfigOrManifestAndSelectComparisonDropdowns(comparisonObject.comparisonRelatedData);
        await AllObjects.baseDeploymentTemplatePage.turnOnOffScopeVariableToggle(true);
        await AllObjects.baseDeploymentTemplatePage.verifyTheResultOfCompareWithConfigOrManifest(comparisonObject);
        await AllObjects.baseDeploymentTemplatePage.closeCompareConfigOrManifestSection();
      }
    })

    await test.step('verifying the scope variable in config diff while deploying', async () => {
      for (let i = 0; i < scopeVarData.compareValueOrManifestObject.length; i++) {
        await AllObjects.workflowPage.clickOnSelectMaterialOrSelectImage(false, 0, i);
        await AllObjects.rollback.checkConfigDiffAndReview();
        await AllObjects.baseDeploymentTemplatePage.turnOnOffScopeVariableToggle(true);
        await AllObjects.baseDeploymentTemplatePage.checkDiffInCompareAndApproveSection(scopeVarData.compareValueOrManifestObject[i].verificationRelatedData, scopeVarData.compareValueOrManifestObject[i].hasDifference, scopeVarData.compareValueOrManifestObject[i].categoryToCheck);
        await AllObjects.workflowPage.cdTriggerDeploymentButton.click();
      }
    })

    await test.step('verifying the scope variable in deployment history', async () => {
      for (let i = 0; i < scopeVarData.compareValueOrManifestForDeploymentHistory.length; i++) {
        await AllObjects.deploymentHistory.clickOnConfigurationAndOpenResources(scopeVarData.compareValueOrManifestForDeploymentHistory[i].stage);
        await AllObjects.baseDeploymentTemplatePage.turnOnOffScopeVariableToggle(true);
        await AllObjects.baseDeploymentTemplatePage.checkDiffInCompareAndApproveSection(scopeVarData.compareValueOrManifestForDeploymentHistory[i].verificationRelatedData, scopeVarData.compareValueOrManifestForDeploymentHistory[i].hasDifference, scopeVarData.compareValueOrManifestForDeploymentHistory[i].categoryToCheck);
        await AllObjects.deploymentHistory.closeComparisonWindow();
      }
    })
  })
})


/**
 * This test case will verify the scope variable in jobs
 * this will verify the value in logs and also will check value in scope 
 * var component
 */
test.describe('scope variable test in jobs', { tag: '@globalConfigurations' }, async () => {
  test.use({ storageState: './LoginAuth.json', jobRelatedData: { customScript: scopeVarConstants.customScript } });
  test('scope variable in jobs_oss', async ({ AllObjects, jobCreation }) => {
    scopeVarConstants.jobName = jobCreation.jobsNames[0];
    await AllObjects.jobsPage.clickOnJobNode();
    await AllObjects.prePostCiCd.prebuildButton.click();
    await AllObjects.prePostCiCd.addInputVariables(scopeVarConstants.addInputVariablesData as any);
    await AllObjects.appConfigurationPage.updatePipelineButton.click();
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink();
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(scopeVarConstants.valuesToCheckInLogsOfJob);
  })
})



/**
 * This test case will verify the scope variable in pre-cd and ci
 * in pre-cd we will check cd level value should get resolved
 */
test.describe('scope variable in pre-cd and ci', { tag: '@globalConfigurations' }, async () => {
  test.use({ storageState: './LoginAuth.json', devtronApps: [scopeVarConstants.devtronAppNameForPreCd], devtronEnvs: ['automation'] });
  test('scope variable in pre-cd_oss', async ({ devtronAppCreation, AllObjects }) => {
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'automation');
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.prePostCiCd.addInputVariables(scopeVarConstants.addInputVariablesData as any);
    await AllObjects.jobsPage.executeCustomScript('pre-cd-task', scopeVarConstants.customScript);
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink(0, 1);
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(scopeVarConstants.valuesToCheckInPreCd);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.prePostCiCd.addInputVariables(scopeVarConstants.addInputVariablesData as any);
    await AllObjects.jobsPage.executeCustomScript('pre-ci-task', scopeVarConstants.customScript);
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.goToBuildHistoryPageViaClickOnDetailsLink(0, 0);
    await AllObjects.buildHistoryPage.verifyBuildLogsAndStatus(scopeVarConstants.valuesToCheckInLogsOfCi);
  })
})



/**
 * this will delete the data for all the tests
 * jobs and devtron apps
 */
test.afterEach('deleting the app', async ({ AllObjects, request }, testInfo) => {
  scopeVarConstants.apiUtils = new ApiUtils(request);
  scopeVarConstants.token = await scopeVarConstants.apiUtils.login(process.env.PASSWORD!);
  let appNameToDelete: string = testInfo.title == "devtron app dt/cm/cs/deploymentHistoryVerification_oss" ? scopeVarConstants.devtronAppName[0] : scopeVarConstants.devtronAppNameForPreCd;
  if (testInfo.title == "scope variable in pre-cd_oss" || testInfo.title == "devtron app dt/cm/cs/deploymentHistoryVerification_oss") {
    await scopeVarConstants.apiUtils.deleteAllNodesOfApplication(appNameToDelete, scopeVarConstants.token);
  }
  if (testInfo.title == "scope variable in jobs") {
    await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication("job", false);
  }

})

