import { test } from "../../../utilities/Fixtures";
import { DataSet } from "../../../utilities/DataObjectss.ts/multipleTestDataObject";
import { BaseTest } from "../../../utilities/BaseTest";

let devtronApp: string[] = [BaseTest.generateRandomStringWithCharsOnly(4)];
test.use({ storageState: './LoginAuth.json', devtronApps: devtronApp, triggerCI: [false] });
test.skip(process.env.clusterType == "eaoss");
if (process.env.isStaging == "true") {
    test.skip();
}
test.beforeEach('prepairing data', async ({ }, testInfo) => {
    const data = DataSet.ResourceCatalog.reourceCatalogData({ helmAppUrl: 'helmAppCreation.helmAppUrl', devtronAppUrl: 'devtronAppCreation.applicationUrl[0]', jobUrl: 'jobCreation.jobUrl', clusterUrl: process.env.BASE_SERVER_URL + '/resource-browser/1/all/node/k8sEmptyGroup' });
    (testInfo as any).data = data;
    // (testInfo as any).helmApp = helmAppCreation.helmAppName;
    // (testInfo as any).devtronAppName = devtronApp[0];
    // (testInfo as any).jobName = jobCreation.jobsNames[0];
})
test('ResourceCatalog_eaMode', { tag: '@globalConfigurations' }, async ({ AllObjects, page }, testInfo) => {
    test.setTimeout(25 * 1000 * 60);
    for (const key of (testInfo as any).data) {
        await AllObjects.catalogFrameWorkPage.navigateToCatalogFrameworkPage();
        console.log('printing the kind' + key.editingResourceCatalog.resourceKind);
        console.log(await AllObjects.catalogFrameWorkPage.checkAvailableCatalogs(key.editingResourceCatalog.resourceKind));
        if (await AllObjects.catalogFrameWorkPage.checkAvailableCatalogs(key.editingResourceCatalog.resourceKind)) {
            await AllObjects.catalogFrameWorkPage.editAnyResourceCatalog(key.editingResourceCatalog);
            await AllObjects.catalogFrameWorkPage.reviewAndSaveChanges(key.reviewChanges);
            await page.goto(key.navigationUrl);
            await AllObjects.overViewPage.clickOnOverviewTab();
            await AllObjects.overViewPage.fillResourceCatalogDetails(key.fillDataInForms);
            await AllObjects.overViewPage.verifyFieldsAfterSaving(key.verificationAfterSavingFields);
        }
    }
})

// test.afterEach('deleting data', async ({ page, AllObjects }, testInfo) => {
//     test.setTimeout(10 * 1000 * 60);
//     for (let i = 0; i < 3; i++) {
//         let isHelm: boolean = i == 0 ? true : false
//         let appType: string = i == 2 ? "job" : "app"
//         let name: string = i == 0 ? (testInfo as any).helmApp : i == 1 ? (testInfo as any).devtronAppName : (testInfo as any).jobName
//         await page.goto(process.env.BASE_SERVER_URL!);
//         await AllObjects.createAppPage.searchAndSelectAppsFromList(name, isHelm, appType);
//         i == 0 ? await AllObjects.deployChartPage.deletingChartStoreApp() : await AllObjects.appConfigurationPage.deleteAllCiCdNodesAndApplication(appType);
//     }
// })
