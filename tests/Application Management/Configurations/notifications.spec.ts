

import { ApiUtils } from "../../../utilities/ApiUtils";
import { BaseTest } from "../../../utilities/BaseTest";
import { test } from "../../../utilities/Fixtures";
import GmailConfig, { verifyContentAndCountOfEmail, verifyTheCountOfEmails } from "../../../utilities/Third Party Clients/GmailNotifications/GmailConfig";
import { dataSetForSesNotificationtest, dataSetForSMTPNotificationtest } from "../../../utilities/DataObjectss.ts/NotificationsDataObjects";

/**
 * Variable Declarations
 */
let apiUtils: ApiUtils;
let token: string;
let sesNotifierName: string = "SES-" + BaseTest.generateRandomString(6);
let smtpNotifierName: string = "SMTP-" + BaseTest.generateRandomString(6);
let devtronAppNames: string[] = ['ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4)];
var slackConfigName: string = "slack-" + BaseTest.generateRandomString(6);
let envNameForCd: string = 'env11';
let recepientName: string;
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");



/**
 * setting up the notifier and user
 * for all the 3 test , notifier is being setup here , not configured
 * apiUtils object is being created here
 */
test.beforeEach('setting up the notifier and user ', async ({ AllObjects, request }, testInfo) => {
  apiUtils = new ApiUtils(request);
  token = await apiUtils.login(process.env.PASSWORD!);
  testInfo.title == "Testing notifications for cd and post cd with ses" ? await AllObjects.notificationsPage.addNewSESConfig(sesNotifierName) : testInfo.title == 'Verify Slack CI-CD Success and Failed Notification_oss' ? await AllObjects.notificationsPage.addNewSlackConfig(slackConfigName, process.env.SLACK_WEBHOOK_URL!) : await AllObjects.notificationsPage.addNewSMTPConfig(smtpNotifierName);
  recepientName = testInfo.title == "Verify Slack CI-CD Success and Failed Notification_oss" ? slackConfigName : '<EMAIL>';
  await apiUtils.getObjectDetailsOfUser(token, '<EMAIL>') != undefined ? '' : await apiUtils.addUser('<EMAIL>', token);
})


/**
 * This test case will test the functionality of SMTP notifier
 * It will configure the SMTP notifier and verify the email is recieved or not
 * The functions that are required like triggering the ci/cd, configuring the pre-post cd, etc. are written in the dataSetForSMTPNotificationtest function
 */
test.describe('Testing notifications for smtp_oss', { tag: '@globalConfigurations' }, () => {
  test.use({ storageState: './LoginAuth.json', triggerExternalCi: false, devtronApps: devtronAppNames, triggerCI: [false], externalCiWorkflowEnvName: envNameForCd });
  test('Testing notifications for smtp',{ tag: '@globalConfigurations' }, async ({ AllObjects, page, devtronAppCreation, createExternalCiWorkflow }) => {
    test.setTimeout(20 * 1000 * 60);
    let dataSetForSMTP = dataSetForSMTPNotificationtest(devtronAppNames[0], await apiUtils.getAppIdFromAppName(devtronAppNames[0], token), AllObjects, envNameForCd);
    await AllObjects.notificationsPage.configureNotification([{ filterName: 'cluster', filterValue: 'default_cluster' }, { filterName: 'environment', filterValue: envNameForCd }], smtpNotifierName, "SMTP", '<EMAIL>');
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await apiUtils.triggerExternalCi(token, createExternalCiWorkflow.nodeId[0]);
    for (let key of dataSetForSMTP) {
      for (let { functionToCall, args } of key.triggerAndConfigurationFuncntionsToCall) {
        await functionToCall(...args);
      }
      await AllObjects.workflowPage.verifyCiCdStatus(key.workflowNumber, key.nodeNumber, key.statusToCheck);
      await verifyTheCountOfEmails(key.subjectOfMailToSearch, key.mailCount);
    }
  })
})


/**
 * This test case will test the functionality of SES notifier
 * It will configure the SES notifier and verify the email is recieved or not
 * The functions that are required like triggering the ci/cd, configuring the pre-post cd, etc. are written in the dataSetForSesNotificationtest function
 */
test.describe('Testing notifications for cd and post cd with ses', { tag: '@globalConfigurations' }, async () => {
  test.use({ storageState: './LoginAuth.json', triggerExternalCi: false, devtronApps: devtronAppNames, triggerCI: [false] })
  test.skip('Testing notifications for cd and post cd with ses',{ tag: '@globalConfigurations' }, async ({ AllObjects, page, devtronAppCreation }) => {
    await AllObjects.notificationsPage.configureNotification([{ filterName: 'application', filterValue: devtronAppNames[0] }], sesNotifierName, "SES", '<EMAIL>');
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.gitopsConfigurationPage.ClickOnSaveButton();
    await AllObjects.appConfigurationPage.createWorkflows({ workflowType: 'linked-cd', config: { sourceEnv: 'automation', destEnv: 'devtron-demo' } });
    let dataSetForSes = dataSetForSesNotificationtest(devtronAppNames[0], await apiUtils.getAppIdFromAppName(devtronAppNames[0], token), AllObjects);
    for (let key of dataSetForSes) {
      for (let { functionToCall, args } of key.triggerAndConfigurationFuncntionsToCall) {
        await functionToCall(...args);
      }
      await AllObjects.workflowPage.verifyCiCdStatus(key.workflowNumber, key.nodeNumber, key.statusToCheck);
      await verifyTheCountOfEmails(key.subjectOfMailToSearch, key.mailCount);
    }
  })
})

/**
 * This test case will test the functionality of Slack notifier
 * It will configure the Slack notifier and verify the notification is recieved or not
 * 
 */
test.describe('Verify Slack CI-CD Success and Failed Notification_oss', { tag: '@globalConfigurations' }, () => {
  test.use({ storageState: './LoginAuth.json', triggerExternalCi: true, devtronApps: devtronAppNames, triggerCI: [false] });
  test('Verify Slack CI-CD Success and Failed Notification_oss',{ tag: '@globalConfigurations' }, async ({ AllObjects, page, request, devtronAppCreation }) => {
    test.setTimeout(25 * 1000 * 60);
    //Configure Slack
    await AllObjects.notificationsPage.configureNotification([{ filterName: 'application', filterValue: devtronAppNames[0] }], "NA", "slack", slackConfigName);
    //Configure Pre-Post CD
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.EnvNameForCD[0]);
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.jobsPage.executeCustomScript('Execute custom task-pre', [], false);
    await AllObjects.prePostCiCd.addPrePostTask('post', 'execute');
    await AllObjects.jobsPage.executeCustomScript('Execute custom task-post', [], true);
    //Trigger and Verify Deployment.
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.workflowPage.clickOnBuildAndDeployTab();
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, 'Succeeded');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Succeeded');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 2, 'Succeeded');
    await AllObjects.workflowPage.verifyCiCdStatus(0, 3, 'Succeeded');

    //Failing CI-In order to verify Slack Notification.
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, 'ci');
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.jobsPage.executeCustomScript('Execute custom task-fail', [`exit 1`], true);
    await AllObjects.workflowPage.triggerCiModule();
    await AllObjects.workflowPage.verifyCiCdStatus(0, 0, "Failed");

    //Adding Fail Condition in PRE-CD and Verifing Slack Notification.
    await page.goto(devtronAppCreation.applicationUrl[0]);
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(0, credentials.EnvNameForCD[0]);
    await AllObjects.prePostCiCd.addPrePostTask('pre', 'execute');
    await AllObjects.jobsPage.executeCustomScript('Execute custom task-fail', [`exit 1`], true);
    await AllObjects.workflowPage.goToBuildAndDeployTab();
    await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0);
    await AllObjects.workflowPage.verifyCiCdStatus(0, 1, 'Failed');

    //Waiting in order to receive all Notifications.
    await page.waitForTimeout(5000)
    //Call Slack Conversation History Api 
    const apiUtils = new ApiUtils(request);
    const responseBody = await apiUtils.fetchSlackNotification(process.env.SLACK_TOKEN!, process.env.SLACK_CHANNEL_ID!);
    //Creating Map to store Slack Notifications Status.
    const frequencyMap: Map<string, number> = new Map();
    //Store Slack Notification Count
    await AllObjects.notificationsPage.storeSlackNotificationCount(responseBody, frequencyMap, devtronAppNames[0]);
    const globalErrors: string[] = [];
    await AllObjects.notificationsPage.verifySlackNotificationCount(frequencyMap, credentials.notifiationStatusArraySlack, globalErrors);
    //Delete Notification.
    await AllObjects.notificationsPage.deleteNotification(slackConfigName);
    if (globalErrors.length > 0) {
      throw new Error(`Slack Notification count is invalid`);
    }
  })
})

/**
 * This test case will delete the notifier and the application
 */
test.afterEach('deletingNodes', async ({ page, AllObjects }, testInfo) => {
  try {
    await AllObjects.notificationsPage.deleteNotification(recepientName);
  }
  catch (error) {
    console.log('not able to delete the notification configuration setup');
  }

  try {
    await apiUtils.deleteAllNodesOfApplication(devtronAppNames[0], token);
  }
  catch (error) {
    console.log('not able to delete the app');
  }
  testInfo.title == "Verify Slack CI-CD Success and Failed Notification_oss" ? await AllObjects.notificationsPage.deleteSlackConfig(slackConfigName) : testInfo.title == "Testing notifications for cd and post cd with ses" ? await AllObjects.notificationsPage.deleteSESConfig(sesNotifierName) : null;

})
