{"name": "dashboard-automation", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"test": "tests"}, "dependencies": {"archiver": "^7.0.1", "authenticator": "^1.1.5", "axios": "^1.10.0", "cheerio": "^1.1.0", "clipboardy": "^2.3.0", "dotenv": "^16.4.5", "js-yaml": "^4.1.0", "jszip": "^3.10.1", "mailslurp-client": "^15.20.2", "node-ssh": "^13.2.1", "nodemailer": "^6.10.0", "octokit": "^3.2.1", "otpauth": "^9.3.6", "playwright": "^1.55.0", "undici-types": "^5.26.5", "yaml": "^2.8.0"}, "devDependencies": {"@playwright/test": "^1.55.0", "@types/js-yaml": "^4.0.9", "@types/node": "^20.16.6", "browserstack-node-sdk": "^1.34.35", "gmail-tester": "^1.3.8", "monocart-reporter": "^2.9.15", "typescript": "^5.8.3"}, "scripts": {"postinstall": "npm update browserstack-node-sdk"}, "repository": {"type": "git", "url": "git+https://github.com/devtron-labs/dashboard-automation.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/devtron-labs/dashboard-automation/issues"}, "homepage": "https://github.com/devtron-labs/dashboard-automation#readme"}