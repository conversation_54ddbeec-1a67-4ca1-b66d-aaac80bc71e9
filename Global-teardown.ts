// global-teardown.ts
import fs from 'fs';
import { BaseTest } from './utilities/BaseTest';
import { APIRequestContext, request } from '@playwright/test';
import { ApiUtils } from './utilities/ApiUtils';
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");

export default async function globalTeardown() {
    try {
        const tokenPath = 'utilities/GmailNotifications/token.json';
        const credentialsPath = 'utilities/GmailNotifications/credentials.json';

        if (fs.existsSync(tokenPath)) {
            fs.unlinkSync(tokenPath);
            console.log('Deleted token.json');
        }

        if (fs.existsSync(credentialsPath)) {
            fs.unlinkSync(credentialsPath);
            console.log('Deleted credentials.json');
        }
    } catch (err) {
        console.error('Error during global teardown:', err);
    }
    const apiRequestContext: APIRequestContext = await request.newContext();
    let apiUtils: ApiUtils = new ApiUtils(apiRequestContext);
    let token = await apiUtils.login(process.env.PASSWORD!);
    try {
        console.log('chart source that we are trying to delete is ' + credentials.ChartSource);
        await apiUtils.deleteAChartRep(token, credentials.ChartSource);
    }
    catch (error) {
        console.log('not able to delete the chart repository');
    }
}
