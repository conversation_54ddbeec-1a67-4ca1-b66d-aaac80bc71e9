import { test as baseTest } from '@playwright/test';
import { InfraProvisioningPage } from '../Pages/GlobalConfiguration/Clutsers and Environments/InfraProvisioningPage';

type InfraProvisioningPageFixtures = {
  infraProvisioningPage: InfraProvisioningPage;
  deleteCluster: (clusterName: string) => Promise<void>;
};

export const test = baseTest.extend<InfraProvisioningPageFixtures>({
  infraProvisioningPage: async ({ page }, use) => {
    const infraPage = new InfraProvisioningPage(page);
    await use(infraPage);
  },
  deleteCluster: async ({ infraProvisioningPage }, use) => {
    await use(async (clusterName: string) => {
      await infraProvisioningPage.deleteCluster(clusterName);
    });
  }
});
