import { test as base, expect } from '@playwright/test';
import { CreateAppPage } from '../Pages/ApplicationManagement/Applications/CreateAppPage';

// Define the type for your custom fixture
type CreateAppPageFixtures = {
  createAppPage: CreateAppPage;
};

// Extend base test with the CreateAppPage fixture
export const test = base.extend<CreateAppPageFixtures>({
  createAppPage: async ({ page }, use) => {
    const createAppPage = new CreateAppPage(page);
    await use(createAppPage);
  },
});

// Re-export expect for test files
export { expect };
