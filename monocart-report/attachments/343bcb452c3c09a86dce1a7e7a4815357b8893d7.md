# Page snapshot

```yaml
- main [ref=e4]:
  - navigation [ref=e5]:
    - complementary [ref=e6]:
      - link "QA devtron QA-ENT-0" [ref=e7] [cursor=pointer]:
        - /url: /dashboard/app
        - generic [ref=e8] [cursor=pointer]:
          - generic [ref=e9] [cursor=pointer]:
            - generic [ref=e11] [cursor=pointer]: QA
            - img [ref=e12] [cursor=pointer]
          - generic [ref=e15] [cursor=pointer]:
            - img "devtron" [ref=e16] [cursor=pointer]
            - generic [ref=e17] [cursor=pointer]: QA-ENT-0
      - link "Applications" [ref=e18] [cursor=pointer]:
        - /url: /dashboard/app
        - generic [ref=e20] [cursor=pointer]:
          - img [ref=e22] [cursor=pointer]
          - generic [ref=e25] [cursor=pointer]: Applications
      - link "Jobs" [ref=e26] [cursor=pointer]:
        - /url: /dashboard/job
        - generic [ref=e27] [cursor=pointer]:
          - img [ref=e29] [cursor=pointer]
          - generic [ref=e32] [cursor=pointer]: Jobs
      - link "Application Groups" [ref=e33] [cursor=pointer]:
        - /url: /dashboard/application-group
        - generic [ref=e34] [cursor=pointer]:
          - img [ref=e36] [cursor=pointer]
          - generic [ref=e39] [cursor=pointer]: Application Groups
      - link "Software Distribution Hub" [ref=e40] [cursor=pointer]:
        - /url: /dashboard/software-distribution-hub
        - generic [ref=e41] [cursor=pointer]:
          - img [ref=e43] [cursor=pointer]
          - generic [ref=e46] [cursor=pointer]: Software Distribution Hub
      - link "Resource Browser" [ref=e47] [cursor=pointer]:
        - /url: /dashboard/resource-browser
        - generic [ref=e48] [cursor=pointer]:
          - img [ref=e50] [cursor=pointer]
          - generic [ref=e53] [cursor=pointer]: Resource Browser
      - link "Resource Watcher" [ref=e54] [cursor=pointer]:
        - /url: /dashboard/resource-watcher
        - generic [ref=e55] [cursor=pointer]:
          - img [ref=e57] [cursor=pointer]
          - generic [ref=e63] [cursor=pointer]: Resource Watcher
      - link "Chart Store" [ref=e64] [cursor=pointer]:
        - /url: /dashboard/chart-store
        - generic [ref=e65] [cursor=pointer]:
          - img [ref=e67] [cursor=pointer]
          - generic [ref=e70] [cursor=pointer]: Chart Store
      - link "Security" [ref=e71] [cursor=pointer]:
        - /url: /dashboard/security
        - generic [ref=e72] [cursor=pointer]:
          - img [ref=e74] [cursor=pointer]
          - generic [ref=e77] [cursor=pointer]: Security
      - link "Bulk Edit" [ref=e78] [cursor=pointer]:
        - /url: /dashboard/bulk-edits
        - generic [ref=e79] [cursor=pointer]:
          - img [ref=e81] [cursor=pointer]
          - generic [ref=e84] [cursor=pointer]: Bulk Edit
      - link "Global Configurations" [ref=e85] [cursor=pointer]:
        - /url: /dashboard/global-config
        - generic [ref=e86] [cursor=pointer]:
          - img [ref=e88] [cursor=pointer]
          - generic [ref=e92] [cursor=pointer]: Global Configurations
  - generic [ref=e93]:
    - generic [ref=e94]:
      - generic [ref=e97]:
        - img [ref=e98]
        - paragraph [ref=e100]: Hello World this is for the testing
      - button "Close banner" [ref=e102] [cursor=pointer]:
        - img [ref=e104] [cursor=pointer]
    - generic [ref=e107]:
      - generic [ref=e108]:
        - heading "devtron apps / ui-automwwsm 9/9 Environments F Help a" [level=1] [ref=e109]:
          - generic [ref=e110]:
            - link "devtron apps" [ref=e111] [cursor=pointer]:
              - /url: /dashboard/app
              - generic [ref=e112] [cursor=pointer]: devtron apps
            - generic [ref=e113]: /
            - generic [ref=e118]:
              - log [ref=e120]
              - generic [ref=e121] [cursor=pointer]:
                - generic [ref=e124] [cursor=pointer]:
                  - generic [ref=e125] [cursor=pointer]: ui-automwwsm
                  - combobox [ref=e127]
                - img [ref=e130] [cursor=pointer]
            - generic [ref=e133]:
              - log [ref=e135]
              - generic [ref=e136]:
                - generic [ref=e137] [cursor=pointer]:
                  - generic [ref=e138] [cursor=pointer]:
                    - img [ref=e139] [cursor=pointer]
                    - generic [ref=e141] [cursor=pointer]: 9/9 Environments
                    - generic [ref=e142] [cursor=pointer]: F
                  - combobox [ref=e144]
                - img [ref=e147]
          - generic [ref=e149]:
            - button "Help" [ref=e151] [cursor=pointer]:
              - img [ref=e152] [cursor=pointer]
              - generic [ref=e154] [cursor=pointer]: Help
              - img [ref=e155] [cursor=pointer]
            - button "a" [ref=e159] [cursor=pointer]:
              - generic [ref=e160] [cursor=pointer]: a
              - img [ref=e161] [cursor=pointer]
        - tablist [ref=e164]:
          - listitem [ref=e165] [cursor=pointer]:
            - link "Overview" [ref=e166] [cursor=pointer]:
              - /url: /dashboard/app/616/overview
              - generic [ref=e168] [cursor=pointer]: Overview
          - listitem [ref=e169] [cursor=pointer]:
            - link "App Details" [ref=e170] [cursor=pointer]:
              - /url: /dashboard/app/616/details
              - generic [ref=e172] [cursor=pointer]: App Details
          - listitem [ref=e173] [cursor=pointer]:
            - link "Build & Deploy" [ref=e174] [cursor=pointer]:
              - /url: /dashboard/app/616/trigger
              - generic [ref=e176] [cursor=pointer]: Build & Deploy
          - listitem [ref=e177] [cursor=pointer]:
            - link "Build History" [ref=e178] [cursor=pointer]:
              - /url: /dashboard/app/616/ci-details
              - generic [ref=e180] [cursor=pointer]: Build History
          - listitem [ref=e181] [cursor=pointer]:
            - link "Deployment History" [ref=e182] [cursor=pointer]:
              - /url: /dashboard/app/616/cd-details
              - generic [ref=e184] [cursor=pointer]: Deployment History
          - listitem [ref=e185] [cursor=pointer]:
            - link "Deployment Metrics" [ref=e186] [cursor=pointer]:
              - /url: /dashboard/app/616/deployment-metrics
              - generic [ref=e188] [cursor=pointer]: Deployment Metrics
          - listitem [ref=e189] [cursor=pointer]:
            - link "Configurations" [active] [ref=e190] [cursor=pointer]:
              - /url: /dashboard/app/616/edit
              - generic [ref=e191] [cursor=pointer]:
                - img [ref=e192] [cursor=pointer]
                - generic [ref=e194] [cursor=pointer]: Configurations
      - generic [ref=e196]:
        - generic [ref=e197]:
          - generic [ref=e198]:
            - link "Git Repository" [ref=e199] [cursor=pointer]:
              - /url: /dashboard/app/616/edit/materials
              - generic [ref=e200] [cursor=pointer]: Git Repository
            - link "Build Configuration" [ref=e201] [cursor=pointer]:
              - /url: /dashboard/app/616/edit/docker-build-config
              - generic [ref=e202] [cursor=pointer]: Build Configuration
            - link "Base Configurations" [ref=e203] [cursor=pointer]:
              - /url: /dashboard/app/616/edit/base-config
              - generic [ref=e204] [cursor=pointer]: Base Configurations
            - link "GitOps Configuration" [ref=e205] [cursor=pointer]:
              - /url: /dashboard/app/616/edit/gitops-config
              - generic [ref=e206] [cursor=pointer]: GitOps Configuration
            - link "Workflow Editor" [ref=e207] [cursor=pointer]:
              - /url: /dashboard/app/616/edit/workflow
              - generic [ref=e208] [cursor=pointer]: Workflow Editor
            - link "External Links" [ref=e211] [cursor=pointer]:
              - /url: /dashboard/app/616/edit/external-links
              - generic [ref=e212] [cursor=pointer]: External Links
            - generic [ref=e213]:
              - generic [ref=e215]: Environment Overrides
              - generic [ref=e217]:
                - link "automation" [ref=e218] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/14
                  - generic [ref=e219] [cursor=pointer]: automation
                - link "devtron-demo" [ref=e220] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/1
                  - generic [ref=e221] [cursor=pointer]: devtron-demo
                - link "env1" [ref=e222] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/2
                  - generic [ref=e223] [cursor=pointer]: env1
                - link "env2" [ref=e224] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/3
                  - generic [ref=e225] [cursor=pointer]: env2
                - link "env3" [ref=e226] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/4
                  - generic [ref=e227] [cursor=pointer]: env3
                - link "env4" [ref=e228] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/5
                  - generic [ref=e229] [cursor=pointer]: env4
                - link "env5" [ref=e230] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/6
                  - generic [ref=e231] [cursor=pointer]: env5
                - link "env6" [ref=e232] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/7
                  - generic [ref=e233] [cursor=pointer]: env6
                - link "env7" [ref=e234] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/env-override/8
                  - generic [ref=e235] [cursor=pointer]: env7
          - button "Delete Application" [ref=e238] [cursor=pointer]:
            - generic [ref=e239] [cursor=pointer]: Delete Application
        - generic [ref=e241]:
          - generic [ref=e242]:
            - generic [ref=e243]:
              - heading "Workflow Editor" [level=1] [ref=e244]
              - button "Info Icon" [ref=e245] [cursor=pointer]:
                - img [ref=e246] [cursor=pointer]
            - button "New Workflow" [ref=e249] [cursor=pointer]:
              - img [ref=e251] [cursor=pointer]
              - generic [ref=e253] [cursor=pointer]: New Workflow
          - generic [ref=e254]:
            - generic [ref=e255]:
              - generic [ref=e256]: wf-616-wj85
              - generic [ref=e257]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e258] [cursor=pointer]
                - link [ref=e264] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1050/edit
            - img [ref=e267]:
              - generic [ref=e279]:
                - img [ref=e280]
                - generic [ref=e282]:
                  - generic [ref=e283]: /sample-html
                  - generic [ref=e285]:
                    - img [ref=e287]
                    - generic [ref=e289]:
                      - generic [ref=e290]: main
                      - img [ref=e291]
              - generic [ref=e299]:
                - img [ref=e300]
                - generic [ref=e302]:
                  - generic [ref=e303]: /sample-html
                  - generic [ref=e305]:
                    - img [ref=e307]
                    - generic [ref=e309]:
                      - generic [ref=e310]: main
                      - img [ref=e311]
              - link "Auto Build ci-616-ykho Add deployment pipeline Delete pipeline" [ref=e319] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1050/ci-pipeline/835
                - generic [ref=e320] [cursor=pointer]:
                  - generic [ref=e321] [cursor=pointer]: Auto
                  - generic [ref=e322] [cursor=pointer]:
                    - generic [ref=e323] [cursor=pointer]:
                      - generic [ref=e324] [cursor=pointer]: Build
                      - generic [ref=e325] [cursor=pointer]: ci-616-ykho
                    - img [ref=e327] [cursor=pointer]
                    - generic [ref=e337] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e340] [cursor=pointer]:
                        - img [ref=e342] [cursor=pointer]
                      - button "Delete pipeline" [ref=e345] [cursor=pointer]:
                        - img [ref=e347] [cursor=pointer]
              - link "Auto Pre-deploy, Deploy devtron-demo Add deployment pipeline Delete pipeline" [ref=e350] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1050/ci-pipeline/835/cd-pipeline/1207
                - generic [ref=e351] [cursor=pointer]:
                  - generic [ref=e352] [cursor=pointer]: Auto
                  - generic [ref=e353] [cursor=pointer]:
                    - generic [ref=e354] [cursor=pointer]:
                      - generic [ref=e355] [cursor=pointer]: Pre-deploy, Deploy
                      - generic [ref=e356] [cursor=pointer]: devtron-demo
                    - img [ref=e358] [cursor=pointer]
                    - generic [ref=e381] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e384] [cursor=pointer]:
                        - img [ref=e386] [cursor=pointer]
                      - button "Delete pipeline" [ref=e389] [cursor=pointer]:
                        - img [ref=e391] [cursor=pointer]
              - link "Auto Pre-deploy, Deploy, Post-deploy automation Add deployment pipeline Delete pipeline" [ref=e394] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1050/ci-pipeline/835/cd-pipeline/1208
                - generic [ref=e395] [cursor=pointer]:
                  - generic [ref=e396] [cursor=pointer]: Auto
                  - generic [ref=e397] [cursor=pointer]:
                    - generic [ref=e398] [cursor=pointer]:
                      - generic [ref=e399] [cursor=pointer]: Pre-deploy, Deploy, Post-deploy
                      - generic [ref=e400] [cursor=pointer]: automation
                    - img [ref=e402] [cursor=pointer]
                    - generic [ref=e425] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e428] [cursor=pointer]:
                        - img [ref=e430] [cursor=pointer]
                      - button "Delete pipeline" [ref=e433] [cursor=pointer]:
                        - img [ref=e435] [cursor=pointer]
          - generic [ref=e437]:
            - generic [ref=e438]:
              - generic [ref=e439]: wf-616-bd0z
              - generic [ref=e440]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e441] [cursor=pointer]
                - link [ref=e447] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1051/edit
            - img [ref=e450]:
              - generic [ref=e459]:
                - img [ref=e460]
                - generic [ref=e462]:
                  - generic [ref=e463]: /sample-html
                  - generic [ref=e465]:
                    - img [ref=e467]
                    - generic [ref=e469]:
                      - generic [ref=e470]: .*
                      - img [ref=e471]
              - generic [ref=e479]:
                - img [ref=e480]
                - generic [ref=e482]:
                  - generic [ref=e483]: /sample-html
                  - generic [ref=e485]:
                    - img [ref=e487]
                    - generic [ref=e489]:
                      - generic [ref=e490]: main
                      - img [ref=e491]
              - link "Auto Build ci-616-cr3f Add deployment pipeline Delete pipeline" [ref=e499] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1051/ci-pipeline/836
                - generic [ref=e500] [cursor=pointer]:
                  - generic [ref=e501] [cursor=pointer]: Auto
                  - generic [ref=e502] [cursor=pointer]:
                    - generic [ref=e503] [cursor=pointer]:
                      - generic [ref=e504] [cursor=pointer]: Build
                      - generic [ref=e505] [cursor=pointer]: ci-616-cr3f
                    - img [ref=e507] [cursor=pointer]
                    - generic [ref=e517] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e520] [cursor=pointer]:
                        - img [ref=e522] [cursor=pointer]
                      - button "Delete pipeline" [ref=e525] [cursor=pointer]:
                        - img [ref=e527] [cursor=pointer]
              - link "Auto Deploy env1 Add deployment pipeline Delete pipeline" [ref=e530] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1051/ci-pipeline/836/cd-pipeline/1209
                - generic [ref=e531] [cursor=pointer]:
                  - generic [ref=e532] [cursor=pointer]: Auto
                  - generic [ref=e533] [cursor=pointer]:
                    - generic [ref=e534] [cursor=pointer]:
                      - generic [ref=e535] [cursor=pointer]: Deploy
                      - generic [ref=e536] [cursor=pointer]: env1
                    - img [ref=e538] [cursor=pointer]
                    - generic [ref=e561] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e564] [cursor=pointer]:
                        - img [ref=e566] [cursor=pointer]
                      - button "Delete pipeline" [ref=e569] [cursor=pointer]:
                        - img [ref=e571] [cursor=pointer]
          - generic [ref=e573]:
            - generic [ref=e574]:
              - generic [ref=e575]: wf-616-6zzf
              - generic [ref=e576]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e577] [cursor=pointer]
                - link [ref=e583] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1052/edit
            - img [ref=e586]:
              - generic [ref=e595]:
                - img [ref=e596]
                - generic [ref=e598]:
                  - generic [ref=e599]: /sample-html
                  - generic [ref=e601]:
                    - img [ref=e603]
                    - generic [ref=e605]:
                      - generic [ref=e606]: Pull Request
                      - img [ref=e607]
              - generic [ref=e615] [cursor=pointer]:
                - img [ref=e616] [cursor=pointer]
                - generic [ref=e618] [cursor=pointer]:
                  - generic [ref=e619] [cursor=pointer]: /sample-html
                  - generic [ref=e622] [cursor=pointer]:
                    - generic [ref=e623] [cursor=pointer]: Not Configured
                    - img [ref=e624] [cursor=pointer]
              - link "Auto Build ci-616-sqpn Add deployment pipeline Delete pipeline" [ref=e632] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1052/ci-pipeline/837
                - generic [ref=e633] [cursor=pointer]:
                  - generic [ref=e634] [cursor=pointer]: Auto
                  - generic [ref=e635] [cursor=pointer]:
                    - generic [ref=e636] [cursor=pointer]:
                      - generic [ref=e637] [cursor=pointer]: Build
                      - generic [ref=e638] [cursor=pointer]: ci-616-sqpn
                    - img [ref=e640] [cursor=pointer]
                    - generic [ref=e650] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e653] [cursor=pointer]:
                        - img [ref=e655] [cursor=pointer]
                      - button "Delete pipeline" [ref=e658] [cursor=pointer]:
                        - img [ref=e660] [cursor=pointer]
              - link "Auto Deploy env2 Add deployment pipeline Delete pipeline" [ref=e663] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1052/ci-pipeline/837/cd-pipeline/1210
                - generic [ref=e664] [cursor=pointer]:
                  - generic [ref=e665] [cursor=pointer]: Auto
                  - generic [ref=e666] [cursor=pointer]:
                    - generic [ref=e667] [cursor=pointer]:
                      - generic [ref=e668] [cursor=pointer]: Deploy
                      - generic [ref=e669] [cursor=pointer]: env2
                    - img [ref=e671] [cursor=pointer]
                    - generic [ref=e694] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e697] [cursor=pointer]:
                        - img [ref=e699] [cursor=pointer]
                      - button "Delete pipeline" [ref=e702] [cursor=pointer]:
                        - img [ref=e704] [cursor=pointer]
          - generic [ref=e706]:
            - generic [ref=e707]:
              - generic [ref=e708]: wf-616-hfju
              - generic [ref=e709]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e710] [cursor=pointer]
                - link [ref=e716] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1053/edit
            - img [ref=e719]:
              - generic [ref=e728]:
                - img [ref=e729]
                - generic [ref=e731]:
                  - generic [ref=e732]: /sample-html
                  - generic [ref=e734]:
                    - img [ref=e736]
                    - generic [ref=e738]:
                      - generic [ref=e739]: Tag Creation
                      - img [ref=e740]
              - generic [ref=e748] [cursor=pointer]:
                - img [ref=e749] [cursor=pointer]
                - generic [ref=e751] [cursor=pointer]:
                  - generic [ref=e752] [cursor=pointer]: /sample-html
                  - generic [ref=e755] [cursor=pointer]:
                    - generic [ref=e756] [cursor=pointer]: Not Configured
                    - img [ref=e757] [cursor=pointer]
              - link "Auto Build ci-616-as9a Add deployment pipeline Delete pipeline" [ref=e765] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1053/ci-pipeline/838
                - generic [ref=e766] [cursor=pointer]:
                  - generic [ref=e767] [cursor=pointer]: Auto
                  - generic [ref=e768] [cursor=pointer]:
                    - generic [ref=e769] [cursor=pointer]:
                      - generic [ref=e770] [cursor=pointer]: Build
                      - generic [ref=e771] [cursor=pointer]: ci-616-as9a
                    - img [ref=e773] [cursor=pointer]
                    - generic [ref=e783] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e786] [cursor=pointer]:
                        - img [ref=e788] [cursor=pointer]
                      - button "Delete pipeline" [ref=e791] [cursor=pointer]:
                        - img [ref=e793] [cursor=pointer]
              - link "Auto Deploy env3 Add deployment pipeline Delete pipeline" [ref=e796] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1053/ci-pipeline/838/cd-pipeline/1211
                - generic [ref=e797] [cursor=pointer]:
                  - generic [ref=e798] [cursor=pointer]: Auto
                  - generic [ref=e799] [cursor=pointer]:
                    - generic [ref=e800] [cursor=pointer]:
                      - generic [ref=e801] [cursor=pointer]: Deploy
                      - generic [ref=e802] [cursor=pointer]: env3
                    - img [ref=e804] [cursor=pointer]
                    - generic [ref=e827] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e830] [cursor=pointer]:
                        - img [ref=e832] [cursor=pointer]
                      - button "Delete pipeline" [ref=e835] [cursor=pointer]:
                        - img [ref=e837] [cursor=pointer]
          - generic [ref=e839]:
            - generic [ref=e840]:
              - generic [ref=e841]: wf-616-ma89
              - generic [ref=e842]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e843] [cursor=pointer]
                - link [ref=e849] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1054/edit
            - img [ref=e852]:
              - generic [ref=e861]:
                - img [ref=e862]
                - generic [ref=e864]:
                  - generic [ref=e865]: /sample-html
                  - generic [ref=e867]:
                    - img [ref=e869]
                    - generic [ref=e871]:
                      - generic [ref=e872]: main
                      - img [ref=e873]
              - generic [ref=e881]:
                - img [ref=e882]
                - generic [ref=e884]:
                  - generic [ref=e885]: /sample-html
                  - generic [ref=e887]:
                    - img [ref=e889]
                    - generic [ref=e891]:
                      - generic [ref=e892]: main
                      - img [ref=e893]
              - 'link "Auto Build: Linked ci-616-bt3e Add deployment pipeline Delete pipeline" [ref=e901] [cursor=pointer]':
                - /url: /dashboard/app/616/edit/workflow/1054/linked-ci/839
                - generic [ref=e902] [cursor=pointer]:
                  - generic [ref=e903] [cursor=pointer]: Auto
                  - generic [ref=e904] [cursor=pointer]:
                    - generic [ref=e905] [cursor=pointer]:
                      - generic [ref=e906] [cursor=pointer]: "Build: Linked"
                      - generic [ref=e907] [cursor=pointer]: ci-616-bt3e
                    - img [ref=e908] [cursor=pointer]
                    - generic [ref=e914] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e917] [cursor=pointer]:
                        - img [ref=e919] [cursor=pointer]
                      - button "Delete pipeline" [ref=e922] [cursor=pointer]:
                        - img [ref=e924] [cursor=pointer]
              - link "Auto Deploy env4 Add deployment pipeline Delete pipeline" [ref=e927] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1054/ci-pipeline/839/cd-pipeline/1212
                - generic [ref=e928] [cursor=pointer]:
                  - generic [ref=e929] [cursor=pointer]: Auto
                  - generic [ref=e930] [cursor=pointer]:
                    - generic [ref=e931] [cursor=pointer]:
                      - generic [ref=e932] [cursor=pointer]: Deploy
                      - generic [ref=e933] [cursor=pointer]: env4
                    - img [ref=e935] [cursor=pointer]
                    - generic [ref=e958] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e961] [cursor=pointer]:
                        - img [ref=e963] [cursor=pointer]
                      - button "Delete pipeline" [ref=e966] [cursor=pointer]:
                        - img [ref=e968] [cursor=pointer]
          - generic [ref=e970]:
            - generic [ref=e971]:
              - generic [ref=e972]: wf-616-17b8
              - generic [ref=e973]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e974] [cursor=pointer]
                - link [ref=e980] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1055/edit
            - img [ref=e983]:
              - link "Webhook External source Add deployment pipeline" [ref=e987] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1055/webhook/239
                - generic [ref=e989] [cursor=pointer]:
                  - generic [ref=e990] [cursor=pointer]:
                    - generic [ref=e991] [cursor=pointer]: Webhook
                    - generic [ref=e992] [cursor=pointer]: External source
                  - img [ref=e993] [cursor=pointer]
                  - button "Add deployment pipeline" [ref=e1008] [cursor=pointer]:
                    - img [ref=e1010] [cursor=pointer]
              - link "Auto Deploy env5 Add deployment pipeline Delete pipeline" [ref=e1013] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1055/webhook/0/cd-pipeline/1213
                - generic [ref=e1014] [cursor=pointer]:
                  - generic [ref=e1015] [cursor=pointer]: Auto
                  - generic [ref=e1016] [cursor=pointer]:
                    - generic [ref=e1017] [cursor=pointer]:
                      - generic [ref=e1018] [cursor=pointer]: Deploy
                      - generic [ref=e1019] [cursor=pointer]: env5
                    - img [ref=e1021] [cursor=pointer]
                    - generic [ref=e1044] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e1047] [cursor=pointer]:
                        - img [ref=e1049] [cursor=pointer]
                      - button "Delete pipeline" [ref=e1052] [cursor=pointer]:
                        - img [ref=e1054] [cursor=pointer]
          - generic [ref=e1056]:
            - generic [ref=e1057]:
              - generic [ref=e1058]: wf-616-p96n
              - generic [ref=e1059]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e1060] [cursor=pointer]
                - link [ref=e1066] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1056/edit
            - img [ref=e1069]:
              - button "Sync with... env5 Add deployment pipeline Delete pipeline" [ref=e1073] [cursor=pointer]:
                - generic [ref=e1074] [cursor=pointer]:
                  - img [ref=e1075] [cursor=pointer]
                  - generic [ref=e1093] [cursor=pointer]:
                    - generic [ref=e1094] [cursor=pointer]: Sync with...
                    - paragraph [ref=e1095] [cursor=pointer]: env5
                - generic [ref=e1096] [cursor=pointer]:
                  - button "Add deployment pipeline" [ref=e1099] [cursor=pointer]:
                    - img [ref=e1101] [cursor=pointer]
                  - button "Delete pipeline" [ref=e1104] [cursor=pointer]:
                    - img [ref=e1106] [cursor=pointer]
              - link "Auto Deploy env6 Add deployment pipeline Delete pipeline" [ref=e1109] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1056/ci-pipeline/840/cd-pipeline/1214
                - generic [ref=e1110] [cursor=pointer]:
                  - generic [ref=e1111] [cursor=pointer]: Auto
                  - generic [ref=e1112] [cursor=pointer]:
                    - generic [ref=e1113] [cursor=pointer]:
                      - generic [ref=e1114] [cursor=pointer]: Deploy
                      - generic [ref=e1115] [cursor=pointer]: env6
                    - img [ref=e1117] [cursor=pointer]
                    - generic [ref=e1140] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e1143] [cursor=pointer]:
                        - img [ref=e1145] [cursor=pointer]
                      - button "Delete pipeline" [ref=e1148] [cursor=pointer]:
                        - img [ref=e1150] [cursor=pointer]
          - generic [ref=e1152]:
            - generic [ref=e1153]:
              - generic [ref=e1154]: wf-616-3pq4
              - generic [ref=e1155]:
                - img "6605A5D1-3E7E-49E0-A6B7-0384A91D2569" [ref=e1156] [cursor=pointer]
                - link [ref=e1162] [cursor=pointer]:
                  - /url: /dashboard/app/616/edit/workflow/1057/edit
            - img [ref=e1165]:
              - generic [ref=e1174]:
                - img [ref=e1175]
                - generic [ref=e1177]:
                  - generic [ref=e1178]: /sample-html
                  - generic [ref=e1180]:
                    - img [ref=e1182]
                    - generic [ref=e1184]:
                      - generic [ref=e1185]: main
                      - img [ref=e1186]
              - generic [ref=e1194]:
                - img [ref=e1195]
                - generic [ref=e1197]:
                  - generic [ref=e1198]: /sample-html
                  - generic [ref=e1200]:
                    - img [ref=e1202]
                    - generic [ref=e1204]:
                      - generic [ref=e1205]: main
                      - img [ref=e1206]
              - link "Manual Job ci-616-4hqq Add deployment pipeline Delete pipeline" [ref=e1214] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1057/ci-job/841
                - generic [ref=e1215] [cursor=pointer]:
                  - generic [ref=e1216] [cursor=pointer]: Manual
                  - generic [ref=e1217] [cursor=pointer]:
                    - generic [ref=e1218] [cursor=pointer]:
                      - generic [ref=e1219] [cursor=pointer]: Job
                      - generic [ref=e1220] [cursor=pointer]: ci-616-4hqq
                    - img [ref=e1222] [cursor=pointer]
                    - generic [ref=e1230] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e1233] [cursor=pointer]:
                        - img [ref=e1235] [cursor=pointer]
                      - button "Delete pipeline" [ref=e1238] [cursor=pointer]:
                        - img [ref=e1240] [cursor=pointer]
              - link "Manual Deploy env7 Add deployment pipeline Delete pipeline" [ref=e1243] [cursor=pointer]:
                - /url: /dashboard/app/616/edit/workflow/1057/ci-pipeline/841/cd-pipeline/1215
                - generic [ref=e1244] [cursor=pointer]:
                  - generic [ref=e1245] [cursor=pointer]: Manual
                  - generic [ref=e1246] [cursor=pointer]:
                    - generic [ref=e1247] [cursor=pointer]:
                      - generic [ref=e1248] [cursor=pointer]: Deploy
                      - generic [ref=e1249] [cursor=pointer]: env7
                    - img [ref=e1251] [cursor=pointer]
                    - generic [ref=e1274] [cursor=pointer]:
                      - button "Add deployment pipeline" [ref=e1277] [cursor=pointer]:
                        - img [ref=e1279] [cursor=pointer]
                      - button "Delete pipeline" [ref=e1282] [cursor=pointer]:
                        - img [ref=e1284] [cursor=pointer]
```