{"name": "playwright automation report", "logo": null, "date": 1756385825056, "dateH": "8/28/2025, 6:27:05 PM", "duration": 128368, "durationH": "2m 8s", "cwd": "/Users/<USER>/Desktop/ui-automation/dashboard-automation", "outputFile": "/Users/<USER>/Desktop/ui-automation/dashboard-automation/monocart-report/index.html", "outputDir": "/Users/<USER>/Desktop/ui-automation/dashboard-automation/monocart-report", "metadata": {"env": "enterprise", "url": "https://qa-ent-cluster-0.devtron.info/dashboard", "actualWorkers": 1}, "system": {"cpu": {"color": "#117DBB", "count": 8, "model": "Apple M1", "speed": 2400}, "mem": {"color": "#8B12AE", "total": 8589934592}, "arch": "arm64", "platform": "darwin", "release": "24.6.0", "type": "<PERSON>", "version": "Darwin Kernel Version 24.6.0: Mon Jul 14 11:30:34 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T8103", "uptime": 482557, "hostname": "apples-MacBook-Pro.local", "pid": 66145, "node": "22.18.0", "v8": "***********-node.27", "timestampStart": 1756385825056, "ticks": [{"cpu": {"percent": 45.36}, "mem": {"free": 152879104}, "timestamp": 1756385827057}, {"cpu": {"percent": 21.33}, "mem": {"free": 163430400}, "timestamp": 1756385829058}, {"cpu": {"percent": 23.08}, "mem": {"free": 156631040}, "timestamp": 1756385831061}, {"cpu": {"percent": 29.33}, "mem": {"free": 115851264}, "timestamp": 1756385833062}, {"cpu": {"percent": 34.5}, "mem": {"free": 146620416}, "timestamp": 1756385835065}, {"cpu": {"percent": 6.75}, "mem": {"free": 132186112}, "timestamp": 1756385837068}, {"cpu": {"percent": 11.15}, "mem": {"free": 123305984}, "timestamp": 1756385839071}, {"cpu": {"percent": 3.5}, "mem": {"free": 114622464}, "timestamp": 1756385841074}, {"cpu": {"percent": 6.98}, "mem": {"free": 118898688}, "timestamp": 1756385843077}, {"cpu": {"percent": 3.5}, "mem": {"free": 97337344}, "timestamp": 1756385845080}, {"cpu": {"percent": 3.74}, "mem": {"free": 114786304}, "timestamp": 1756385847084}, {"cpu": {"percent": 8.81}, "mem": {"free": 79626240}, "timestamp": 1756385849085}, {"cpu": {"percent": 2.75}, "mem": {"free": 77807616}, "timestamp": 1756385851088}, {"cpu": {"percent": 3.75}, "mem": {"free": 97304576}, "timestamp": 1756385853091}, {"cpu": {"percent": 3.76}, "mem": {"free": 126140416}, "timestamp": 1756385855093}, {"cpu": {"percent": 2.63}, "mem": {"free": 115884032}, "timestamp": 1756385857097}, {"cpu": {"percent": 2.5}, "mem": {"free": 106954752}, "timestamp": 1756385859100}, {"cpu": {"percent": 2.63}, "mem": {"free": 78970880}, "timestamp": 1756385861103}, {"cpu": {"percent": 40.43}, "mem": {"free": 95698944}, "timestamp": 1756385863106}, {"cpu": {"percent": 38.41}, "mem": {"free": 50446336}, "timestamp": 1756385865109}, {"cpu": {"percent": 20.43}, "mem": {"free": 109969408}, "timestamp": 1756385867112}, {"cpu": {"percent": 17.38}, "mem": {"free": 108576768}, "timestamp": 1756385869116}, {"cpu": {"percent": 21.8}, "mem": {"free": 126484480}, "timestamp": 1756385871119}, {"cpu": {"percent": 21.26}, "mem": {"free": 104579072}, "timestamp": 1756385873120}, {"cpu": {"percent": 20.48}, "mem": {"free": 97271808}, "timestamp": 1756385875123}, {"cpu": {"percent": 8.39}, "mem": {"free": 79855616}, "timestamp": 1756385877126}, {"cpu": {"percent": 6.52}, "mem": {"free": 90193920}, "timestamp": 1756385879129}, {"cpu": {"percent": 8.26}, "mem": {"free": 71172096}, "timestamp": 1756385881131}, {"cpu": {"percent": 17.88}, "mem": {"free": 68780032}, "timestamp": 1756385883133}, {"cpu": {"percent": 8.75}, "mem": {"free": 66633728}, "timestamp": 1756385885135}, {"cpu": {"percent": 18.11}, "mem": {"free": 62668800}, "timestamp": 1756385887138}, {"cpu": {"percent": 14.93}, "mem": {"free": 68419584}, "timestamp": 1756385889142}, {"cpu": {"percent": 16.25}, "mem": {"free": 112361472}, "timestamp": 1756385891144}, {"cpu": {"percent": 16.69}, "mem": {"free": 139476992}, "timestamp": 1756385893146}, {"cpu": {"percent": 38}, "mem": {"free": 90095616}, "timestamp": 1756385895153}, {"cpu": {"percent": 4.61}, "mem": {"free": 74842112}, "timestamp": 1756385897156}, {"cpu": {"percent": 18.97}, "mem": {"free": 77037568}, "timestamp": 1756385899159}, {"cpu": {"percent": 6.37}, "mem": {"free": 79069184}, "timestamp": 1756385901162}, {"cpu": {"percent": 4.11}, "mem": {"free": 85475328}, "timestamp": 1756385903165}, {"cpu": {"percent": 6.11}, "mem": {"free": 83034112}, "timestamp": 1756385905167}, {"cpu": {"percent": 5.13}, "mem": {"free": 67158016}, "timestamp": 1756385907170}, {"cpu": {"percent": 12.27}, "mem": {"free": 104267776}, "timestamp": 1756385909172}, {"cpu": {"percent": 35.48}, "mem": {"free": 120225792}, "timestamp": 1756385911174}, {"cpu": {"percent": 15.74}, "mem": {"free": 149995520}, "timestamp": 1756385913176}, {"cpu": {"percent": 18.81}, "mem": {"free": 93716480}, "timestamp": 1756385915178}, {"cpu": {"percent": 12.16}, "mem": {"free": 78135296}, "timestamp": 1756385917181}, {"cpu": {"percent": 13.64}, "mem": {"free": 85409792}, "timestamp": 1756385919183}, {"cpu": {"percent": 14.52}, "mem": {"free": 89194496}, "timestamp": 1756385921186}, {"cpu": {"percent": 10.24}, "mem": {"free": 198819840}, "timestamp": 1756385923189}, {"cpu": {"percent": 20.05}, "mem": {"free": 115621888}, "timestamp": 1756385925191}, {"cpu": {"percent": 22.21}, "mem": {"free": 102924288}, "timestamp": 1756385927195}, {"cpu": {"percent": 15.6}, "mem": {"free": 93569024}, "timestamp": 1756385929198}, {"cpu": {"percent": 13.58}, "mem": {"free": 105005056}, "timestamp": 1756385931199}, {"cpu": {"percent": 17.38}, "mem": {"free": 65667072}, "timestamp": 1756385933202}, {"cpu": {"percent": 13.14}, "mem": {"free": 69926912}, "timestamp": 1756385935205}, {"cpu": {"percent": 12.91}, "mem": {"free": 64798720}, "timestamp": 1756385937207}, {"cpu": {"percent": 18.9}, "mem": {"free": 63881216}, "timestamp": 1756385939209}, {"cpu": {"percent": 20.38}, "mem": {"free": 66732032}, "timestamp": 1756385941213}, {"cpu": {"percent": 21.8}, "mem": {"free": 99221504}, "timestamp": 1756385943214}, {"cpu": {"percent": 16.77}, "mem": {"free": 89686016}, "timestamp": 1756385945216}, {"cpu": {"percent": 18.82}, "mem": {"free": 97288192}, "timestamp": 1756385947219}, {"cpu": {"percent": 15.88}, "mem": {"free": 101515264}, "timestamp": 1756385949221}, {"cpu": {"percent": 48.86}, "mem": {"free": 685260800}, "timestamp": 1756385951224}, {"cpu": {"percent": 46.88}, "mem": {"free": 179404800}, "timestamp": 1756385953226}, {"cpu": {"percent": 44.76}, "mem": {"free": 216711168}, "timestamp": 1756385953419}], "workers": 4, "jobs": [{"caseId": "03d9d9cd127a548dfb86", "parallelIndex": 0, "workerIndex": 0, "timestamp": 1756385834075, "duration": 27508}, {"caseId": "fbc0a8eafa5c8da91afb", "parallelIndex": 0, "workerIndex": 1, "timestamp": 1756385861993, "duration": 47891}, {"caseId": "0a3594ab5f2c3dc96284", "parallelIndex": 0, "workerIndex": 1, "timestamp": 1756385909887, "duration": 42049}, {"caseId": "80b24f1e1e9a9cbe313c", "parallelIndex": -1, "workerIndex": -1, "timestamp": 1756385951939, "duration": 2}], "cwd": "/Users/<USER>/Desktop/ui-automation/dashboard-automation", "configFile": "playwright.config.ts", "playwright": "1.55.0", "monocart": "2.9.15", "testDir": "tests", "outputFile": "monocart-report/index.html", "outputDir": "monocart-report", "timestampEnd": 1756385953424}, "artifacts": [], "trends": [], "columns": [{"id": "caseType", "name": "", "width": 36, "sortable": false, "align": "center", "formatter": "iconCaseType", "detailed": false}, {"id": "title", "name": "Title", "searchable": true, "width": 350, "maxWidth": 1230, "detailed": false}, {"id": "type", "name": "Type", "width": 50, "sortable": false, "align": "center", "formatter": "iconType", "detailed": false}, {"id": "duration", "name": "Duration", "align": "right", "sortAsc": false, "formatter": "duration", "detailed": false}, {"id": "errors", "name": "Errors", "width": 60, "align": "center", "comparer": "errors", "formatter": "errors", "detailed": false}, {"id": "logs", "name": "Logs", "width": 60, "align": "center", "comparer": "logs", "formatter": "logs", "detailed": false}, {"id": "annotations", "name": "Annotations", "width": 100, "markdown": true, "searchable": true, "comparer": "annotations", "formatter": "annotations", "detailed": false}, {"id": "attachments", "name": "Attachments", "width": 100, "align": "center", "formatter": "attachments", "detailed": false}, {"id": "status", "name": "Status", "align": "center", "detailed": false}, {"id": "expectedStatus", "name": "Expected", "align": "center", "detailed": false}, {"id": "outcome", "name": "Outcome", "align": "center", "width": 85, "detailed": false}, {"id": "retry", "name": "Retry", "align": "center", "width": 50, "detailed": false}, {"id": "location", "name": "Location", "classMap": "mcr-location", "width": 200, "maxWidth": 1981, "detailed": false}], "rows": [{"id": "80437a44a661d1411742", "title": "setup", "type": "suite", "suiteType": "project", "caseNum": 1, "subs": [{"id": "956200db76d81c8e66bf", "title": "PreRequisite.spec.ts", "type": "suite", "suiteType": "file", "caseNum": 1, "subs": [{"id": "03d9d9cd127a548dfb86", "title": "pre-requisite data creation", "type": "case", "caseType": "passed", "ok": true, "outcome": "expected", "expectedStatus": "passed", "location": "tests/PreRequisite.spec.ts:32:5", "logs": ["playwright chart repo added successfully! plfnf\n", "going to save gitops provider\n"], "timestamps": [1756385834075, 1756385861583], "duration": 27508, "tags": [], "timeout": 300000, "retry": 0, "status": "passed", "stepNum": 68, "stepFailed": 0, "stepSubs": true, "subs": [{"id": "m766aulsvudwoh3a4f1k", "title": "Before Hooks", "type": "step", "stepType": "hook", "duration": 23, "location": "", "subs": [{"id": "r1mef8ekk2ars55zbbn0", "title": "Fixture \"request\"", "type": "step", "stepType": "fixture", "duration": 17, "location": "", "subs": [{"id": "bn2tjjugdgsgjhokvjzs", "title": "Create request context", "type": "step", "stepType": "pw:api", "duration": 13, "location": ""}]}]}, {"id": "rzb5jw6lko18u64zes1p", "title": "POST \"/orchestrator/api/v1/session\"", "type": "step", "stepType": "pw:api", "duration": 133, "location": "utilities/ApiUtils.ts:31:47"}, {"id": "x6roxmiqo1q3nef9vhy5", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 1, "location": "utilities/ApiUtils.ts:33:39"}, {"id": "gu098pqfnpkggc9xyokl", "title": "GET \"/orchestrator/chart-repo/list\"", "type": "step", "stepType": "pw:api", "duration": 16, "location": "utilities/ApiUtils.ts:1663:39"}, {"id": "arazodaf4mh9xr0e4s9u", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 1, "location": "utilities/ApiUtils.ts:1668:31"}, {"id": "ddz6c9ixvp4tm0skkpzj", "title": "POST \"/orchestrator/chart-repo/create\"", "type": "step", "stepType": "pw:api", "duration": 7071, "location": "utilities/ApiUtils.ts:1618:43"}, {"id": "qhl484y01i225503hwhs", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1631:35"}, {"id": "dtjj2arcogitv4erqs1p", "title": "GET \"/orchestrator/chart-repo/list\"", "type": "step", "stepType": "pw:api", "duration": 19, "location": "utilities/ApiUtils.ts:1663:39"}, {"id": "mye45eektw94twxkuqoi", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1668:31"}, {"id": "k9hpmnbe4sl767p2cjjp", "title": "POST \"/orchestrator/app-store/chart-provider/sync-chart\"", "type": "step", "stepType": "pw:api", "duration": 7040, "location": "utilities/ApiUtils.ts:1642:43"}, {"id": "fb2b584eol4w3a4gw8hj", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1652:35"}, {"id": "v58yuvgd05w8e8mhwd24", "title": "creating cm and secret", "type": "step", "stepType": "test.step", "duration": 99, "location": "tests/PreRequisite.spec.ts:46:14", "subs": [{"id": "fjt44eq5zlosinxvokgy", "title": "POST \"/orchestrator/k8s/resources/apply\"", "type": "step", "stepType": "pw:api", "duration": 26, "location": "utilities/ApiUtils.ts:56:43"}, {"id": "5kl93i54k9nt4qx7b1q9", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:61:35"}, {"id": "de43qhv7kg9lx4g9hxz2", "title": "POST \"/orchestrator/k8s/resources/apply\"", "type": "step", "stepType": "pw:api", "duration": 20, "location": "utilities/ApiUtils.ts:56:43"}, {"id": "uq646t3u1k81koaaf48u", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:61:35"}, {"id": "choognxrhkblfyxc4ggd", "title": "POST \"/orchestrator/k8s/resources/apply\"", "type": "step", "stepType": "pw:api", "duration": 18, "location": "utilities/ApiUtils.ts:56:43"}, {"id": "bdgz0dmi44i7ft3mzr67", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:61:35"}, {"id": "5xi4s8952x1n8af2ptxv", "title": "POST \"/orchestrator/k8s/resources/apply\"", "type": "step", "stepType": "pw:api", "duration": 21, "location": "utilities/ApiUtils.ts:56:43"}, {"id": "icsh8wr6wc8m8acmogv3", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:61:35"}]}, {"id": "3ge3fmzx8i1vepormkzz", "title": "creating registry", "type": "step", "stepType": "test.step", "duration": 47, "location": "tests/PreRequisite.spec.ts:58:14", "subs": [{"id": "2hzkbxtvibhcmvmwz9dx", "title": "GET \"/orchestrator/docker/registry\"", "type": "step", "stepType": "pw:api", "duration": 41, "location": "utilities/ApiUtils.ts:77:43"}, {"id": "nawxv3c7x3lsxq58p4ym", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:81:35"}]}, {"id": "oz08p7eu6awmxzrka45v", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 20, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "f4wn86lda2gfnc3p1kbh", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "6s3ne932sk3bo6e50sms", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 14, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "9qia677a3wk5p8375gep", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "9q0i1ch4kn36x2qiynxr", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 15, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "wv8mymbc9ehtnk7ngvz8", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "1jlbd3mn8af6x3nb65a4", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 14, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "16s41zt2ouua80w7amwh", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "0pn514a8mf5kucjk7qlz", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 18, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "53olndwln16v5j9vp5y0", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "78g5s8z5b6jxst2p4pff", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 13, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "3ms5kyl2r07t2ciopz8d", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "612kr3vdu6eocitsqsj6", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 16, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "vn6o7ueghtenhaljud50", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "j4kva5kj0uroa0cwxwrm", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 14, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "kmv5l1j3brqwwoszl7rc", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "ibzkzs9yabbd8ixx9ct7", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 13, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "b6hfdp0a25stdrt1xdop", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "2s5taxv0excmeeq3i9qu", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 19, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "sl7qmbn5hrdcn30wbkre", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "lvfdeeojqzwaah3ziqdf", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 19, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "9cyltopogmehm8t3mrfp", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "fs2l6in1l7ddq2vrztw8", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 14, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "hvhxp4rbxt07ri793c1c", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "7w7qa3fq0kbp6afmaxs1", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 14, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "rxiafxk4p4wmvxj1n6jl", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "f5i4jjonbj5vg51wbsym", "title": "GET \"/orchestrator/env\"", "type": "step", "stepType": "pw:api", "duration": 14, "location": "utilities/ApiUtils.ts:287:49"}, {"id": "vniqe7b0ehibxte583g7", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:292:35"}, {"id": "tp3q0ifyg5l24462l7qb", "title": "GET \"/orchestrator/gitops/config\"", "type": "step", "stepType": "pw:api", "duration": 12, "location": "utilities/ApiUtils.ts:168:45"}, {"id": "4rtuhuq50sbyms1zuxvw", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:173:31"}, {"id": "onlyhb5sp9dbqhup80jz", "title": "PUT \"/orchestrator/gitops/config\"", "type": "step", "stepType": "pw:api", "duration": 12580, "location": "utilities/ApiUtils.ts:258:47"}, {"id": "0jgp3oqulo9hbaqymcka", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:265:35"}, {"id": "fg0ci9ijas6ohn2nujcm", "title": "POST \"/orchestrator/digest-policy\"", "type": "step", "stepType": "pw:api", "duration": 26, "location": "utilities/ApiUtils.ts:1426:43"}, {"id": "xgpci62ro01dvgs3bx0l", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1435:35"}, {"id": "3xc8y5k1cfeed4289d64", "title": "GET \"/orchestrator/api-token\"", "type": "step", "stepType": "pw:api", "duration": 52, "location": "utilities/ApiUtils.ts:1501:43"}, {"id": "tjyhu1otbfqv7hsgzmqg", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1506:35"}, {"id": "wylghzc2lwglrhsrzgf0", "title": "GET \"/orchestrator/api-token\"", "type": "step", "stepType": "pw:api", "duration": 21, "location": "utilities/ApiUtils.ts:1501:43"}, {"id": "df3rzyd6a5rcnizzddvr", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1506:35"}, {"id": "25jotgx4fagiih3n271s", "title": "GET \"/orchestrator/global/cm-cs/all\"", "type": "step", "stepType": "pw:api", "duration": 11, "location": "utilities/ApiUtils.ts:1213:43"}, {"id": "nfkw9tnmcgu2pmpmldr7", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:1218:31"}, {"id": "475vrrcpx83dodnuj0nm", "title": "After Hooks", "type": "step", "stepType": "hook", "duration": 38, "location": "", "subs": [{"id": "kl5v58rm3dlntq7m4l1i", "title": "Fixture \"request\"", "type": "step", "stepType": "fixture", "duration": 32, "location": ""}, {"id": "gwnb5n1qfeejg4c2b2bw", "title": "Fixture \"storageState\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "tests/PreRequisite.spec.ts:11:6"}]}], "attachments": [{"name": "trace", "path": "attachments/6c24f72df0d70e8d5affd292f42fdb7642f8e425.zip", "contentType": "application/zip", "retry": 0}]}], "location": "tests/PreRequisite.spec.ts:0:0"}]}, {"id": "38e2d5ebe13ffc5c9e7c", "title": "vm2Automation", "type": "suite", "suiteType": "project", "caseNum": 3, "subs": [{"id": "86b67446358e08cfab02", "title": "ApplicationTemplate.spec.ts", "type": "suite", "suiteType": "file", "caseNum": 3, "subs": [{"id": "fbc0a8eafa5c8da91afb", "title": "application template creation and validations", "type": "case", "caseType": "passed", "ok": true, "outcome": "expected", "expectedStatus": "passed", "location": "tests/ApplicationTemplate.spec.ts:117:5", "logs": ["\n", "workflow found at index 0\n", "workflow found at index 1\n", "workflow found at index 2\n", "workflow found at index 3\n", "workflow found at index 4\n", "workflow found at index 5\n", "workflow found at index 6\n", "workflow found at index 7\n", "i we are getting is 0\n", "i we are getting is 1\n", "i we are getting is 2\n", "i we are getting is 3\n", "i we are getting is 4\n", "i we are getting is 5\n", "i we are getting is 6\n", "i we are getting is 7\n", "i we are getting is 8\n", "i we are getting is 9\n", "i we are getting is 10\n", "i we are getting is 11\n", "i we are getting is 12\n", "i we are getting is 13\n", "i we are getting is 14\n", "i we are getting is 15\n", "i we are getting is 16\n", "i we are getting is 17\n", "i we are getting is 18\n"], "timestamps": [1756385861993, 1756385909884], "duration": 47891, "tags": ["@globalConfigurations"], "timeout": 1500000, "retry": 0, "status": "passed", "stepNum": 146, "stepFailed": 2, "stepSubs": true, "subs": [{"id": "y5jjd1t5i4lj5vhrqbld", "title": "Before Hooks", "type": "step", "stepType": "hook", "duration": 1619, "location": "", "subs": [{"id": "ns8tt1fc149xjm1hfw8n", "title": "setting up app", "type": "step", "stepType": "hook", "duration": 1300, "location": "tests/ApplicationTemplate.spec.ts:39:6", "subs": [{"id": "acbvc5k5tbtw8ka7obyj", "title": "Fixture \"browser\"", "type": "step", "stepType": "fixture", "duration": 470, "location": "", "subs": [{"id": "hm2n2j66tti16l318oa9", "title": "Launch browser", "type": "step", "stepType": "pw:api", "duration": 467, "location": ""}]}, {"id": "7m8m0m0r9fj820xw2ch4", "title": "Create request context", "type": "step", "stepType": "pw:api", "duration": 2, "location": "tests/ApplicationTemplate.spec.ts:41:25"}, {"id": "zz7x1c11vl2gjw8b4rt4", "title": "POST \"/orchestrator/api/v1/session\"", "type": "step", "stepType": "pw:api", "duration": 203, "location": "utilities/ApiUtils.ts:31:47"}, {"id": "4nrq52s03u3s0ehrn5ub", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 1, "location": "utilities/ApiUtils.ts:33:39"}, {"id": "34f4j8v5yj9p3y0btcil", "title": "Create page", "type": "step", "stepType": "pw:api", "duration": 470, "location": "tests/ApplicationTemplate.spec.ts:44:30"}, {"id": "ouu2j6f1ifc2kzuw2pzl", "title": "Expect \"toPass\"", "type": "step", "stepType": "test.step", "duration": 29, "location": "utilities/ApiUtils.ts:890:16", "subs": [{"id": "qqlajqmmw40tgsmi02eh", "title": "POST \"/orchestrator/app/list\"", "type": "step", "stepType": "pw:api", "duration": 25, "location": "utilities/ApiUtils.ts:882:51"}, {"id": "f62rmshe3fs39t8i3xgb", "title": "Expect \"toBeTruthy\"", "type": "step", "stepType": "expect", "duration": 0, "location": "utilities/ApiUtils.ts:889:39"}]}, {"id": "lj0exo0obfs2qt508nhf", "title": "Close context", "type": "step", "stepType": "pw:api", "duration": 13, "location": "tests/ApplicationTemplate.spec.ts:110:5"}, {"id": "tlzcer6bzxk2zruub7wq", "title": "Fixture \"storageState\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "tests/ApplicationTemplate.spec.ts:29:6"}]}, {"id": "7g9bp2mhcodf4hmlep56", "title": "Fixture \"context\"", "type": "step", "stepType": "fixture", "duration": 190, "location": "", "subs": [{"id": "46z2q8t2lu8sc66193vm", "title": "Create context", "type": "step", "stepType": "pw:api", "duration": 188, "location": ""}]}, {"id": "80k0oy2cy86fyodx2rns", "title": "Fixture \"page\"", "type": "step", "stepType": "fixture", "duration": 119, "location": "", "subs": [{"id": "fz8ajag30bfexqy28yph", "title": "Create page", "type": "step", "stepType": "pw:api", "duration": 119, "location": ""}]}, {"id": "dfowjuok2ox9vsw4jygp", "title": "Fixture \"AllObjects\"", "type": "step", "stepType": "fixture", "duration": 2, "location": "utilities/Fixtures.ts:67:26"}, {"id": "nrqc4bozaowlulgytehk", "title": "Fixture \"request\"", "type": "step", "stepType": "fixture", "duration": 2, "location": "", "subs": [{"id": "ktv0va84i1howah72p5g", "title": "Create request context", "type": "step", "stepType": "pw:api", "duration": 0, "location": ""}]}]}, {"id": "5gntyhzdjrndto4n3rah", "title": "Navigate to \"/dashboard/global-config/templates/devtron-apps\"", "type": "step", "stepType": "pw:api", "duration": 1361, "location": "Pages/GlobalConfigurations/GlobalConfigurationPage.ts:75:25"}, {"id": "zo2wz3avxsxmfvcfn5ew", "title": "Click getByTestId('create-template-button')", "type": "step", "stepType": "pw:api", "duration": 726, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:54:41"}, {"id": "qds0oo0jgftypek1uif9", "title": "Fill \"playwright-auto-apptemplate\" getByTestId('application-list-search')", "type": "step", "stepType": "pw:api", "duration": 684, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:55:48"}, {"id": "58jw0hzgdrxofmylvfxq", "title": "Press \"Enter\"", "type": "step", "stepType": "pw:api", "duration": 624, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:56:34"}, {"id": "xf3wxe1zq3rooo9a3jnf", "title": "Click locator('//*[text()=\"playwright-auto-apptemplate\"]/ancestor::div[3]')", "type": "step", "stepType": "pw:api", "duration": 640, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:58:97"}, {"id": "if5q6kzbbmds2fhg206s", "title": "Fill \"playwright-auto-template\" getByTestId('template-display-name')", "type": "step", "stepType": "pw:api", "duration": 632, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:63:49"}, {"id": "0xh9htbfuvf2yl45n0jn", "title": "Fill \"playwright-auto-template\" getByTestId('template-template-id')", "type": "step", "stepType": "pw:api", "duration": 625, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:64:47"}, {"id": "biicdghj0o6k5iqtlbgn", "title": "Fill \"\" getByPlaceholder('Write a description for this template')", "type": "step", "stepType": "pw:api", "duration": 632, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:65:58"}, {"id": "n1ccftnospj6vjvnryt3", "title": "Click locator('//*[@class=\"drawer right show\"]').locator(getByTestId('create-template-button'))", "type": "step", "stepType": "pw:api", "duration": 646, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:66:80"}, {"id": "13jsix7yoe8d3t5sujk4", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 5639, "location": "Pages/AppConfigurationsPage.ts:1083:36", "count": 8}, {"id": "1glc5n0bizj54ooan58t", "title": "Click getByTestId('workflow-editor-link')", "type": "step", "stepType": "pw:api", "duration": 8468, "location": "Pages/AppConfigurationsPage.ts:1084:34", "count": 8}, {"id": "i3epouqhuiiwykep6rlk", "title": "Wait for selector getByTestId('workflow-header').first()", "type": "step", "stepType": "pw:api", "duration": 353, "location": "Pages/AppConfigurationsPage.ts:1085:39", "count": 8}, {"id": "i5hk9sy9xhc2ssrc2u3p", "title": "Query count getByTestId('workflow-header')", "type": "step", "stepType": "pw:api", "duration": 288, "location": "Pages/AppConfigurationsPage.ts:1086:57", "count": 8}, {"id": "6x9klk2u38h30ha35wut", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 639, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "wjpxwholngdtjl6s07o1", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 30, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "5rohoy0zvh7gaailxlnb", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 635, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "x6kd3t9jt1fucxbjp421", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 25, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "mnnpkp6qxtbkpb104j8q", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 640, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "2fu758gt4pdxv6s15cy6", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 15, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "xdzac9a2212lzngxh972", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 649, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "ib4imn7plp6i49jcm5ro", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 36, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 3}, {"id": "34dsjiojq7z3jt0xnlhf", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 661, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "9gaku0vadxnhd9vebg6o", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 29, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "912uq6f6l30ilcktg9at", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 637, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "rs27dmbz6j6ovhifphne", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 15, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "vgphbrpepwn5o2v0kfit", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 649, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "w774427wwv8k6xfex4cf", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 33, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "rn46b31juoagrrhltgpe", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 630, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "nyhq3uughmntiscjl5yq", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 29, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "filk0aqs2rkz0qx1q8ub", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 631, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "6corrxd9plivqksfuke9", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 41, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "q9o9fvr6xatw1paav7g9", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 639, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "ho2augiflevd53gf8zrn", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 31, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "u3md0hwly0vki2e9nrnt", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 632, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "stfl2q0m7p9ud6ua51v7", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 30, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "cnq1mgb36cnr94cw9cy3", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 635, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "520px9szamsj8grhx5a4", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 17, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "p286hg8wveauhi2m91cv", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 646, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "4ymp1767uu5cmkupnmj7", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 30, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "2131vswxiq82td0jtwx0", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 632, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "15burphazqix6b5ibwck", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 17, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "v9sfu9oz5a49kx8j80ar", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 649, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "rb0x8umm5518jzu77n6e", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 34, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "66x0c2y5qhf41iihrxe4", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 642, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "q9hz9lbil49uo02vvbat", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 21, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "b0yb1c8uwp81egsu3rw4", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 645, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "540wmyqws63iogvd30zo", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 32, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "75p928h4xldz3flee5qt", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 628, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "on3y4rbr5t0x3ky6dwna", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 18, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "9qbu6g9zzvowgllksh2q", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 646, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "3y0keth4jiac9jyg2n3b", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 40, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "broet6im0jwvel5ughy0", "title": "Expect \"toPass\"", "type": "step", "stepType": "test.step", "duration": 1666, "location": "Pages/AppConfigurationsPage.ts:481:8", "subs": [{"id": "cz9zbkbcoxlgo4rsx8ir", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 1646, "location": "Pages/AppConfigurationsPage.ts:479:38"}, {"id": "iek03maho9tent66amhv", "title": "Wait for selector locator('//*[@data-testid=\"new-workflow-button\" or @data-testid=\"job-pipeline-button\" or text()=\" Completed\" ]').first()", "type": "step", "stepType": "pw:api", "duration": 16, "location": "Pages/AppConfigurationsPage.ts:480:62"}]}, {"id": "x9z249n392epkmo9kssv", "title": "Click locator('//*[contains(@class,\"workflow__body\")]').first().locator('//*[contains(@data-testid,\"-ci\")]').first()", "type": "step", "stepType": "pw:api", "duration": 841, "location": "Pages/AppConfigurationsPage.ts:482:112"}, {"id": "rfr216t3gqgskxt9jad4", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 36, "location": "Pages/BuildConfigurationPage.ts:64:102"}, {"id": "fprbyq1p3kujc43oim0h", "title": "Expect \"toEqual\"", "type": "step", "stepType": "expect", "duration": 1, "location": "Pages/BuildConfigurationPage.ts:66:32"}, {"id": "dbczj95u5wgdk1xdyce7", "title": "Expect \"toContain\"", "type": "step", "stepType": "expect", "duration": 0, "location": "Pages/AppConfigurationsPage.ts:1132:35"}, {"id": "n3ruu3yp5uei8251x0vr", "title": "Click getByTestId('build-pipeline-button')", "type": "step", "stepType": "pw:api", "duration": 644, "location": "utilities/DataObjectss.ts/ApplicationTemplateDataObject.ts:348:60"}, {"id": "w9fn8v243j2f0v1hdp07", "title": "Expect \"toPass\"", "type": "step", "stepType": "test.step", "duration": 1698, "location": "Pages/AppConfigurationsPage.ts:481:8", "subs": [{"id": "ks4rnuahoqufmv21kem5", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 1663, "location": "Pages/AppConfigurationsPage.ts:479:38"}, {"id": "u78hthjijp7z7etgrsdj", "title": "Wait for selector locator('//*[@data-testid=\"new-workflow-button\" or @data-testid=\"job-pipeline-button\" or text()=\" Completed\" ]').first()", "type": "step", "stepType": "pw:api", "duration": 33, "location": "Pages/AppConfigurationsPage.ts:480:62"}]}, {"id": "elrcxusdk06oranw8e3d", "title": "Click locator('//*[contains(@class,\"workflow__body\")]').nth(1).locator('//*[contains(@data-testid,\"-env1\")]').first()", "type": "step", "stepType": "pw:api", "duration": 777, "location": "Pages/AppConfigurationsPage.ts:482:112"}, {"id": "2hh0izbqpgupjl10jdpx", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 25, "location": "Pages/AppConfigurationsPage.ts:1142:80"}, {"id": "6onlcel56g6k31sfpe4x", "title": "Expect \"toBeChecked\"", "type": "step", "stepType": "expect", "duration": 14, "location": "Pages/AppConfigurationsPage.ts:1138:89"}, {"id": "i93kye540mpqj7jdsfw6", "title": "Click locator('//*[contains(@data-testid,\\'build-pipeline-button\\') or contains(@data-testid,\\'linked-cd\\')]')", "type": "step", "stepType": "pw:api", "duration": 667, "location": "utilities/DataObjectss.ts/ApplicationTemplateDataObject.ts:353:68"}, {"id": "x2x5gbjgjve4bj78x69f", "title": "Click getByTestId('close-build-deploy-button')", "type": "step", "stepType": "pw:api", "duration": 842, "location": "Pages/AppConfigurationsPage.ts:491:36"}, {"id": "sn3es19v05cip8sol534", "title": "Fill \"automation\" locator('//*[@class=\"env-config-selector__input\"]')", "type": "step", "stepType": "pw:api", "duration": 6641, "location": "Pages/Jobs/Jobs.ts:264:81", "errors": ["TimeoutError: locator.fill: Timeout 6000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('//*[@class=\"env-config-selector__input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 262 |\u001b[39m         \u001b[36mtry\u001b[39m {\n \u001b[90m 263 |\u001b[39m             \u001b[36mvar\u001b[39m stageToSearch \u001b[33m=\u001b[39m stage \u001b[33m==\u001b[39m \u001b[32m\"base-configurations\"\u001b[39m \u001b[33m?\u001b[39m \u001b[32m'Base configurations'\u001b[39m \u001b[33m:\u001b[39m stage\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 264 |\u001b[39m             \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mlocator(\u001b[32m'//*[@class=\"env-config-selector__input\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(stageToSearch\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m6000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[0m\n\n    at JobsPage.operBaseOrEnvOverRideResources (/Users/<USER>/Desktop/ui-automation/dashboard-automation/Pages/Jobs/Jobs.ts:264:81)\n    at verifyConfigurationOfTemplateAndApp (/Users/<USER>/Desktop/ui-automation/dashboard-automation/utilities/DataObjectss.ts/ApplicationTemplateDataObject.ts:357:35)\n    at /Users/<USER>/Desktop/ui-automation/dashboard-automation/tests/ApplicationTemplate.spec.ts:122:5"], "errorNum": 1}, {"id": "zt2ecgbat6wbybe35mxs", "title": "Click locator('//*[@data-testid=\"automation-link\"]')", "type": "step", "stepType": "pw:api", "duration": 868, "location": "Pages/Jobs/Jobs.ts:269:78"}, {"id": "2p6yr3zlexau2kt0fec0", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 20, "location": "Pages/BaseDeploymentTemplatePage.ts:548:46"}, {"id": "podfju88ns6jhd5zqx0m", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 7, "location": "Pages/BaseDeploymentTemplatePage.ts:285:112"}, {"id": "d9n56vb57nvz96ka0yhb", "title": "Click locator('//*[contains(@href,\"edit/workflow\")]').first()", "type": "step", "stepType": "pw:api", "duration": 734, "location": "Pages/Jobs/Jobs.ts:283:45"}, {"id": "z9pdojtty1b4ox9p2mbs", "title": "Fill \"devtron-demo\" locator('//*[@class=\"env-config-selector__input\"]')", "type": "step", "stepType": "pw:api", "duration": 6623, "location": "Pages/Jobs/Jobs.ts:264:81", "errors": ["TimeoutError: locator.fill: Timeout 6000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('//*[@class=\"env-config-selector__input\"]')\u001b[22m\n\n\n\u001b[0m \u001b[90m 262 |\u001b[39m         \u001b[36mtry\u001b[39m {\n \u001b[90m 263 |\u001b[39m             \u001b[36mvar\u001b[39m stageToSearch \u001b[33m=\u001b[39m stage \u001b[33m==\u001b[39m \u001b[32m\"base-configurations\"\u001b[39m \u001b[33m?\u001b[39m \u001b[32m'Base configurations'\u001b[39m \u001b[33m:\u001b[39m stage\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 264 |\u001b[39m             \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mpage\u001b[33m.\u001b[39mlocator(\u001b[32m'//*[@class=\"env-config-selector__input\"]'\u001b[39m)\u001b[33m.\u001b[39mfill(stageToSearch\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m6000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                 \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[0m\n\n    at JobsPage.operBaseOrEnvOverRideResources (/Users/<USER>/Desktop/ui-automation/dashboard-automation/Pages/Jobs/Jobs.ts:264:81)\n    at verifyConfigurationOfTemplateAndApp (/Users/<USER>/Desktop/ui-automation/dashboard-automation/utilities/DataObjectss.ts/ApplicationTemplateDataObject.ts:357:35)\n    at /Users/<USER>/Desktop/ui-automation/dashboard-automation/tests/ApplicationTemplate.spec.ts:122:5"], "errorNum": 1}, {"id": "ogzh39t965mhnngp7q56", "title": "Click locator('//*[@data-testid=\"devtron-demo-link\"]')", "type": "step", "stepType": "pw:api", "duration": 785, "location": "Pages/Jobs/Jobs.ts:269:78"}, {"id": "78f98m82v4lfm04kxsdu", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 21, "location": "Pages/BaseDeploymentTemplatePage.ts:548:46"}, {"id": "z74trt4rcufmo56i13z7", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 7, "location": "Pages/BaseDeploymentTemplatePage.ts:285:112"}, {"id": "9k5biio7jt1r7i9f3aqu", "title": "Click locator('//*[contains(@href,\"edit/workflow\")]').first()", "type": "step", "stepType": "pw:api", "duration": 731, "location": "Pages/Jobs/Jobs.ts:283:45"}, {"id": "9bjw72nr56oaes94v4dm", "title": "After Hooks", "type": "step", "stepType": "hook", "duration": 674, "location": "", "subs": [{"id": "ppasi4w7macwp5gb4fqi", "title": "Fixture \"request\"", "type": "step", "stepType": "fixture", "duration": 25, "location": ""}, {"id": "gj546fy8gw61vvuzy9g0", "title": "Fixture \"AllObjects\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "utilities/Fixtures.ts:67:26"}, {"id": "wulsj3k16eqjogslj0no", "title": "Fixture \"page\"", "type": "step", "stepType": "fixture", "duration": 0, "location": ""}, {"id": "j9hdd1wzm3te8o6t4d24", "title": "Fixture \"context\"", "type": "step", "stepType": "fixture", "duration": 627, "location": "", "subs": [{"id": "ybfgin2sg30y5paolvx5", "title": "Close context", "type": "step", "stepType": "pw:api", "duration": 18, "location": ""}]}, {"id": "zytxye84jyysh2w3e34l", "title": "Fixture \"storageState\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "tests/ApplicationTemplate.spec.ts:29:6"}]}], "attachments": [{"name": "trace", "path": "attachments/2945bc1febd2de9b44cc525c1d4aaa26e33ee79e.zip", "contentType": "application/zip", "retry": 0}]}, {"id": "0a3594ab5f2c3dc96284", "title": "devtron app creation from template with cicd and changes on configuration page", "type": "case", "caseType": "failed", "ok": false, "outcome": "unexpected", "expectedStatus": "passed", "location": "tests/ApplicationTemplate.spec.ts:130:5", "logs": ["workflow found at index 0\n", "workflow found at index 1\n", "workflow found at index 2\n", "workflow found at index 3\n", "workflow found at index 4\n", "workflow found at index 5\n", "workflow found at index 6\n", "workflow found at index 7\n", "\n", "workflow found at index 0\n", "workflow found at index 1\n", "workflow found at index 2\n", "workflow found at index 3\n", "workflow found at index 4\n", "workflow found at index 5\n", "workflow found at index 6\n", "workflow found at index 7\n", "i we are getting is 0\n", "i we are getting is 1\n", "i we are getting is 2\n", "i we are getting is 3\n"], "timestamps": [1756385909887, 1756385951936], "duration": 42049, "tags": ["@globalConfigurations"], "timeout": 1500000, "retry": 0, "status": "failed", "stepNum": 122, "stepFailed": 1, "stepSubs": true, "subs": [{"id": "wfelm0bkyip6x2jtvh91", "title": "Before Hooks", "type": "step", "stepType": "hook", "duration": 466, "location": "", "subs": [{"id": "azj0dcd84kn7a4v8ce3x", "title": "Fixture \"context\"", "type": "step", "stepType": "fixture", "duration": 191, "location": "", "subs": [{"id": "e1r287l6ehyt1ee7jzdb", "title": "Create context", "type": "step", "stepType": "pw:api", "duration": 188, "location": ""}]}, {"id": "hbo036ks4ncii6jp4nge", "title": "Fixture \"page\"", "type": "step", "stepType": "fixture", "duration": 255, "location": "", "subs": [{"id": "by5fdvqng6n6znt7o69i", "title": "Create page", "type": "step", "stepType": "pw:api", "duration": 254, "location": ""}]}, {"id": "eqjsd8pu66xr0jfq6zof", "title": "Fixture \"AllObjects\"", "type": "step", "stepType": "fixture", "duration": 14, "location": "utilities/Fixtures.ts:67:26"}]}, {"id": "kd1eefjfek8mhlspm1vy", "title": "Navigate to \"/dashboard/global-config/templates/devtron-apps/detail/19/edit/workflow\"", "type": "step", "stepType": "pw:api", "duration": 1351, "location": "tests/ApplicationTemplate.spec.ts:132:16"}, {"id": "bxhngl5rihpeh6ebhcnw", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 5648, "location": "Pages/AppConfigurationsPage.ts:1083:36", "count": 8}, {"id": "zv0kcdep9jp37y5voeum", "title": "Click getByTestId('workflow-editor-link')", "type": "step", "stepType": "pw:api", "duration": 8082, "location": "Pages/AppConfigurationsPage.ts:1084:34", "count": 8}, {"id": "t6drbq02cfoqxqvv1tis", "title": "Wait for selector getByTestId('workflow-header').first()", "type": "step", "stepType": "pw:api", "duration": 16, "location": "Pages/AppConfigurationsPage.ts:1085:39"}, {"id": "1tvcfd6mzi9r00nsbjro", "title": "Query count getByTestId('workflow-header')", "type": "step", "stepType": "pw:api", "duration": 10, "location": "Pages/AppConfigurationsPage.ts:1086:57"}, {"id": "bn2n6ugg13sq347i6or0", "title": "Wait for selector getByTestId('workflow-header').first()", "type": "step", "stepType": "pw:api", "duration": 282, "location": "Pages/AppConfigurationsPage.ts:1085:39", "count": 7}, {"id": "fdyx36v8gonyitdtzelo", "title": "Query count getByTestId('workflow-header')", "type": "step", "stepType": "pw:api", "duration": 236, "location": "Pages/AppConfigurationsPage.ts:1086:57", "count": 7}, {"id": "6rik4bjybi2hnlqhg2uf", "title": "Navigate to \"/dashboard/dashboard/app/list/d\"", "type": "step", "stepType": "pw:api", "duration": 953, "location": "Pages/CreateAppPage.ts:210:21"}, {"id": "j0619ireapkae1bazp1z", "title": "Click getByTestId('create-app-button-on-header')", "type": "step", "stepType": "pw:api", "duration": 656, "location": "Pages/CreateAppPage.ts:317:40"}, {"id": "e7aos2kwuy2ikt32tcvc", "title": "Click getByText('Custom app', { exact: true })", "type": "step", "stepType": "pw:api", "duration": 768, "location": "Pages/CreateAppPage.ts:318:30"}, {"id": "urncdda488ib0j7xpe9t", "title": "Click locator('//*[@aria-label=\"Creation method: From template\"]')", "type": "step", "stepType": "pw:api", "duration": 645, "location": "Pages/CreateAppPage.ts:319:44"}, {"id": "0wi8tkkmdh8f0qnvgkl9", "title": "Fill \"playwright-auto-template\" getByTestId('template-list-search')", "type": "step", "stepType": "pw:api", "duration": 644, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:34:45"}, {"id": "xqk7oss61bb63qmszzgp", "title": "Press \"Enter\"", "type": "step", "stepType": "pw:api", "duration": 620, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:35:34"}, {"id": "79ecgvqi2rm717naqcye", "title": "Click locator('//*[text()=\"playwright-auto-template\"]/ancestor::div[3]')", "type": "step", "stepType": "pw:api", "duration": 686, "location": "Pages/GlobalConfigurations/ApplicationTemplatePage.ts:37:74"}, {"id": "h8c6zf0qwml7ivv6a3dh", "title": "Fill \"ui-automwwsm\" locator('//*[@name=\"name\"]')", "type": "step", "stepType": "pw:api", "duration": 654, "location": "Pages/CreateAppPage.ts:321:31"}, {"id": "v7jv1dpxng8jx2zwavqw", "title": "Click locator('//*[contains(@class,\\'project__control\\')]').first()", "type": "step", "stepType": "pw:api", "duration": 675, "location": "Pages/CreateAppPage.ts:198:44"}, {"id": "fn87a6ue6w8anrpz58w2", "title": "Click locator('//*[@role=\"listbox\"]').locator('//*[text()=\"devtron-demo\"]')", "type": "step", "stepType": "pw:api", "duration": 682, "location": "Pages/CreateAppPage.ts:206:93"}, {"id": "3qnp7xey6ox773r5mhz8", "title": "Click locator('//*[contains(text(),\\'Add tags\\')]')", "type": "step", "stepType": "pw:api", "duration": 676, "location": "Pages/CreateAppPage.ts:324:32"}, {"id": "jhxkoipd996s6wj4ovpq", "title": "Fill \"key1\" locator('//*[contains(@data-testid,\\'-tagKey\\') or @class=\"dynamic-data-table__cell__select-picker-text-area__input\"]')", "type": "step", "stepType": "pw:api", "duration": 636, "location": "Pages/TagPropogation.ts:70:29"}, {"id": "9j4vsdno86l66jshb7o4", "title": "Fill \"value1\" locator('//*[contains(@data-testid,\\'-tagValue\\')]')", "type": "step", "stepType": "pw:api", "duration": 634, "location": "Pages/TagPropogation.ts:73:31"}, {"id": "eyecw5y5m86nv5uuomj8", "title": "Click locator('//button[contains(@class,\\'pointer\\')]')", "type": "step", "stepType": "pw:api", "duration": 674, "location": "Pages/TagPropogation.ts:74:32"}, {"id": "nc5te5o8mxfpb159qjun", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 25, "location": "Pages/GitRepositoryPage.ts:117:101"}, {"id": "hlws9wl6rx1fjazqjyig", "title": "Expect \"toBe\"", "type": "step", "stepType": "expect", "duration": 0, "location": "Pages/GitRepositoryPage.ts:119:24"}, {"id": "wvjdhqi2q7eno1x00yqx", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 20, "location": "Pages/GitRepositoryPage.ts:117:101"}, {"id": "xd1irjnp56i3htkllcde", "title": "Expect \"toBe\"", "type": "step", "stepType": "expect", "duration": 0, "location": "Pages/GitRepositoryPage.ts:119:24"}, {"id": "fz05ivaj1y3fjcui2320", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 9, "location": "Pages/BuildConfigurationPage.ts:64:102"}, {"id": "hsm2osb3arus0t76q0ha", "title": "Expect \"toEqual\"", "type": "step", "stepType": "expect", "duration": 0, "location": "Pages/BuildConfigurationPage.ts:66:32"}, {"id": "4ztmxc39on7srksqqmk2", "title": "Expect \"toBeVisible\"", "type": "step", "stepType": "expect", "duration": 14, "location": "Pages/CreateAppPage.ts:304:128", "count": 2}, {"id": "pvc702zzfqa46h13bjkn", "title": "Click getByTestId('create')", "type": "step", "stepType": "pw:api", "duration": 631, "location": "Pages/CreateAppPage.ts:343:40"}, {"id": "qcvly33i0ntfh2ieuc47", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 2593, "location": "tests/ApplicationTemplate.spec.ts:135:63"}, {"id": "oo0ytm0g0p5vp3k9o93h", "title": "Wait for selector locator('//*[@data-testid=\"workflow-editor-link\"]')", "type": "step", "stepType": "pw:api", "duration": 30, "location": "Pages/GItopsConfigurationPage.ts:105:77"}, {"id": "rctvlc99zgc4oeb6yvy4", "title": "Click getByTestId('gitops-configuration-link')", "type": "step", "stepType": "pw:api", "duration": 709, "location": "Pages/GItopsConfigurationPage.ts:107:61"}, {"id": "vwoy6pojdf9qj034pcxt", "title": "Expect \"toBeVisible\"", "type": "step", "stepType": "expect", "duration": 14, "location": "Pages/GItopsConfigurationPage.ts:108:59"}, {"id": "q36axdjavgpcvxhhlli5", "title": "Expect \"toBeVisible\"", "type": "step", "stepType": "expect", "duration": 9, "location": "Pages/GItopsConfigurationPage.ts:109:60"}, {"id": "uc9pbtllbqtkin28jixt", "title": "Click getByTestId('auto-create-repository-span').getByText('Auto-create repository', { exact: true })", "type": "step", "stepType": "pw:api", "duration": 649, "location": "Pages/GItopsConfigurationPage.ts:110:52"}, {"id": "tdr50a0id7krxwa30kfg", "title": "Expect \"toPass\"", "type": "step", "stepType": "test.step", "duration": 11584, "location": "Pages/GItopsConfigurationPage.ts:114:16", "subs": [{"id": "f83vfq6ni60icjz2m0d0", "title": "Click getByTestId('save_cluster_list_button_after_selection')", "type": "step", "stepType": "pw:api", "duration": 651, "location": "Pages/GItopsConfigurationPage.ts:112:51"}, {"id": "kyuifpcizudjf57tbhuh", "title": "Expect \"toBeVisible\"", "type": "step", "stepType": "expect", "duration": 10931, "location": "Pages/GItopsConfigurationPage.ts:113:76"}]}, {"id": "iyj6m3nyt2lqxp1nmsog", "title": "Click getByTestId('workflow-editor-link')", "type": "step", "stepType": "pw:api", "duration": 713, "location": "tests/ApplicationTemplate.spec.ts:137:53"}, {"id": "ad2kxo7wgrbg99wxe11b", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 5755, "location": "Pages/AppConfigurationsPage.ts:1083:36", "count": 8}, {"id": "zwt1pn5gubcrgn196ve0", "title": "Click getByTestId('workflow-editor-link')", "type": "step", "stepType": "pw:api", "duration": 8641, "location": "Pages/AppConfigurationsPage.ts:1084:34", "count": 8}, {"id": "57j2clt64msbog94y2ct", "title": "Wait for selector getByTestId('workflow-header').first()", "type": "step", "stepType": "pw:api", "duration": 411, "location": "Pages/AppConfigurationsPage.ts:1085:39", "count": 8}, {"id": "aku6anhtggaf0b0crbhc", "title": "Query count getByTestId('workflow-header')", "type": "step", "stepType": "pw:api", "duration": 277, "location": "Pages/AppConfigurationsPage.ts:1086:57", "count": 8}, {"id": "zgzaqn2xjcqp7v2m9a5b", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 646, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "2xi7k1jz6zlja6npnxbj", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 28, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "byzn30evgeov3k8b4v85", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 648, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "m25848mj3htkegpoe97z", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 29, "location": "Pages/AppConfigurationsPage.ts:1101:186", "count": 2}, {"id": "y354f3ba92kyd2e24hvw", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 650, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "uztgp0gdt5x6tywxvrtv", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 14, "location": "Pages/AppConfigurationsPage.ts:1101:186"}, {"id": "y882flo2eg3i1hnqag6w", "title": "Click locator('//*[contains(@data-testid,\\'-config-link\\')]')", "type": "step", "stepType": "pw:api", "duration": 636, "location": "Pages/AppConfigurationsPage.ts:1099:36"}, {"id": "rdn5t09aev0v54ww4gyb", "title": "Expect \"toContainText\"", "type": "step", "stepType": "expect", "duration": 5037, "location": "Pages/AppConfigurationsPage.ts:1101:186", "errors": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('//*[contains(@class,\"workflow__body\")]').first().locator('//*[contains(@class,\\'workflow-node \\') or contains(@data-testid,\\'workflow-editor-link-cd\\')]').nth(3)\nExpected string: \u001b[32m\"\u001b[7ma\u001b[27muto\u001b[7mmation\u001b[27m\"\u001b[39m\nReceived string: \u001b[31m\"\u001b[7mA\u001b[27muto\u001b[7mPre-deploy, Deploydevtron-demo\u001b[27m\"\u001b[39m\nTimeout: 5000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('//*[contains(@class,\"workflow__body\")]').first().locator('//*[contains(@class,\\'workflow-node \\') or contains(@data-testid,\\'workflow-editor-link-cd\\')]').nth(3)\u001b[22m\n\u001b[2m    9 × locator resolved to <div class=\"workflow-node cursor pl-16\" data-testid=\"workflow-editor-cd-node-devtron-demo\">…</div>\u001b[22m\n\u001b[2m      - unexpected value \"AutoPre-deploy, Deploydevtron-demo\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 1099 |\u001b[39m     \u001b[36mawait\u001b[39m \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mappConfigurationTab\u001b[33m.\u001b[39mclick()\u001b[33m;\u001b[39m\n \u001b[90m 1100 |\u001b[39m     \u001b[36mfor\u001b[39m (\u001b[36mlet\u001b[39m i \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m i \u001b[33m<\u001b[39m data\u001b[33m.\u001b[39mtextToVerify\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m i\u001b[33m++\u001b[39m) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 1101 |\u001b[39m       \u001b[36mawait\u001b[39m expect(\u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mworkflowDiv\u001b[33m.\u001b[39mnth(data\u001b[33m.\u001b[39mworkflowNumber)\u001b[33m.\u001b[39mlocator(\u001b[32m`//*[contains(@class,'workflow-node ') or contains(@data-testid,'workflow-editor-link-cd')]`\u001b[39m)\u001b[33m.\u001b[39mnth(data\u001b[33m.\u001b[39mnodeNumber))\u001b[33m.\u001b[39mtoContainText(data\u001b[33m.\u001b[39mtextToVerify[i])\u001b[33m;\u001b[39m\n \u001b[90m      |\u001b[39m                                                                                                                                                                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[0m\n\n    at AppConfigurationPage.verifyDetailsOfWorkflow (/Users/<USER>/Desktop/ui-automation/dashboard-automation/Pages/AppConfigurationsPage.ts:1101:186)\n    at verifyConfigurationOfTemplateAndApp (/Users/<USER>/Desktop/ui-automation/dashboard-automation/utilities/DataObjectss.ts/ApplicationTemplateDataObject.ts:342:9)\n    at /Users/<USER>/Desktop/ui-automation/dashboard-automation/tests/ApplicationTemplate.spec.ts:139:5"], "errorNum": 1}, {"id": "mowyxa4zowdqfz3k9fxp", "title": "After Hooks", "type": "step", "stepType": "hook", "duration": 972, "location": "", "subs": [{"id": "do5e0kqf8ey5vkuzn3nd", "title": "Fixture \"AllObjects\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "utilities/Fixtures.ts:67:26"}, {"id": "rqdr5tejuow7np8nw0gw", "title": "Fixture \"page\"", "type": "step", "stepType": "fixture", "duration": 0, "location": ""}, {"id": "fltua546uma7raayxi2b", "title": "Fixture \"context\"", "type": "step", "stepType": "fixture", "duration": 799, "location": "", "subs": [{"id": "lw5qt53xar4zy542y26b", "title": "Close context", "type": "step", "stepType": "pw:api", "duration": 35, "location": ""}]}, {"id": "94yq1638ffadz4p7cm26", "title": "Fixture \"storageState\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "tests/ApplicationTemplate.spec.ts:29:6"}]}, {"id": "mxv8kkzgpv2ot557v5bd", "title": "Worker Cleanup", "type": "step", "stepType": "hook", "duration": 147, "location": "", "subs": [{"id": "rq11aw2sti64zrth3p4d", "title": "Fixture \"browser\"", "type": "step", "stepType": "fixture", "duration": 146, "location": ""}]}], "attachments": [{"name": "screenshot", "path": "attachments/c00ac42b5af4d8c93caa1b3ec58f9a6475c48b5d.png", "contentType": "image/png", "retry": 0}, {"name": "error-context", "path": "attachments/343bcb452c3c09a86dce1a7e7a4815357b8893d7.md", "contentType": "text/markdown", "retry": 0, "content": "# Page snapshot\n\n```yaml\n- main [ref=e4]:\n  - navigation [ref=e5]:\n    - complementary [ref=e6]:\n      - link \"QA devtron QA-ENT-0\" [ref=e7] [cursor=pointer]:\n        - /url: /dashboard/app\n        - generic [ref=e8] [cursor=pointer]:\n          - generic [ref=e9] [cursor=pointer]:\n            - generic [ref=e11] [cursor=pointer]: QA\n            - img [ref=e12] [cursor=pointer]\n          - generic [ref=e15] [cursor=pointer]:\n            - img \"devtron\" [ref=e16] [cursor=pointer]\n            - generic [ref=e17] [cursor=pointer]: QA-ENT-0\n      - link \"Applications\" [ref=e18] [cursor=pointer]:\n        - /url: /dashboard/app\n        - generic [ref=e20] [cursor=pointer]:\n          - img [ref=e22] [cursor=pointer]\n          - generic [ref=e25] [cursor=pointer]: Applications\n      - link \"Jobs\" [ref=e26] [cursor=pointer]:\n        - /url: /dashboard/job\n        - generic [ref=e27] [cursor=pointer]:\n          - img [ref=e29] [cursor=pointer]\n          - generic [ref=e32] [cursor=pointer]: Jobs\n      - link \"Application Groups\" [ref=e33] [cursor=pointer]:\n        - /url: /dashboard/application-group\n        - generic [ref=e34] [cursor=pointer]:\n          - img [ref=e36] [cursor=pointer]\n          - generic [ref=e39] [cursor=pointer]: Application Groups\n      - link \"Software Distribution Hub\" [ref=e40] [cursor=pointer]:\n        - /url: /dashboard/software-distribution-hub\n        - generic [ref=e41] [cursor=pointer]:\n          - img [ref=e43] [cursor=pointer]\n          - generic [ref=e46] [cursor=pointer]: Software Distribution Hub\n      - link \"Resource Browser\" [ref=e47] [cursor=pointer]:\n        - /url: /dashboard/resource-browser\n        - generic [ref=e48] [cursor=pointer]:\n          - img [ref=e50] [cursor=pointer]\n          - generic [ref=e53] [cursor=pointer]: Resource Browser\n      - link \"Resource Watcher\" [ref=e54] [cursor=pointer]:\n        - /url: /dashboard/resource-watcher\n        - generic [ref=e55] [cursor=pointer]:\n          - img [ref=e57] [cursor=pointer]\n          - generic [ref=e63] [cursor=pointer]: Resource Watcher\n      - link \"Chart Store\" [ref=e64] [cursor=pointer]:\n        - /url: /dashboard/chart-store\n        - generic [ref=e65] [cursor=pointer]:\n          - img [ref=e67] [cursor=pointer]\n          - generic [ref=e70] [cursor=pointer]: Chart Store\n      - link \"Security\" [ref=e71] [cursor=pointer]:\n        - /url: /dashboard/security\n        - generic [ref=e72] [cursor=pointer]:\n          - img [ref=e74] [cursor=pointer]\n          - generic [ref=e77] [cursor=pointer]: Security\n      - link \"Bulk Edit\" [ref=e78] [cursor=pointer]:\n        - /url: /dashboard/bulk-edits\n        - generic [ref=e79] [cursor=pointer]:\n          - img [ref=e81] [cursor=pointer]\n          - generic [ref=e84] [cursor=pointer]: Bulk Edit\n      - link \"Global Configurations\" [ref=e85] [cursor=pointer]:\n        - /url: /dashboard/global-config\n        - generic [ref=e86] [cursor=pointer]:\n          - img [ref=e88] [cursor=pointer]\n          - generic [ref=e92] [cursor=pointer]: Global Configurations\n  - generic [ref=e93]:\n    - generic [ref=e94]:\n      - generic [ref=e97]:\n        - img [ref=e98]\n        - paragraph [ref=e100]: Hello World this is for the testing\n      - button \"Close banner\" [ref=e102] [cursor=pointer]:\n        - img [ref=e104] [cursor=pointer]\n    - generic [ref=e107]:\n      - generic [ref=e108]:\n        - heading \"devtron apps / ui-automwwsm 9/9 Environments F Help a\" [level=1] [ref=e109]:\n          - generic [ref=e110]:\n            - link \"devtron apps\" [ref=e111] [cursor=pointer]:\n              - /url: /dashboard/app\n              - generic [ref=e112] [cursor=pointer]: devtron apps\n            - generic [ref=e113]: /\n            - generic [ref=e118]:\n              - log [ref=e120]\n              - generic [ref=e121] [cursor=pointer]:\n                - generic [ref=e124] [cursor=pointer]:\n                  - generic [ref=e125] [cursor=pointer]: ui-automwwsm\n                  - combobox [ref=e127]\n                - img [ref=e130] [cursor=pointer]\n            - generic [ref=e133]:\n              - log [ref=e135]\n              - generic [ref=e136]:\n                - generic [ref=e137] [cursor=pointer]:\n                  - generic [ref=e138] [cursor=pointer]:\n                    - img [ref=e139] [cursor=pointer]\n                    - generic [ref=e141] [cursor=pointer]: 9/9 Environments\n                    - generic [ref=e142] [cursor=pointer]: F\n                  - combobox [ref=e144]\n                - img [ref=e147]\n          - generic [ref=e149]:\n            - button \"Help\" [ref=e151] [cursor=pointer]:\n              - img [ref=e152] [cursor=pointer]\n              - generic [ref=e154] [cursor=pointer]: Help\n              - img [ref=e155] [cursor=pointer]\n            - button \"a\" [ref=e159] [cursor=pointer]:\n              - generic [ref=e160] [cursor=pointer]: a\n              - img [ref=e161] [cursor=pointer]\n        - tablist [ref=e164]:\n          - listitem [ref=e165] [cursor=pointer]:\n            - link \"Overview\" [ref=e166] [cursor=pointer]:\n              - /url: /dashboard/app/616/overview\n              - generic [ref=e168] [cursor=pointer]: Overview\n          - listitem [ref=e169] [cursor=pointer]:\n            - link \"App Details\" [ref=e170] [cursor=pointer]:\n              - /url: /dashboard/app/616/details\n              - generic [ref=e172] [cursor=pointer]: App Details\n          - listitem [ref=e173] [cursor=pointer]:\n            - link \"Build & Deploy\" [ref=e174] [cursor=pointer]:\n              - /url: /dashboard/app/616/trigger\n              - generic [ref=e176] [cursor=pointer]: Build & Deploy\n          - listitem [ref=e177] [cursor=pointer]:\n            - link \"Build History\" [ref=e178] [cursor=pointer]:\n              - /url: /dashboard/app/616/ci-details\n              - generic [ref=e180] [cursor=pointer]: Build History\n          - listitem [ref=e181] [cursor=pointer]:\n            - link \"Deployment History\" [ref=e182] [cursor=pointer]:\n              - /url: /dashboard/app/616/cd-details\n              - generic [ref=e184] [cursor=pointer]: Deployment History\n          - listitem [ref=e185] [cursor=pointer]:\n            - link \"Deployment Metrics\" [ref=e186] [cursor=pointer]:\n              - /url: /dashboard/app/616/deployment-metrics\n              - generic [ref=e188] [cursor=pointer]: Deployment Metrics\n          - listitem [ref=e189] [cursor=pointer]:\n            - link \"Configurations\" [active] [ref=e190] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit\n              - generic [ref=e191] [cursor=pointer]:\n                - img [ref=e192] [cursor=pointer]\n                - generic [ref=e194] [cursor=pointer]: Configurations\n      - generic [ref=e196]:\n        - generic [ref=e197]:\n          - generic [ref=e198]:\n            - link \"Git Repository\" [ref=e199] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit/materials\n              - generic [ref=e200] [cursor=pointer]: Git Repository\n            - link \"Build Configuration\" [ref=e201] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit/docker-build-config\n              - generic [ref=e202] [cursor=pointer]: Build Configuration\n            - link \"Base Configurations\" [ref=e203] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit/base-config\n              - generic [ref=e204] [cursor=pointer]: Base Configurations\n            - link \"GitOps Configuration\" [ref=e205] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit/gitops-config\n              - generic [ref=e206] [cursor=pointer]: GitOps Configuration\n            - link \"Workflow Editor\" [ref=e207] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit/workflow\n              - generic [ref=e208] [cursor=pointer]: Workflow Editor\n            - link \"External Links\" [ref=e211] [cursor=pointer]:\n              - /url: /dashboard/app/616/edit/external-links\n              - generic [ref=e212] [cursor=pointer]: External Links\n            - generic [ref=e213]:\n              - generic [ref=e215]: Environment Overrides\n              - generic [ref=e217]:\n                - link \"automation\" [ref=e218] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/14\n                  - generic [ref=e219] [cursor=pointer]: automation\n                - link \"devtron-demo\" [ref=e220] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/1\n                  - generic [ref=e221] [cursor=pointer]: devtron-demo\n                - link \"env1\" [ref=e222] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/2\n                  - generic [ref=e223] [cursor=pointer]: env1\n                - link \"env2\" [ref=e224] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/3\n                  - generic [ref=e225] [cursor=pointer]: env2\n                - link \"env3\" [ref=e226] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/4\n                  - generic [ref=e227] [cursor=pointer]: env3\n                - link \"env4\" [ref=e228] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/5\n                  - generic [ref=e229] [cursor=pointer]: env4\n                - link \"env5\" [ref=e230] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/6\n                  - generic [ref=e231] [cursor=pointer]: env5\n                - link \"env6\" [ref=e232] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/7\n                  - generic [ref=e233] [cursor=pointer]: env6\n                - link \"env7\" [ref=e234] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/env-override/8\n                  - generic [ref=e235] [cursor=pointer]: env7\n          - button \"Delete Application\" [ref=e238] [cursor=pointer]:\n            - generic [ref=e239] [cursor=pointer]: Delete Application\n        - generic [ref=e241]:\n          - generic [ref=e242]:\n            - generic [ref=e243]:\n              - heading \"Workflow Editor\" [level=1] [ref=e244]\n              - button \"Info Icon\" [ref=e245] [cursor=pointer]:\n                - img [ref=e246] [cursor=pointer]\n            - button \"New Workflow\" [ref=e249] [cursor=pointer]:\n              - img [ref=e251] [cursor=pointer]\n              - generic [ref=e253] [cursor=pointer]: New Workflow\n          - generic [ref=e254]:\n            - generic [ref=e255]:\n              - generic [ref=e256]: wf-616-wj85\n              - generic [ref=e257]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e258] [cursor=pointer]\n                - link [ref=e264] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1050/edit\n            - img [ref=e267]:\n              - generic [ref=e279]:\n                - img [ref=e280]\n                - generic [ref=e282]:\n                  - generic [ref=e283]: /sample-html\n                  - generic [ref=e285]:\n                    - img [ref=e287]\n                    - generic [ref=e289]:\n                      - generic [ref=e290]: main\n                      - img [ref=e291]\n              - generic [ref=e299]:\n                - img [ref=e300]\n                - generic [ref=e302]:\n                  - generic [ref=e303]: /sample-html\n                  - generic [ref=e305]:\n                    - img [ref=e307]\n                    - generic [ref=e309]:\n                      - generic [ref=e310]: main\n                      - img [ref=e311]\n              - link \"Auto Build ci-616-ykho Add deployment pipeline Delete pipeline\" [ref=e319] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1050/ci-pipeline/835\n                - generic [ref=e320] [cursor=pointer]:\n                  - generic [ref=e321] [cursor=pointer]: Auto\n                  - generic [ref=e322] [cursor=pointer]:\n                    - generic [ref=e323] [cursor=pointer]:\n                      - generic [ref=e324] [cursor=pointer]: Build\n                      - generic [ref=e325] [cursor=pointer]: ci-616-ykho\n                    - img [ref=e327] [cursor=pointer]\n                    - generic [ref=e337] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e340] [cursor=pointer]:\n                        - img [ref=e342] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e345] [cursor=pointer]:\n                        - img [ref=e347] [cursor=pointer]\n              - link \"Auto Pre-deploy, Deploy devtron-demo Add deployment pipeline Delete pipeline\" [ref=e350] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1050/ci-pipeline/835/cd-pipeline/1207\n                - generic [ref=e351] [cursor=pointer]:\n                  - generic [ref=e352] [cursor=pointer]: Auto\n                  - generic [ref=e353] [cursor=pointer]:\n                    - generic [ref=e354] [cursor=pointer]:\n                      - generic [ref=e355] [cursor=pointer]: Pre-deploy, Deploy\n                      - generic [ref=e356] [cursor=pointer]: devtron-demo\n                    - img [ref=e358] [cursor=pointer]\n                    - generic [ref=e381] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e384] [cursor=pointer]:\n                        - img [ref=e386] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e389] [cursor=pointer]:\n                        - img [ref=e391] [cursor=pointer]\n              - link \"Auto Pre-deploy, Deploy, Post-deploy automation Add deployment pipeline Delete pipeline\" [ref=e394] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1050/ci-pipeline/835/cd-pipeline/1208\n                - generic [ref=e395] [cursor=pointer]:\n                  - generic [ref=e396] [cursor=pointer]: Auto\n                  - generic [ref=e397] [cursor=pointer]:\n                    - generic [ref=e398] [cursor=pointer]:\n                      - generic [ref=e399] [cursor=pointer]: Pre-deploy, Deploy, Post-deploy\n                      - generic [ref=e400] [cursor=pointer]: automation\n                    - img [ref=e402] [cursor=pointer]\n                    - generic [ref=e425] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e428] [cursor=pointer]:\n                        - img [ref=e430] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e433] [cursor=pointer]:\n                        - img [ref=e435] [cursor=pointer]\n          - generic [ref=e437]:\n            - generic [ref=e438]:\n              - generic [ref=e439]: wf-616-bd0z\n              - generic [ref=e440]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e441] [cursor=pointer]\n                - link [ref=e447] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1051/edit\n            - img [ref=e450]:\n              - generic [ref=e459]:\n                - img [ref=e460]\n                - generic [ref=e462]:\n                  - generic [ref=e463]: /sample-html\n                  - generic [ref=e465]:\n                    - img [ref=e467]\n                    - generic [ref=e469]:\n                      - generic [ref=e470]: .*\n                      - img [ref=e471]\n              - generic [ref=e479]:\n                - img [ref=e480]\n                - generic [ref=e482]:\n                  - generic [ref=e483]: /sample-html\n                  - generic [ref=e485]:\n                    - img [ref=e487]\n                    - generic [ref=e489]:\n                      - generic [ref=e490]: main\n                      - img [ref=e491]\n              - link \"Auto Build ci-616-cr3f Add deployment pipeline Delete pipeline\" [ref=e499] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1051/ci-pipeline/836\n                - generic [ref=e500] [cursor=pointer]:\n                  - generic [ref=e501] [cursor=pointer]: Auto\n                  - generic [ref=e502] [cursor=pointer]:\n                    - generic [ref=e503] [cursor=pointer]:\n                      - generic [ref=e504] [cursor=pointer]: Build\n                      - generic [ref=e505] [cursor=pointer]: ci-616-cr3f\n                    - img [ref=e507] [cursor=pointer]\n                    - generic [ref=e517] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e520] [cursor=pointer]:\n                        - img [ref=e522] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e525] [cursor=pointer]:\n                        - img [ref=e527] [cursor=pointer]\n              - link \"Auto Deploy env1 Add deployment pipeline Delete pipeline\" [ref=e530] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1051/ci-pipeline/836/cd-pipeline/1209\n                - generic [ref=e531] [cursor=pointer]:\n                  - generic [ref=e532] [cursor=pointer]: Auto\n                  - generic [ref=e533] [cursor=pointer]:\n                    - generic [ref=e534] [cursor=pointer]:\n                      - generic [ref=e535] [cursor=pointer]: Deploy\n                      - generic [ref=e536] [cursor=pointer]: env1\n                    - img [ref=e538] [cursor=pointer]\n                    - generic [ref=e561] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e564] [cursor=pointer]:\n                        - img [ref=e566] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e569] [cursor=pointer]:\n                        - img [ref=e571] [cursor=pointer]\n          - generic [ref=e573]:\n            - generic [ref=e574]:\n              - generic [ref=e575]: wf-616-6zzf\n              - generic [ref=e576]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e577] [cursor=pointer]\n                - link [ref=e583] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1052/edit\n            - img [ref=e586]:\n              - generic [ref=e595]:\n                - img [ref=e596]\n                - generic [ref=e598]:\n                  - generic [ref=e599]: /sample-html\n                  - generic [ref=e601]:\n                    - img [ref=e603]\n                    - generic [ref=e605]:\n                      - generic [ref=e606]: Pull Request\n                      - img [ref=e607]\n              - generic [ref=e615] [cursor=pointer]:\n                - img [ref=e616] [cursor=pointer]\n                - generic [ref=e618] [cursor=pointer]:\n                  - generic [ref=e619] [cursor=pointer]: /sample-html\n                  - generic [ref=e622] [cursor=pointer]:\n                    - generic [ref=e623] [cursor=pointer]: Not Configured\n                    - img [ref=e624] [cursor=pointer]\n              - link \"Auto Build ci-616-sqpn Add deployment pipeline Delete pipeline\" [ref=e632] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1052/ci-pipeline/837\n                - generic [ref=e633] [cursor=pointer]:\n                  - generic [ref=e634] [cursor=pointer]: Auto\n                  - generic [ref=e635] [cursor=pointer]:\n                    - generic [ref=e636] [cursor=pointer]:\n                      - generic [ref=e637] [cursor=pointer]: Build\n                      - generic [ref=e638] [cursor=pointer]: ci-616-sqpn\n                    - img [ref=e640] [cursor=pointer]\n                    - generic [ref=e650] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e653] [cursor=pointer]:\n                        - img [ref=e655] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e658] [cursor=pointer]:\n                        - img [ref=e660] [cursor=pointer]\n              - link \"Auto Deploy env2 Add deployment pipeline Delete pipeline\" [ref=e663] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1052/ci-pipeline/837/cd-pipeline/1210\n                - generic [ref=e664] [cursor=pointer]:\n                  - generic [ref=e665] [cursor=pointer]: Auto\n                  - generic [ref=e666] [cursor=pointer]:\n                    - generic [ref=e667] [cursor=pointer]:\n                      - generic [ref=e668] [cursor=pointer]: Deploy\n                      - generic [ref=e669] [cursor=pointer]: env2\n                    - img [ref=e671] [cursor=pointer]\n                    - generic [ref=e694] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e697] [cursor=pointer]:\n                        - img [ref=e699] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e702] [cursor=pointer]:\n                        - img [ref=e704] [cursor=pointer]\n          - generic [ref=e706]:\n            - generic [ref=e707]:\n              - generic [ref=e708]: wf-616-hfju\n              - generic [ref=e709]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e710] [cursor=pointer]\n                - link [ref=e716] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1053/edit\n            - img [ref=e719]:\n              - generic [ref=e728]:\n                - img [ref=e729]\n                - generic [ref=e731]:\n                  - generic [ref=e732]: /sample-html\n                  - generic [ref=e734]:\n                    - img [ref=e736]\n                    - generic [ref=e738]:\n                      - generic [ref=e739]: Tag Creation\n                      - img [ref=e740]\n              - generic [ref=e748] [cursor=pointer]:\n                - img [ref=e749] [cursor=pointer]\n                - generic [ref=e751] [cursor=pointer]:\n                  - generic [ref=e752] [cursor=pointer]: /sample-html\n                  - generic [ref=e755] [cursor=pointer]:\n                    - generic [ref=e756] [cursor=pointer]: Not Configured\n                    - img [ref=e757] [cursor=pointer]\n              - link \"Auto Build ci-616-as9a Add deployment pipeline Delete pipeline\" [ref=e765] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1053/ci-pipeline/838\n                - generic [ref=e766] [cursor=pointer]:\n                  - generic [ref=e767] [cursor=pointer]: Auto\n                  - generic [ref=e768] [cursor=pointer]:\n                    - generic [ref=e769] [cursor=pointer]:\n                      - generic [ref=e770] [cursor=pointer]: Build\n                      - generic [ref=e771] [cursor=pointer]: ci-616-as9a\n                    - img [ref=e773] [cursor=pointer]\n                    - generic [ref=e783] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e786] [cursor=pointer]:\n                        - img [ref=e788] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e791] [cursor=pointer]:\n                        - img [ref=e793] [cursor=pointer]\n              - link \"Auto Deploy env3 Add deployment pipeline Delete pipeline\" [ref=e796] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1053/ci-pipeline/838/cd-pipeline/1211\n                - generic [ref=e797] [cursor=pointer]:\n                  - generic [ref=e798] [cursor=pointer]: Auto\n                  - generic [ref=e799] [cursor=pointer]:\n                    - generic [ref=e800] [cursor=pointer]:\n                      - generic [ref=e801] [cursor=pointer]: Deploy\n                      - generic [ref=e802] [cursor=pointer]: env3\n                    - img [ref=e804] [cursor=pointer]\n                    - generic [ref=e827] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e830] [cursor=pointer]:\n                        - img [ref=e832] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e835] [cursor=pointer]:\n                        - img [ref=e837] [cursor=pointer]\n          - generic [ref=e839]:\n            - generic [ref=e840]:\n              - generic [ref=e841]: wf-616-ma89\n              - generic [ref=e842]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e843] [cursor=pointer]\n                - link [ref=e849] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1054/edit\n            - img [ref=e852]:\n              - generic [ref=e861]:\n                - img [ref=e862]\n                - generic [ref=e864]:\n                  - generic [ref=e865]: /sample-html\n                  - generic [ref=e867]:\n                    - img [ref=e869]\n                    - generic [ref=e871]:\n                      - generic [ref=e872]: main\n                      - img [ref=e873]\n              - generic [ref=e881]:\n                - img [ref=e882]\n                - generic [ref=e884]:\n                  - generic [ref=e885]: /sample-html\n                  - generic [ref=e887]:\n                    - img [ref=e889]\n                    - generic [ref=e891]:\n                      - generic [ref=e892]: main\n                      - img [ref=e893]\n              - 'link \"Auto Build: Linked ci-616-bt3e Add deployment pipeline Delete pipeline\" [ref=e901] [cursor=pointer]':\n                - /url: /dashboard/app/616/edit/workflow/1054/linked-ci/839\n                - generic [ref=e902] [cursor=pointer]:\n                  - generic [ref=e903] [cursor=pointer]: Auto\n                  - generic [ref=e904] [cursor=pointer]:\n                    - generic [ref=e905] [cursor=pointer]:\n                      - generic [ref=e906] [cursor=pointer]: \"Build: Linked\"\n                      - generic [ref=e907] [cursor=pointer]: ci-616-bt3e\n                    - img [ref=e908] [cursor=pointer]\n                    - generic [ref=e914] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e917] [cursor=pointer]:\n                        - img [ref=e919] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e922] [cursor=pointer]:\n                        - img [ref=e924] [cursor=pointer]\n              - link \"Auto Deploy env4 Add deployment pipeline Delete pipeline\" [ref=e927] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1054/ci-pipeline/839/cd-pipeline/1212\n                - generic [ref=e928] [cursor=pointer]:\n                  - generic [ref=e929] [cursor=pointer]: Auto\n                  - generic [ref=e930] [cursor=pointer]:\n                    - generic [ref=e931] [cursor=pointer]:\n                      - generic [ref=e932] [cursor=pointer]: Deploy\n                      - generic [ref=e933] [cursor=pointer]: env4\n                    - img [ref=e935] [cursor=pointer]\n                    - generic [ref=e958] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e961] [cursor=pointer]:\n                        - img [ref=e963] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e966] [cursor=pointer]:\n                        - img [ref=e968] [cursor=pointer]\n          - generic [ref=e970]:\n            - generic [ref=e971]:\n              - generic [ref=e972]: wf-616-17b8\n              - generic [ref=e973]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e974] [cursor=pointer]\n                - link [ref=e980] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1055/edit\n            - img [ref=e983]:\n              - link \"Webhook External source Add deployment pipeline\" [ref=e987] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1055/webhook/239\n                - generic [ref=e989] [cursor=pointer]:\n                  - generic [ref=e990] [cursor=pointer]:\n                    - generic [ref=e991] [cursor=pointer]: Webhook\n                    - generic [ref=e992] [cursor=pointer]: External source\n                  - img [ref=e993] [cursor=pointer]\n                  - button \"Add deployment pipeline\" [ref=e1008] [cursor=pointer]:\n                    - img [ref=e1010] [cursor=pointer]\n              - link \"Auto Deploy env5 Add deployment pipeline Delete pipeline\" [ref=e1013] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1055/webhook/0/cd-pipeline/1213\n                - generic [ref=e1014] [cursor=pointer]:\n                  - generic [ref=e1015] [cursor=pointer]: Auto\n                  - generic [ref=e1016] [cursor=pointer]:\n                    - generic [ref=e1017] [cursor=pointer]:\n                      - generic [ref=e1018] [cursor=pointer]: Deploy\n                      - generic [ref=e1019] [cursor=pointer]: env5\n                    - img [ref=e1021] [cursor=pointer]\n                    - generic [ref=e1044] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e1047] [cursor=pointer]:\n                        - img [ref=e1049] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e1052] [cursor=pointer]:\n                        - img [ref=e1054] [cursor=pointer]\n          - generic [ref=e1056]:\n            - generic [ref=e1057]:\n              - generic [ref=e1058]: wf-616-p96n\n              - generic [ref=e1059]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e1060] [cursor=pointer]\n                - link [ref=e1066] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1056/edit\n            - img [ref=e1069]:\n              - button \"Sync with... env5 Add deployment pipeline Delete pipeline\" [ref=e1073] [cursor=pointer]:\n                - generic [ref=e1074] [cursor=pointer]:\n                  - img [ref=e1075] [cursor=pointer]\n                  - generic [ref=e1093] [cursor=pointer]:\n                    - generic [ref=e1094] [cursor=pointer]: Sync with...\n                    - paragraph [ref=e1095] [cursor=pointer]: env5\n                - generic [ref=e1096] [cursor=pointer]:\n                  - button \"Add deployment pipeline\" [ref=e1099] [cursor=pointer]:\n                    - img [ref=e1101] [cursor=pointer]\n                  - button \"Delete pipeline\" [ref=e1104] [cursor=pointer]:\n                    - img [ref=e1106] [cursor=pointer]\n              - link \"Auto Deploy env6 Add deployment pipeline Delete pipeline\" [ref=e1109] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1056/ci-pipeline/840/cd-pipeline/1214\n                - generic [ref=e1110] [cursor=pointer]:\n                  - generic [ref=e1111] [cursor=pointer]: Auto\n                  - generic [ref=e1112] [cursor=pointer]:\n                    - generic [ref=e1113] [cursor=pointer]:\n                      - generic [ref=e1114] [cursor=pointer]: Deploy\n                      - generic [ref=e1115] [cursor=pointer]: env6\n                    - img [ref=e1117] [cursor=pointer]\n                    - generic [ref=e1140] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e1143] [cursor=pointer]:\n                        - img [ref=e1145] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e1148] [cursor=pointer]:\n                        - img [ref=e1150] [cursor=pointer]\n          - generic [ref=e1152]:\n            - generic [ref=e1153]:\n              - generic [ref=e1154]: wf-616-3pq4\n              - generic [ref=e1155]:\n                - img \"6605A5D1-3E7E-49E0-A6B7-0384A91D2569\" [ref=e1156] [cursor=pointer]\n                - link [ref=e1162] [cursor=pointer]:\n                  - /url: /dashboard/app/616/edit/workflow/1057/edit\n            - img [ref=e1165]:\n              - generic [ref=e1174]:\n                - img [ref=e1175]\n                - generic [ref=e1177]:\n                  - generic [ref=e1178]: /sample-html\n                  - generic [ref=e1180]:\n                    - img [ref=e1182]\n                    - generic [ref=e1184]:\n                      - generic [ref=e1185]: main\n                      - img [ref=e1186]\n              - generic [ref=e1194]:\n                - img [ref=e1195]\n                - generic [ref=e1197]:\n                  - generic [ref=e1198]: /sample-html\n                  - generic [ref=e1200]:\n                    - img [ref=e1202]\n                    - generic [ref=e1204]:\n                      - generic [ref=e1205]: main\n                      - img [ref=e1206]\n              - link \"Manual Job ci-616-4hqq Add deployment pipeline Delete pipeline\" [ref=e1214] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1057/ci-job/841\n                - generic [ref=e1215] [cursor=pointer]:\n                  - generic [ref=e1216] [cursor=pointer]: Manual\n                  - generic [ref=e1217] [cursor=pointer]:\n                    - generic [ref=e1218] [cursor=pointer]:\n                      - generic [ref=e1219] [cursor=pointer]: Job\n                      - generic [ref=e1220] [cursor=pointer]: ci-616-4hqq\n                    - img [ref=e1222] [cursor=pointer]\n                    - generic [ref=e1230] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e1233] [cursor=pointer]:\n                        - img [ref=e1235] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e1238] [cursor=pointer]:\n                        - img [ref=e1240] [cursor=pointer]\n              - link \"Manual Deploy env7 Add deployment pipeline Delete pipeline\" [ref=e1243] [cursor=pointer]:\n                - /url: /dashboard/app/616/edit/workflow/1057/ci-pipeline/841/cd-pipeline/1215\n                - generic [ref=e1244] [cursor=pointer]:\n                  - generic [ref=e1245] [cursor=pointer]: Manual\n                  - generic [ref=e1246] [cursor=pointer]:\n                    - generic [ref=e1247] [cursor=pointer]:\n                      - generic [ref=e1248] [cursor=pointer]: Deploy\n                      - generic [ref=e1249] [cursor=pointer]: env7\n                    - img [ref=e1251] [cursor=pointer]\n                    - generic [ref=e1274] [cursor=pointer]:\n                      - button \"Add deployment pipeline\" [ref=e1277] [cursor=pointer]:\n                        - img [ref=e1279] [cursor=pointer]\n                      - button \"Delete pipeline\" [ref=e1282] [cursor=pointer]:\n                        - img [ref=e1284] [cursor=pointer]\n```"}, {"name": "trace", "path": "attachments/a8a0d8afc416c20c4ad3a1cbd2bab88f25c98ebc.zip", "contentType": "application/zip", "retry": 0}], "errorNum": 1, "errorId": "rdn5t09aev0v54ww4gyb"}, {"id": "80b24f1e1e9a9cbe313c", "title": "application temoplate with changes in runtime and config as well", "type": "case", "caseType": "skipped", "ok": true, "outcome": "skipped", "expectedStatus": "passed", "location": "tests/ApplicationTemplate.spec.ts:173:5", "timestamps": [1756385951939, 1756385951941], "duration": 2, "tags": ["@globalConfigurations"], "timeout": 30000, "retry": 0, "status": "skipped", "stepNum": 0, "stepFailed": 0, "stepSubs": false}], "location": "tests/ApplicationTemplate.spec.ts:0:0"}]}], "formatters": {}, "suiteTypes": ["project", "file", "describe", "shard"], "caseTypes": ["failed", "flaky", "skipped", "passed"], "traceViewerUrl": "https://trace.playwright.dev/?trace={traceUrl}", "mermaid": null, "groupOptions": null, "tags": {"globalConfigurations": {"value": 3}}, "summary": {"tests": {"name": "Tests", "value": 4, "nav": true, "id": "tests"}, "failed": {"name": "Failed", "value": 1, "color": "#d00", "nav": true, "id": "failed", "percent": "25.0%"}, "flaky": {"name": "<PERSON><PERSON><PERSON>", "value": 0, "color": "orange", "nav": true, "id": "flaky", "percent": "0.0%"}, "skipped": {"name": "Skipped", "value": 1, "color": "gray", "nav": true, "id": "skipped", "percent": "25.0%"}, "passed": {"name": "Passed", "value": 2, "color": "green", "nav": true, "id": "passed", "percent": "50.0%"}, "steps": {"name": "Steps", "value": 237, "id": "steps"}, "suites": {"name": "Suites", "value": 2, "id": "suites"}, "projects": {"name": "Projects", "description": "Suite type is project", "value": 2, "id": "projects"}, "files": {"name": "Files", "description": "Suite type is file", "value": 2, "id": "files"}, "describes": {"name": "Describes", "description": "Suite type is describe", "value": 0, "id": "describes"}, "shards": {"name": "Shards", "description": "Suite type is shard (only in shading mode)", "value": 0, "id": "shards"}, "errors": {"name": "Errors", "icon": "error", "value": 3, "id": "errors"}, "retries": {"name": "Retries", "icon": "retry", "value": 0, "id": "retries"}, "logs": {"name": "Logs", "icon": "log", "value": 51, "id": "logs"}, "attachments": {"name": "Attachments", "icon": "attachment", "value": 5, "id": "attachments"}, "artifacts": {"name": "Artifacts", "value": 0}}, "pieChart": {"ns": "mcr-pie", "width": 360, "height": 150, "margin": 10, "svg": "<svg viewBox=\"0 0 360 150\" width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\"><g class=\"mcr-pie-path-failed\"><path d=\"M75,75 L75,10 A65,65 0 0 1 140,75 z\" fill=\"#d00\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"7.1,-7.1\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g class=\"mcr-pie-path-flaky\"><path d=\"\" fill=\"orange\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"10,0\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g class=\"mcr-pie-path-skipped\"><path d=\"M75,75 L140,75 A65,65 0 0 1 75,140 z\" fill=\"gray\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"7.1,7.1\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g class=\"mcr-pie-path-passed\"><path d=\"M75,75 L75,140 A65,65 0 1 1 75,10 z\" fill=\"green\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"-10,0\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g transform=\"translate(160 15)\"><g class=\"mcr-pie-legend-failed\" transform=\"translate(0 0)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"#d00\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Failed</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">1</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">25.0%</text></g><g class=\"mcr-pie-legend-flaky\" transform=\"translate(0 30)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"orange\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Flaky</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">0</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">0.0%</text></g><g class=\"mcr-pie-legend-skipped\" transform=\"translate(0 60)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"gray\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Skipped</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">1</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">25.0%</text></g><g class=\"mcr-pie-legend-passed\" transform=\"translate(0 90)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"green\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Passed</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">2</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">50.0%</text></g></g></svg>"}, "htmlPath": "monocart-report/index.html"}