{"name": "playwright automation report", "logo": null, "date": 1758349786364, "dateH": "9/20/2025, 11:59:46 AM", "duration": 25808, "durationH": "25.8s", "cwd": "/Users/<USER>/Desktop/ui-automation/dashboard-automation", "outputFile": "/Users/<USER>/Desktop/ui-automation/dashboard-automation/monocart-report/index.html", "outputDir": "/Users/<USER>/Desktop/ui-automation/dashboard-automation/monocart-report", "metadata": {"env": "enterprise", "url": "https://staging.devtron.info/dashboard", "actualWorkers": 1}, "system": {"cpu": {"color": "#117DBB", "count": 8, "model": "Apple M1", "speed": 2400}, "mem": {"color": "#8B12AE", "total": 8589934592}, "arch": "arm64", "platform": "darwin", "release": "24.6.0", "type": "<PERSON>", "version": "Darwin Kernel Version 24.6.0: Mon Jul 14 11:30:34 PDT 2025; root:xnu-11417.140.69~1/RELEASE_ARM64_T8103", "uptime": 2446521, "hostname": "apples-MacBook-Pro.local", "pid": 82695, "node": "22.18.0", "v8": "***********-node.27", "timestampStart": 1758349786364, "ticks": [{"cpu": {"percent": 45.4}, "mem": {"free": 146440192}, "timestamp": 1758349788367}, {"cpu": {"percent": 52.08}, "mem": {"free": 164724736}, "timestamp": 1758349790690}, {"cpu": {"percent": 58.24}, "mem": {"free": 178339840}, "timestamp": 1758349792693}, {"cpu": {"percent": 29.13}, "mem": {"free": 167706624}, "timestamp": 1758349794696}, {"cpu": {"percent": 39.57}, "mem": {"free": 154435584}, "timestamp": 1758349796699}, {"cpu": {"percent": 34.05}, "mem": {"free": 125566976}, "timestamp": 1758349798703}, {"cpu": {"percent": 38.79}, "mem": {"free": 121421824}, "timestamp": 1758349800707}, {"cpu": {"percent": 40.33}, "mem": {"free": 145293312}, "timestamp": 1758349802714}, {"cpu": {"percent": 22.31}, "mem": {"free": 143818752}, "timestamp": 1758349804716}, {"cpu": {"percent": 18.24}, "mem": {"free": 116359168}, "timestamp": 1758349806719}, {"cpu": {"percent": 12.17}, "mem": {"free": 133595136}, "timestamp": 1758349808723}, {"cpu": {"percent": 51.76}, "mem": {"free": 522469376}, "timestamp": 1758349810725}, {"cpu": {"percent": 58.7}, "mem": {"free": 183123968}, "timestamp": 1758349812157}], "workers": 1, "jobs": [{"caseId": "03d9d9cd127a548dfb86", "parallelIndex": -1, "workerIndex": -1, "timestamp": 1758349789722, "duration": 0}, {"caseId": "c8c61479c493b81d4786", "parallelIndex": 0, "workerIndex": 0, "timestamp": 1758349790422, "duration": 19946}], "cwd": "/Users/<USER>/Desktop/ui-automation/dashboard-automation", "configFile": "playwright.config.ts", "playwright": "1.55.0", "monocart": "2.9.15", "testDir": "tests", "outputFile": "monocart-report/index.html", "outputDir": "monocart-report", "timestampEnd": 1758349812172}, "artifacts": [], "trends": [], "columns": [{"id": "caseType", "name": "", "width": 36, "sortable": false, "align": "center", "formatter": "iconCaseType", "detailed": false}, {"id": "title", "name": "Title", "searchable": true, "width": 350, "maxWidth": 1230, "detailed": false}, {"id": "type", "name": "Type", "width": 50, "sortable": false, "align": "center", "formatter": "iconType", "detailed": false}, {"id": "duration", "name": "Duration", "align": "right", "sortAsc": false, "formatter": "duration", "detailed": false}, {"id": "errors", "name": "Errors", "width": 60, "align": "center", "comparer": "errors", "formatter": "errors", "detailed": false}, {"id": "logs", "name": "Logs", "width": 60, "align": "center", "comparer": "logs", "formatter": "logs", "detailed": false}, {"id": "annotations", "name": "Annotations", "width": 100, "markdown": true, "searchable": true, "comparer": "annotations", "formatter": "annotations", "detailed": false}, {"id": "attachments", "name": "Attachments", "width": 100, "align": "center", "formatter": "attachments", "detailed": false}, {"id": "status", "name": "Status", "align": "center", "detailed": false}, {"id": "expectedStatus", "name": "Expected", "align": "center", "detailed": false}, {"id": "outcome", "name": "Outcome", "align": "center", "width": 85, "detailed": false}, {"id": "retry", "name": "Retry", "align": "center", "width": 50, "detailed": false}, {"id": "location", "name": "Location", "classMap": "mcr-location", "width": 200, "maxWidth": 1981, "detailed": false}], "rows": [{"id": "80437a44a661d1411742", "title": "setup", "type": "suite", "suiteType": "project", "caseNum": 1, "subs": [{"id": "956200db76d81c8e66bf", "title": "PreRequisite.spec.ts", "type": "suite", "suiteType": "file", "caseNum": 1, "subs": [{"id": "03d9d9cd127a548dfb86", "title": "pre-requisite data creation", "type": "case", "caseType": "skipped", "ok": true, "outcome": "skipped", "expectedStatus": "skipped", "location": "tests/PreRequisite.spec.ts:32:5", "timestamps": [1758349789722, 1758349789722], "duration": 0, "annotations": [{"type": "skip", "location": {"file": "/Users/<USER>/Desktop/ui-automation/dashboard-automation/tests/PreRequisite.spec.ts", "line": 28, "column": 8}}], "tags": [], "timeout": 300000, "retry": 0, "status": "skipped", "stepNum": 0, "stepFailed": 0, "stepSubs": false}], "location": "tests/PreRequisite.spec.ts:0:0"}]}, {"id": "188030263b30ef2614ea", "title": "vm1Automation", "type": "suite", "suiteType": "project", "caseNum": 1, "subs": [{"id": "69ffb4896e9cbe349567", "title": "ApplicationManagement/ChartStoreDeployment.spec.ts", "type": "suite", "suiteType": "file", "caseNum": 1, "subs": [{"id": "c8c61479c493b81d4786", "title": "just testing", "type": "case", "caseType": "passed", "ok": true, "outcome": "expected", "expectedStatus": "passed", "location": "tests/ApplicationManagement/ChartStoreDeployment.spec.ts:156:6", "logs": ["printing the index is 0\n", "locator is locator('//*[contains(@class,\\'navigation \\')]').locator('//*[@href=\"/dashboard/cost-visibility/overview\"]').first()\n"], "timestamps": [1758349790422, 1758349810368], "duration": 19946, "tags": ["@chartstore"], "timeout": 0, "retry": 0, "status": "passed", "stepNum": 21, "stepFailed": 0, "stepSubs": true, "subs": [{"id": "adxwkqlwlz15lur7127o", "title": "Before Hooks", "type": "step", "stepType": "hook", "duration": 2707, "location": "", "subs": [{"id": "nva18baitp0z8yxhefbs", "title": "Fixture \"browser\"", "type": "step", "stepType": "fixture", "duration": 778, "location": "", "subs": [{"id": "px1zh6a2fxa4i61j2efj", "title": "Launch browser", "type": "step", "stepType": "pw:api", "duration": 775, "location": ""}]}, {"id": "z8l97rsvw0h3nhtq4vkq", "title": "Fixture \"context\"", "type": "step", "stepType": "fixture", "duration": 1387, "location": "", "subs": [{"id": "eqb8jli52fzek7zuq04m", "title": "Create context", "type": "step", "stepType": "pw:api", "duration": 1382, "location": ""}]}, {"id": "b2zn4jnrtofcpmn1bm8k", "title": "Fixture \"page\"", "type": "step", "stepType": "fixture", "duration": 512, "location": "", "subs": [{"id": "61cdah9dx259c7cv80z3", "title": "Create page", "type": "step", "stepType": "pw:api", "duration": 510, "location": ""}]}, {"id": "eig8kuwggx6a91lom6bs", "title": "Fixture \"AllObjects\"", "type": "step", "stepType": "fixture", "duration": 20, "location": "utilities/Fixtures.ts:70:26"}]}, {"id": "mnh4exn5oauv2h3zollm", "title": "Navigate to \"/dashboard\"", "type": "step", "stepType": "pw:api", "duration": 6532, "location": "Pages/NavbarPage.ts:22:23"}, {"id": "ol6k3t3z11uyp1q70ofl", "title": "Click locator('//*[contains(@class,\\'navigation \\')]').locator('//*[@href=\"/dashboard/cost-visibility/overview\"]').first()", "type": "step", "stepType": "pw:api", "duration": 2901, "location": "Pages/NavbarPage.ts:24:36"}, {"id": "bpuaqzg05xgvs6bi6cju", "title": "Fill \"overview\" locator('//*[contains(@class,\\'navigation \\')]').locator(getByTestId('search-bar'))", "type": "step", "stepType": "pw:api", "duration": 626, "location": "Pages/NavbarPage.ts:32:74"}, {"id": "fbiy6rxljdwnkkbtmkn9", "title": "Press \"Enter\"", "type": "step", "stepType": "pw:api", "duration": 611, "location": "Pages/NavbarPage.ts:33:34"}, {"id": "2jonv3jzmza2g8q99gij", "title": "Click locator('//*[contains(@class,\\'nav-item\\') or @role=\"treeitem\"]').first()", "type": "step", "stepType": "pw:api", "duration": 717, "location": "Pages/NavbarPage.ts:34:67"}, {"id": "crdraaekfxpq9dem42jo", "title": "Click getByTestId('page-header-help-button')", "type": "step", "stepType": "pw:api", "duration": 636, "location": "Pages/NavbarPage.ts:27:35"}, {"id": "drqp1g5pmx2qvuletm0x", "title": "Pause", "type": "step", "stepType": "pw:api", "duration": 4426, "location": "tests/ApplicationManagement/ChartStoreDeployment.spec.ts:159:16"}, {"id": "zhpej8iyf8t68xv3cid1", "title": "After Hooks", "type": "step", "stepType": "hook", "duration": 501, "location": "", "subs": [{"id": "sdiaure98nvg60teaq3m", "title": "Fixture \"AllObjects\"", "type": "step", "stepType": "fixture", "duration": 1, "location": "utilities/Fixtures.ts:70:26"}, {"id": "vka0d3h4istjbd2v2dlx", "title": "Fixture \"page\"", "type": "step", "stepType": "fixture", "duration": 0, "location": ""}, {"id": "pil3p5oiu8azbhb37l2o", "title": "Fixture \"context\"", "type": "step", "stepType": "fixture", "duration": 490, "location": "", "subs": [{"id": "dyompy076h0s5vxyerfb", "title": "Close context", "type": "step", "stepType": "pw:api", "duration": 47, "location": ""}]}, {"id": "9uzszp8yv44k93akt692", "title": "Fixture \"storageState\"", "type": "step", "stepType": "fixture", "duration": 0, "location": "tests/ApplicationManagement/ChartStoreDeployment.spec.ts:16:6"}]}], "attachments": [{"name": "trace", "path": "attachments/b62be69978b63f6eb25c4eccc406d9ddcc00c452.zip", "contentType": "application/zip", "retry": 0}]}], "location": "tests/ApplicationManagement/ChartStoreDeployment.spec.ts:0:0"}]}], "formatters": {}, "suiteTypes": ["project", "file", "describe", "shard"], "caseTypes": ["failed", "flaky", "skipped", "passed"], "traceViewerUrl": "https://trace.playwright.dev/?trace={traceUrl}", "mermaid": null, "groupOptions": null, "tags": {"chartstore": {"value": 1}}, "summary": {"tests": {"name": "Tests", "value": 2, "nav": true, "id": "tests"}, "failed": {"name": "Failed", "value": 0, "color": "#d00", "nav": true, "id": "failed", "percent": "0.0%"}, "flaky": {"name": "<PERSON><PERSON><PERSON>", "value": 0, "color": "orange", "nav": true, "id": "flaky", "percent": "0.0%"}, "skipped": {"name": "Skipped", "value": 1, "color": "gray", "nav": true, "id": "skipped", "percent": "50.0%"}, "passed": {"name": "Passed", "value": 1, "color": "green", "nav": true, "id": "passed", "percent": "50.0%"}, "steps": {"name": "Steps", "value": 21, "id": "steps"}, "suites": {"name": "Suites", "value": 2, "id": "suites"}, "projects": {"name": "Projects", "description": "Suite type is project", "value": 2, "id": "projects"}, "files": {"name": "Files", "description": "Suite type is file", "value": 2, "id": "files"}, "describes": {"name": "Describes", "description": "Suite type is describe", "value": 0, "id": "describes"}, "shards": {"name": "Shards", "description": "Suite type is shard (only in shading mode)", "value": 0, "id": "shards"}, "errors": {"name": "Errors", "icon": "error", "value": 0, "id": "errors"}, "retries": {"name": "Retries", "icon": "retry", "value": 0, "id": "retries"}, "logs": {"name": "Logs", "icon": "log", "value": 2, "id": "logs"}, "attachments": {"name": "Attachments", "icon": "attachment", "value": 1, "id": "attachments"}, "artifacts": {"name": "Artifacts", "value": 0}}, "pieChart": {"ns": "mcr-pie", "width": 360, "height": 150, "margin": 10, "svg": "<svg viewBox=\"0 0 360 150\" width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\"><g class=\"mcr-pie-path-failed\"><path d=\"\" fill=\"#d00\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"0,-10\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g class=\"mcr-pie-path-flaky\"><path d=\"\" fill=\"orange\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"0,-10\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g class=\"mcr-pie-path-skipped\"><path d=\"M75,75 L75,10 A65,65 0 1 1 75,140 z\" fill=\"gray\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"10,0\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g class=\"mcr-pie-path-passed\"><path d=\"M75,75 L75,140 A65,65 0 1 1 75,10 z\" fill=\"green\" opacity=\"0.8\" /><animateTransform from=\"0,0\" to=\"0,0\" pos=\"-10,0\" attributeName=\"transform\" type=\"translate\" dur=\"0.2s\" fill=\"freeze\" repeatCount=\"1\" restart=\"always\" /></g><g transform=\"translate(160 15)\"><g class=\"mcr-pie-legend-failed\" transform=\"translate(0 0)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"#d00\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Failed</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">0</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">0.0%</text></g><g class=\"mcr-pie-legend-flaky\" transform=\"translate(0 30)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"orange\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Flaky</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">0</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">0.0%</text></g><g class=\"mcr-pie-legend-skipped\" transform=\"translate(0 60)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"gray\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Skipped</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">1</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">50.0%</text></g><g class=\"mcr-pie-legend-passed\" transform=\"translate(0 90)\"><circle cx=\"10\" cy=\"15\" r=\"10\" fill=\"green\" opacity=\"0.8\" /><text x=\"30\" y=\"15\" alignment-baseline=\"middle\">Passed</text><text x=\"115\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"middle\">1</text><text x=\"190\" y=\"15\" alignment-baseline=\"middle\" text-anchor=\"end\">50.0%</text></g></g></svg>"}, "htmlPath": "monocart-report/index.html"}