import * as fs from 'fs';
import { expect, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, APIRequestContext } from 'playwright/test';
import { AllTypes } from './Types';
import JSZip from 'jszip';
import path from 'path';
import os from 'os'


// Utility class for common testing functions
export class BaseTest {
  

  static writeParametersToFile(filePath: string, data: any): void {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    }

  // Reads parameters from a file
  static readParametersFromFile(filePath: string): AllTypes.BaseCredentils.TestParameters {
    // Get the path to the configuration file
    const pathToConfigFile = filePath;

    // Check if the configuration file exists
    const configFileExists = fs.existsSync(pathToConfigFile);
    if (!configFileExists) {
      // Throw an error if the file is not found
      throw new Error(`Config file not found: ${pathToConfigFile}`);
    }

    // Read the contents of the configuration file
    const configFileContents = fs.readFileSync(pathToConfigFile, 'utf8');

    // Parse the JSON content of the file into TestParameters object
    const parameters: AllTypes.BaseCredentils.TestParameters = JSON.parse(configFileContents);
    return parameters;
  }

  // Generates a random string with alphanumeric characters
  static generateRandomString(length: number): string {
    let result = '';
    const characters = 'abcdefghijklmnopqrstuvwxyz134567890';

    // Loop to generate the random string of specified length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }

  // Generates a random string with alphabetic characters only
  static generateRandomStringWithCharsOnly(length: number): string {
    let result = '';
    const characters = 'abcdefghijklmnopqrstuvwxyz';

    // Loop to generate the random string of specified length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }
  static generateRandomStringWithDigitsOnly(length: number): string {
    let result = '';
    const digits = '123456789';

    for (let i = 0; i < length; i++) {
      result += digits.charAt(Math.floor(Math.random() * digits.length));
    }
    return result;
  }

  async waitForElementWithText(
    page: Page,
    locator: string,
    expectedText: string,
    timeout: number
  ) {
    // Wait for the page to reach a stable state with no network activity
    await page.waitForLoadState('load');
    // Use polling to repeatedly check for the presence of the element with expected text
    await expect
      .poll(
        async () => {
          try {
            const element = page.locator(locator);
            const actualText = await element.textContent();
            // Check if the actual text contains the expected text
            expect(actualText).toContain(expectedText);
            return true;
          } catch (error) {
            const actualContent = await page.textContent(locator);
            return false;
          }
        },
        {
          message: `Element ${locator} not found within ${timeout / 1000} seconds or did not contain the expected text`,
          timeout,
        }
      )
      .toBe(true);// Ensure the polling expectation is met
  }
  static async checkToast(page: Page, buttonResponsibleForToast: Locator, toastMessage: string) {
    try {
      await Promise.all([
        expect(page.locator(`//*[contains(@class,"Toastify__toast")]//*[text()="${toastMessage}"]`).nth(0)).toBeVisible({ timeout: 60000 }),
        buttonResponsibleForToast.click()
      ]);
      //await page.getByText(`${toastMessage}`).first().waitFor({ state: "hidden", timeout: 40000 });
      await expect(page.locator('//*[text()="Success"]').first()).toBeHidden({ timeout: 12000 });
      return true;
    } catch (error) {
      console.error(`An error occurred during ${toastMessage}: ${error.message}`);
      throw error;
    }
  }

  // Generates a random string with uppercase alphabets and special characters
  static generateRandomStringWithUpperCaseAndSpecialChars(length: number): string {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()?/|{}[],.;: ';

    // Loop to generate the random string of specified length
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }
  static async splitKeyValuePair(input: string) {
    // Split the input string into key and value
    let values = input.split(':').map(part => part.trim());
    let valueToReturn: string
    if (values.length == 3) {
      valueToReturn = values[1] + ":" + values[2];
      console.log('we are printing value to return' + valueToReturn);
    }
    else {
      valueToReturn = values[1];
    }
    // Return the key-value pair
    return { key: values[0], value: valueToReturn! };
  }

  // static async splitKeyValuePair(input: string[]): Promise<{ key: string; value: string; }> {
  //   // Split the input string into key and value
  //   const key = input[0];
  //   const value = input[1];
  //   // Return the key-value pair
  //   return { key, value };
  // }

  /**
   * this function is used to create a new page with custom cookies 
   * @param browser  pass browser object 
   * @param tokenValue token value that you want to set
   * @param name token key that you want to set
   * @returns new custom page object 
   */
  static async createNewPageWithCustomCookies(browser: Browser, tokenValue: string, name: string = 'argocd.token'): Promise<Page> {
    var otherUserContext = await browser.newContext();
    otherUserContext.addCookies([{
      name: name,
      url: process.env.BASE_SERVER_URL?.slice(0, process.env.BASE_SERVER_URL.indexOf('/dashboard')),
      value: tokenValue
    }
    ])
    return await otherUserContext.newPage();
  }


  /**
   * this is the generic scenarios that comes when we login into using api-tokens 
   * @param page 
   */
  static async clickOnToolTipOkayButton(page: Page) {
    await page.addLocatorHandler(page.getByTestId('getting-started-okay'), async () => {
      try {
        await page.getByTestId('getting-started-okay').click({ timeout: 10000, force: true });
      } catch (error) {
        console.log('Clicking the tooltip okay button failed, but moving on...');
      }
    });
  }
  static async clickOnDarkMode(page: Page) {
    await page.addLocatorHandler(page.locator(`//*[contains(@class,'theme-preference-option__container')]`).first(), async () => {
      try {
        await page.locator(`//*[@for="theme-preference-option__input-light"]`).first().click({ timeout: 5000 });
        await page.locator('//*[text()="Save Preference"]').click({ timeout: 5000 });
      }
      catch (error) {
        console.log('color ')
      }
    })
  }



  /**
   * this is the method that helps us to wait for particular api response 
   * @param page 
   * @param urlToCheck 
   * @param statusCode 
   * @param actionToPerform 
   */
  static async waitForApiResponse(page: Page, urlToCheck: string, statusCode: number, actionToPerform: (page: Page) => Promise<void>): Promise<void> {
    await Promise.all([page.waitForResponse(response => {
      if (response.url().includes(urlToCheck) && response.status() === statusCode) {
        return true;
      }
      return false;
    }),
    actionToPerform!(page)]);
  }

  // static async verifyDownloads(page: Page, downloadButton: Locator, expectedValue: string, isSuccessfull: boolean) {
  //   if (isSuccessfull) {
  //     const [download] = await Promise.all([
  //       page.waitForEvent('download'),
  //       downloadButton.click(),
  //     ]);

  //     // Get the temporary file path
  //     const tempPath = await download.path();
  //     if (tempPath) {
  //       const fileContent = fs.readFileSync(tempPath, 'utf8');
  //       console.log('File Content:', fileContent);
  //       expect(fileContent).toContain(expectedValue);
  //     }
  //   }
  //   else {
  //     await BaseTest.checkToast(page, downloadButton, "Error");
  //   }

  // }



  static async verifyDownloads(page: Page, downloadButton: Locator, expectedValue: string, isSuccessfull: boolean) {
    if (isSuccessfull) {
      const [download] = await Promise.all([
        page.waitForEvent('download'),
        await downloadButton.click(),
      ]);

      const fileName = download.suggestedFilename();

      console.log('file name we are getting is ' + fileName);
      let tempPath = os.tmpdir() + '/' + fileName
      await download.saveAs(tempPath);
      console.log('path we are getting is ' + tempPath);
      // Check if the downloaded file is a ZIP
      if (path.extname(fileName).toLowerCase() === '.zip') {
        console.log("Detected ZIP file. Extracting contents...");

        const zipBuffer = fs.readFileSync(tempPath); // Read ZIP as binary buffer
        console.log('zip buffer we are getting is ' + zipBuffer);
        const zip = await JSZip.loadAsync(zipBuffer as any); // Load ZIP
        

        // Get the expected file inside the ZIP
        console.log("Files in ZIP:", Object.keys(zip.files)); // Print all file names

        const extractedFile = zip.files[fileName.substring(0, fileName.length - 4)]; // Try using the correct file name
        if (!extractedFile || extractedFile.dir) {
          throw new Error(`Expected file ${fileName} not found in ZIP archive.`);
        }

        // Extract the file as text
        const fileContent = await extractedFile.async("text");
        console.log(`Extracted File Content:`, fileContent);

        expect(fileContent).toContain(expectedValue);
      } else {
        // If not a ZIP, read as a normal text file
        const fileContent = fs.readFileSync(tempPath, 'utf8');
        console.log('File Content:', fileContent);
        expect(fileContent).toContain(expectedValue);
      }
    } else {
      await BaseTest.checkToast(page, downloadButton, "Error");
    }
  }

  static getCurrentMonthNameInLocalizedFormat() {
    const date = new Date();
    const formatter = new Intl.DateTimeFormat('en-US', { month: 'long' });
    const monthName = formatter.format(date);

    console.log(monthName); // Example: "March"
    return monthName;

  }
  static normalizeString(str: string | undefined): string {
    return (str || "")
      .trim()
      .replace(/\u00A0/g, " ")  // Convert non-breaking space to normal space
      .replace(/\u2013|\u2014/g, "-");  // Normalize en dash & em dash to normal dash
  }

  static async fetchTimeFromBrowser(page: Page) {
    const dateInfo = await page.evaluate(() => {
      const now = new Date();

      const day = now.toLocaleDateString('en-US', { weekday: 'short' });
      const date = now.getDate();
      const year = now.getFullYear();

      let hours = now.getHours();
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const period = hours >= 12 ? 'PM' : 'AM';

      hours = hours % 12 || 12;  // 0 becomes 12 for AM
      const formattedHours = hours.toString().padStart(2, '0');

      const time = `${formattedHours}:${minutes} ${period}`;
      const month = now.toLocaleDateString('en-US', { month: 'long' });

      const totalMinutes = now.getHours() * 60 + now.getMinutes(); // 24 hr format minutes

      return { day, date, year, time, month, totalMinutes };
    });

    return dateInfo;
  }



  static mapTimeToNearestHalfHour(totalMinutes: number, mode = 'pre') {
    let mappedMinutes = 0;
    console.log('total minutes we are getting' + totalMinutes);
    const minutePart = totalMinutes % 60;
    let hourPart = Math.floor(totalMinutes / 60);
    console.log('hour part we are getting is' + hourPart);
    if (mode === 'pre') {
      mappedMinutes = minutePart < 30 ? 0 : 30;
    } else if (mode === 'post') {
      if (minutePart <= 30) {
        mappedMinutes = 30;
      } else {
        mappedMinutes = 0;
        hourPart += 1;
      }
    }

    hourPart = hourPart % 24;

    const finalHour = ((hourPart % 12 === 0 ? 12 : hourPart % 12)).toString().padStart(2, '0');
    const finalPeriod = hourPart >= 12 ? 'PM' : 'AM';
    const formattedMinutes = mappedMinutes.toString().padStart(2, '0');

    return `${finalHour}:${formattedMinutes} ${finalPeriod}`;
  }



}



