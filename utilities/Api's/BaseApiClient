import { APIRequestContext, expect } from "playwright/test";


export async function apiClientObjectCreations(request:APIRequestContext){
        let token :string;
        let baseUrl:string= process.env.BASE_SERVER_URL!.slice(0, process.env.BASE_SERVER_URL!.indexOf('/dashboard'));
        if(process.env.isStaging == "true"){
            token = process.env.stagingSuperAdminToken!;
        }
        else{
            token = await (await request.post(`${process.env.BASE_SERVER_URL}/orchestrator/api/v1/session`, { data: { "username": "admin", "password": `${process.env.PASSWORD!}` } })).json().then(res=>res.result.token);
        }
        return {
            
        }
       
}