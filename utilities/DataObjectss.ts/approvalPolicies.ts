// import { ApiUtils } from "../ApiUtils";
import { Page } from "playwright";
import { editDTCMCSDTO, expressEditDTCMCS } from "../../DTOs/DeevtronAppsDto/BaseDeploymentPageDTO";
import { exceptionCategoryEnum } from "../../enums/Application Management/Policies/ApprovalPolicyPageEnums";
import { ApplyProfileFilterByEnum, ApplyProfileScopeCategoryEnum } from "../../enums/Application Management/Policies/LockConfigurationPageEnums";
import { AllTypes } from "../Types"
import { BaseTest } from "../BaseTest";
let credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export function dataSetForApprovalPolicyConfigPart(profileName: string, superAdminObject: AllTypes.fixtures.allPageObjects, nonSuperAdminObject: AllTypes.fixtures.allPageObjects, appName: string) {
    return [
        objectCreationForApprovalPolicyConfigPart('base-configurations', profileName, superAdminObject, nonSuperAdminObject, appName),
        objectCreationForApprovalPolicyConfigPart('automation', profileName, superAdminObject, nonSuperAdminObject, appName)
    ]
}


function objectCreationForApprovalPolicyConfigPart(stage: string, profileName: string, AllObjects: AllTypes.fixtures.allPageObjects, nonSuperAdminObject: AllTypes.fixtures.allPageObjects, appName: string) {
    let createProfileConfig = stage == "base-configurations" ? { profileName: profileName, setUserConfigurationForApprovalPolicy: { normalApproverCount: 1 }, lockConfigPage: AllObjects.lockConfig } : { profileName: profileName, setUserConfigurationForApprovalPolicy: { userGroupDetails: { count: 1, name: 'test' }, specificUsers: { clearUserGroup: false, names: ['playwright-non-super'] } }, lockConfigPage: AllObjects.lockConfig };
    let applyProfileConfig = stage == "base-configurations" ? { applyPolicyData: { profileName: profileName, scope: ApplyProfileScopeCategoryEnum.specificCriteria, filterType: [{ filterName: ApplyProfileFilterByEnum.baseConfiguration, filterValue: "" }, { filterName: ApplyProfileFilterByEnum.application, filterValue: appName }] }, scope: { configuration: { configMaps: true, secrets: true, dt: true } }, lockConfigPage: AllObjects.lockConfig } : { applyPolicyData: { profileName: profileName, scope: ApplyProfileScopeCategoryEnum.byMatchCriteria, filterType: [{ filterName: ApplyProfileFilterByEnum.application, filterValue: appName }, { filterName: ApplyProfileFilterByEnum.environment, filterValue: "automation" }] }, scope: { configuration: { configMaps: true, secrets: true, dt: true } }, lockConfigPage: AllObjects.lockConfig };
    let exceptionConfig = stage == "base-configurations" ? { allowSuperAdmins: true, exceptionFor: exceptionCategoryEnum.configurationChange, specificUsersData: [{ userName: 'playwright-non-super', addUser: false }] } : { allowSuperAdmins: false, exceptionFor: exceptionCategoryEnum.configurationChange, specificUsersData: [{ userName: 'playwright-non-super', addUser: true }] };
    return {
        createProfileConfig: createProfileConfig,
        applyProfileConfig: applyProfileConfig,
        exceptionConfig: exceptionConfig,
        raiseDraftOrProposeChanges: { stage: 'Propose changes', approverList: ['<EMAIL>'] },
        editResourceRelatedData: [resourceLevelEditConfigurationData('dt', 'testcm', stage, nonSuperAdminObject, AllObjects, appName), resourceLevelEditConfigurationData('ConfigMaps', 'testcm', stage, nonSuperAdminObject, AllObjects, appName), resourceLevelEditConfigurationData('Secrets', 'testsec', stage, nonSuperAdminObject, AllObjects, appName)]
    }
}


function resourceLevelEditConfigurationData(rType: string, rName: string, stage: string, nonSuperAdminObject: AllTypes.fixtures.allPageObjects, superAdminObject: AllTypes.fixtures.allPageObjects, appName: string) {
    let editRelatedData: editDTCMCSDTO = { resType: rType, resName: rName, fieldsToEdit: ['GracePeriod'], valuesToEdit: ['33'], stage: stage, jobsPage: superAdminObject.jobsPage, mergeStrat: { configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } } };
    let expressEditData: { pageObject: AllTypes.fixtures.allPageObjects, expressEditVerification: expressEditDTCMCS }[];
    let editRelatedDataForExpress: editDTCMCSDTO = { resType: rType, resName: rName, fieldsToEdit: ['GracePeriod'], valuesToEdit: ['34'], stage: stage, jobsPage: superAdminObject.jobsPage, mergeStrat: { configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } } };
    if (stage == "base-configurations") {
        expressEditData = [{ pageObject: superAdminObject, expressEditVerification: { configuration: { isEligible: true, editRelatedData: editRelatedDataForExpress } } }, { pageObject: nonSuperAdminObject, expressEditVerification: { configuration: { isEligible: false } } }];
    }
    else {
        editRelatedDataForExpress.jobsPage = nonSuperAdminObject.jobsPage;
        expressEditData = [{ pageObject: superAdminObject, expressEditVerification: { configuration: { isEligible: false } } }, { pageObject: nonSuperAdminObject, expressEditVerification: { configuration: { isEligible: true, editRelatedData: editRelatedDataForExpress } } }];
    }
    return {
        editRelatedData: editRelatedData,
        expressEditData: expressEditData,
        approvalRelatedData: approvalRelatedData(stage, rType, appName)
    }


}
function approvalRelatedData(stage: string, rType: string, appName: string) {
    let mailSubjectToFilterOut: string;
    let toastMessageWhileApproving: string;
    let mailMessageWhileApproving: string;
    let mailNumberToVerify: number = rType == "dt" ? 1 : rType == "ConfigMaps" ? 2 : 3;
    if (stage == "base-configurations") {
        mailSubjectToFilterOut = `Config Change approval requested  Application: ${appName}  Change Impacts: Base configuration`;
        toastMessageWhileApproving = "Success";
        mailMessageWhileApproving = "Configuration change is already approved";
        //Configuration change approved successfully
    } else {
        mailSubjectToFilterOut = `Config Change approval requested  Application: ${appName}  Change Impacts: automation`;
        toastMessageWhileApproving = "Success";
        mailMessageWhileApproving = "Configuration change approved successfully";
    }

    return {
        mailSubjectToFilterOut: mailSubjectToFilterOut,
        toastMessageWhileApproving: toastMessageWhileApproving,
        mailMessageWhileApproving: mailMessageWhileApproving,
        mailNumberToVerify: mailNumberToVerify
    }



}

export function dataSetForImageApprovalPolicy(profileName: string, superAdminObject: AllTypes.fixtures.allPageObjects, appName: string) {
    return [
        objectCreationForImageApprovalPolicy(ApplyProfileScopeCategoryEnum.specificCriteria, profileName, superAdminObject, appName),
        objectCreationForImageApprovalPolicy(ApplyProfileScopeCategoryEnum.byMatchCriteria, profileName, superAdminObject, appName)
    ]
}

function objectCreationForImageApprovalPolicy(applyPolicyScope: ApplyProfileScopeCategoryEnum, profileName: string, AllObjects: AllTypes.fixtures.allPageObjects, appName: string) {
    let createProfileConfig = applyPolicyScope == ApplyProfileScopeCategoryEnum.specificCriteria ? { profileName: profileName, setUserConfigurationForApprovalPolicy: { normalApproverCount: 1 }, lockConfigPage: AllObjects.lockConfig } : { profileName: profileName, setUserConfigurationForApprovalPolicy: { userGroupDetails: { count: 1, name: 'test' }, specificUsers: { clearUserGroup: false, names: ['playwright-super-admin'] } }, lockConfigPage: AllObjects.lockConfig };
    let applyProfileConfig = applyPolicyScope == ApplyProfileScopeCategoryEnum.specificCriteria ? { applyPolicyData: { profileName: profileName, scope: ApplyProfileScopeCategoryEnum.specificCriteria, filterType: [{ filterName: ApplyProfileFilterByEnum.application, filterValue: appName }, { filterName: ApplyProfileFilterByEnum.environment, filterValue: "automation" }] }, scope: { imageApproval: true }, lockConfigPage: AllObjects.lockConfig } : { applyPolicyData: { profileName: profileName, scope: ApplyProfileScopeCategoryEnum.byMatchCriteria, filterType: [{ filterName: ApplyProfileFilterByEnum.application, filterValue: appName }, { filterName: ApplyProfileFilterByEnum.environment, filterValue: "automation" }] }, scope: { imageApproval: true }, lockConfigPage: AllObjects.lockConfig };
    let mailMessageToVerify: string = applyPolicyScope == ApplyProfileScopeCategoryEnum.specificCriteria ? "Image is already approved" : "Image approved successfully";
    let eligibleApproversAndCountOfApprovalsDone = applyPolicyScope == ApplyProfileScopeCategoryEnum.specificCriteria ? { eligibleApprovers: ['You'] } : { eligibleApprovers: ['<EMAIL>'], count: { totalApprovalsRequired: 2, approvalsDone: 0 } };
    let imageApproverIsAppliedOrNot = applyPolicyScope == ApplyProfileScopeCategoryEnum.specificCriteria ? { isApplied: true, envName: 'automation' } : { isApplied: false, envName: 'devtron-demo' };
    let mailCountToVerify = applyPolicyScope == ApplyProfileScopeCategoryEnum.specificCriteria ? 1 : 2;
    let mailSubjectToFilterMails = `Image approval requested  Application: ${appName}  Environment: automation`;
    return {
        createProfileConfig: createProfileConfig,
        applyProfileConfig: applyProfileConfig,
        mailMessageToVerify: mailMessageToVerify,
        eligibleApproversAndCountOfApprovalsDone: eligibleApproversAndCountOfApprovalsDone,
        sendApprovalRequest: { cancelRequest: false, imageNumber: 0, notifyUsers: ['<EMAIL>'] },
        imageApproverIsAppliedOrNot: imageApproverIsAppliedOrNot,
        mailCountToVerify: mailCountToVerify,
        mailSubjectToFilterMails: mailSubjectToFilterMails
    }

}

export async function verifyExpressEditFeatureForDeploymentAppDetailsAppGrp(appUrl: string, appName: string, AllObjects: AllTypes.fixtures.allPageObjects, page: Page, checkExpressEditThrough: string) {
    await page.goto(appUrl);
    switch (checkExpressEditThrough) {
        case 'deployment': {
            await AllObjects.workflowPage.clickOnSelectMaterialOrSelectImage(false);
            await AllObjects.workflowPage.triggerDeploymentAndVerifyToast("Success", true);
            await AllObjects.deploymentHistory.selectEnvironment("automation");
            await AllObjects.deploymentHistory.verifyDeployedByExceptionUser();
            break;
        }
        case 'app-details': {
            await AllObjects.appDetailsPageCiCd.appDetailsTabButton.click();
            await AllObjects.appDetailsPageCiCd.scaleUpAndDownWorkflow(false);
            await AllObjects.workflowPage.verifyImageAndTriggerDeployment(0, [], [], 0, true);
            break;
        }
        case 'app-group': {
            await AllObjects.globalConfigPage.clickingOnApplicationGroup();
            await AllObjects.applicationGroupPage.VerifyApplicationGroupPage(credentials.applicationGroup);
            await AllObjects.applicationGroupPage.searchEnvironment(credentials.EnvNameForCD[0]);
            let filterName = "appgroup-filter" + BaseTest.generateRandomStringWithCharsOnly(6);
            await AllObjects.applicationGroupPage.selectApplcations(appName);
            await AllObjects.applicationGroupPage.createOrApplyFilter(filterName);
            await AllObjects.applicationGroupPage.selectAllAppsCheckBox.click();
            await AllObjects.applicationGroupPage.bulkDeploy();
            break;
        }
    }
}