import { BaseTest } from "../BaseTest";

export function dataObjectForRelease(apps: string[], virtualEnvName: string, normalEnvName: string) {
    let installationNames: string[] = [BaseTest.generateRandomStringWithCharsOnly(4), BaseTest.generateRandomStringWithCharsOnly(4)];
    let channelName: string = BaseTest.generateRandomStringWithCharsOnly(4);
    let tenantName: string = BaseTest.generateRandomStringWithCharsOnly(4);
    let releaseTrackName: string = BaseTest.generateRandomStringWithCharsOnly(5);

    let result = {
        releaseTrackName: releaseTrackName,
        releaseCreation: createReleaseOrCloneRelease(false, releaseTrackName),
        dataForManageApps: objectCreationForManageApplications(apps),
        editApplicationsData: editApplications(apps),
        setImageForAppData: setImageForAnApp(apps, false),
        addTenantData: addTenant(tenantName),
        releaseChannelData: releaseChannelCreation(channelName),
        installationData: installationData(channelName, installationNames),
        mapEnvToInstallationData: mapEnvToInstallations(installationNames, virtualEnvName, normalEnvName),
        verifyEnvMappingToTenants: verifyEnvMappingToTenants(installationNames, virtualEnvName, tenantName, normalEnvName),
        normalTriggerDeploymentData: [triggerDeploymentData(apps, false, 1, 0, virtualEnvName, normalEnvName, false, true, undefined), triggerDeploymentData(apps, true, 1, 0, virtualEnvName, normalEnvName, false, true, undefined)],
        appliedFilterAndVerificationData: appliedFilterAndVerificationData(channelName, apps, virtualEnvName, normalEnvName),
        triggerDeploymentDataForVirtual: triggerDeploymentData(apps, false, 1, 0, virtualEnvName, normalEnvName, true, false, { tenantName: tenantName, installationName: installationNames[1] }),
        cloneRelease: createReleaseOrCloneRelease(true, releaseTrackName),
        setImageForAnAppCloning: setImageForAnApp(apps, true),
        triggerDeploymentDataFeasibility: triggerDeploymentData(apps, false, 0, 1, virtualEnvName, normalEnvName, false, true)



    }
    return result;
}
function objectCreationForManageApplications(apps: string[]) {
    let result: { appName: string, isMandatory: boolean, releaseStageNumber: number }[] = [];
    for (let i = 0; i < apps.length; i++) {
        let object = {
            appName: apps[i],
            isMandatory: true,
            releaseStageNumber: i
        }
        result.push(object);
    }
    return result;
}
function editApplications(apps: string[]) {
    let result: { appName: string, addAsNewApp: boolean }[] = [];
    for (let i = 0; i < apps.length; i++) {
        let object = {
            appName: apps[i],
            addAsNewApp: true
        }
        result.push(object);
    }
    return result;
}
function setImageForAnApp(apps: string[], fromPreviousRelease: boolean = false) {
    let result: { appName: string, workflowNumber: number, selectFromPreviousRelease?: boolean }[] = [];
    for (let i = 0; i < apps.length; i++) {
        let object: { appName: string, workflowNumber: number, selectFromPreviousRelease?: boolean } = {
            appName: apps[i],
            workflowNumber: 0
        }
        if (fromPreviousRelease == true) {
            object.selectFromPreviousRelease = true
        }
        result.push(object);
    }
    return result;
}
function addTenant(tenantName: string) {
    let object = { tenantName: tenantName, tenantId: tenantName, imageUrl: 'https://companieslogo.com/img/orig/DELHIVERY.NS-24f77207.png?t=1720244491' };
    return object;
}
function releaseChannelCreation(channelName: string) {
    return { releaseChannelName: channelName, isDefault: false, channelId: channelName };
}
function installationData(channelName: string, installationNames: string[]) {
    let result: { installationId: string, installationName: string, releaseChannel?: string }[] = [];
    for (let i = 0; i < 2; i++) {
        let object: { installationId: string, installationName: string, releaseChannel?: string } = { installationId: installationNames[i], installationName: installationNames[i] }
        if (i == 0) {
            object.releaseChannel = channelName
        }
        result.push(object);
    }
    return result;
}
function mapEnvToInstallations(installations: string[], virtualEnv: string, normalEnv: string) {
    let result: {
        installationName: string, mappingData: { clusterName: string, envName: string, isBlocked?: boolean, isVirtualEnv: boolean }[], registryData?: { registryName: string, repoName: string }
    }[];
    result = [
        {
            installationName: installations[0],
            mappingData: [{ clusterName: 'virtual-cluster-automation', envName: virtualEnv, isVirtualEnv: true }, { clusterName: 'default_cluster', envName: normalEnv, isVirtualEnv: false }]
        },
        {
            installationName: installations[1],
            mappingData: [{ clusterName: 'virtual-cluster-automation', envName: virtualEnv, isVirtualEnv: true }, { clusterName: 'default_cluster', envName: normalEnv, isBlocked: true, isVirtualEnv: false }],
            registryData: { registryName: 'bains', repoName: 'deep10/bool' }
        }
    ]
    return result;
}
function verifyEnvMappingToTenants(installations: string[], virtualEnv: string, tenantName: string, normalEnv: string) {
    let result: { installationName: string, tenantName: string, envNames: string[] }[] = [];
    for (let i = 0; i < installations.length; i++) {
        let envName: string = i == 0 ? normalEnv : virtualEnv;
        let object = {
            installationName: installations[i], tenantName: tenantName, envNames: [envName]
        }
        result.push(object);
    }
    return result;

}
//apps, false, 1, 0, virtualEnvName, normalEnvName, false, true, undefined
function triggerDeploymentData(appNames: string[], isHold: boolean, canTriggerCount: number, cantTriggerCount: number, virtualEnv: string, normalEnv: string, isVirtualEnv: boolean, isSecondAppBlocked: boolean, tenantRelatedData?: { tenantName: string, installationName: string }) {
    let data = tenantRelatedData ? [{ envName: tenantRelatedData!.installationName, appName: tenantRelatedData!.tenantName, isBlocked: false }] : [envSelectionRelatedData(appNames[0], normalEnv, false), envSelectionRelatedData(appNames[1], normalEnv, isSecondAppBlocked)]
    let object = {
        envSelectionRelatedData: data,
        feasibilityPage: { canTriggerCount: canTriggerCount, cantTriggerCount: cantTriggerCount },
        isHoldOrRescinded: isHold,
        isVirtualEnv: isVirtualEnv
    }
    return object;

}
function envSelectionRelatedData(appName: string, envName: string, isBlocked: boolean) {
    let object = {
        envName: envName,
        appName: appName,
        isBlocked: isBlocked
    }
    return object;
}
function createReleaseOrCloneRelease(isClonedRelease: boolean, releaseTrackName: string) {
    let releaseName: string = isClonedRelease ? '0.2.0' : "0.1.0";
    let object: { releaseTrackName: string, releaseName: string, releaseVerName: string, cloneReleaseFromExistingRelease?: string } = { releaseTrackName: releaseTrackName, releaseName: releaseName, releaseVerName: releaseName };
    if (isClonedRelease) {
        object.cloneReleaseFromExistingRelease = "0.1.0"
    }
    return object;
}
function appliedFilterAndVerificationData(releaseChannelName: string, apps: string[], virtualEnv: string, normalEnv: string) {
    let object = {
        applyingFilter: [applyingFilter('Release channel', [releaseChannelName])],
        verification: [verificationObject(apps[0], normalEnv, true), verificationObject(apps[0], virtualEnv, false)]
    }
    return object;
}
function applyingFilter(filterType: string, filterValue: string[]) {
    let object = {
        filterType: filterType,
        filterValue: filterValue
    }
    return object;
}
function verificationObject(appName: string, envName: string, isVisible: boolean) {
    let object = {
        appName: appName,
        envName: envName,
        isVisible: isVisible
    }
    return object;
}
