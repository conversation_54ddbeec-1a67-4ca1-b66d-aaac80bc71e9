import { BaseTest } from "../BaseTest";

export function dataForProfileCreation() {
    let result: { profileName: string, description?: string, passFailConditions: { conditionType: 'Pass' | "Fail", conditionValue: string }, approvalConfig?: { requiredApproval: number, approverCanDeploy?: boolean, requesterCanApprove?: boolean, builderCanApprove?: boolean } }[] = [];
    for (let i = 0; i < 4; i++) {
        let requiredApprovalCount = i == 0 || i == 1 ? 0 : 1;
        let conditionType = i == 1 ? "Fail" : 'Pass';
        let approverCanDeploy = true;
        let requesterCanApprove = true;
        let builderCanApprove = true;
        if (i == 2) {
            approverCanDeploy = false;
            requesterCanApprove = false;
            builderCanApprove = false;
        }
        let object: { profileName: string, description?: string, passFailConditions: { conditionType: 'Pass' | "Fail", conditionValue: string }, approvalConfig?: { requiredApproval: number, approverCanDeploy?: boolean, requesterCanApprove?: boolean, builderCanApprove?: boolean } } =
        {
            profileName: BaseTest.generateRandomStringWithCharsOnly(5),
            passFailConditions: {
                conditionType: conditionType as 'Pass' | "Fail",
                conditionValue: `containerImage.contains('quay.io')`,
            },
            approvalConfig: {
                requiredApproval: requiredApprovalCount,
                approverCanDeploy: approverCanDeploy,
                requesterCanApprove: requesterCanApprove,
                builderCanApprove: builderCanApprove
            }
        }
        result.push(object);
    }
    return result;
}
export function imagePromotionOnEnvLevel() {
    return {
        envLevelConfiguration: [envLevelConfiguration(1), envLevelConfiguration(2)],
        approvalData: {
            user1Data: [{ envName: 'env1', isEligible: false }, { envName: 'env2', isEligible: true }],
            user2Data: [{ envName: 'env1', isEligible: true }]
        }

    }

}
function envLevelConfiguration(configurationNumber: number) {
    let envData: { envName: string, isEligible: boolean, messageAfterRequestRaised?: string }[] = [];
    let imageSource: string = configurationNumber == 1 ? 'CI Pipeline' : 'automation'
    for (let i = 0; i < 4; i++) {
        let enviornmentName = i == 0 ? 'automation' : i == 1 ? 'devtron-demo' : i == 2 ? 'env1' : 'env2';
        let eligibility = i == 1 ? false : true;
        let messageToCheck = i == 0 && configurationNumber == 1 ? 'image promoted' : i == 0 && configurationNumber == 2 ? 'source and the destination cannot be same' : i == 2 && configurationNumber == 2 ? 'promotion request already raised' : i == 2 && configurationNumber == 1 ? "sent for approval" : i == 3 && configurationNumber == 2 ? 'source and destination pipeline order mismatch' : 'sent for approval'
        let object: { envName: string, isEligible: boolean, messageAfterRequestRaised?: string } = {
            envName: enviornmentName,
            isEligible: eligibility,
            messageAfterRequestRaised: messageToCheck
        }
        envData.push(object);
    }
    return {
        imageSource: imageSource,
        envConfiguration: envData
    }

}