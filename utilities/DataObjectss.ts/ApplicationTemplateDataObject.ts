import { AllTypes } from "../Types";
export function dataForDefaultAppSetup(linkedAppName: string, sourcePipelineName: string): ({ workflowType: 'build-from', config: { sourceType: 'Branch Fixed' | "Branch Regex", branchName: string[] } | { sourceType: 'Pull Request' | "Tag Creation" }, cdConfig?: AllTypes.appConfiguration.addCdModule } | { workflowType: 'linked-build', config: { linkedPipelineName: string, sourcePipelineName: string, appName: string, checkValidation: boolean }, cdConfig?: AllTypes.appConfiguration.addCdModule } | { workflowType: 'deploy-image', cdConfig: AllTypes.appConfiguration.addCdModule } | { workflowType: 'linked-cd', config: { sourceEnv: string, destEnv: string } } | { workflowType: 'job-ci-pipeline-button', config: { pipelineName: string, branchName: string, script: string[] }, cdConfig?: AllTypes.appConfiguration.addCdModule })[] {
    return [{
        workflowType: 'build-from',
        config: {
            branchName: ['.*'],
            sourceType: 'Branch Regex'
        },
        cdConfig: {
            envNameForCd: 'env1',
            helmOrGitops: 'gitops',
            deploymentStrat: 'RECREATE',
            autoOrManual: 'Auto',
            clusterName: 'default_cluster'
        }
    }, {
        workflowType: 'build-from',
        config: {
            sourceType: 'Pull Request'
        },
        cdConfig: {
            envNameForCd: 'env2',
            helmOrGitops: 'helm',
            deploymentStrat: 'ROLLING',
            autoOrManual: 'Auto',
            clusterName: 'default_cluster'
        }
    },
    {
        workflowType: 'build-from',
        config: {
            sourceType: 'Tag Creation'
        },
        cdConfig: {
            envNameForCd: 'env3',
            helmOrGitops: 'helm',
            deploymentStrat: 'ROLLING',
            autoOrManual: 'Auto',
            clusterName: 'default_cluster'
        }
    },
    {
        workflowType: 'linked-build',
        config: {
            linkedPipelineName: 'deep',
            sourcePipelineName: sourcePipelineName,
            appName: linkedAppName,
            checkValidation: false
        },
        cdConfig: {
            envNameForCd: 'env4',
            helmOrGitops: 'helm',
            deploymentStrat: 'ROLLING',
            autoOrManual: 'Auto',
            clusterName: 'default_cluster'
        }
    },
    {
        workflowType: 'deploy-image',
        cdConfig: {
            envNameForCd: 'env5',
            helmOrGitops: 'helm',
            deploymentStrat: 'ROLLING',
            autoOrManual: 'Auto',
            clusterName: 'default_cluster'
        }
    }, {
        workflowType: 'linked-cd',
        config: {
            sourceEnv: 'env5',
            destEnv: 'env6'
        }
    }, {
        workflowType: 'job-ci-pipeline-button',
        config: {
            pipelineName: 'deepe',
            branchName: 'main',
            script: ['echo "hello"']
        },
        cdConfig: {
            envNameForCd: 'env7',
            helmOrGitops: 'helm',
            deploymentStrat: 'ROLLING',
            autoOrManual: 'Manual',
            clusterName: 'default_cluster'
        }
    }


    ]
}
export function updatingDefaultCreatedApp() {
    return {
        prePostCiCdData: [
            // {
            //     workflowNumber: 0,
            //     stage: 'post',
            //     taskName: 'Copy container image',
            //     nodeName: 'ci',
            //     autoOrManul: undefined
            // },
            {
                workflowNumber: 0,
                stage: 'post',
                taskName: 'execute',
                nodeName: 'automation',
                autoOrManul: 'Manual',
                script: ['echo $key1', 'echo $key2']
            }, {
                workflowNumber: 0,
                stage: 'pre',
                taskName: 'execute',
                nodeName: 'automation',
                autoOrManul: 'Auto',
                script: ['echo $key1', 'echo $key2']
            },
            {
                workflowNumber: 0,
                stage: 'pre',
                taskName: 'execute',
                nodeName: 'devtron-demo',
                autoOrManul: 'Auto',
                script: ['echo $key1', 'echo $key2']
            }],
        cmcsCreationData: [{ resource: 'ConfigMaps', resourceType: 'Kubernetes ConfigMap', resourceName: 'test1', Data: [`key1:value1`], mode: 'gui', stage: "base-configurations" },
        { resource: 'Secret', resourceType: 'Kubernetes Secret', resourceName: 'test2', Data: [`key2:value2`], mode: 'gui', stage: "base-configurations" }
        ],
        cmcsUpdationData: [{
            resource: 'ConfigMaps',
            resourceName: 'test1',
            inheritOverrideData: {
                configuration: {
                    allowOverrideOrDelete: 'allow',
                    replaceMergerStrat: true
                }
            },
            key: 'key1',
            value: 'test',
            stage: 'automation'
        },
        {
            resource: 'Secrets',
            resourceName: 'test2',
            inheritOverrideData: {
                configuration: {
                    allowOverrideOrDelete: 'allow',
                    replaceMergerStrat: true
                }
            },
            key: 'key2',
            value: 'best',
            stage: 'automation'
        },
        {
            resource: 'ConfigMaps',
            resourceName: 'test1',
            inheritOverrideData: {
                configuration: {
                    allowOverrideOrDelete: 'allow',
                    replaceMergerStrat: process.env.clusterType?.includes('oss') ? true : false
                }
            },
            key: 'key1',
            value: 'rest',
            stage: 'devtron-demo'
        },
        {
            resource: 'Secrets',
            resourceName: 'test2',
            inheritOverrideData: {
                configuration: {
                    allowOverrideOrDelete: 'allow',
                    replaceMergerStrat: process.env.clusterType?.includes('oss') ? true : false
                }
            },
            key: 'key2',
            value: 'nest',
            stage: 'devtron-demo'
        },
        ],
        dtData: [{
            stage: 'automation',
            key: 'GracePeriod',
            value: '10',
            inheritOverrideData: {
                configuration: {
                    allowOverrideOrDelete: 'allow',
                    replaceMergerStrat: true
                }
            }
        },
        {
            stage: 'devtron-demo',
            key: 'GracePeriod',
            value: '30',
            inheritOverrideData: {
                configuration: {
                    allowOverrideOrDelete: 'allow',
                    replaceMergerStrat: process.env.clusterType?.includes('oss') ? true : false
                }
            }
        }]
    }
}
export async function dataSetForApplicationTemplate(allObjects: AllTypes.fixtures.allPageObjects) {
    let workflows = ['devtron-demo', 'env1', 'env2', 'env3', 'env4', 'env5', 'env6', 'env7']
    let workflowNumbers =
        await Promise.all(workflows.map(async (workflow) => {
            return await allObjects.appConfigurationPage.findIndexOfWorkflow(workflow);
        }));
    return {
        configurationValidationAppTemplate: [
            { workflowNumber: workflowNumbers[0], nodeNumber: 0, textToVerify: ['sample-html', 'main'] },
            { workflowNumber: workflowNumbers[0], nodeNumber: 1, textToVerify: ['sample-html', 'main'] },
            { workflowNumber: workflowNumbers[0], nodeNumber: 2, textToVerify: ['Build'] },
            { workflowNumber: workflowNumbers[0], nodeNumber: 3, textToVerify: ['automation', 'Pre-deploy', "Post-deploy"] },
            { workflowNumber: workflowNumbers[0], nodeNumber: 4, textToVerify: ['devtron-demo', 'Pre-deploy'] },
            { workflowNumber: workflowNumbers[1], nodeNumber: 1, textToVerify: ['sample-html'] },
            { workflowNumber: workflowNumbers[1], nodeNumber: 3, textToVerify: ['env1', 'Deploy'] },
            { workflowNumber: workflowNumbers[2], nodeNumber: 0, textToVerify: ['Pull Request', 'sample-html'] },
            { workflowNumber: workflowNumbers[2], nodeNumber: 3, textToVerify: ['Auto', 'env2'] },
            { workflowNumber: workflowNumbers[3], nodeNumber: 0, textToVerify: ['Tag Creation', 'sample-html'] },
            { workflowNumber: workflowNumbers[3], nodeNumber: 3, textToVerify: ['Auto', 'env3'] },
            { workflowNumber: workflowNumbers[4], nodeNumber: 2, textToVerify: ['Build: Linked'] },
            { workflowNumber: workflowNumbers[4], nodeNumber: 3, textToVerify: ['Auto', 'env4'] },
            { workflowNumber: workflowNumbers[5], nodeNumber: 0, textToVerify: ['External source'] },
            { workflowNumber: workflowNumbers[5], nodeNumber: 1, textToVerify: ['Auto', 'env5'] },
            { workflowNumber: workflowNumbers[6], nodeNumber: 0, textToVerify: ['Sync'] },
            { workflowNumber: workflowNumbers[6], nodeNumber: 1, textToVerify: ['env6', 'Auto'] },
            { workflowNumber: workflowNumbers[7], nodeNumber: 2, textToVerify: ['Job'] },
            { workflowNumber: workflowNumbers[7], nodeNumber: 3, textToVerify: ['env7', 'Manual'] },
        ],
        specificCiNodeVerification: {
            workflowNumber: workflowNumbers[0],
            nodeName: 'ci',
            targetPlatform: 'linux/amd64',
            regName: 'bains',
            repoName: 'deep10/reen'
        },
        specificCdNodeVerification: {
            workflowNumber: workflowNumbers[1],
            nodeName: 'env1',
            isGitops: true,
            deploymentStrat: 'RECREATE'
        },
        dtConfiguration: [{
            stage: 'automation',
            key: 'GracePeriod',
            value: '10',
            mergeStrat: "Replace"
        }, {
            stage: 'devtron-demo',
            key: 'GracePeriod',
            value: '30',
            mergeStrat: process.env.clusterType?.includes('oss') ? "Replace" : "Patch"
        }],
        createAppPageValidation: {
            codeSource: [{ gitAccountName: 'Github Public', gitRepoUrl: 'https://github.com/deepak-devtron/sample-html.git' }, { gitAccountName: 'Github Public', gitRepoUrl: 'https://github.com/aman10000q/sample-html.git' }],
            buildConfiguration: {
                registryName: 'devtron', repoName: 'test'
            },
            workflow: [{
                workflowNumber: workflowNumbers[0],
                envNames: ['automation', 'devtron-demo']
            }]
        },
        createAppPageWorkflowChanges: undefined as any,
        tags: [{ key: 'key1', value: 'value1' }],
        triggerRelatedData: [{
            triggerType: 'ci',
            workflowNumber: workflowNumbers[0],
            runtimeParams: undefined
        },
        {
            triggerType: 'ci',
            workflowNumber: workflowNumbers[7],
            runtimeParams: ['externalCiArtifact:prakhr/test:6a824121-1-5']
        },
        {
            triggerType: 'webhook',
        }
        ],
        statusverificationData: [{
            workflowNumber: workflowNumbers[7],
            nodeNumber: 0,
            status: 'Running'
        }, {
            workflowNumber: 0,
            nodeNumber: 1,
            status: 'Running'
        },
        {
            workflowNumber: workflowNumbers[0],
            nodeNumber: 2,
            status: 'Progressing'
        },
        {
            workflowNumber: workflowNumbers[6],
            nodeNumber: 0,
            status: 'Progressing'
        }, {
            workflowNumber: workflowNumbers[7],
            nodeNumber: 0,
            status: 'Succeeded'
        }, {
            workflowNumber: workflowNumbers[7],
            nodeNumber: 1,
            status: 'Not Deployed'
        }
        ],
        logVerifications: [
            {
                workflowNumber: workflowNumbers[0],
                nodeNumber: 1,
                logs: ['test', 'best']
            },
            {
                workflowNumber: workflowNumbers[0],
                nodeNumber: 0,
                logs: ['Login Succeeded']
            },
            {
                workflowNumber: workflowNumbers[0],
                nodeNumber: 3,
                logs: ['rest', 'nest']
            },
            {
                workflowNumber: workflowNumbers[7],
                nodeNumber: 0,
                logs: ['hello']
            }

        ]

    }
}

export async function verifyConfigurationOfTemplateAndApp(dataObject: any, AllObjects: AllTypes.fixtures.allPageObjects, checkUpdation: boolean = true) {
    let i = 0;
    for (let key of dataObject.configurationValidationAppTemplate) {
        console.log('i we are getting is ' + i);
        await AllObjects.appConfigurationPage.verifyDetailsOfWorkflow(key);
        i++;
    }
    await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(dataObject.specificCiNodeVerification.workflowNumber, 'ci');
    await AllObjects.buildConfigurationPage.verifySavedRegistry(dataObject.specificCiNodeVerification.regName, dataObject.specificCiNodeVerification.repoName);
    await AllObjects.appConfigurationPage.checkAppliedTargetPlatform(['linux/amd64']);
    await AllObjects.buildConfigurationPage.updatePipeline.click();
    if (checkUpdation) {
        await AllObjects.appConfigurationPage.clickOnSpecificCiCdNode(dataObject.specificCdNodeVerification.workflowNumber, dataObject.specificCdNodeVerification.nodeName);
        await AllObjects.appConfigurationPage.verifydeployStratConfiguredOnCd(dataObject.specificCdNodeVerification.deploymentStrat);
        await AllObjects.appConfigurationPage.verifyHelmOrGitopsSelectedInCd(dataObject.specificCdNodeVerification.isGitops);
        await AllObjects.appConfigurationPage.updatePipelineButton.click();
        await AllObjects.appConfigurationPage.closeUpdationModal();
    }
    for (let key of dataObject.dtConfiguration) {
        await AllObjects.jobsPage.operBaseOrEnvOverRideResources(key.stage, true);
        await AllObjects.baseDeploymentTemplatePage.verifyTheMergeStrategyApplied(key.mergeStrat);
        await AllObjects.baseDeploymentTemplatePage.verfiyFieldValues([key.key], [key.value]);
        await AllObjects.jobsPage.goBackToWorkflowPage();
    }
}
