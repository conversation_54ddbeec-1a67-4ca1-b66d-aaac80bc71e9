import { AllTypes } from "../Types";
export function dataCreationForMandatoryPlugins(profileName: string, applicationName1: string, applicationName2: string) {
    let isCi: boolean[] = [false, true];
    let resultantArray: AllTypes.mandatoryPlugin.mandatoryPlugin[] = [];
    for (let key of isCi) {
        let isBuildOrDeploy: string = key ? "build" : 'deploy'
        if (key != true) {
            resultantArray.push(objectCreationForMandatoryPlugins(key, `Pre-${isBuildOrDeploy} stage`, 'Allow respective triggers with warning', 'Global', applicationName1, applicationName2))
        }
        resultantArray.push(objectCreationForMandatoryPlugins(key, `Post-${isBuildOrDeploy} stage`, 'Block respective triggers immediately', 'Criteria', applicationName1, applicationName2))
    }
    return resultantArray;
}
function objectCreationForMandatoryPlugins(isCi: boolean, stage: string, consequences: string, applyingType: string, app1: string, app2: string): AllTypes.mandatoryPlugin.mandatoryPlugin {
    let result: AllTypes.mandatoryPlugin.mandatoryPlugin = {
        profileCreationData: { isCi: isCi, plugins: [{ pluginName: 'Code Scan', stage: stage }], consequences: consequences },
        applyingProfile: { applyingType: applyingType },
        viewNonCompliancePipelinesApp: app1,
        affectedPipelinesDetails: objectCreationForAffectedPipelines([app1, app2], stage, isCi, applyingType)
    }
    if (applyingType == "Criteria") {
        result.applyingProfile.matchCriteriaData = {
            Project: 'devtron-demo',
            Application: app1,
            Environment: 'env9',
            Cluster: 'virtual-cluster-automation',
        }
    }
    return result
}
function objectCreationForAffectedPipelines(appNames: string[], stage: string, isCi: boolean, applyingType: string) {
    let resultArray: any = [];
    let nonImpactedCiCdNumber: number = -1;
    let impactedCiCdNumber: number = -1;
    for (let i = 0; i < appNames.length; i++) {
        if (applyingType != "Global") {
            if (isCi) {
                if (i == 0) {
                    impactedCiCdNumber = 0;
                    nonImpactedCiCdNumber = 1;
                }
                else {
                    nonImpactedCiCdNumber = 0;
                    impactedCiCdNumber = -1;
                }
            }
            else {
                if (i == 0) {
                    stage == "Pre-deploy stage" ? impactedCiCdNumber = 0 : impactedCiCdNumber = 2;
                    nonImpactedCiCdNumber = impactedCiCdNumber == 0 ? 2 : 0;
                }
                else {
                    stage == "Pre-deploy stage" ? nonImpactedCiCdNumber = 0 : nonImpactedCiCdNumber = 2;
                    impactedCiCdNumber = -1;
                }
            }
        }
        else {
            if (isCi) {
                impactedCiCdNumber = 0;
                nonImpactedCiCdNumber = 1;
            }
            else {
                stage == "Pre-deploy stage" ? impactedCiCdNumber = 0 : impactedCiCdNumber = 2;
                stage == "Pre-deploy stage" ? nonImpactedCiCdNumber = 2 : 0;
            }
        }
        let result = {
            appName: appNames[i],
            impactedCiCdNumber: impactedCiCdNumber,
            nonImpactedCiCdNumber: nonImpactedCiCdNumber
        }
        resultArray.push(result);
    }
    return resultArray;
}