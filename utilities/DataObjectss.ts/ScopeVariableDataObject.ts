import { Page } from "playwright";
import { AllTypes } from "../Types";
import { BaseTest } from "../BaseTest";
import { CompareValueManifestCategorySection } from "../../enums/ApplicationManagement/Applications/CompareValueManifestCategorySectionEnum";
import { scopeVariableConfigurationForEnterprise, scopeVariableConfigurationForOss } from "../clipboardyYamls.ts/YamlForScopedVariableEnterprise";
import { ApiUtils } from "../ApiUtils";


/**
 * Parent data Object function for enterprise 
 * @param cmName 
 * @param csName 
 * @param superAdminObjects 
 * @param nonSuperAdminPageObjects 
 * @returns 
 */
export function dataObjectForDevtronAppDtCmTestCase(cmName: string, csName: string, superAdminObjects: AllTypes.fixtures.allPageObjects, nonSuperAdminPageObjects: AllTypes.fixtures.allPageObjects) {
    return {
        dtcmcsVerificationObject: [objectCreationOfScopeVariableTestCase('base-configurations', { rName: '', rType: 'dt' }, superAdminObjects, nonSuperAdminPageObjects), objectCreationOfScopeVariableTestCase('base-configurations', { rName: cmName, rType: 'ConfigMaps' }, superAdminObjects, nonSuperAdminPageObjects), objectCreationOfScopeVariableTestCase('base-configurations', { rName: csName, rType: 'Secrets' }, superAdminObjects, nonSuperAdminPageObjects),
        objectCreationOfScopeVariableTestCase('automation', { rName: '', rType: 'dt' }, superAdminObjects, nonSuperAdminPageObjects), objectCreationOfScopeVariableTestCase('automation', { rName: cmName, rType: 'ConfigMaps' }, superAdminObjects, nonSuperAdminPageObjects), objectCreationOfScopeVariableTestCase('automation', { rName: csName, rType: 'Secrets' }, superAdminObjects, nonSuperAdminPageObjects),
        objectCreationOfScopeVariableTestCase('devtron-demo', { rName: '', rType: 'dt' }, superAdminObjects, nonSuperAdminPageObjects), objectCreationOfScopeVariableTestCase('devtron-demo', { rName: cmName, rType: 'ConfigMaps' }, superAdminObjects, nonSuperAdminPageObjects), objectCreationOfScopeVariableTestCase('devtron-demo', { rName: csName, rType: 'Secrets' }, superAdminObjects, nonSuperAdminPageObjects),
        ],
        compareValueOrManifestObject: [compareValueOrManifestObjectCreation('automation', 'playwright', `automtest`, CompareValueManifestCategorySection.ConfigMaps),
        compareValueOrManifestObjectCreation('devtron-demo', 'GracePeriod', `33`, CompareValueManifestCategorySection.DeploymentTemplate)
        ],
        compareValueOrManifestForDeploymentHistory: [compareValueOrManifestObjectCreation('automation', 'playwright', `automtest`, CompareValueManifestCategorySection.Secrets),
        compareValueOrManifestObjectCreation('automation', 'GracePeriod', `13`, CompareValueManifestCategorySection.DeploymentTemplate),
        compareValueOrManifestObjectCreation('devtron-demo', 'GracePeriod', `33`, CompareValueManifestCategorySection.DeploymentTemplate),
        compareValueOrManifestObjectCreation('devtron-demo', 'value', `value1`, CompareValueManifestCategorySection.DeploymentTemplate)
        ]

    }
}

/**
 * Helper function which will get called in parent function , it will give scope var related data for dt cm cs verification 
 * @param stage 
 * @param rtConfig 
 * @param superAdminPageObjects 
 * @param nonSuperAdminPageObjects 
 * @returns 
 */
function objectCreationOfScopeVariableTestCase(stage: string, rtConfig: { rName: string, rType: string }, superAdminPageObjects: AllTypes.fixtures.allPageObjects, nonSuperAdminPageObjects: AllTypes.fixtures.allPageObjects) {
    let valuesToAddForScopeVar = {
        GracePeriod: {
            dt: `"@{{KAFKA}}"`,
            ConfigMaps: '@{{KAFKA}}',
            Secrets: '@{{KAFKA}}'
        },
        playwright: {
            dt: `"@{{sensitive}}"`,
            ConfigMaps: '@{{sensitive}}',
            Secrets: '@{{sensitive}}'
        }
    }
    let fieldsToEdit: string[] = ['GracePeriod'];
    let valuesToEdit: string[] = [valuesToAddForScopeVar.GracePeriod[rtConfig.rType]];
    if (rtConfig.rType == "dt") {
        fieldsToEdit.push('EnvVariables');
        valuesToEdit.push(`"@{{object}}"`);
    }
    let useReplaceAsStrategy: boolean = true;
    if (stage == "automation") {
        fieldsToEdit = ['playwright'];
        valuesToEdit = [valuesToAddForScopeVar!.playwright[rtConfig.rType]];
        useReplaceAsStrategy = false
    }

    return {
        stage: stage,
        strat: { configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: useReplaceAsStrategy } },
        fieldsToEdit: fieldsToEdit,
        valuesToEdit: valuesToEdit,
        resourceTypeConfiguration: rtConfig,
        scopeVarVerificationData: scopeVarVerificationObject(rtConfig.rType, stage, superAdminPageObjects, nonSuperAdminPageObjects)
    }
}


/**
 * Another helper function , which will be called by it's own parent
 * @param rType 
 * @param stage 
 * @param superAdminPageObjects 
 * @param nonSuperAdminPageObjects 
 * @returns 
 */
function scopeVarVerificationObject(rType: string, stage: string, superAdminPageObjects: AllTypes.fixtures.allPageObjects, nonSuperAdminPageObjects: AllTypes.fixtures.allPageObjects) {

    let mappingValuesToVerifyOnStageLevel = {
        'base-configurations': {
            'KAFKA': '13'
        },
        'automation': {
            'KAFKA': '13'
        },
        'devtron-demo': {
            'KAFKA': '33'
        }
    }
    let visibilityOfComplexObject = rType == "dt" ? true : false;
    let tooltipVerificationData = [{ scopeVariableName: 'KAFKA', isVisible: true, valueToVerify: [mappingValuesToVerifyOnStageLevel[stage].KAFKA] }, { scopeVariableName: 'sensitive', isVisible: true, valueToVerify: ['automtest'] }, { scopeVariableName: 'object', isVisible: visibilityOfComplexObject, valueToVerify: ['test1'] }];
    let valueForSuperAdminsToVerify: string[] = stage == "automation" ? ['automtest'] : [mappingValuesToVerifyOnStageLevel[stage].KAFKA];
    let valueForNonSuperAdminsToVerify: string[] = stage == "automation" ? ['hidden-value'] : [mappingValuesToVerifyOnStageLevel[stage].KAFKA];
    let fieldsToEdit: string[] = stage == 'automation' ? ['playwright'] : ['GracePeriod'];
    return {
        tooltipVerificationData: tooltipVerificationData,
        dtcmcsVerificationData: {
            fields: fieldsToEdit,
            userLevelConfig: [{
                pageObject: superAdminPageObjects,
                values: valueForSuperAdminsToVerify
            },
            {
                pageObject: nonSuperAdminPageObjects,
                values: valueForNonSuperAdminsToVerify
            }]
        }
    }
}


/**
 * Another helper function which will return comparison object
 * @param stage 
 * @param fields 
 * @param values 
 * @param catToCheck 
 * @returns 
 */
function compareValueOrManifestObjectCreation(stage: string, fields: string, values: string, catToCheck: CompareValueManifestCategorySection) {
    return {
        stage: stage,
        comparisonRelatedData: { compareWithEntity: 'Base Configurations', isManifestOutput: false },
        verificationRelatedData: [{ field: fields, value: values, isEditable: true, sideToVerifyDiff: 'right' }],
        hasDifference: true,
        categoryToCheck: catToCheck
    }
}


/**
 * This function contains all the constants which will be used in scope variable test cases
 * @returns 
 */
export function scopeVarTestCaseConstants() {
    return {
        scopeVariableConfigFilePath: 'utilities/variables.yaml',
        updatedScopeVariableConfiguration: process.env.clusterType === "enterprise" ? scopeVariableConfigurationForEnterprise() : scopeVariableConfigurationForOss(),
        objectForReviewTheChangesInYaml: [{ field: 'name', sideToVerifyDiff: 'right', isEditable: true, value: 'KAFKA' }],
        objectForCheckingSensitiveVariable: [{ varName: 'KAFKA', isSensitive: false }, { varName: 'sensitive', isSensitive: true }],
        valueToVerifyInDownloadedDefaultTemplate: process.env.clusterType === "enterprise" ? ['ApplicationName: dev-test-1'] : ['category: Global'],
        valueToVerifyInDownloadedLastSavedTemplate: process.env.clusterType === "enterprise" ? ['ApplicationName: playwright-scope-test'] : ['category: Global'],
        devtronAppName: ['playwright-scope-test'],
        devtronAppNameForPreCd: 'ui-autom' + BaseTest.generateRandomStringWithCharsOnly(4),
        devtronEnvNames: ['automation', 'devtron-demo'],
        apiUtils: undefined as unknown as ApiUtils,
        token: '',
        jobName: '',
        addInputVariablesData: [{ varName: 'KAFKA', varType: 'String', value: '@{{KAFKA}}' }, { varName: 'sensitive', varType: 'String', value: '@{{sensitive}}' }],
        valuesToCheckInLogsOfJob: process.env.clusterType == "enterprise" ? ['global', 'automtest'] : ['71', '61'],
        valuesToCheckInLogsOfCi: process.env.clusterType == "enterprise" ? ['global', 'automtest'] : ['71', '61'],
        valuesToCheckInPreCd: process.env.clusterType == "oss" ? ['71', '61'] : ['23', 'automtest'],
        customScript: ['echo $KAFKA', 'echo $sensitive']
    };

}



/**
 * This is hardcoded json for Oss only 
 * @param cmName 
 * @param csName 
 * @param superAdminObjects 
 * @param nonSuperAdminPageObjects 
 * @returns 
 */
export function scopeVariableObjectCreationForOss(cmName: string, csName: string, superAdminObjects: AllTypes.fixtures.allPageObjects, nonSuperAdminPageObjects: AllTypes.fixtures.allPageObjects) {
    return {
        dtcmcsVerificationObject: [
            {
                stage: 'base-configurations',
                strat: { configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } },
                fieldsToEdit: ['GracePeriod'],
                valuesToEdit: [`"@{{KAFKA}}"`],
                resourceTypeConfiguration: { rName: '', rType: 'dt' },
                scopeVarVerificationData: {
                    tooltipVerificationData: [{ scopeVariableName: 'KAFKA', isVisible: true, valueToVerify: ['71'] }, { scopeVariableName: 'sensitive', isVisible: true, valueToVerify: ['61'] }],
                    dtcmcsVerificationData: {
                        fields: ['GracePeriod'],
                        userLevelConfig: [{
                            pageObject: superAdminObjects,
                            values: ['71']
                        },
                        {
                            pageObject: nonSuperAdminPageObjects,
                            values: ['71']
                        }]
                    }
                }
            }, {
                stage: 'automation',
                strat: { configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } },
                fieldsToEdit: ['GracePeriod'],
                valuesToEdit: [`"@{{sensitive}}"`],
                resourceTypeConfiguration: { rName: '', rType: 'dt' },
                scopeVarVerificationData: {
                    tooltipVerificationData: [{ scopeVariableName: 'KAFKA', isVisible: true, valueToVerify: ['71'] }, { scopeVariableName: 'sensitive', isVisible: true, valueToVerify: ['61'] }],
                    dtcmcsVerificationData: {
                        fields: ['GracePeriod'],
                        userLevelConfig: [{
                            pageObject: superAdminObjects,
                            values: ['61']
                        },
                        {
                            pageObject: nonSuperAdminPageObjects,
                            values: ['hidden-value']
                        }]
                    }
                }
            }, {
                stage: 'automation',
                strat: { configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: true } },
                fieldsToEdit: ['GracePeriod'],
                valuesToEdit: ["@{{sensitive}}"],
                resourceTypeConfiguration: { rName: csName, rType: 'Secrets' },
                scopeVarVerificationData: {
                    tooltipVerificationData: [{ scopeVariableName: 'KAFKA', isVisible: true, valueToVerify: ['71'] }, { scopeVariableName: 'sensitive', isVisible: true, valueToVerify: ['61'] }],
                    dtcmcsVerificationData: {
                        fields: ['GracePeriod'],
                        userLevelConfig: [{
                            pageObject: superAdminObjects,
                            values: ['61']
                        },
                        {
                            pageObject: nonSuperAdminPageObjects,
                            values: ['hidden-value']
                        }]
                    }
                }
            }
        ],
        compareValueOrManifestObject: [compareValueOrManifestObjectCreation('automation', 'GracePeriod', `61`, CompareValueManifestCategorySection.Secrets)
        ],
        compareValueOrManifestForDeploymentHistory: [compareValueOrManifestObjectCreation('automation', 'GracePeriod', `61`, CompareValueManifestCategorySection.Secrets)
        ]
    }
}


/**
 * 
 * @param data 
 * @param rType 
 * @param fields 
 * @param superAdminPage 
 * @param nonSuperAdminPage 
 */
export async function verifyScopeVariableValueForSuperAndNonSuperAdmin(data: { pageObject: AllTypes.fixtures.allPageObjects, values: string[] }[], rType: string, fields: string[], superAdminPage: Page, nonSuperAdminPage: Page) {
    for (let i = 0; i < data.length; i++) {
        let currentPageUrl = superAdminPage.url();
        await nonSuperAdminPage.goto(currentPageUrl);
        await BaseTest.clickOnDarkMode(nonSuperAdminPage);
        await BaseTest.clickOnToolTipOkayButton(nonSuperAdminPage);
        await data[i].pageObject.baseDeploymentTemplatePage.clickOnAdvancedYamlOrGui();
        await data[i].pageObject.baseDeploymentTemplatePage.turnOnOffScopeVariableToggle(true);
        await data[i].pageObject.baseDeploymentTemplatePage.verifyFieldsValuesInDtCmCS(fields, data[i].values, rType, data[i].pageObject.jobsPage);
        await nonSuperAdminPage.bringToFront();
    }
    await superAdminPage.bringToFront();
}