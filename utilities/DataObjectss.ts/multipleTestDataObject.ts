
import { customHelmCatalogTemplate } from "../clipboardyYamls.ts/customObjectsOrYaml";
import { AllTypes } from "../Types";
import { BaseTest } from "../BaseTest";
//this is the change 
export namespace DataSet {
    export namespace LockConfigData {

        export function lockConfigDataObject(applicationCreated: string) {
            return {
                profiles: [profileCreation('specific-automation', 'GracePeriod', applicationCreated), profileCreation('match-criteria-automation', 'MaxSurge'), profileCreation('global-autmation', 'MaxUnavailable')],
                fieldsRelatedData: [fieldsData('base-configurations')],//, fieldsData('automation')
                filterRelatedData: [filtersData('profile', ['specific-automation', 'match-criteria-automation'], false, true), filtersData('environment', ['devtron-demo'], true, false)]
            }
        }
        function profileCreation(profileName: string, lockValue: string, applicationCreated = '') {

            var customAppCreated: string
            if (applicationCreated != '') {
                customAppCreated = applicationCreated!;
            }
            var scopeValue = profileName == "specific-automation" ? 'Specific deployment templates' : profileName == "match-criteria-automation" ? 'By match criteria' : 'Global (All deployment templates)';
            let filterTypeValue: any;
            var profileCreated = {
                profileName: profileName,
                lockValue: lockValue,
                scope: scopeValue,
            }
            if (profileName == 'specific-automation') {
                filterTypeValue = [applyFiltersWhileApplyingProfiles('Base deployment template', ''), applyFiltersWhileApplyingProfiles('Application', customAppCreated!)];
                (profileCreated as any).filterType = filterTypeValue;
            }
            else if (profileName == 'match-criteria-automation') {
                filterTypeValue = [applyFiltersWhileApplyingProfiles('Environment', 'automation')];
                (profileCreated as any).filterType = filterTypeValue;
            }
            return profileCreated;
        }
        function applyFiltersWhileApplyingProfiles(filterName: string, filterValue: string) {
            return {
                filterName: filterName,
                filterValue: filterValue
            }
        }
        function fieldsData(stage: string) {
            var fieldsResult = {
                stage: stage,
                fieldsToEdit: ['GracePeriod', 'MaxSurge', 'MaxUnavailable'],
                valuesToEdit: stage == 'base-configurations' ? ['50', '40', '30'] : ['80', '20', '40'],
                eligibleNotEligible: stage == 'base-configurations' ? eligibleNotEligibleFields(['GracePeriod', 'MaxSurge', 'MaxUnavailable'], [false, true, false]) : eligibleNotEligibleFields(['GracePeriod', 'MaxSurge', 'MaxUnavailable'], [true, false, false]),
                hideLockedKeys: stage == 'base-configurations' ? ['GracePeriod', 'MaxUnavailable'] : ['MaxSurge', 'MaxUnavailable'],
                verifyFieldsAfterSaving: stage == 'base-configurations' ? fieldsAfterSavingTemplate(['GracePeriod', 'MaxSurge'], ['50', '40'], [false, true]) : fieldsAfterSavingTemplate(['GracePeriod', 'MaxSurge'], ['80', '20'], [true, false])
            }
            return fieldsResult;
        }
        function eligibleNotEligibleFields(keyName: string[], isEligible: boolean[]) {
            let resultant: any = [];
            for (let i = 0; i < keyName.length; i++) {
                resultant.push({
                    fieldName: keyName[i],
                    isEligible: isEligible[i]
                })
            }
            return resultant;
        }
        function fieldsAfterSavingTemplate(field: string[], value: string[], isEditable: boolean[]) {
            let result: any = [];
            for (let i = 0; i < field.length; i++) {
                result.push({
                    field: field[i],
                    value: value[i],
                    isEditable: isEditable[i]
                })
            }
            return result;
        }
        function filtersData(filterType: string, filterValue: string[], isNull: boolean, ClearPreviousFilter: boolean) {
            let resultant = {
                filterType: filterType,
                filterValue: filterValue,
                isNullState: isNull,
                ClearPreviousFilter: ClearPreviousFilter
            }
            if (!isNull) {
                (resultant as any).verificationOfFields = [{ scope: "Base deployment template", profileValue: 'specific-automation' }, {
                    scope: "matching", profileValue: 'match-criteria-automation'
                }]
            }
            return resultant;

        }

    }

    export namespace ResourceCatalog {
        export function reourceCatalogData(data: { helmAppUrl: string, devtronAppUrl: string, jobUrl: string, clusterUrl: string }) {
            return [prepairingData({ kind: 'Devtron applications', isSampleSchema: true, navigationUrl: data.devtronAppUrl }),
            prepairingData({ kind: 'Jobs', isSampleSchema: true, navigationUrl: data.jobUrl }),
            prepairingData({ kind: 'Clusters', isSampleSchema: true, navigationUrl: data.clusterUrl }),
            prepairingData({ kind: 'Helm applications', isSampleSchema: false, navigationUrl: data.helmAppUrl, customObject: customHelmCatalogTemplate() }),
            prepairingData({ kind: 'Helm applications', isSampleSchema: true, navigationUrl: data.helmAppUrl })
            ];
        }
        function prepairingData(data: { kind: string, isSampleSchema: boolean, navigationUrl: string, customObject?: any }) {
            const result = {
                navigationUrl: data.navigationUrl,
                editingResourceCatalog: {
                    resourceKind: data.kind,
                    isSampleSchema: data.isSampleSchema,
                },
                reviewChanges: [configDiff(data.kind, data.isSampleSchema)],
                fillDataInForms: enterDataInForms(data.kind, data.isSampleSchema),
                verificationAfterSavingFields: verificationOfFieldsData(data.kind, data.isSampleSchema)
            }
            if (data.customObject) {
                (result.editingResourceCatalog as any).customizedObjects = data.customObject;
            }
            return result;

        }
        function configDiff(kind: string, isSampleSchema: boolean) {
            let theLastKey = kind == "Clusters" ? 'Documentation' : kind == "Devtron applications" || kind == "Helm applications" ? 'refType' : 'Operational'
            let result = {
                fieldName: kind == "Devtron applications" || kind == 'Helm applications' ? 'Runbook' : 'Team',
                sideToVerify: 'right',
                isVisible: isSampleSchema,
                theLastKey: theLastKey
            }
            return result;
        }
        function enterDataInForms(kind: string, isSampleSchema: boolean) {
            let resultantArray: any = [];
            if (kind == 'Clusters') {
                resultantArray.push({ keyName: 'Owner', values: ['system'] });
                resultantArray.push({ keyName: 'Team', values: ['growth team'] });
            }
            else if (kind == 'Devtron applications' || kind == "Helm applications") {
                if (!isSampleSchema) {
                    resultantArray.push({ keyName: 'Code owners', values: ['admin'] });
                }
                else {
                    resultantArray.push({ keyName: 'Code owners', values: ['system'] });
                }
            }
            else {
                resultantArray.push({ keyName: 'Access manager', values: ['system'] });
                resultantArray.push({ keyName: 'Owner', values: ['system'] });
            }
            return resultantArray;
        }
        function verificationOfFieldsData(kind: string, isSampleSchema: boolean) {
            let valueToVerify: string = kind == "Helm applications" && !isSampleSchema ? 'admin' : 'system';
            if ((kind == "Helm applications" || kind == "Devtron applications")) {
                let helmobjectResult = {
                    gui: {
                        sectionName: 'Owners & Pager Duty',
                        keyName: 'Code owners',
                        valueName: [valueToVerify]
                    }
                }
                if (kind == "Helm applications" && !isSampleSchema) {
                    (helmobjectResult as any).gui.hiddenFields = ['Runbook'];
                }
                return helmobjectResult;
            }
            else if (kind == "Clusters") {
                let objectResult = {
                    gui: {
                        sectionName: 'Contacts',
                        keyName: 'Owner',
                        valueName: ['system']
                    }
                }
                return objectResult;
            }
            else {
                let jobObjectResult = {
                    gui: {
                        sectionName: 'Contacts',
                        keyName: 'Owner',
                        valueName: ['system']
                    },
                    json: ['system']
                }
                return jobObjectResult;
            }

        }
    }
    export namespace dependencyInjection {
        export function dependencyInjectionData(currentAppName: string, MappedAppName: string) {
            let data = {
                envMapping: [envMapping('automation', 'automation'), envMapping('devtron-demo', 'devtron-demo')],
                dependencyDetails: [verifyDependencyDetails(currentAppName, MappedAppName, 'devtron-demo', true),
                verifyDependencyDetails(currentAppName, MappedAppName, 'automation', true)
                ],
                dependencydetailsAfterDeletion: [verifyDependencyDetails(currentAppName, MappedAppName, 'devtron-demo', true),
                verifyDependencyDetails(currentAppName, MappedAppName, 'automation', false)]
            }
            return data;

        }
        function envMapping(currentAppEnv: string, mappedAppEnv: string) {
            return {
                currentAppEnvName: currentAppEnv,
                mappedAppEnvName: mappedAppEnv
            }
        }
        function verifyDependencyDetails(currentAppName: string, mappedAppName: string, envName: string, isvisible: boolean) {
            let dataToBeReturned = {
                currentEnvName: envName,
                verificationData: {
                    0: { [envName]: isvisible, 'test': isvisible, [mappedAppName]: true },
                    1: { 'test': true, [currentAppName]: true }
                }
            }
            return dataToBeReturned;
        }
    }
    export namespace ApitokenData {
        export function apitokenData(tokenName: string, permissionType: string, applications: string[] = ['All applications'], environments: string[] = ['All environments'], acess: string[] = ['Admin'], resourceType: string = "Devtron Apps", projectName: string[] = ['devtron-demo'], workflowName: string = '') {
            var result: AllTypes.apiTokenPage.apiTokenObject = {
                tokenName: tokenName, expirationDetails: { type: 'No expiration' }, permissionData: {
                    permissionType: permissionType
                }
            }
            if (permissionType == 'specific-user-permission') {
                let dropdownNameForAppsOrJobs: string = workflowName ? 'Select Job' : 'Select applications'
                result.permissionData.specificPermission = {
                    ResourceType: resourceType,
                    permissions: {
                        'project': projectName,
                        'environment': environments,
                        'appOrJob': applications,
                        'role': acess
                    }
                }
                if (workflowName) {
                    result.permissionData.specificPermission.permissions['workflow-for-job'] = [workflowName]
                }
            }

            return result;

        }
    }
    export namespace ApplicationGroupTest {
        export function appGroupDataSet() {
            const result = [objectCreation('hibernation', true), objectCreation('hibernation', false), objectCreation('unhibernation', true), objectCreation('restartWorkloads', true), objectCreation('clonePipeline', true), objectCreation('clonePipeline', false)];
            return result;
        }
        function objectCreation(processName: string, isSuccedded: boolean) {
            let isFilterRequired = (processName == "hibernation" && isSuccedded == true) ? false : true;
            return {
                isFilterRequired: isFilterRequired,
                processName: processName,
                isSuccedded: isSuccedded,
                functionToCall: processToDo
            }
        }
        async function processToDo(processName: string, isSuccedded: boolean, AllObjects: AllTypes.fixtures.allPageObjects, appName: string) {
            switch (processName) {
                case 'hibernation':
                case 'unhibernation': {
                    let messageToVerify = isSuccedded == true && processName == "hibernation" ? 'Hibernation Initiated' : isSuccedded == false && processName == "hibernation" ? 'Hibernation already in progress' : 'Unhibernation Initiated';
                    let isHibernation = processName == "hibernation" ? true : false;
                    await AllObjects.overViewPage.bulkHibernateOrUnhibernateOnAppGroup(isHibernation, messageToVerify);
                    if (isSuccedded) {
                        await AllObjects.createAppPage.searchAndSelectAppsFromList(appName);
                        await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus(isHibernation ? 'Hibernating' : 'Progressing');
                    }
                    break;
                }
                case 'restartWorkloads': {
                    let messageToVerify: string = isSuccedded ? 'initiated' : 'failed';
                    await AllObjects.overViewPage.bukRestartWorkloads([{ message: messageToVerify, numberOfOccurences: 1 }]);
                    if (isSuccedded) {
                        await AllObjects.createAppPage.searchAndSelectAppsFromList(appName);
                        await AllObjects.chartStoreAppDetailsPage.verifyPodAge(50);
                    }
                    break;
                }
                case 'clonePipeline': {
                    let cloneToSameWorkflow = isSuccedded ? false : true
                    let messageToVerify = isSuccedded ? 'Clone successful' : 'Target pipeline already exists'
                    await AllObjects.overViewPage.clonePipelineConfiguration({ envName: 'devtron-demo', cloneToSameWorkflow: cloneToSameWorkflow, responseVerification: [{ text: messageToVerify, numberOfOccurrences: 1 }] });
                    if (isSuccedded) {
                        await AllObjects.createAppPage.searchAndSelectAppsFromList(appName);
                        await AllObjects.appConfigurationPage.appConfigurationTab.click();
                        await AllObjects.appConfigurationPage.verifyCdNodes({ workflowNumber: 1, cdName: 'devtron-demo', textToVerify: [{ text: 'Pre-deploy', isVisible: true }] });
                        await AllObjects.jobsPage.operBaseOrEnvOverRideResources('devtron-demo', true);
                        await AllObjects.jobsPage.clickOnAnyCmCSAndReturnExistence('ConfigMaps', 'custom-test');
                        await AllObjects.jobsPage.verifyCmSecrets({
                            keyName: ['custom-key'],
                            valueName: ['custom-value'],
                        });
                    }
                    break;
                }

            }
        }
    }
    export namespace appListPage {
        export function dataSetForFiltering() {
            return [objectCreation('environment', 'devtron-demo', 'devtron-demo'), objectCreation('app-status', 'Healthy', 'Healthy')];
        }
        function objectCreation(filterType: string, filterValue: string, verificationText: string) {
            return { filterType: filterType, filterValue: filterValue, clearPreviousFilter: false, verificationText: verificationText }
        }
    }
    export namespace externalHelmInstallation {
        export function dataSetForExternalHelm() {
            let array: any = [];
            for (let i = 0; i < 2; i++) {
                let chartName: string = i == 0 ? 'memcached' : 'memcached';
                let chartVersion: string = i == 0 ? '7.4.1' : '7.4.1';
                let envName: string = i == 0 ? 'default' : 'automation';
                let projectName: string = i == 0 ? '' : 'devtron-demo';

                // Create a new object for each iteration
                let result: any = {
                    chartName: chartName,
                    chartVersion: chartVersion,
                    envName: envName,
                    projectName: projectName
                };

                array.push(result);
            }
            return array;
        }

    }
    export namespace Rbac {
        export function dataSetForDevtronApps(appName1: string, appName2: string, tokenName: string, projectName: string, tokenNameForManager: string, superAdminPermissionGroupName: string) {
            return [objectCreationForDevtronApps('Manager', 'All applications', 'All environments', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('Manager', appName1, 'devtron-demo', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('View only', 'All applications', 'All environments', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('View only', appName1, 'devtron-demo', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('Admin', 'All applications', 'All environments', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('Admin', appName1, 'devtron-demo', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('Build and deploy', 'All applications', 'All environments', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            objectCreationForDevtronApps('Build and deploy', appName1, 'devtron-demo', tokenName, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName),
            ];

        }
        function objectCreationForDevtronApps(acess: string, appsPermission: string, envPermission: string, tokenName: string, appName1: string, appName2: string, projectName: string, tokenNameForManager: string, superAdminPermissionGroupName: string) {

            let result: AllTypes.Rbac.devtronAppObjectCreation = {
                permissionDetails: ApitokenData.apitokenData(tokenName, 'specific-user-permission', [appsPermission], [envPermission], [acess]),
                accessibility: accessibilityObjectCreationForDevtronApps(acess, appsPermission, appName1, appName2, projectName, tokenNameForManager, superAdminPermissionGroupName)
            }
            return result;
        }
        export function accessibilityObjectCreationForDevtronApps(acess: string, appsPermission: string, appName1: string, appName2: string, projectName: string, tokenNameForManager: string, superAdminPermissionGroupName: string) {
            let result: AllTypes.Rbac.acessibilityDevtronApps = {};
            if (acess == "View only") {
                if (appsPermission == "All applications") {
                    result.appListing = [{ appName: appName1, isVisible: true }, { appName: appName2, isVisible: true }];
                    result.trigger = { passFailCriteria: [{ appName: appName1, isEligible: false, envName: 'devtron-demo', isTriggerPageVisible: true }, { appName: appName1, isEligible: false, envName: 'automation', isTriggerPageVisible: true }] };
                }
                else {
                    result.appListing = [{ appName: appName1, isVisible: true }, { appName: appName2, isVisible: false }];
                    result.appDetails = [{ terminalConnection: false, downloadLogs: false, appName: appName1, envName: 'devtron-demo' }];
                    result.trigger = { passFailCriteria: [{ appName: appName1, isEligible: false, envName: 'devtron-demo', isTriggerPageVisible: true }, { appName: appName1, isEligible: false, envName: 'automation', isTriggerPageVisible: false }] };
                }
                result.create = { isEligible: false, projectNameNotVisible: projectName };
                result.edit = { passFailCriteria: [{ appName: appName1, isEligible: false, envName: 'devtron-demo' }] }
            }
            else if (acess == "Build and deploy") {
                if (appsPermission == "All applications") {
                    result.trigger = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true, isTriggerPageVisible: true }, { appName: appName2, isEligible: true, envName: 'automation', isTriggerPageVisible: true }] }
                }
                else {
                    result.trigger = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true, isTriggerPageVisible: true }, { appName: appName1, isEligible: false, envName: 'automation', isTriggerPageVisible: false }] }
                    result.appGroup = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true }, { appName: appName1, envName: 'automation', isEligible: false }] }
                    result.delete = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: false }] }
                    result.appDetails = [{ terminalConnection: false, downloadLogs: false, appName: appName1, envName: 'devtron-demo' }];
                }
            }
            else if (acess == "Admin") {
                if (appsPermission == "All applications") {
                    result.trigger = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true, isTriggerPageVisible: true }, { appName: appName2, isEligible: true, envName: 'automation', isTriggerPageVisible: true }] }
                    result.create = { isEligible: true };
                }
                else {
                    result.trigger = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true, isTriggerPageVisible: true }, { appName: appName1, isEligible: false, envName: 'auomation', isTriggerPageVisible: false }] }
                    result.appDetails = [{ terminalConnection: true, downloadLogs: true, envName: 'devtron-demo', appName: appName1 }, { terminalConnection: false, downloadLogs: false, envName: 'automation', appName: appName1 }];
                    result.delete = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true }, { appName: appName1, envName: 'automation', isEligible: false }] }
                    result.userPermissions = { isPageVisible: false };
                }
            }
            else if (acess == "Manager") {
                if (appsPermission == "All applications") {

                    result.edit = { passFailCriteria: [{ appName: appName1, envName: 'devtron-demo', isEligible: true }, { appName: appName2, envName: 'automation', isEligible: true }] }
                    result.appLevelConfig = [{ appName: appName1, isEligible: true }];
                }
                else {
                    result.appDetails = [{ terminalConnection: true, downloadLogs: true, envName: 'devtron-demo', appName: appName1 }, { terminalConnection: false, downloadLogs: false, envName: 'automation', appName: appName1 }];
                    result.userPermissions = { isPageVisible: true, superAdminPermissionGroupName: superAdminPermissionGroupName, secondApiTokenName: tokenNameForManager };
                }
            }
            return result;
        }

        export function dataCreationForHelmRbac(tokenName: string, unassginedAppName: string, assignedAppName: string) {

            return [objectCreationForHelmApps(tokenName, 'devtron-demo', 'All existing + future environments in default_cluster', 'All applications', 'Admin', unassginedAppName, assignedAppName),
            objectCreationForHelmApps(tokenName, 'devtron-demo', 'automation', assignedAppName, 'Admin', unassginedAppName, assignedAppName),
            objectCreationForHelmApps(tokenName, 'devtron-demo', 'automation', 'All applications', 'View only', unassginedAppName, assignedAppName),
            objectCreationForHelmApps(tokenName, 'devtron-demo', 'automation', 'All applications', 'View & Edit', unassginedAppName, assignedAppName),
            objectCreationForHelmApps(tokenName, 'Unassigned', 'All existing + future environments in default_cluster', 'All applications', 'Admin', unassginedAppName, assignedAppName),
            objectCreationForHelmApps(tokenName, 'Unassigned', 'All existing + future environments in default_cluster', 'All applications', 'View only', unassginedAppName, assignedAppName)

            ]
        }
        function objectCreationForHelmApps(tokenName: string, projectName: string, envName: string, applicationName: string, acess: string, unassginedAppName: string, assignedAppName: string) {
            let result: AllTypes.Rbac.helmAppDataCreation = {
                permissionDetails: ApitokenData.apitokenData(tokenName, 'specific-user-permission', [applicationName], [envName], [acess], 'Helm Apps', [projectName]),
                accessibility: acessibilityCreationForHelmApps(projectName, envName, unassginedAppName, assignedAppName, acess)
            }
            return result;
            function acessibilityCreationForHelmApps(projectName: string, envName: string, unassginedAppName: string, assignedAppName: string, acess: string) {
                let result: AllTypes.Rbac.helmAppsAccessibility = {}
                if (acess == "Admin") {
                    if (projectName == "devtron-demo") {
                        envName == "All existing + future environments in default_cluster" ? result.create = { isEligible: true } : result.create = { isEligible: false }
                        result.edit = [{ appName: assignedAppName, isEligible: true }];
                        result.appDetails = [{ terminalConnection: true, downloadLogs: true, appName: assignedAppName }]
                    }
                    else {
                        result.create = { isEligible: false };
                        result.delete = [{ isEligible: true, appName: unassginedAppName }];
                        result.appListing = [{ isEligible: false, appName: assignedAppName }];
                        result.appDetails = [{ terminalConnection: true, downloadLogs: true, appName: unassginedAppName }]
                    }
                }
                else if (acess == "View only") {
                    if (projectName == "devtron-demo") {
                        result.appDetails = [{ terminalConnection: false, downloadLogs: false, appName: assignedAppName }];
                        result.delete = [{ isEligible: false, appName: assignedAppName }];
                    }
                    else {
                        result.appDetails = [{ terminalConnection: false, downloadLogs: false, appName: unassginedAppName }];
                        result.delete = [{ isEligible: false, appName: unassginedAppName }];
                    }
                }
                else if (acess == "View & Edit") {
                    let appNameToPerformAction: string = unassginedAppName;
                    if (projectName == "devtron-demo") {
                        result.edit = [{ appName: assignedAppName, isEligible: true }];
                        appNameToPerformAction = assignedAppName;
                    }
                    result.delete = [{ isEligible: false, appName: appNameToPerformAction }];
                }

                return result;
            }
        }
        export function dataCreationForJobsRbac(jobsData: { jobName1: string, workflowName1: string, jobName2: string, workflowName2: string }, tokenName: string) {
            let resultant = [objectCreationForJobs('devtron-demo', 'All Jobs', jobsData.workflowName1, 'All environments', tokenName, 'Admin', jobsData),
            objectCreationForJobs('devtron-demo', 'All Jobs', 'All Workflows', 'All environments', tokenName, 'Admin', jobsData),
            objectCreationForJobs('devtron-demo', jobsData.jobName1, 'All Workflows', 'All environments', tokenName, 'Run job', jobsData),
                //objectCreationForJobs('devtron-demo', jobsData.jobName1, 'All Workflows', 'All environments', tokenName, 'View only', jobsData)
            ];
            return resultant;

        }
        function objectCreationForJobs(project: string, jobNamePermission: string, workflowNamePermission: string, envNamePermisison: string, tokenName: string, acess: string, jobsData: any) {
            let result: AllTypes.Rbac.jobsDataCreation = {
                permissionDetails: ApitokenData.apitokenData(tokenName, 'specific-user-permission', [jobNamePermission], [envNamePermisison], [acess], 'Jobs', [project], workflowNamePermission),
                accessibility: acessibilityForJobs(jobNamePermission, workflowNamePermission, jobsData, acess)
            }
            return result;
        }
        export function acessibilityForJobs(jobNamePermission: string, workflowNamePermission: string, jobsData: any, acess: string) {
            let result: AllTypes.Rbac.jobsAcessibility = {}
            if (acess == "Admin") {
                if (jobNamePermission == "All Jobs") {
                    if (workflowNamePermission != 'All Workflows') {
                        let jobNameWithNoWorkflowVisible = workflowNamePermission == jobsData.workflowName1 ? jobsData.jobName2 : jobsData.jobName1;
                        let jobsNameWithWorkflowVisible = jobNameWithNoWorkflowVisible == jobsData.jobName1 ? jobsData.jobName2 : jobsData.jobName1;
                        result.workflowListing = [{ jobName: jobNameWithNoWorkflowVisible, workflowVisibility: false }, { jobName: jobsNameWithWorkflowVisible, workflowVisibility: true }]
                    } else {
                        result.run = [{ jobName: jobsData.jobName1, isEligible: true }, { jobName: jobsData.jobName2, isEligible: true }]
                        result.edit = [{ jobName: jobsData.jobName1, isEligible: true }]
                    }
                }
                else {
                    result.edit = [{ jobName: jobsData.jobName1, isEligible: true }]
                }
            }
            // we are giving first time wrong env 
            else if (acess == "Run job") {
                if (workflowNamePermission == jobsData.workflowName2) {
                    result.run = [{ jobName: jobsData.jobName1, isEligible: false }]
                }
                else {
                    result.run = [{ jobName: jobsData.jobName1, isEligible: true }]
                    result.edit = [{ jobName: jobsData.jobName1, isEligible: false }]
                }

            }
            else if (acess == "View only") {
                if (jobNamePermission == "All Jobs") {
                    result.jobListing = [{ jobName: jobsData.jobName1, isVisible: true }, { jobName: jobsData.jobName2, isVisible: true }]
                    result.edit = [{ jobName: jobsData.jobName1, isEligible: false }]
                }
                else {
                    result.jobListing = [{ jobName: jobsData.jobName1, isVisible: true }, { jobName: jobsData.jobName2, isVisible: false }]
                    result.run = [{ jobName: jobsData.jobName1, isEligible: false }]
                }
            }
            return result;

        }
        export function dataCreationFork8sResources(deploymentName1: string, deploymentName2: string, providerType: string) {
            return [objectCreationFork8s('All Namespaces / Cluster scoped', 'Deployment', 'Active', 'Admin', deploymentName1, deploymentName2, providerType),
            objectCreationFork8s('automation', 'Deployment', 'Active', 'Admin', deploymentName1, deploymentName2, providerType),
            objectCreationFork8s('All Namespaces / Cluster scoped', 'Deployment', 'Active', 'View', deploymentName1, deploymentName2, providerType),
            objectCreationFork8s('All Namespaces / Cluster scoped', 'Node', 'Active', 'View', deploymentName1, deploymentName2, providerType)
            ]
        }
        function objectCreationFork8s(namespacePerm: string, kindPerm: string, statusPerm: string, rolePerm: string, deploymenName1: string, deploymentName2: string, providerType: string) {
            let result = {
                accessDetails: dataForAcessFork8s(namespacePerm, kindPerm, statusPerm, rolePerm),
                accessibility: accessibilityFork8sResources(namespacePerm, statusPerm, rolePerm, deploymenName1, deploymentName2, providerType, kindPerm)
            }
            return result;
        }
        function accessibilityFork8sResources(namespacePerm, statusPerm, rolePerm, deploymentName1, deploymentName2, providerType: string, kindPerm: string): AllTypes.Rbac.k8sAcessibility {
            let result: AllTypes.Rbac.k8sAcessibility = {}
            if (rolePerm == "Admin") {
                if (namespacePerm == "All Namespaces / Cluster scoped" && statusPerm != "Inactive") {
                    result.visibilityOfResources = [{ resourceType: 'pod', isResourceTypeVisible: true, resourceName: deploymentName1, isResourceNameVisible: true }, { resourceType: 'pod', isResourceTypeVisible: true, resourceName: deploymentName2, isResourceNameVisible: true }]
                    result.resourceLevelOperations = [{ resourceType: 'Deployment', resourceName: deploymentName1, manifest: { isEligible: true } }, { resourceType: 'pod', resourceName: deploymentName1, terminal: { isEligible: true }, delete: { isEligible: true } }]
                }
                else if (namespacePerm != "All Namespaces / Cluster scoped" && statusPerm != "Inactive") {
                    result.visibilityOfResources = [{ resourceType: 'Deployment', isResourceTypeVisible: true, resourceName: deploymentName1, isResourceNameVisible: true }, { resourceType: 'Deployment', isResourceTypeVisible: true, resourceName: deploymentName2, isResourceNameVisible: false }];
                    result.resourceLevelOperations = [{ resourceType: 'Deployment', resourceName: deploymentName1, manifest: { isEligible: true } }];
                }
            } else if (rolePerm == "View") {
                if (kindPerm == "Node") {
                    result.nodeActions = { cordon: false, taints: false, drain: false };
                }
                else {
                    result.visibilityOfResources = [{ resourceType: 'Deployment', isResourceTypeVisible: true, resourceName: deploymentName1, isResourceNameVisible: true }, { resourceType: 'Pod', isResourceTypeVisible: true, resourceName: deploymentName2, isResourceNameVisible: true }];
                    result.resourceLevelOperations = [{ resourceType: 'Deployment', resourceName: deploymentName1, manifest: { isEligible: false } }, { resourceType: "Pod", resourceName: deploymentName1, terminal: { isEligible: false } }];
                }

            }

            return result;
        }
        function dataForAcessFork8s(namespace: string, kind: string, status: string, role: string) {
            return {
                cluster: 'default_cluster',
                namespace: namespace,
                api: 'All API groups',
                kind: kind,
                resource: 'All resources',
                role: role,
                status: status,
            }
        }
        //cluster: string, namespace: string, api: string, kind: string, resource: string, status: string
        export function dataCreationForChartGroups(chartName: string) {

            return [objectCreationForChartGroups({ create: true }, { createChartGroup: { isEligible: true }, editChartGroup: { chartGroupName: chartName, isEligible: true } }),
            objectCreationForChartGroups({ create: false, editForSpecificChartNames: [chartName] }, { createChartGroup: { isEligible: false }, editChartGroup: { chartGroupName: chartName, isEligible: true } })
            ]
        }
        function objectCreationForChartGroups(acessRelatedData: AllTypes.Rbac.dataForAcessForChartGroups, acessibility: AllTypes.Rbac.acessibilityForChartGroups) {
            let result = {
                acess: acessRelatedData,
                acessibility: acessibility
            };
            return result;
        }



    }
    // export namespace ApprovalPolicy {
    //     export function dataSetForApprovalPolicy(appName: string, userGroupName: string, mailsluprUser: string) {
    //         return [objectCreationForApprovalPolicy(['specific Users', 'user group'], "By match criteria", appName, userGroupName, mailsluprUser),
    //         objectCreationForApprovalPolicy(['specific Users'], "Specific criteria", appName, userGroupName, mailsluprUser),
    //         objectCreationForApprovalPolicy(['normal approver'], "Global", appName, userGroupName, mailsluprUser)]
    //     }

    //     function objectCreationForApprovalPolicy(userApprovalDetails: string[], scope: string, appName: string, userGroupName: string, mailSlurpUser: string) {
    //         let stageForConfigurationApproval = scope == "By match criteria" ? "base-configurations" : "automation";
    //         let notifUsers = scope == "By match criteria" ? [mailSlurpUser] : undefined;
    //         let reasonForProposal = scope == "Global" ? "delete" : "edit";
    //         let approveSuccessfully: boolean = scope == "Global" ? false : true;
    //         let cdTypeAutoManual: 'Auto' | "Manual" = scope == "By match criteria" ? "Auto" : "Manual";
    //         let numberOfApprovedImages: number = scope == "By match criteria" ? 0 : 1
    //         let triggerOrExpire: "trigger" | "expire" | undefined = scope == "By match criteria" ? undefined : scope == "Global" ? "expire" : "trigger";
    //         return {
    //             profileCreationData: objectCreationForProfileCreationData({ name: 'playwright-test', userApprover: userApprovalDetails, profileApplyingScope: scope, appName: appName, userGroupName: userGroupName }),
    //             configurationRelatedData: [objectCreationForConfigurationVerification(stageForConfigurationApproval, "dt", notifUsers, reasonForProposal, approveSuccessfully, 'testing'), objectCreationForConfigurationVerification(stageForConfigurationApproval, "ConfigMaps", notifUsers!, reasonForProposal, approveSuccessfully, 'testing')],
    //             deploymentRelatedConfig: {
    //                 notifUsers: notifUsers,
    //                 approveSuccessfully: approveSuccessfully,
    //                 cdType: cdTypeAutoManual,
    //                 numberOfApprovedImages: numberOfApprovedImages,
    //                 triggerOrExpire: triggerOrExpire
    //             }
    //         }
    //     }

    //     function objectCreationForProfileCreationData(data: { name: string, userApprover: string[], userGroupName: string, profileApplyingScope: any, appName: string }) {
    //         let userApprovalObject: any = {};
    //         for (let key of data.userApprover) {
    //             if (key == "normal approver") {
    //                 userApprovalObject.normalApproverCount = 1;
    //             }
    //             if (key == "user group") {
    //                 userApprovalObject.userGroupDetails = { count: 1, name: data.userGroupName }
    //             }
    //             if (key == "specific Users") {
    //                 let clearUserGroup: boolean = data.userApprover.length == 1 ? true : false
    //                 userApprovalObject.specificUsers = { clearUserGroup: clearUserGroup, names: ['playwright-super-admin'] }
    //             }
    //         }
    //         return {
    //             profileName: data.name,
    //             userApprovalData: userApprovalObject,
    //             profileApplyingData: [objectCreationForApplyingProfile(data.profileApplyingScope, { configuration: { configMaps: true, secrets: true, dt: true } }, data.appName), objectCreationForApplyingProfile(data.profileApplyingScope, { imageApproval: true }, data.appName)]
    //         }
    //     }
    //     function objectCreationForApplyingProfile(scope: "Global" | "By match criteria" | "Specific deployment templates" | "Specific criteria", selectPolicyApplyingStrategy: any, appName: string) {
    //         return {
    //             applyingStrategy: selectPolicyApplyingStrategy,
    //             scope: scope,
    //             filterData: [{ filterName: 'Project', filterValue: "devtron-demo" }, { filterName: 'Application', filterValue: appName }]
    //         }
    //     }
    //     function objectCreationForConfigurationVerification(stage: string, resourceType: string, notifUsers: string[] | undefined, reasonForProposal: string, approveSuccessfully: boolean, resourceName: string) {
    //         let stageValue = resourceType == "ConfigMaps" && stage == "base-configurations" && reasonForProposal != "delete" ? "automation" : resourceType == "ConfigMaps" && stage == "automation" && reasonForProposal != "delete" ? "base-configurations" : stage;
    //         let approveValue = reasonForProposal == "delete" && resourceType == "ConfigMaps" ? false : approveSuccessfully;
    //         let value = stageValue == "base-configurations" || (reasonForProposal == "delete" && approveValue) || (resourceType == "ConfigMaps" && reasonForProposal == "delete") ? "32" : "34"
    //         return {
    //             stage: stageValue,
    //             resourceType: resourceType,
    //             resourceName: resourceName,
    //             notifUsers: notifUsers,
    //             reasonForProposal: reasonForProposal,
    //             approveSuccessfully: approveValue,
    //             key: "GracePeriod",
    //             value: value
    //         }

    //     }
    // }
    export namespace bulkEdit {
        export function dataObjectForBulkEdit(applications: string[], envIds: number[]) {
            return {
                includeNames: applications,
                envIds: envIds,
                global: false,
                deploymentPatch: {
                    addPath: "/GracePeriod",
                    replacePath: "/GracePeriod",
                    value: 47,
                },
                configMapPatch: {
                    addPath: "/newadded",
                    replacePath: "/custom-key",
                    key: "SomeKey",
                    value: "cmvalue",
                },
                secretPatch: {
                    addPath: "/newadd",
                    replacePath: "/custom-key",
                    key: "AnotherKey",
                    value: "mayank",
                },
                configMapNames: ["custom-test"],
                secretNames: ["secretnew"],
            }
        }
    }
    export namespace DownloadLogs {
        export function dataObjectForDownloadLogs() {
            return [objectCreationForDownloadLogs("Custom...", 'set duration'), objectCreationForDownloadLogs("Custom...", 'set lines'), objectCreationForDownloadLogs("Custom...", 'specific date'), objectCreationForDownloadLogs("Custom...", 'All logs')];
        }
        function objectCreationForDownloadLogs(valueToSelect: 'Last 15 minutes' | "Last 30 minutes" | "Last 1 hour" | "Last 2 hours" | "5,000 lines" | "1,000 lines" | "500 lines" | "10,000 lines" | "Custom...", customValue?: string): AllTypes.downloadLogs.downloadLogsData {
            let dateObject = new Date();
            let result: AllTypes.downloadLogs.downloadLogsData = {} as AllTypes.downloadLogs.downloadLogsData;
            result.valueToSelectFromDropdown = valueToSelect;
            if (customValue == "set duration") {
                result.customDataSet = {
                    'Set duration': {
                        value: "1",
                        unit: "Minutes",
                        checkValidation: true,
                        isSuccessfull: false
                    }
                }
            }
            if (customValue == "set lines") {
                result.customDataSet = {
                    'Set lines': {
                        value: "2",
                        checkValidationo: true,
                        isSuccessfull: true
                    }
                }
            }
            if (customValue == "specific date") {
                result.customDataSet = {
                    'Since date & time': {
                        month: BaseTest.getCurrentMonthNameInLocalizedFormat(),
                        year: dateObject.getFullYear().toString(),
                        date: (dateObject.getDate() - 1).toString(),
                        time: "",
                        isSuccessfull: true
                    }
                }
            }
            if (customValue == "All logs") {
                result.customDataSet = {
                    'All available': {
                        isSuccessfull: true
                    }
                }
            }
            return result;
        }
    }
    export namespace webhook {
        export function webhookDataSet() {
            let result = {
                webhookConfiguration: {
                    isAutoGeneratedToken: Boolean,
                    checkValidation: Boolean
                },
                nodeConfigurations: [{

                }]


            }
        }
    }
}
