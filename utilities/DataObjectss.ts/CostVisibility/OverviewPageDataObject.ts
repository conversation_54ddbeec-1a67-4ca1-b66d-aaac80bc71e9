import fs from 'fs';
import { MockRoute } from '../../ApiRouter';

let serverUrl:string=process.env.BASE_SERVER_URL!.substring(0,process.env.BASE_SERVER_URL!.indexOf('/dashboard'));

// Separate object for file paths
const filePaths = {
    trendForAdditionalCostTypes: 'utilities/MockedApiResponses/CostVisibility/OverviewPageApiResponses/trendForAdditionalCostTypes.json',
    potentialSavingsAndSortOrder: 'utilities/MockedApiResponses/CostVisibility/OverviewPageApiResponses/potentialSavingsAndSortOrder.json',
    totalCostAndSortOrder: 'utilities/MockedApiResponses/CostVisibility/OverviewPageApiResponses/totalCostAndSortOrder.json',
    trendForAdditionalCostTypesEmpty: 'utilities/MockedApiResponses/CostVisibility/OverviewPageApiResponses/trendForAdditionalCostTypesEmpty.json',
    potentialSavingsAndSordOrderEmpty: 'utilities/MockedApiResponses/CostVisibility/OverviewPageApiResponses/potentialSavingsAndSordOrderEmpty.json',
    totalCostAndSortOrderEmpty: 'utilities/MockedApiResponses/CostVisibility/OverviewPageApiResponses/totalCostAndSortOrderEmpty.json'
};

export let mockedApisData: MockRoute[]= [ {
    url: `${serverUrl}/orchestrator/cost/summary/cluster?timeWindow=month&includeTrendForAdditionalCostTypes=true&includeTrendForSubResources=false`,
    method: 'GET',
    body: JSON.stringify(JSON.parse(fs.readFileSync(filePaths.trendForAdditionalCostTypes, 'utf8')),null,2)
},
{
    url: `${serverUrl}/orchestrator/cost/breakdown/cluster?timeWindow=month&sortBy=potentialSavings&sortOrder=DESC&size=5&offset=0`,
    method: 'GET',
    body: JSON.stringify(JSON.parse(fs.readFileSync(filePaths.potentialSavingsAndSortOrder, 'utf8')),null,2)
},
{
    url: `${serverUrl}/cost/breakdown/cluster?timeWindow=month&sortBy=totalCost&sortOrder=DESC&size=8&offset=0`,
    method: 'GET',
    body: JSON.stringify(JSON.parse(fs.readFileSync(filePaths.totalCostAndSortOrder, 'utf8')),null,2)
},{
    url: `${serverUrl}/orchestrator/cost/summary/cluster?timeWindow=lastMonth&includeTrendForAdditionalCostTypes=true&includeTrendForSubResources=false`,
    method: 'GET',
    body: JSON.stringify(JSON.parse(fs.readFileSync(filePaths.trendForAdditionalCostTypesEmpty, 'utf8')),null,2)
},{
    url: `${serverUrl}/orchestrator/cost/breakdown/cluster?timeWindow=lastMonth&sortBy=potentialSavings&sortOrder=DESC&size=5&offset=0`,
    method: 'GET',
    body: JSON.stringify(JSON.parse(fs.readFileSync(filePaths.potentialSavingsAndSordOrderEmpty, 'utf8')),null,2)
},{
    url: `${serverUrl}/cost/breakdown/cluster?timeWindow=lastMonth&sortBy=totalCost&sortOrder=DESC&size=8&offset=0`,
    method: 'GET',
    body: JSON.stringify(JSON.parse(fs.readFileSync(filePaths.totalCostAndSortOrderEmpty, 'utf8')),null,2)
}]

export function dataSetForOverviewPageTestcase() {
    return[{
        fetchDataFromFilterValue:'This Month',
        
    }]
}