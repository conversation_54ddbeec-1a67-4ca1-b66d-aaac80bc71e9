export function dataSetForCustomPlugin(pluginName: string, pluginId: string) {
    let result = [{
        taskStage: 'pre',
        outputVariableNames: ['deep'],
        outputVariableTypes: ['String'],
        keyValuePairForPassFailure: 'deep:reen',
        inputVariables: [{ varName: 'deep', value: 'reen', varType: 'String', valueConfiguration: { choices: ['reen'], dontAllowCustomInput: true, askValueAtRuntime: true } }, { varName: 'beep', value: '', varType: 'String' }],
        saveCustomPlugin: { imageUrl: 'https://companieslogo.com/img/orig/DELHIVERY.NS-24f77207.png?t=1720244491', pluginName: pluginName, pluginVersion: '0.1.0', pulginId: pluginId, tags: ['Code Review'], mandatVariableConfig: [{ varName: 'deep', isMandat: true }, { varName: 'beep', isMandat: true }], replaceTaskWithPlugin: true },
        script: ['echo $deep', 'echo $beep'],
    }, {
        taskStage: 'post',
        saveCustomPlugin: { newVersionOfExistingPlugin: { existingPluginName: pluginName, newPluginVersion: '0.2.0' }, replaceTaskWithPlugin: false }

    }]
    return result;
}