export let changeCiDataObject = [{
    changeWorkflowConfig: {
        visibleAndNotVisibleModals: [{ workflowType: 'build-from', isHidden: true }],
        workflowNumber: 1,
        workflowCreationConfig: {
            workflowType: 'linked-build',
            config: {
                linkedPipelineName: 'linked-new',
                sourcePipelineName: 'ci',
                appName: '',
                checkValidation: false
            }
        },
        textVerificationDetails: {
            workflowNumber: 1,
            nodeNumber: 2,
            textToVerify: ['Linked']
        },
        triggerRelatedData: {
            workflowNumber: 0,
            triggerCi: true,
            cdNodeNumberToVerifyStatus: 0,
            nodeNumberToclickOnDetails: 0
        }
    }
},
{
    changeWorkflowConfig: {
        visibleAndNotVisibleModals: [{ workflowType: 'linked-build', isHidden: true }],
        workflowNumber: 1,
        workflowCreationConfig: {
            workflowType: 'build-from',
            config: {
                sourceType: 'Branch Fixed', branchName: ['main'],
            }
        },
        textVerificationDetails: {
            workflowNumber: 1,
            nodeNumber: 2,
            textToVerify: ['ci']
        },
        triggerRelatedData: {
            workflowNumber: 1,
            triggerCi: true,
            cdNodeNumberToVerifyStatus: 0,
            nodeNumberToclickOnDetails: 1
        }
    }
},
{
    changeWorkflowConfig: {
        visibleAndNotVisibleModals: [{ workflowType: 'build-from', isHidden: true }],
        workflowNumber: 1,
        workflowCreationConfig: {
            workflowType: 'deploy-image',
            config: undefined
        },
        textVerificationDetails: {
            workflowNumber: 1,
            nodeNumber: 1,
            textToVerify: ['Webhook']
        },
        triggerRelatedData: {
            workflowNumber: 0,
            triggerCi: true,
            cdNodeNumberToVerifyStatus: 0
        }
    }
},
{
    changeWorkflowConfig: {
        visibleAndNotVisibleModals: [{ workflowType: 'deploy-image', isHidden: true }],
        workflowNumber: 1,
        workflowCreationConfig: {
            workflowType: 'job-ci-pipeline-button',
            config: {
                pipelineName: 'ci-jobs',
                branchName: 'main',
                script: ['echo "hello"']
            }
        },
        textVerificationDetails: {
            workflowNumber: 1,
            nodeNumber: 2,
            textToVerify: ['Job']
        },
        triggerRelatedData: {
            workflowNumber: 0,
            triggerCi: false
        }
    }
}
]