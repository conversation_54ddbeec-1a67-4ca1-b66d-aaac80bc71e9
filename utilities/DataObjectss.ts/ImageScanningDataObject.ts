import { BaseTest } from "../BaseTest";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");

export function dataObjectForImageScanningTestCase() {
    return {
        prePostTaskConfiguration: [ObjectForPrePostTaskConfiguration(0, 'pre', 'execute', credentials.EnvNameForCD[0]), ObjectForPrePostTaskConfiguration(0, 'post', 'execute', credentials.EnvNameForCD[0]), ObjectForPrePostTaskConfiguration(0, 'post', 'Code Scan', 'ci'), ObjectForPrePostTaskConfiguration(3, 'pre', 'IMAGE SCAN', 'ci'), ObjectForPrePostTaskConfiguration(3, 'pre', 'Code Scan', 'ci')],
        cicdStatusVerification: [ObjectForVerifyCiCdStatus(0, 0, 'Succeeded', 12 * 1000 * 60), ObjectForVerifyCiCdStatus(0, 1, 'Succeeded', 6 * 1000 * 60), ObjectForVerifyCiCdStatus(0, 2, 'Succeeded', 8 * 1000 * 60), ObjectForVerifyCiCdStatus(0, 3, 'Succeeded', 6 * 1000 * 60), ObjectForVerifyCiCdStatus(1, 0, 'Progressing', 7 * 1000 * 60), ObjectForVerifyCiCdStatus(2, 0, 'Succeeded', 2 * 1000 * 60), ObjectForVerifyCiCdStatus(3, 0, 'Succeeded', 5 * 1000 * 60), ObjectForVerifyCiCdStatus(3, 1, 'Succeeded', 6 * 1000 * 60)],
        imageScanningVerificationOnCd: [ObjectForImageScanningVerificationOnCd(0, 0), ObjectForImageScanningVerificationOnCd(1, 0), ObjectForImageScanningVerificationOnCd(2, 0), ObjectForImageScanningVerificationOnCd(0, 1), ObjectForImageScanningVerificationOnCd(0, 2)]
    }
}
function ObjectForPrePostTaskConfiguration(wfNumber: number, stage: string, pluginName: string, nodeName: string) {
    return {
        workflowNumber: wfNumber,
        stage: stage,
        pluginName: pluginName,
        nodeName: nodeName
    }
}
function ObjectForVerifyCiCdStatus(workflowNumber: number, nodeNumber: number, status: string, timeout: number) {
    return {
        workflowNumber: workflowNumber,
        nodeNumber: nodeNumber,
        status: status,
        timeout: timeout
    }

}
function ObjectForImageScanningVerificationOnCd(nodeNumber: number, workflowNumber: number) {
    return {
        cdNodeNumber: nodeNumber,
        workflowNumber: workflowNumber
    }
}