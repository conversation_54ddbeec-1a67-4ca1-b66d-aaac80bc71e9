import { AllTypes } from "../Types";
export let datasetForBuildInfra: AllTypes.BuildInfra.dataSetForBuildInfra[] = [{
    editProfiles: [{
        profileName: 'global',
        runnerConfiguration: {
            buildConfiguration: {
                cpu: {
                    request: {
                        value: '130',
                        units: 'm'
                    },
                    limit: {
                        value: '300',
                        units: 'm'
                    },
                    isInherited: false
                }

            },
            cmcsConfiguration: [{
                name: 'cm-test',
                type: 'Kubernetes ConfigMap',
                cmOrSecret: 'ConfigMaps',
                subType: 'Environment Variable',
                configuration: {
                    key: 'key1',
                    value: 'value1'
                },
                isInherited: false
            }]

        },
        k8sDriverConfiguration: [{
            turnOn: true,
            targetPlatform: 'linux/amd64',
            configuration: {
                cpu: {
                    request: {
                        value: '122',
                        units: 'm'
                    },
                    limit: {
                        value: '400',
                        units: 'm'
                    },
                    isInherited: false
                },
                tolerance: {
                    key: 'test',
                    value: 'best',
                    isInherited: false
                }
            }
        }]
    }, {
        profileName: 'automation-test',
        runnerConfiguration: {
            buildConfiguration: {
                cpu: {
                    isInherited: true
                },
                memory: {
                    isInherited: false,
                    request: {
                        value: '125',
                        units: "Mi"
                    },
                    limit: {
                        value: '300',
                        units: 'Mi'
                    }
                }
            },
            cmcsConfiguration: [{
                name: 'secret-test',
                type: 'Kubernetes Secret',
                cmOrSecret: 'Secret',
                subType: 'Environment Variable',
                configuration: {
                    key: 'key2',
                    value: 'value2'
                },
                isInherited: false
            }]
        },
        k8sDriverConfiguration: [{
            turnOn: true,
            targetPlatform: 'linux/amd64',
            configuration: {
                cpu: {
                    isInherited: true
                },
                tolerance: {
                    isInherited: true
                },
                memory: {
                    isInherited: false,
                    request: {
                        value: '110',
                        units: 'Mi'
                    },
                    limit: {
                        value: '350',
                        units: 'Mi'
                    }
                }



            }
        }, {
            turnOn: true,
            targetPlatform: 'linux/arm64',
            configuration: {
                tolerance: {
                    key: 'tool',
                    value: 'pool',
                    isInherited: false
                }
            }
        }]

    }


    ],
    validationCheck: [{
        filterType: 'linux/amd64',
        config: {
            cpu: {
                value: '122 m — 400 m',
                overOrInh: 'Inheriting global'
            },
            memory: {
                value: '110 Mi — 350 Mi',
                overOrInh: 'Overridden'
            }
        }
    }],
    valuesToCheckInLogs: ['value1', 'value2'],
    valuesToCheckInManifest: {
        runnerManifest: ['130m', '300M'],
        "linux/arm64": ['tool'],
        "linux/amd64": ['400m', 'test', '110M'],
        "linux/arm/v7": ['130m']
    },
    platformDuringBuild: ['linux/arm64', 'linux/amd64', 'linux/arm/v7'],
    isCiFailed: false
},
{
    editProfiles: [{
        profileName: 'automation-test',
        runnerConfiguration: {
            cmcsConfiguration: [{
                name: 'cm-test',
                type: 'Kubernetes ConfigMap',
                cmOrSecret: 'ConfigMaps',
                subType: 'Environment Variable',
                isInherited: false,
                configuration: {
                    key: 'key1',
                    value: 'hello'
                }
            }]
        },
        k8sDriverConfiguration: [{
            turnOn: false
        }, {
            turnOn: true
        }]
    }],
    valuesToCheckInLogs: ['hello'],
    platformDuringBuild: ['linux/amd64', 'linux/arm64'],
    validationCheck: [{
        filterType: 'Runner configuration',
        config: {
            cm: {
                value: '01',
                overOrInh: 'Overridden'
            }
        }
    }],
    valuesToCheckInManifest: {
        runnerManifest: ['130m', '300Mi'],
        "linux/arm64": ['300m'],
        "linux/amd64": ['400m', 'test'],
    },
    isCiFailed: false

}, {
    editProfiles: [{
        profileName: 'automation-test',
        k8sDriverConfiguration: [{
            turnOn: false
        }]
    }],
    valuesToCheckInLogs: ['docker run --privileged --rm quay.io/devtron/binfmt:stable --install all'],
    platformDuringBuild: ['linux/amd64', 'linux/arm64'],
    validationCheck: [{
        filterType: 'Runner configuration',
        config: {
            cm: {
                value: '01',
                overOrInh: 'Overridden'
            }
        }
    }],
    valuesToCheckInManifest: {
        runnerManifest: ['130m', '300Mi']
    },
    isCiFailed: false
}, {
    editProfiles: [{
        profileName: 'automation-test',
        runnerConfiguration: {
            buildConfiguration: {
                timeout: {
                    value: '90',
                    units: 'Seconds',
                    isInherited: false
                }
            }
        }
    }],
    valuesToCheckInLogs: ['checkout commit'],
    platformDuringBuild: ['linux/amd64', 'linux/arm64'],
    validationCheck: [{
        filterType: 'Runner configuration',
        config: {
            cm: {
                value: '01',
                overOrInh: 'Overridden'
            }
        }
    }],
    valuesToCheckInManifest: {
        runnerManifest: ['300m', '300Mi']
    },
    isCiFailed: true
}]

export async function dataObjectForApplyProfileFilterAndVerifyConfig(AllObjects: AllTypes.fixtures.allPageObjects): Promise<{
    profileName: string,
    filterValue: AllTypes.BuildInfra.RunnerOrTargetPlatformFilterType,
    value: AllTypes.BuildInfra.configurationAppliedOnAProfile | string
}[]> {
    let profileNames = ['global', 'automation-test'];
    let globalProfileValue: AllTypes.BuildInfra.configurationAppliedOnAProfile | string = await AllObjects.buildInfraPage.fetchConfigurationOfAProfileAndVerify(profileNames[0], 'Runner configuration');
    let ourValue = globalProfileValue as AllTypes.BuildInfra.configurationAppliedOnAProfile;
    let value: AllTypes.BuildInfra.configurationAppliedOnAProfile | string;
    let profileName: string;
    let filterValue: AllTypes.BuildInfra.RunnerOrTargetPlatformFilterType

    let resultantArray: {
        profileName: string,
        filterValue: AllTypes.BuildInfra.RunnerOrTargetPlatformFilterType,
        value: AllTypes.BuildInfra.configurationAppliedOnAProfile | string
    }[] = [];

    for (let i = 0; i <= 2; i++) {
        if (i == 0) {
            value = { cpu: { value: ourValue.cpu?.value!, overOrInh: 'Inheriting global' }, cm: { value: ourValue.cm?.value!, overOrInh: 'Inheriting global' } };
            profileName = profileNames[1];
            filterValue = 'Runner configuration';
        }
        if (i == 1) {
            value = 'No configurations set for \'linux/arm64\' in \'global\' profile.';
            profileName = profileNames[0];
            filterValue = 'linux/arm64';
        }
        if (i == 2) {
            value = { cpu: { value: ourValue.cpu?.value!, overOrInh: 'Overridden' }, cm: { value: ourValue.cm?.value!, overOrInh: 'Inheriting runner' } }
            profileName = profileNames[1];
            filterValue = 'linux/arm64';
        }
        resultantArray.push({ profileName: profileName!, filterValue: filterValue!, value: value! });
    }
    return resultantArray;

}

export function dataObjectForOssBuildInfraProfileConfig(): AllTypes.BuildInfra.editOrCreateProfile {
    return {
        profileName: 'global', runnerConfiguration: {
            buildConfiguration: {
                cpu: {
                    request: {
                        value: '130',
                        units: 'm'
                    },
                    limit: {
                        value: '300',
                        units: 'm'
                    },
                    isInherited: false
                },
                memory: {
                    request: {
                        value: '0.2',
                        units: 'G'
                    },
                    limit: {
                        value: '0.5',
                        units: 'G'
                    },
                    isInherited: false
                }

            }

        }
    }


}
