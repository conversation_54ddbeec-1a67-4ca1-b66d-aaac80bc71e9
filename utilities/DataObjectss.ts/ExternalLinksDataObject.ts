import { link } from "fs";
import { BaseTest } from "../BaseTest";

export function externalLinkDataObject(linkNames: string, appNames: string[]) {
    let resultantArray: { linkName: string, description?: string, applicationSpecific: boolean, clusterOrAppValue: string[], urlTemplate: string, openInNewTab: boolean, appAdminsCanEdit?: boolean }[] = [];
    // 
    for (let i = 0; i < 3; i++) {
        if (process.env.clusterType?.includes('ea')! && i == 0) {
            continue;
        }
        let isApplicationOnly = i == 2 ? false : true;
        let values = i == 2 ? ['default_cluster', 'virtual-cluster-automation'] : process.env.clusterType?.includes('ea')! ? [appNames[1]] : [appNames[0]];
        let editByAdmin = i == 0 ? true : false
        let result = {
            linkName: linkNames,
            applicationSpecific: isApplicationOnly,
            clusterOrAppValue: values,
            urlTemplate: 'https://deep/{appName}/{envId}',
            openInNewTab: true,
            appAdminsCanEdit: editByAdmin
        }
        resultantArray.push(result);
    }
    return resultantArray;

}

export function dataObjectArrayForExternalLinks(devtronAppUrl: string, helmAppUrl: string, devtronName: string, helmAppName: string) {
    if (process.env.clusterType?.includes('ea')!) {
        return {

        }
    } else {
        return {
            createExternalLinksConfigArray: [objectCreationForConfiguringExternalLinksOnGlobalConfigPage(true, [devtronName])]
        }
    }
}

function objectCreationForConfiguringExternalLinksOnGlobalConfigPage(applicationSpecific: boolean, clusterOrAppValue: string[], urlTemplate: string = 'https://deep/{appName}/{envId}', openInNewTab: boolean = true, appAdminsCanEdit: boolean = false, linkName: string = BaseTest.generateRandomStringWithCharsOnly(5)) {

}