import { DevtronDeploymentChart } from "../../enums/ApplicationManagement/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum";
import { BaseTest } from "../BaseTest";

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export function DataObjectForHibernationPatch(appUrl: string[]) {

    return [objectCreationForHibernationPatch(appUrl[1], false, false, credentials.customChartName),
    objectCreationForHibernationPatch(appUrl[1], false, true, credentials.customChartName),
    objectCreationForHibernationPatch(appUrl[0], false, true, DevtronDeploymentChart.Deployment),
    objectCreationForHibernationPatch(appUrl[0], true, true, DevtronDeploymentChart.Deployment, 'ClusterIP'),
    ]
}

function objectCreationForHibernationPatch(appUrl: string, resetToDefault: boolean, isHibernationSuccessfull: boolean = false, chartName: string, service: string = "NodePort") {
    let yamlConfiguration = isHibernationSuccessfull ? `service:
  type: NodePort
replicaCount: 0`: undefined;
    return {
        appUrl: appUrl,
        chartName: chartName,
        serviceTypeToVerify: service,
        isHibernationSuccessfull: isHibernationSuccessfull,
        resetToDefault: resetToDefault,
        yamlConfigurationToEnter: yamlConfiguration
    }
}