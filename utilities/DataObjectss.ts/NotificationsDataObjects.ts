import { AllTypes } from "../Types";
type Step<T extends any[] = any[]> = {
    functionToCall: (...args: T) => Promise<void> | void;
    args: T;
};

type NotificationTestStep = {
    nodeNumber: number;
    workflowNumber: number;
    statusToCheck: string;
    subjectOfMailToSearch: string;
    htmlBodyContentToVerifyInMail: string;
    mailCount: number;
    triggerAndConfigurationFuncntionsToCall: Step[];
};

export function dataSetForSesNotificationtest(devtronAppName: string, devtronAppId: number, AllObjects: AllTypes.fixtures.allPageObjects): NotificationTestStep[] {
    return [{
        nodeNumber: 0,
        workflowNumber: 0,
        statusToCheck: 'Running',
        subjectOfMailToSearch: `Build pipeline triggered  Application: ${devtronAppName}`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/ci-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: AllObjects.workflowPage.triggerCiModule.bind(AllObjects.workflowPage),
            args: []
        }]
    }, {
        nodeNumber: 0,
        workflowNumber: 0,
        statusToCheck: 'Succeeded',
        subjectOfMailToSearch: `Build pipeline Succeeded  Application: ${devtronAppName}`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/ci-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: async (..._args: any[]) => { },
            args: []
        }]
    }, {
        nodeNumber: 1,
        workflowNumber: 0,
        statusToCheck: 'Progressing',
        subjectOfMailToSearch: `Deployment pipeline triggered  Application: ${devtronAppName}  Environment:  automation`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: async (..._args: any[]) => { },
            args: []
        }]
    },
    {
        nodeNumber: 1,
        workflowNumber: 0,
        statusToCheck: 'Succeeded',
        subjectOfMailToSearch: `CD success for app: ${devtronAppName} on environment: automation`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: async (..._args: any[]) => { },
            args: []
        }]
    },
    {
        nodeNumber: 0,
        workflowNumber: 1,
        statusToCheck: 'Progressing',
        subjectOfMailToSearch: `Deployment pipeline triggered  Application: ${devtronAppName}  Environment:  devtron-demo`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: async (..._args: any[]) => { },
            args: []
        }]
    }, {
        nodeNumber: 2,
        workflowNumber: 0,
        statusToCheck: 'Succeeded',
        subjectOfMailToSearch: `CD success for app: ${devtronAppName} on environment: automation`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 2,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: AllObjects.appConfigurationPage.clickOnSpecificCiCdNode.bind(AllObjects.appConfigurationPage),
            args: [0, 'automation']
        }, {
            functionToCall: AllObjects.prePostCiCd.addPrePostTask.bind(AllObjects.prePostCiCd),
            args: ['post', 'execute']
        }, {
            functionToCall: AllObjects.jobsPage.executeCustomScript.bind(AllObjects.jobsPage),
            args: ['post', [''], true]
        }, {
            functionToCall: AllObjects.workflowPage.verifyImageAndTriggerDeployment.bind(AllObjects.workflowPage),
            args: [1]
        }]
    }, {
        nodeNumber: 2,
        workflowNumber: 0,
        statusToCheck: 'Failed',
        subjectOfMailToSearch: `Deployment pipeline failed Application: ${devtronAppName}  Environment: automation`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: AllObjects.appConfigurationPage.clickOnSpecificCiCdNode.bind(AllObjects.appConfigurationPage),
            args: [0, 'automation']
        }, {
            functionToCall: AllObjects.prePostCiCd.deleteTask.bind(AllObjects.prePostCiCd),
            args: ['post']
        }, {
            functionToCall: AllObjects.prePostCiCd.addPrePostTask.bind(AllObjects.prePostCiCd),
            args: ['post', 'execute']
        }, {
            functionToCall: AllObjects.jobsPage.executeCustomScript.bind(AllObjects.jobsPage),
            args: ['post', ['exit 1'], true]
        }, {
            functionToCall: AllObjects.workflowPage.verifyImageAndTriggerDeployment.bind(AllObjects.workflowPage),
            args: [1]
        }
        ]
    }

    ]
}
export function dataSetForSMTPNotificationtest(devtronAppName: string, devtronAppId: number, AllObjects: AllTypes.fixtures.allPageObjects, cdName: string): NotificationTestStep[] {
    return [{
        nodeNumber: 0,
        workflowNumber: 0,
        statusToCheck: 'Succeeded',
        subjectOfMailToSearch: `Deployment pipeline triggered  Application: ${devtronAppName}  Environment:  ${cdName}`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 3,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: AllObjects.appConfigurationPage.clickOnSpecificCiCdNode.bind(AllObjects.appConfigurationPage),
            args: [0, cdName]
        }, {
            functionToCall: AllObjects.prePostCiCd.addPrePostTask.bind(AllObjects.prePostCiCd),
            args: ['pre', 'execute']
        }, {
            functionToCall: AllObjects.jobsPage.executeCustomScript.bind(AllObjects.jobsPage),
            args: ['pre', [''], true]
        },
        {
            functionToCall: AllObjects.workflowPage.verifyImageAndTriggerDeployment.bind(AllObjects.workflowPage),
            args: [0]
        }
        ]
    }, {
        nodeNumber: 1,
        workflowNumber: 0,
        statusToCheck: 'Succeeded',
        subjectOfMailToSearch: `CD success for app: ${devtronAppName} on environment: ${cdName}`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: async (..._args: any[]) => { },
            args: []
        }]
    }, {
        nodeNumber: 0,
        workflowNumber: 0,
        statusToCheck: 'Failed',
        subjectOfMailToSearch: `Deployment pipeline failed Application: ${devtronAppName}  Environment: ${cdName}`,
        htmlBodyContentToVerifyInMail: `${process.env.BASE_SERVER_URL!}/app/${devtronAppId}/cd-details`,
        mailCount: 1,
        triggerAndConfigurationFuncntionsToCall: [{
            functionToCall: AllObjects.appConfigurationPage.clickOnSpecificCiCdNode.bind(AllObjects.appConfigurationPage),
            args: [0, cdName]
        }, {
            functionToCall: AllObjects.prePostCiCd.addPrePostTask.bind(AllObjects.prePostCiCd),
            args: ['pre', 'execute']
        }, {
            functionToCall: AllObjects.jobsPage.executeCustomScript.bind(AllObjects.jobsPage),
            args: ['test', ['exit 1 '], true]
        }, {
            functionToCall: AllObjects.workflowPage.verifyImageAndTriggerDeployment.bind(AllObjects.workflowPage),
            args: [0]
        }
        ]
    }]
}