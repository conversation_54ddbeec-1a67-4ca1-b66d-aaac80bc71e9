import { BaseTest } from "../BaseTest";
let credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export let mine = {
    envNameForWorkflowCreation: process.env.clusterType == "enterprise" ? 'env9' : 'devtron-demo',
    addCdModuleConfig: process.env.clusterType == "enterprise" ? { envNameForCd: credentials.VirtualEnvironment, clusterName: credentials.VirtualClusterName, virtualEnvConfig: { pushOrNot: 'Push to registry', regName: 'bains', repoName: `deep10/${BaseTest.generateRandomStringWithCharsOnly(4)}` } } : { envNameForCd: 'env1', clusterName: 'default_cluster', helmOrGitops: 'helm', autoOrManual: 'Auto' },
    statusToVerify: [{
        cdNodeNumber: 0,
        statusToCheck: 'Not Triggered',
    }, {
        cdNodeNumber: 0,
        statusToCheck: "Succeeded"
    }, {
        cdNodeNumber: 1,
        statusToCheck: process.env.clusterType == "enterprise" ? "Succeeded" : "Progressing",
    },
    {
        cdNodeNumber: 2,
        statusToCheck: "Succeeded"
    }, {
        cdNodeNumber: 3,
        statusToCheck: "Progressing"
    }, {
        cdNodeNumber: 2,
        statusToCheck: "Succeeded"
    },
    {
        cdNodeNumber: 3,
        statusToCheck: "Not Triggered",
    }

    ]


}
