import cluster from "cluster";
import { Constants } from "../Constants";
import { BaseTest } from "../BaseTest";
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export function preRequistiteDataObject() {
    let requiredUserName: string;
    let requiredGitopsProvider: string;
    let requiredPat: string;
    let requiredGroupId: string;
    let providerUrl: string;
    let orgIdKeyName: string;
    if (process.env.isStaging == "false") {
        requiredUserName = process.env.GITLAB_USERNAME!;
        requiredGitopsProvider = 'GITLAB';
        requiredPat = process.env.GITLAB_PAT!;
        requiredGroupId = process.env.GITLAB_GROUPID!;
        providerUrl = 'https://gitlab.com/';
        orgIdKeyName = 'gitLabGroupId';
    }
    else {
        requiredUserName = process.env.GITHUB_USERNAME!;
        requiredGitopsProvider = 'GITHUB';
        requiredPat = process.env.GITHUB_PAT!;
        requiredGroupId = Constants.GITHUB_ORGANISATION_NAME;
        providerUrl = 'https://github.com/';
        orgIdKeyName = 'gitHubOrgId';
    }
    return {
        cmcsData: [{
            resourceName: 'secret-ext-env',
            resourceType: 'Secret',
            keyName: 'username',
            valueName: 'username'
        }, {
            resourceName: 'secret-ext-datavol',
            resourceType: 'Secret',
            keyName: 'username',
            valueName: 'username'
        }, {
            resourceName: 'configmap-ext-datavol',
            resourceType: 'ConfigMap',
            keyName: 'key2',
            valueName: 'value2'
        }, {
            resourceName: 'configmap-ext-env',
            resourceType: 'ConfigMap',
            keyName: 'key1',
            valueName: 'value1'
        }],
        containerRegistryData: [{
            registrytype: 'other',
            registryName: 'devtron-test',
            registryUsername: process.env.QUAY_REGISTRY_USERNAME!,
            registryPassword: process.env.QUAY_REGISTRY_TOKEN!,
            registryUrl: process.env.QUAY_REGISTRY_URL!
        },
        {
            registrytype: 'docker-hub',
            registryName: 'bains',
            registryUsername: process.env.DOCKER_REGISTRY_USERNAME!,
            registryPassword: process.env.DOCKER_REGISTRY_TOKEN!,
            registryUrl: process.env.DOCKER_REGISTRY_URL!
        }],
        requiredUserName: requiredUserName,
        requiredGitopsProvider: requiredGitopsProvider,
        requiredPat: requiredPat,
        requiredGroupId: requiredGroupId,
        providerUrl: providerUrl,
        orgIdKeyName: orgIdKeyName,
        envCreationData: [{
            envName: 'env1',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env2',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env3',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env4',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env5',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env6',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env7',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env8',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env9',
            clusterName: credentials.VirtualClusterName,
        },
        {
            envName: 'env10',
            clusterName: 'default_cluster',
        }
            ,
        {
            envName: 'env11',
            clusterName: 'default_cluster',
        },
        {
            envName: 'env12',
            clusterName: 'default_cluster',
        },
        {
            envName: 'automation',
            clusterName: 'default_cluster',
        },
        {
            envName: credentials.VirtualEnvironment,
            clusterName: credentials.VirtualClusterName,
        }
        ],
        apiTokenData: [{
            tokenName: 'playwright-super-admin',
            superAdmin: true
        }, {
            tokenName: 'playwright-non-super',
            superAdmin: false
        }]
    }
}