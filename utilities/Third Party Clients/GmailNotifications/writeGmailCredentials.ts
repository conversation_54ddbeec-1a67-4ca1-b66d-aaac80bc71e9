import fs from 'fs';
import axios from 'axios';
import dotenv from 'dotenv';

/**
 * This function writes the Gmail credentials to a file.
 */
export function writeGmailCredentialsToFile() {
    const credentials = {
        installed: {
            client_id: process.env.GMAIL_CLIENT_ID,
            project_id: 'balmy-moonlight-461313-a7',
            auth_uri: 'https://accounts.google.com/o/oauth2/auth',
            token_uri: 'https://oauth2.googleapis.com/token',
            auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
            client_secret: process.env.GMAIL_CLIENT_SECRET,
            redirect_uris: ['http://localhost']
        }
    };

    const outputPath = 'utilities/GmailNotifications/credentials.json';
    try {
        fs.writeFileSync(outputPath, JSON.stringify(credentials, null, 2));
        console.log(`Gmail credentials written to ${outputPath}`);
    } catch (error) {
        console.log(`Error writing gmail credentials to file: ${error}`);
    }
}

/**
 * This function refreshes the Gmail access token using the refresh token.
 */
export async function refreshGmailToken() {
    try {
        const response = await axios.post(
            'https://oauth2.googleapis.com/token',
            new URLSearchParams({
                client_id: process.env.GMAIL_CLIENT_ID!,
                client_secret: process.env.GMAIL_CLIENT_SECRET!,
                refresh_token: process.env.GMAIL_REFRESH_TOKEN!,
                grant_type: 'refresh_token',
            }),
            { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
        );
        return response.data; // { access_token, expires_in, scope, token_type }
    } catch (error: any) {
        const errorMessage = error.response?.data
            ? JSON.stringify(error.response.data, null, 2)
            : error.message;
        console.error(`Error refreshing Gmail token:\n${errorMessage}`);

    }
}

/**
 * This function writes the Gmail token to a file.
 * @param tokenData - The new token data to write.
 */
export function writeGmailTokenToFile(tokenData: any) {
    const token = {
        access_token: tokenData.access_token,
        refresh_token: process.env.GMAIL_REFRESH_TOKEN,
        scope: tokenData.scope,
        token_type: tokenData.token_type,
        expiry_date: Date.now() + tokenData.expires_in * 1000,
    };
    const outputPath = 'utilities/GmailNotifications/token.json';
    try {
        fs.writeFileSync(outputPath, JSON.stringify(token, null, 2));
        console.log(`Gmail token written to ${outputPath}`);
    } catch (error) {
        console.log(`Error writing gmail token to file: ${error}`);
    }
}
/**
 * Main function to refresh token & write required files.
 * Call this from Playwright globalSetup.
 */
export async function setupGmailTesterFiles() {
    console.log(" Refreshing Gmail token and writing files...");
    // Refresh token and write token.json
    const newToken = await refreshGmailToken();
    console.log('new token object details we are getting is' + JSON.stringify(newToken));
    writeGmailTokenToFile(newToken);
    // Write credentials.json (usually doesn't change, but keeps things consistent)
    writeGmailCredentialsToFile();
    console.log("Gmail tester setup completed successfully.");
}
