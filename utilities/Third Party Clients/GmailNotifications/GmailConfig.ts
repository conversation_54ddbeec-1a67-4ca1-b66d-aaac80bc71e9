import gmail, { get_messages } from "gmail-tester";
import { <PERSON>rows<PERSON>, expect } from "playwright/test";
import * as cheerio from 'cheerio';
const GmailConfig = {
    credentialsPath: 'utilities/GmailNotifications/credentials.json',
    tokenPath: 'utilities/GmailNotifications/token.json',
    options: {
        subject: "test email",
        from: "<EMAIL>",
        to: "<EMAIL>",
        wait_time_sec: 10,
        max_wait_time_sec: 60,
        include_body: true,
    }
};
export default GmailConfig;


export async function waitForEmailToGetRecieved(subjectOfMailToSearch: string) {
    try {
        await gmail.check_inbox(GmailConfig.credentialsPath, GmailConfig.tokenPath, { subject: subjectOfMailToSearch, max_wait_time_sec: 2 * 60 });
    }
    catch (error) {
        console.log(`error in waiting for email ${subjectOfMailToSearch} to get recieved` + error);
        throw error;
    }
}
export async function verifyTheCountOfEmails(subjectOfMailToSearch: string, countOfEmails: number) {
    try {
        await expect(async () => {
            await waitForEmailToGetRecieved(subjectOfMailToSearch);
            let mail = await gmail.get_messages(GmailConfig.credentialsPath, GmailConfig.tokenPath, { subject: subjectOfMailToSearch, include_body: true });
            expect(mail.length).toBe(countOfEmails);
        }).toPass({ timeout: 3 * 60 * 1000 });
    }
    catch (error) {
        console.log(`error in verifying the count of emails with subject ${subjectOfMailToSearch}` + error);
        throw error;
    }
}
export async function verifyTheContentOfEmail(subjectOfMailToSearch: string, contentToSearch: string, mailNumber: number = 0) {
    try {

        await waitForEmailToGetRecieved(subjectOfMailToSearch);
        let mail = await gmail.get_messages(GmailConfig.credentialsPath, GmailConfig.tokenPath, { subject: subjectOfMailToSearch, include_body: true });
        expect(mail[mailNumber].body?.html).toContain(contentToSearch);
        return mail[mailNumber].body?.html;
    }
    catch (error) {
        console.log('error in verifying the content of email with subject ' + subjectOfMailToSearch + error);
        throw error;
    }
}

export async function verifyContentAndCountOfEmail(subjectOfMailToSearch: string, contentToSearch: string, countOfEmails: number, mailNumber: number = 0) {
    try {
        let mail = await gmail.get_messages(GmailConfig.credentialsPath, GmailConfig.tokenPath, { subject: subjectOfMailToSearch, include_body: true, });
        expect(mail.length).toBe(countOfEmails);
        expect(mail[mailNumber].body?.html).toContain(contentToSearch);
    }
    catch (error) {
        console.log('error in verifying the content and count of email' + error);
        throw error;
    }
}

async function fetchExactUrlOfAButtonInGmailBody(rawHtml: string, textOfButtonToSearch: string = 'approve') {
    const parsedHtml = cheerio.load(rawHtml);
    const buttonUrl = parsedHtml('a').filter((_, ele) => parsedHtml(ele).text().trim().toLowerCase() === 'approve').attr('href');
    return buttonUrl!;
}
export async function approveTheRequestInGmailAndVerifyMessage(subjectOfMailToFilter: string, browser: Browser, textToVerify, emailNumberToVerifyInInbox: number) {
    await verifyTheCountOfEmails(subjectOfMailToFilter, emailNumberToVerifyInInbox);
    let mailBody = await verifyTheContentOfEmail(subjectOfMailToFilter, 'approve', 0);
    let buttonUrl = await fetchExactUrlOfAButtonInGmailBody(mailBody!);
    let page = await browser.newPage();
    await page.goto(buttonUrl);
    await expect(page.locator(`//*[text()="${textToVerify}"]`)).toBeVisible({ timeout: 12000 });
    await page.close();
}

