/**
 * A module to run some code in worker once, usually in beforeAll / afterAll hooks.
 * Implementation: 
 * 1. hook into Playwright worker creation to intercept messages from worker
 * 2. on every invocation of isFirstRun() workers sends message to host with stack trace
 * 3. host checks if it's the first run of the particular line in the code and sends back the result
 */
import path from 'node:path';
import fs from 'node:fs';

const invocations = new Set<string>();
const callbacks = new Map<number, (isFirstRun: boolean) => void>();

let lastMessageId = 0;

if (process.env.TEST_WORKER_INDEX) {
    registerWorker();
} else {
    registerHost();
}
type CheckFirstRunMessage = {
    method: '__check_first_run__',
    params: {
        id: number,
        stack: string,
        isFirstRun?: boolean,
    }
}

/**
 * Returns true on the first invocation of the particular line in the worker code.
 */
export async function isFirstRun() {
    // Get invocation place by stack trace
    const stack = new Error().stack || '';
    return new Promise<boolean>(resolve => {
        const id = ++lastMessageId;
        callbacks.set(id, resolve);
        process.send!(buildMessage(id, stack));
    });
}

function registerHost() {
    hookWorkerProcess((workerProcess) => {
        workerProcess.on('message', (message: unknown) => {
            if (isCheckFirstRunMessage(message)) {
                const { id, stack } = message.params;
                const isFirstRun = !invocations.has(stack);
                if (isFirstRun) invocations.add(stack);
                workerProcess.send!(buildMessage(id, stack, isFirstRun));
            }
        });
    });
}

function registerWorker() {
    process.on('message', (message: unknown) => {
        if (isCheckFirstRunMessage(message)) {
            const { id, isFirstRun } = message.params;
            callbacks.get(id)?.(Boolean(isFirstRun));
            callbacks.delete(id);
        }
    });
}

// till pw 1.37 node_modules/@playwright/test/lib/runner/workerHost.js
// since pw 1.38 node_modules/playwright/lib/runner/workerHost.js
function getWorkerHostPath() {
    let pwPath = require.resolve('@playwright/test');
    let workerHostPath = `${path.dirname(pwPath)}/lib/runner/workerHost.js`;
    if (fs.existsSync(workerHostPath)) return workerHostPath;
    pwPath = require.resolve('playwright');
    return `${path.dirname(pwPath)}/lib/runner/workerHost.js`;
}

function hookWorkerProcess(fn: (workerProcess: NodeJS.Process) => void) {
    const { WorkerHost } = require(getWorkerHostPath());
    const origStart = WorkerHost.prototype.start;
    WorkerHost.prototype.start = async function (...args: any[]) {
        const result = await origStart.call(this, ...args);
        fn(this.process);
        return result;
    };
}

function buildMessage(id: number, stack: string, isFirstRun?: boolean): CheckFirstRunMessage {
    return {
        method: '__check_first_run__',
        params: { id, stack, isFirstRun }
    };
}

function isCheckFirstRunMessage(message: unknown): message is CheckFirstRunMessage {
    return (message as CheckFirstRunMessage)?.method === '__check_first_run__';
}