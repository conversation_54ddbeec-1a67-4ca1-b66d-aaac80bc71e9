import { Octokit } from "octokit";
import { BaseTest } from "../BaseTest";
import { expect } from "@playwright/test";
import { Constants } from "../Constants";


//class to call github api endpoints

export class OctokitClient {

  readonly credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
  readonly octokit: Octokit;


  constructor(auth: any) {

    this.octokit = new Octokit({
      auth: auth
    });

  }



  async createWebhook(owner: string, repo: string, webhook: string, secret: string) {
    const response = await this.octokit.request('POST /repos/{owner}/{repo}/hooks', {
      owner: owner,
      repo: repo,
      name: 'web',
      active: true,
      events: [
        'create',
        'pull_request'
      ],
      config: {
        url: webhook,
        content_type: 'json',
        insecure_ssl: '0',
        secret: `${secret}`
      },
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })

    expect(response.status == 201).toBeTruthy();

    return response.data.id;

  }

  async deleteWebhook(owner: string, repo: string, id: number) {
    const response = await this.octokit.request('DELETE /repos/{owner}/{repo}/hooks/{hook_id}', {
      owner: owner,
      repo: repo,
      hook_id: id,
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })



    await expect(response.status == 204).toBeTruthy();
  }

  async createPullRequest(owner: string, repo: string, title?: string, body?: string, head?: string, base?: string) {
    title = title || 'Pull request for testing doner';
    body = body || "Please pull these awesome heller";
    head = head || "test-branch";
    base = base || "main";
    const response = await this.octokit.request('POST /repos/{owner}/{repo}/pulls', {
      owner: owner,
      repo: repo,
      title: title,
      body: body,
      head: head,
      base: `main`,
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })



    await expect(response.status == 201).toBeTruthy();

    await new Promise(f => setTimeout(f, 1000 * 5));

    return response.data.number
  }

  async closePullRequest(pull_number: number, owner: string, repo: string) {
    const response = await this.octokit.request('PATCH /repos/{owner}/{repo}/pulls/{pull_number}', {
      owner: owner,
      repo: repo,
      pull_number: pull_number,
      state: 'closed',
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })


    await expect(response.status == 200).toBeTruthy();
  }

  async createReleaseWithTag(owner: string, repo: string, tag: string) {
    const response = await this.octokit.request('POST /repos/{owner}/{repo}/releases', {
      owner: owner,
      repo: repo,
      tag_name: tag,
      target_commitish: 'main',
      name: 'automation-test buddy',
      body: 'used for automation testing buddy',
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })


    expect(response.status == 201).toBeTruthy();

    await new Promise(f => setTimeout(f, 1000 * 35));

    return response.data.id;

  }

  async deleteTag(owner: string, repo: string, tag: string) {
    const response = await this.octokit.request('DELETE /repos/{owner}/{repo}/git/refs/tags/{ref}', {
      owner: owner,
      repo: repo,
      ref: tag,
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })

    await expect(response.status == 204).toBeTruthy();
  }

  async deleteRelease(owner: string, repo: string, releaseID: number) {
    const response = await this.octokit.request('DELETE /repos/{owner}/{repo}/releases/{release_id}', {
      owner: owner,
      repo: repo,
      release_id: releaseID,
      headers: {
        'X-GitHub-Api-Version': Constants.X_GITHUB_API_VERSION
      }
    })

    await expect(response.status == 204).toBeTruthy();
  }

  /**
   * this method is used to ge the latest commit of a branch
   * @param owner 
   * @param repo 
   * @returns 
   */
  async getLatestCommit(owner: string, repo: string): Promise<string> {
    const { data: ref } = await this.octokit.rest.git.getRef({
      owner: owner,
      repo: repo,
      ref: 'heads/main'
    });
    return ref.object.sha;
  }

  /**
   * use this method to create a new commit , everything is handled inside this method 
   * @param owner 
   * @param repo 
   * @param message 
   * @param filePath 
   * @param content 
   * @param branch 
   * @returns 
   */
  async createCommit(owner: string, repo: string, message: string, filePath: string, content: string, branch: string = 'main') {
    // Get the latest commit SHA
    const latestCommitSha = await this.getLatestCommit(owner, repo);

    // Create a new tree
    const { data: tree } = await this.octokit.rest.git.createTree({
      owner,
      repo,
      base_tree: latestCommitSha,
      tree: [
        {
          path: filePath,
          mode: '100644',
          type: 'blob',
          content: content
        }
      ]
    });

    // Create a new commit
    const { data: newCommit } = await this.octokit.rest.git.createCommit({
      owner,
      repo,
      message,
      tree: tree.sha,
      parents: [latestCommitSha]
    });

    // Update the reference of the branch to point to the new commit
    await this.octokit.rest.git.updateRef({
      owner,
      repo,
      ref: `heads/${branch}`,
      sha: newCommit.sha
    });

    return newCommit.sha;
  }
}

