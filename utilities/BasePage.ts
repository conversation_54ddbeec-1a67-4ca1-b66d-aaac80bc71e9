import { Page, Locator, expect, Browser } from 'playwright/test';
import * as fs from 'fs';
import path from 'path';
import JSZip from 'jszip';
import { monthMappingForCalendar } from "../utilities/clipboardyYamls.ts/customObjectsOrYaml";

export class BasePage {
  page: Page;

  readonly dialogDeleteConfirmationButton: Locator;
  readonly codeMirrorEditorTextArea: Locator;
  readonly codeMirrorSingleLineClass: string
  readonly deleteModalInputField: Locator;
  readonly yearAndMonthTextOnCalendar: Locator;
  readonly calendarNavigateRightButton: Locator;
  readonly calendarNavigateLeftButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.dialogDeleteConfirmationButton = this.page.getByTestId('confirmation-modal-primary-button');
    this.codeMirrorEditorTextArea = this.page.locator('//*[@role="textbox"]');
    this.codeMirrorSingleLineClass = 'cm-line';
    this.deleteModalInputField = this.page.locator('//*[@placeholder="Type to confirm"]');
    this.yearAndMonthTextOnCalendar = this.page.locator('//*[@class="CalendarMonth_caption CalendarMonth_caption_1"]');
    this.calendarNavigateRightButton = this.page.locator("//*[contains(@class,'DayPickerNavigation_rightButton')]");
    this.calendarNavigateLeftButton = this.page.locator("//*[contains(@class,'DayPickerNavigation_leftButton')]");
  }

  private async handleError(message: string, error: any) {
    throw new Error(`${message}: ${error.message}`);
  }

  static async verifyDownloads(page: Page, downloadButton: Locator, expectedValue: string, isSuccessfull: boolean) {
    if (isSuccessfull) {
      const [download] = await Promise.all([
        page.waitForEvent('download'),
        downloadButton.click(),
      ]);

      const fileName = download.suggestedFilename();

      console.log('file name we are getting is ' + fileName);
      const tempPath = await download.path();
      // Check if the downloaded file is a ZIP
      if (path.extname(fileName).toLowerCase() === '.zip') {
        console.log("Detected ZIP file. Extracting contents...");

        const zipBuffer = fs.readFileSync(tempPath); // Read ZIP as binary buffer
        const zip = await JSZip.loadAsync(zipBuffer as any); // Load ZIP

        // Get the expected file inside the ZIP
        console.log("Files in ZIP:", Object.keys(zip.files)); // Print all file names

        const extractedFile = zip.files[fileName.substring(0, fileName.length - 4)]; // Try using the correct file name
        if (!extractedFile || extractedFile.dir) {
          throw new Error(`Expected file ${fileName} not found in ZIP archive.`);
        }

        // Extract the file as text
        const fileContent = await extractedFile.async("text");
        console.log(`Extracted File Content:`, fileContent);

        expect(fileContent).toContain(expectedValue);
      } else {
        // If not a ZIP, read as a normal text file
        const fileContent = fs.readFileSync(tempPath, 'utf8');
        console.log('File Content:', fileContent);
        expect(fileContent).toContain(expectedValue);
      }
    } else {
      await BasePage.checkToast(page, downloadButton, "Error");
    }
  }

  static async checkToast(page: Page, buttonResponsibleForToast: Locator, toastMessage: string) {
    try {
      await Promise.all([
        expect(page.locator(`//*[contains(@class,"Toastify__toast")]//*[text()="${toastMessage}"]`).nth(0)).toBeVisible({ timeout: 40000 }),
        buttonResponsibleForToast.click()
      ]);
      //await page.getByText(`${toastMessage}`).first().waitFor({ state: "hidden", timeout: 40000 });
      await expect(page.locator('//*[text()="Success"]').first()).toBeHidden({ timeout: 12000 });
      return true;
    } catch (error) {
      console.error(`An error occurred during ${toastMessage}: ${error.message}`);
      throw error;
    }
  }

  // Wait for an element to be visible
  async waitForElement(locator: Locator, timeout: number = 5000): Promise<void> {
    try {
      await locator.waitFor({ state: 'visible', timeout });
    } catch (error) {
      this.handleError(`Element ${locator} not visible after ${timeout}ms`, error);
    }
  }

  // Wait for an element with specific text
  async waitForElementWithText(locator: Locator, expectedText: string, timeout: number): Promise<void> {
    try {
      const actualText = await locator.textContent();
      expect(actualText).toContain(expectedText);
    } catch (error) {
      this.handleError(`Error checking text of element ${locator}`, error);
    }
  }

  // Click an element and wait for visibility
  async click(locator: Locator, timeout: number = 5000): Promise<void> {
    try {
      await this.waitForElement(locator, timeout);
      await locator.click();
    } catch (error) {
      this.handleError(`Error clicking element ${locator}`, error);
    }
  }

  // Check if the toast message is visible
  async checkToast(toastLocator: Locator, expectedMessage: string, timeout: number = 5000): Promise<void> {
    try {
      await this.waitForElement(toastLocator, timeout);
      const toastMessage = await toastLocator.textContent();
      expect(toastMessage).toContain(expectedMessage);
    } catch (error) {
      this.handleError('Error checking toast message', error);
    }
  }

  // Generate random strings with specific patterns
  static generateRandomString(length: number, chars: string): string {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  static generateRandomStringWithCharsOnly(length: number): string {
    return this.generateRandomString(length, 'abcdefghijklmnopqrstuvwxyz');
  }

  static generateRandomStringWithDigitsOnly(length: number): string {
    return this.generateRandomString(length, '123456789');
  }

  static generateRandomStringWithUpperCaseAndSpecialChars(length: number): string {
    return this.generateRandomString(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()?/|{}[],.;: ');
  }

  // Split key-value pair from string
  static async splitKeyValue(input: string) {
    const values = input.split(':').map(part => part.trim());
    const valueToReturn = values.length === 3 ? values[1] + ':' + values[2] : values[1];
    return { key: values[0], value: valueToReturn };
  }

  // Create a new page with custom cookies
  static async createNewPageWithCustomCookies(browser: Browser, tokenValue: string, name: string = 'argocd.token'): Promise<Page> {
    const context = await browser.newContext();
    context.addCookies([{
      name,
      url: process.env.BASE_SERVER_URL?.split('/dashboard')[0],
      value: tokenValue
    }]);
    return await context.newPage();
  }

  // Click the tooltip OK button
  static async clickTooltipOkButton(page: Page) {
    const tooltipButton = page.locator('[data-testid="getting-started-okay"]');
    await tooltipButton.click({ timeout: 12000 }).catch(() => {
      console.log('Tooltip OK button click failed, but continuing...');
    });
  }

  // Toggle Dark Mode
  static async toggleDarkMode(page: Page) {
    const darkModeButton = page.locator('.theme-preference-option__container').first();
    await darkModeButton.click({ timeout: 5000 });
    await page.locator('[for="theme-preference-option__input-light"]').click({ timeout: 5000 });
    await page.locator('//*[text()="Save Preference"]').click({ timeout: 5000 });
  }

  // Wait for a specific API response
  static async waitForApiResponse(page: Page, urlToCheck: string, statusCode: number, performAction: (page: Page) => Promise<void>): Promise<void> {
    await Promise.all([
      page.waitForResponse(response => response.url().includes(urlToCheck) && response.status() === statusCode),
      performAction(page)
    ]);
  }


  // Get the current month in localized format
  static getCurrentMonthInLocalizedFormat(): string {
    const date = new Date();
    return new Intl.DateTimeFormat('en-US', { month: 'long' }).format(date);
  }

  // Normalize string (remove non-breaking spaces and normalize dash characters)
  static normalizeString(str: string | undefined): string {
    return (str || "").trim()
      .replace(/\u00A0/g, " ")  // Non-breaking space
      .replace(/\u2013|\u2014/g, "-");  // En dash & em dash
  }

  // Fetch current time information from browser
  static async fetchTimeFromBrowser(page: Page) {
    return await page.evaluate(() => {
      const now = new Date();
      const day = now.toLocaleDateString('en-US', { weekday: 'short' });
      const date = now.getDate();
      const year = now.getFullYear();
      let hours = now.getHours();
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const period = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12 || 12;
      const formattedHours = hours.toString().padStart(2, '0');
      const time = `${formattedHours}:${minutes} ${period}`;
      const month = now.toLocaleDateString('en-US', { month: 'long' });
      const totalMinutes = now.getHours() * 60 + now.getMinutes();
      return { day, date, year, time, month, totalMinutes };
    });
  }

  // Map time to the nearest half hour
  static mapTimeToNearestHalfHour(totalMinutes: number, mode = 'pre'): string {
    let mappedMinutes = 0;
    let hourPart = Math.floor(totalMinutes / 60);
    const minutePart = totalMinutes % 60;

    if (mode === 'pre') {
      mappedMinutes = minutePart < 30 ? 0 : 30;
    } else if (mode === 'post') {
      mappedMinutes = minutePart <= 30 ? 30 : 0;
      if (mappedMinutes === 0) hourPart += 1;
    }

    hourPart %= 24;
    const finalHour = hourPart % 12 === 0 ? 12 : hourPart % 12;
    const finalPeriod = hourPart >= 12 ? 'PM' : 'AM';
    return `${finalHour.toString().padStart(2, '0')}:${mappedMinutes.toString().padStart(2, '0')} ${finalPeriod}`;
  }
    /**
* This function is to select date from calendar , if you want to set custom date , use this method
* @param date 
* @param month 
* @param year 
*/

async setCustomDate(date: string, month: string, year: string) {
  console.log('we are printing our date');
  console.log(date);
  var yearAndMonth = await this.yearAndMonthTextOnCalendar.nth(1).textContent();
  while (yearAndMonth?.split(' ')[1] != year || yearAndMonth?.split(' ')[0] != month) {
      console.log('year is' + yearAndMonth?.split(' ')[1]);
      console.log('month is ' + yearAndMonth?.split(' ')[0]);
      if (Number(year) > Number(yearAndMonth?.split(' ')[1]) || Number(monthMappingForCalendar[month]) > Number(monthMappingForCalendar[yearAndMonth?.split(' ')[0]!])) {
          await this.calendarNavigateRightButton.click();
          console.log('verified value is ' + Number(monthMappingForCalendar[yearAndMonth?.split(' ')[0]!]));
      }
      else {
          await this.calendarNavigateLeftButton.click();
      }
      yearAndMonth = await this.yearAndMonthTextOnCalendar.nth(1).textContent();
  }
  await this.page.locator(`//*[text()="${date}"]`).nth(1).click();
 }

}