import { Locator, Page, Request, expect, APIRequest, APIRequestContext } from "@playwright/test";
import { obejctOfCreateAppApi } from "./ApiPayloads/CreateDevtronApp";
import { dataForTriggerCi } from "./ApiPayloads/CiCdTriggerAndStatusVerificationData";
import { updateDeploymentTemplatePayload } from "./ApiPayloads/UpdateDeploymentTemplate";
import { payloadForExternalCiWorkflowCreation } from "./ApiPayloads/ExternalCIWorkflowCreation";
import { payloadForSuperAdminOrDevtronAppsAdmin } from "./ApiPayloads/UpdateTokenPermissions";
import { DevtronAllChartVersion, DevtronDeploymentChart } from "../enums/ApplicationManagement/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum";



export class ApiUtils {
    readonly serverUrl: string;
    constructor(private request: APIRequestContext) {
        this.request = request;
        this.serverUrl = process.env.BASE_SERVER_URL!.slice(0, process.env.BASE_SERVER_URL!.indexOf('/dashboard'));
    }


    /**
     * use this method to generate token , that we are going to use for all api hits
     * @param password 
     * @returns token 
     */
    async login(password: string) {
        let response;
        try {
            if (process.env.isStaging == "true") {
                return process.env.stagingSuperAdminToken;
            }
            else {
                response = await this.request.post(`${this.serverUrl}/orchestrator/api/v1/session`, { data: { "username": "admin", "password": `${password}` } });
                const parsedResponse = await response.json();
                expect(response.ok()).toBeTruthy();
                return parsedResponse.result.token;
            }
        }
        catch (error) {
            console.log('error in getting the login token ');
            console.log(JSON.stringify(await response.json()));
            throw error;
        }
    }


    /**
     * use this method to create resource from resouurce browser 
     * @param token 
     * @param resourceType 
     * @param resourceName 
     * @param keyName 
     * @param valueName 
     */
    async createResource(token: string, resourceType: string, resourceName: string, keyName: string, valueName: string) {
        let response;
        try {
            response = await this.request.post(`${this.serverUrl}/orchestrator/k8s/resources/apply`,
                {
                    data: { "clusterId": 1, "manifest": `apiVersion: v1\nkind: ${resourceType}\nmetadata:\n  name: ${resourceName}\n  namespace: automation\ntype: Opaque\ndata:\n  ${keyName}: ${valueName}` },
                    headers: { 'argocd.token': token }
                })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log(`error in creating the resource  ${resourceType} with name ${resourceName}` + JSON.stringify(await response.json()));
            throw error;
        }
    }

    /**
     * use this method to get the list of registry list
     * @param token 
     * @returns the list of registries present in the cluster
     */
    async getRegistryList(token: string) {
        let response;
        try {
            response = await this.request.get(`${this.serverUrl}/orchestrator/docker/registry`,
                {
                    headers: { 'argocd.token': token }
                });
            expect(response.ok()).toBeTruthy();
            const parsedResponse = await response.json();
            return parsedResponse.result;
        }
        catch (error) {
            console.log('error in getting the registry list' + JSON.stringify(await response.json()));
            throw error;
        }
    }



    /**
     * use this method to create registry or edit registry
     * @param token 
     * @param registryName 
     * @param registryUrl 
     * @param userName 
     * @param password 
     * @param registryType 
     * @param callType  -> we are setting this type for put or post call
     */
    async createRegistry(token: string, registryName: string, registryUrl: string, userName: string, password: string, registryType: string, callType: string) {
        let response;
        try {
            let data: any = {
                "id": `${registryName}`,
                "pluginId": "cd.go.artifact.docker.registry",
                "registryType": `${registryType}`,
                "isDefault": false,
                "isOCICompliantRegistry": true,
                ociRegistryConfig: {
                    "CONTAINER": "PULL/PUSH"
                },
                "isPublic": false,
                "repositoryList": null,
                "registryUrl": `${registryUrl}`,
                "username": `${userName}`,
                "password": `${password}`,
                "connection": "secure",
                "cert": "",
                "ipsConfig": {
                    "id": 0,
                    "credentialType": "SAME_AS_REGISTRY",
                    "credentialValue": "",
                    "appliedClusterIdsCsv": "",
                    "ignoredClusterIdsCsv": "-1"
                },
                "remoteConnectionConfig": {
                    "connectionMethod": "DIRECT",
                    "proxyConfig": null,
                    "sshConfig": null
                }
            }
            if (process.env.clusterType == "enterprise") {
                data.ociRegistryConfig.CHART = "PUSH"
            }
            if (callType == "Put") {
                response = await this.request.put(`${this.serverUrl}/orchestrator/docker/registry`,
                    {
                        data: data,
                        headers: { 'argocd.token': token }
                    })
                expect(response.ok()).toBeTruthy();
            } else {
                response = await this.request.post(`${this.serverUrl}/orchestrator/docker/registry`,
                    {
                        data: data,
                        headers: { 'argocd.token': token }
                    })
                expect(response.ok()).toBeTruthy();
            }
        }
        catch (error) {
            console.log('we are not able to add the required registry getting error');
            console.log(JSON.stringify(await response.json()));
            throw error;
        }


    }


    /**
     * use this method to get list of all gitops provider configurations present in our cluster
     */
    async getGitopsProviderDetail(token: string) {
        const response = await this.request.get(`${this.serverUrl}/orchestrator/gitops/config`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        const parsedResponse = await response.json();
        return parsedResponse.result;
    }


    /**
     * Adds or updates a GitOps provider configuration with extended options
     * @param requestType - 'post' for new or 'put' for update
     * @param Apitoken - Authentication token
     * @param id - Provider ID (null for new provider)
     * @param provider - Provider type (GITLAB, GITHUB, etc.)
     * @param userName - Username for the provider
     * @param host - Host URL for the provider
     * @param token - Auth token for the provider
     * @param orgIdKeyName - Key name for organization ID ('gitHubOrgId', 'gitLabGroupId', etc.)
     * @param orgIdValue - Value for organization ID
     * @param options - Optional extended configuration parameters
     * @returns {Promise<void>}
     */
    async addGitopsProvider(
        requestType: string,
        Apitoken: string,
        id: Number | null,
        provider: string,
        userName: string,
        host: string,
        token: string,
        orgIdKeyName: string,
        orgIdValue: string,
        options?: {
            active?: boolean,
            azureProjectName?: string,
            bitBucketWorkspaceId?: string,
            bitBucketProjectKey?: string,
            allowCustomRepository?: boolean,
            enableTLSVerification?: boolean,
            tlsConfig?: any,
            isCADataPresent?: boolean,
            isTLSCertDataPresent?: boolean,
            isTLSKeyDataPresent?: boolean,
            authMode?: string,
            sshKey?: string,
            sshHost?: string
        }
    ) {
        let stringifiedResponse: string;
        try {
            var response: any
            const body = {
                "id": id,
                "provider": provider,
                "username": userName,
                "host": host,
                "token": token,
                [orgIdKeyName]: orgIdValue,
                "azureProjectName": options?.azureProjectName || "",
                "bitBucketWorkspaceId": options?.bitBucketWorkspaceId || "",
                "bitBucketProjectKey": options?.bitBucketProjectKey || "",
                "allowCustomRepository": options?.allowCustomRepository !== undefined ? options.allowCustomRepository : true,
                "active": options?.active !== undefined ? options.active : true,
                "enableTLSVerification": options?.enableTLSVerification || false,
                "tlsConfig": options?.tlsConfig || null,
                "isCADataPresent": options?.isCADataPresent || false,
                "isTLSCertDataPresent": options?.isTLSCertDataPresent || false,
                "isTLSKeyDataPresent": options?.isTLSKeyDataPresent || false,
                "authMode": options?.authMode || undefined,
                "sshKey": options?.sshKey || undefined,
                "sshHost": options?.sshHost || undefined
            }

            // Remove undefined properties from the body
            Object.keys(body).forEach(key => body[key] === undefined && delete body[key]);

            const headersContent = {
                'argocd.token': Apitoken
            }
            if (requestType == 'post') {
                response = await this.request.post(`${this.serverUrl}/orchestrator/gitops/config`, {
                    data: body,
                    headers: headersContent,
                    timeout: 3 * 1000 * 60
                })
            }
            else {
                response = await this.request.put(`${this.serverUrl}/orchestrator/gitops/config`, {
                    data: body,
                    headers: headersContent,
                    timeout: 3 * 1000 * 60
                })
            }
            stringifiedResponse = JSON.stringify(await response.json());
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('not able to save gitops provider');
            console.log(stringifiedResponse!);
            throw error;
        }
    }




    /**
     * use this method to get the object of env if found , 
     * @param token 
     * @returns 
     * use id, to get the id , from this object from where u are calling this method
     * 
     */
    async getEnvObject(token: string, envName: string) {
        let parsedResponse;
        try {
            const response = await this.request.get(`${this.serverUrl}/orchestrator/env`, {
                headers: {
                    'argocd.token': token
                }
            })
            expect(response.ok()).toBeTruthy();
            parsedResponse = await response.json();
            return parsedResponse.result.find(key => key.environment_name == envName);
        }
        catch (error) {
            console.log('not able to get the env objectData');
            console.error(JSON.stringify(parsedResponse));
            throw error;
        }
    }



    /**
     * use this method to add an env 
     * @param token 
     * @param methodType 
     * @param id 
     * @param envName 
     * @param isProd 
     */
    async addEnv(token: string, methodType: string, id: number | null, envName: string, isProd: boolean, clusterId: number, clusterType: 'virtual' | 'normal') {
        var response: any;
        let parsedResponse;
        let urlToHit = clusterType == 'normal' ? `${this.serverUrl}/orchestrator/env` : `${this.serverUrl}/orchestrator/env/virtual`
        try {
            const body: any = {
                "id": id,
                "environment_name": envName,
                "cluster_id": clusterId,
                "namespace": envName,
                "active": true,
                "default": isProd,
                "description": ""
            }
            clusterType == 'virtual' ? body.IsVirtualEnvironment = true : ''

            const headersBody = {
                'argocd.token': token
            }
            if (methodType == "post") {
                response = await this.request.post(urlToHit, {
                    data: body,
                    headers: headersBody
                })
            }
            else {
                response = await this.request.put(urlToHit, {
                    data: body,
                    headers: headersBody
                })
            }
            parsedResponse = await response.json();
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.error('not able to add env getting error' + JSON.stringify(parsedResponse));
            throw error;
        }

    }



    /**
     * 
     * @param token 
     * @param appId 
     * @param cdName 
     * @returns 
     */
    async getCdWorkflowId(token: string, appId: string, cdName: string): Promise<number> {
        const response = await this.request.get(`${this.serverUrl}/orchestrator/app/cd-pipeline/${appId}`, {
            headers: {
                'argocd.token': token
            }
        })
        const parsedJson = await response.json();
        try {
            expect(response.ok()).toBeTruthy();
        } catch (error) {
            console.log('error in getting the cd pipeline id ' + JSON.stringify(parsedJson));
            throw error;
        }

        let resultantObject = parsedJson.result.pipelines.find(
            function (key) {
                if (key.environmentName.trim().toLowerCase() === cdName.trim().toLowerCase()) {
                    return key;
                }
                else {
                }
            })
        return resultantObject.id;
    }



    /**
     * 
     * @param token 
     * @param appid 
     * @param workflowId 
     */
    async deleteWorkflowCd(token: string, appid: number, workflowId: number) {
        let response;
        try {
            response = await this.request.post(`${this.serverUrl}/orchestrator/app/cd-pipeline/patch`, {
                headers: {
                    'argocd.token': token
                },
                data: { action: 1, appId: appid, pipeline: { id: workflowId } }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in deleting the workflow cd pipeline' + JSON.stringify(await response.json()));
            throw error;
        }
    }



    /**
     * 
     * @param data 
     * @returns 
     */
    async getNotifiersList(data: { token: string }) {
        const response = await this.request.get(`${this.serverUrl}/orchestrator/notification/channel`, {
            headers: {
                'argocd.token': data.token
            }
        });
        expect(response.ok()).toBeTruthy();
        return await response.json();
    }


    /**
     * 
     * @param appId 
     * @param token 
     * @param envId 
     * @param key 
     * @param value 
     * @param name 
     * @param isSecret 
     */
    async addConfigOrSecretInEnv(cmcsObjectData: {
        appId: number,
        token: string,
        key: string,
        value: string,
        name: string,
        isSecret: boolean,
        envId?: number, // New parameter to determine if it's a secret
    }[]) {
        for (let cmcsObject of cmcsObjectData) {
            let data: any =
            {
                "appId": cmcsObject.appId,
                "configData": [
                    {
                        "name": cmcsObject.name,
                        "type": "environment",
                        "external": false,
                        "data": {
                            [cmcsObject.key]: cmcsObject.isSecret ? Buffer.from(cmcsObject.value).toString('base64') : cmcsObject.value // Base64 for secrets
                        },
                        "roleARN": null,
                        "externalType": null,
                        "secretData": null,
                        "esoSecretData": null,
                        "mountPath": null,
                        "subPath": null,
                        "filePermission": null,
                        "esoSubPath": null,
                        "mergeStrategy": null
                    }
                ]
            }
            if (cmcsObject.envId != undefined) {
                data.environmentId = cmcsObject.envId;
            }
            let url: string = this.serverUrl + `/orchestrator/config/${cmcsObject.envId != undefined ? "environment" : "global"}/${cmcsObject.isSecret ? "cs" : "cm"}`;

            let parsedResponse;
            try {
                let response = await this.request.post(
                    url, // Dynamic endpoint
                    {
                        headers: {
                            'argocd.token': cmcsObject.token
                        },
                        data: data
                    }
                );
                parsedResponse = await response.json();
                expect(response.ok()).toBeTruthy();
            }
            catch (error) {
                console.log(`not able to add cm/cs named as ${cmcsObject.name}` + JSON.stringify(parsedResponse));
                throw error;
            }
        }

    }


    /**
     * 
     * @param token 
     * @param identifier 
     * @param email_id 
     */
    async addUserInUserGroup(token: string, identifier: string, email_id: string) {
        let response;
        try {
            response = await this.request.post(`${this.serverUrl}/orchestrator/user`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "id": 0,
                    "superAdmin": true,
                    "email_id": email_id,
                    "userRoleGroups": [],
                    "roleFilters": [
                        {
                            "entity": "chart-group",
                            "action": "view",
                            "entityName": "",
                            "team": "",
                            "environment": "",
                            "status": "active"
                        }
                    ],
                    "userGroups": [
                        {
                            "identifier": identifier
                        }
                    ],
                    "userStatus": "active"
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log(`error in adding user in user group  with name as ${email_id}` + JSON.stringify(await response.json()));
            throw error;
        }
    }



    /**
     * 
     * @param token 
     * @param groupId 
     * @param identifier 
     * @param name 
     * @param description 
     */
    async createUserGroup(token: string, groupId: number, identifier: string, name: string, description: string) {
        let response;
        try {
            response = await this.request.post(`${this.serverUrl}/orchestrator/user-group`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    id: groupId,
                    identifier: identifier,
                    name: name,
                    description: description
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log(`error in creating user group with name as ${name}` + JSON.stringify(await response.json()));
            throw error;
        }
    }


    /**
     * 
     * @param slackToken 
     * @param channelId 
     * @returns 
     */
    async fetchSlackNotification(slackToken: string, channelId: string) {
        const response = await this.request.get('https://slack.com/api/conversations.history', {
            headers: {
                Authorization: `Bearer ${slackToken}`,
            },
            params: {
                channel: `${channelId}`,
                limit: 20, // Fetch only the latest message
            },
        });

        const responseBody = await response.json();
        expect(response.ok()).toBeTruthy();
        expect(response.status()).toBe(200);

        return responseBody;
    }

    async fetchEnvOnAppConfigurationsPage(appid: number) {
        let token: string = '';
        if (process.env.isStaging) {
            token = process.env.stagingSuperAdminToken as string;
        } else {
            token = await this.login(process.env.PASSWORD as string);
        }
        const response = await this.request.get(`${this.serverUrl}/orchestrator/app/other-env/min?app-id=${appid}`, {
            headers: {
                'argocd.token': token
            }
        })
        const responseBody = await response.json();
        expect(response.ok()).toBeTruthy();
        expect(response.status()).toBe(200);

        return responseBody

    }



    /**
     * 
     * @param envName 
     * @param token 
     * @returns 
     */
    async findAtenantFromInstallationName(envName: string, token: string) {
        let resut = await this.request.get(this.serverUrl + '/orchestrator/resource/list/tenant/alpha1?fetchChild=true', {
            headers: {
                'argocd.token': token
            },
        })
        let parsedResult = await resut.json();
        let data = parsedResult.result.data;
        for (let i = 0; i < data.length; i++) {
            if (data[i].childObjects) {
                for (let j = 0; j < data[i].childObjects.length; j++) {
                    let tenantName = data[i].name.toLowerCase();
                    let installationResult = await this.request.get(this.serverUrl + `/orchestrator/resource/dependencies/installation/alpha1?identifier=${tenantName}-${data[i].childObjects[j].name}`);
                    let parsedInstallationResult = await installationResult.json();
                    let installationsData = parsedInstallationResult.result.dependencies
                    for (let k = 0; k < installationsData.length; k++) {
                        if (installationsData[k].identifier == envName) {
                            return data[i].name;
                        }
                    }
                }
            }
        }
        return null;
    }



    /**
     * 
     * @param appName 
     * @param token 
     */
    async deleteAllNodesOfApplication(appName: string, token: string, deleteAppALso: boolean = true) {

        let appId = await this.getAppIdFromAppName(appName, token);
        let workflows = await this.getWorkflowListingOfApp(appId, token);
        for (let i = 0; i < workflows.length; i++) {
            let skippedIndex: number = 0;
            for (let j = workflows[i].tree.length - 1; j >= 0; j--) {
                if (workflows[i].tree[j].type == "CI_PIPELINE") {
                    if (j != 0) {
                        skippedIndex = j;
                        continue;
                    }
                    let ciPipelineData = await this.getDetailsOfCiPipeline(appId, String(workflows[i].tree[j].componentId), token);
                    await this.deleteCiNode(token, ciPipelineData, appId, workflows[i].id);
                    await this.deleteAworkflow(appId, workflows[i].id, token);
                }
                else {
                    if (workflows[i].tree[j].type == "WEBHOOK") { continue; }
                    await this.deleteCdNode(appId, workflows[i].tree[j].componentId, token);
                    if (workflows[i].tree[j].parentType == "WEBHOOK" && j == 1) {
                        break;
                    }
                }
            }
            if (skippedIndex) {
                let ciPipelineData = await this.getDetailsOfCiPipeline(appId, String(workflows[i].tree[skippedIndex].componentId), token);
                await this.deleteCiNode(token, ciPipelineData, appId, workflows[i].id);
                await this.deleteAworkflow(appId, workflows[i].id, token);
                skippedIndex = 0;
            }
        }

        deleteAppALso ? await this.deleteTheAppWhenWorkflowsAreDeleted(appId, token) : '';
        console.log('app has been deleted successfully ' + appName);
    }





    /**
     * 
     * @param appId 
     * @param token 
     * @returns 
     */
    async getWorkflowListingOfApp(appId: string, token: string) {
        let response = await this.request.get(this.serverUrl + `/orchestrator/app/app-wf/${appId}`, {
            headers: {
                'argocd.token': token
            }
        });
        let workflowsArray:any[]=[];
        let parsedResponse = await response.json();
        let ciPipelinesData = await this.getDetailsOfCiPipeline(appId, 0, token, true);
        if(ciPipelinesData){
            let ciPipelineIdsHavingLinkedAsType = ciPipelinesData.filter((key: any) => {
                if (key.pipelineType == "LINKED_CD" || key.pipelineType == "LINKED") {
                    return key;
                }
            })
            ciPipelineIdsHavingLinkedAsType = ciPipelineIdsHavingLinkedAsType.map((key: any) => {
                return key.id;
            })
            console.log('prinitng the array value' + JSON.stringify(ciPipelineIdsHavingLinkedAsType));
            workflowsArray = parsedResponse.result.workflows.filter((workflow: any) => {
                return workflow.tree.some((pipeline: any) => {
                    if (ciPipelineIdsHavingLinkedAsType.includes(pipeline.componentId)) {
                        console.log('match found');
                    }
                    const match = ciPipelineIdsHavingLinkedAsType.includes(pipeline.componentId);
                    return match;
                });
            });
        }
        console.log('printing the workfow array spcl entry' + JSON.stringify(workflowsArray));
        parsedResponse.result.workflows.forEach((key: any) => {
            if (!workflowsArray.includes(key)) {
                workflowsArray.push(key);   
            }
        })
        console.log('printing the final workfow array' + JSON.stringify(workflowsArray));
        return workflowsArray;
    }




    /**
     * 
     * @param appId 
     * @param ciPipelineId 
     * @param token 
     * @returns 
     */
    async getDetailsOfCiPipeline(appId: string, ciPipelineIdOrNumber: string | number, token: string, getCompleteArrayObject: boolean = false) {
        let response = await this.request.get(this.serverUrl + `/orchestrator/app/ci-pipeline/${appId}`, {
            headers: {
                'argocd.token': token
            }
        });
        let parsedResponse = await response.json();
        let ciPipelines = parsedResponse?.result?.ciPipelines;
        if (getCompleteArrayObject) {
            return ciPipelines;
        }
        if (typeof ciPipelineIdOrNumber == "string") {
            return parsedResponse.result.ciPipelines.find(key => key.id == ciPipelineIdOrNumber);
        }
        else {
            return parsedResponse.result.ciPipelines[ciPipelineIdOrNumber];
        }
    }




    /**
     * 
     * @param appId 
     * @param pipelineId 
     * @param token 
     */

    async deleteCdNode(appId: string, pipelineId: string, token: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/app/cd-pipeline/patch`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    action: 1,
                    appId: Number(appId),
                    pipeline: {
                        id: Number(pipelineId)
                    }
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in deleting the cd node' + JSON.stringify(await response.json()));
            throw error;
        }
    }




    /**
     * 
     * @param appId 
     * @param workflowId 
     * @param token 
     */
    async deleteAworkflow(appId: number, workflowId: number, token: string) {
        let response;
        try {
            response = await this.request.delete(this.serverUrl + `/orchestrator/app/app-wf/${appId}/${workflowId}`, {
                headers: {
                    'argocd.token': token
                }
            });
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in deleting the workflow' + JSON.stringify(await response.json()));
            throw error;
        }
    }




    /**
     * 
     * @param token 
     * @param ciPipelineData 
     * @param appId 
     * @param appWorkflowId 
     */
    async deleteCiNode(token: string, ciPipelineData: any, appId: number, appWorkflowId: number) {
        let body = {
            "appId": appId,
            "appWorkflowId": appWorkflowId,
            "action": 2,
            "ciPipeline": ciPipelineData
        }
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/app/ci-pipeline/patch`, {
                headers: {
                    'argocd.token': token
                },
                data: body
            });
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in deleting the ci node' + JSON.stringify(await response.json()));
            throw error;
        }
    }




    /**
     * 
     * @param appName 
     * @param token 
     * @returns 
     */
    async getAppIdFromAppName(appName: string, token: string) {
        let parsedResponse;
        try {
            await expect(async () => {
                let response = await this.request.post(this.serverUrl + `/orchestrator/app/list`, {
                    headers: {
                        'argocd.token': token
                    },
                    data: { appNameSearch: appName, offset: 0, size: 20, sortBy: "appNameSort", sortOrder: "ASC", teams: [] }
                })
                parsedResponse = await response.json();
                expect(response.ok()).toBeTruthy();
            }).toPass({ timeout: 3 * 1000 * 60 });
            if (parsedResponse.result.appContainers) {
                return parsedResponse.result.appContainers[0].appId;
            }
            return undefined;
        }
        catch (error) {
            console.error('error in getting app id' + JSON.stringify(parsedResponse));
            throw error;
        }


    }


    /**
     * 
     * @param appId 
     * @param token 
     */
    async deleteTheAppWhenWorkflowsAreDeleted(appId: string, token: string) {
        let response = await this.request.delete(this.serverUrl + `/orchestrator/app/${appId}`, {
            headers: {
                'argocd.token': token
            }
        });
        let parsedResponse = await response.json();
        try {
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in deleting the application when all workflos are deleted ' + JSON.stringify(parsedResponse));
        }
    }


    /**
     * 
     * @param token 
     * @param envName 
     */
    async deleteEnvFromCluster(token: string, envName: string) {
        let envObject = await this.getEnvObject(token, envName);
        let response;
        try {
            response = await this.request.delete(this.serverUrl + `/orchestrator/env`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    id: envObject.id, environment_name: envObject.environment_name, cluster_id: envObject.cluster_id, namespace: envObject.namespace, active: envObject.active, default: envObject.default, description: envObject.description
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in deleting the env' + JSON.stringify(await response.json()));
            throw error;
        }
    }




    /**
     * 
     * @param filterName 
     * @param token 
     * @returns 
     */
    async getObjectOfCdFilter(filterName: string, token: string) {
        let response = await this.request.get(this.serverUrl + '/orchestrator/filters', {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        return parsedResponse.result.find(key => key.name == filterName)
    }



    /**
     * 
     * @param filterName 
     * @param token 
     */
    async deleteCdFilter(filterName: string, token: string) {
        let filterObject = await this.getObjectOfCdFilter(filterName, token);
        let response = await this.request.delete(this.serverUrl + `/orchestrator/filters/${filterObject.id}`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
    }



    /**
     * use this method to create devtron apps ,parameters are open for the current requirement only , for new requirements modify the params and data object also
     * @param token 
     * @param data 
     */
    async createDevtronApp(token: string, data: { appName: string, regName: string, repoName: string, repoUrl: string, projName: string, envName: string[] }, deploymentTemplateChartName?: DevtronDeploymentChart | string, allChartVersion?: DevtronAllChartVersion | string) {
        console.log('devtron app we are going to create is ' + data.appName);
        let response;
        try {
            let chartRefId = await this.getChartRefid(token, deploymentTemplateChartName, allChartVersion);
            response = await this.request.post(this.serverUrl + `/orchestrator/core/v1beta1/application`, {
                headers: {
                    token: token
                },
                data: obejctOfCreateAppApi({ appName: data.appName, regName: data.regName, repoName: data.repoName, repoUrl: data.repoUrl, envName: data.envName, projName: data.projName, chartRefId: chartRefId }),
                timeout: 2 * 1000 * 60
            });
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in creation of devtron app through api' + JSON.stringify(await response.json()));
            throw error;
        }
    }



    async triggerCi(appName: string, token: string, ciPipelineNumber: number) {
        console.log('Triggering the ci through api');
        let response;
        try {
            let appId = await this.getAppIdFromAppName(appName, token);
            let ciDetailsObject = await this.getDetailsOfCiPipeline(appId, ciPipelineNumber, token);
            let combinedGitMaterial = await this.fetchGitMaterialObjectOfAParticularCi(token, ciDetailsObject.id);
            let dataForObjectCreation: { materialId: string, commit: string }[] = [];
            for (let key of combinedGitMaterial) {
                let object = {
                    materialId: key.id,
                    commit: key.history[0].Commit
                }
                dataForObjectCreation.push(object);
            }
            response = await this.request.post(this.serverUrl + `/orchestrator/app/ci-pipeline/trigger`, {
                headers: {
                    token: token
                },
                data: dataForTriggerCi(ciDetailsObject.id, dataForObjectCreation)
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in triggering ci pipeline' + JSON.stringify(await response.json()));
            throw error;
        }


    }


    async fetchGitMaterialObjectOfAParticularCi(token: string, ciPipelineId: string) {
        let response = await this.request.get(this.serverUrl + `/orchestrator/app/ci-pipeline/${ciPipelineId}/material`, {
            headers: {
                'argocd.token': token
            }
        })
        let parsedResponse = await response.json();
        expect(response.ok()).toBeTruthy();
        return parsedResponse.result;
    }
    async verifyAndWaitForParticularStatusOfCiCd(appName: string, token: string, nodeNumber: number, statusToVerify: string, isCiNode: boolean,) {
        let status = "";
        let count = 0;
        let appId = await this.getAppIdFromAppName(appName, token);
        let response;
        console.log('waiting for the ci status to get succeeded ....');
        try {
            while (status != statusToVerify && count <= 100) {
                response = await this.request.get(this.serverUrl + `/orchestrator/app/workflow/status/${appId}/v2`, {
                    headers: {
                        'argocd.token': token
                    }
                })
                expect(response.ok()).toBeTruthy();
                let parsedResponse = await response.json();
                count += 1;
                if (isCiNode) {
                    status = parsedResponse.result.ciWorkflowStatus[nodeNumber].ciStatus;
                }
                else {
                    status = parsedResponse.result.cdWorkflowStatus[nodeNumber].deploy_status;
                }
                await new Promise(resolve => setTimeout(resolve, 4000));
            }
            expect(status).toBe(statusToVerify);
            console.log('ci got succeeded moving ahead ...');
        }
        catch (error) {
            console.log('instead of getting succedded ci got' + status);
            throw error;
        }



    }
    async UserGroupExistsOrNot(userGroupName: string, token: string) {
        let response = await this.request.get(process.env.BASE_SERVER_URL! + `/orchestrator/user-group`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let result = await response.json();
        if (result.result.length == 0 || !result.result.find(key => key.name == userGroupName)) {
            return false;
        }
        return true;
    }

    async deleteApprovalPolicy(policyName: string, token: string) {
        let response = await this.request.delete(this.serverUrl + `/orchestrator/global/policy/approval/alpha1?identifier=${policyName}`);
        expect(response.ok()).toBeTruthy();
    }

    async fetchUserIdFromUsername(userEmail: string, token: string) {
        let response = await this.request.get(this.serverUrl + `/orchestrator/user/v2?size=1000&offset=0&sortBy=email_id&sortOrder=ASC`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        let userObject = parsedResponse.result.users.find(key => key.email_id == userEmail);
        return userObject.id;
    }
    async deleteAnUser(userEmail: string, token: string) {
        let userId = await this.fetchUserIdFromUsername(userEmail, token)
        let response = await this.request.delete(this.serverUrl + `/orchestrator/user/${userId}`);
        expect(response.ok()).toBeTruthy();
    }
    async getArtifactGeneratedAfterCiSuccess(token: string, workflowNumber: number, ciBuildNumber: number, appName: string) {
        let appid = await this.getAppIdFromAppName(appName, token);
        let ciPipelineObject = await this.getDetailsOfCiPipeline(appid, workflowNumber, token);
        let ciPipelineId = ciPipelineObject.id
        await this.verifyAndWaitForParticularStatusOfCiCd(appName, token, workflowNumber, "Succeeded", true);
        let response = await this.request.get(this.serverUrl + `/orchestrator/app/ci-pipeline/${ciPipelineId}/workflows?offset=0&size=20`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        return parsedResponse.result.ciWorkflows[ciBuildNumber].artifact;
    }
    async getChartRefid(token: string, chartType?: string, chartVersion?: string) {
        let response = await this.request.get(this.serverUrl + `/orchestrator/deployment/template/fetch`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        let chartTypeToSearch = chartType || "Deployment";
        let versionToSearch = chartVersion || "4.20.0"
        let chartObject = parsedResponse.result.find(key => key.version == versionToSearch && key.name == chartTypeToSearch);
        if (chartObject) {
            return chartObject.id;
        }
        return undefined;
    }
    async getTemplateDetails(token: string, chartType: string, chartVersion: string, appId: string) {
        let templateId = await this.getChartRefid(token, chartType, chartVersion);
        let response = await this.request.get(this.serverUrl + `/orchestrator/app/template/${appId}/${templateId}`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        return parsedResponse.result;
    }
    async updateTemplate(token: string, chartType: string, chartVersion: string, appId: string) {
        let response;
        try {
            let templateId = await this.getChartRefid(token, chartType, chartVersion);
            let object = await this.getTemplateDetails(token, chartType, chartVersion, appId);
            response = await this.request.post(this.serverUrl + `/orchestrator/app/template/update`, {
                headers: {
                    'argocd.token': token
                },
                data: updateDeploymentTemplatePayload(object.globalConfig.id, templateId, chartVersion, Number(appId))
            });
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in updating the template' + JSON.stringify(await response.json()));
            throw error;
        }

    }
    async createBuildInfraProfileWithDefaultConfig(token: string, profileName: string, statusCode: number) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/infra-config/profile/alpha1`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "name": profileName,
                    "description": "",
                    "type": "NORMAL",
                    "configurations": {
                        "runner": []
                    }
                }
            });
            expect(response.status()).toBe(statusCode);
        }
        catch (error) {
            console.log('error in creating the build infra profile' + JSON.stringify(await response.json()));
            throw error;
        }
    }
    async getExistingGlobalCmCs(token: string) {
        let response = await this.request.get(this.serverUrl + `/orchestrator/global/cm-cs/all`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        return parsedResponse.result;
    }
    async createGlobalCmCsEnv(token: string, name: string, data: { key: string, value: string }, configOrSecret: "CONFIGMAP" | "SECRET", cicd: 'CI/CD' | 'CD' | 'CI') {
        let response;
        try {
            response = await this.request.post(this.serverUrl + '/orchestrator/global/cm-cs', {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "name": name,
                    "configType": configOrSecret,
                    "type": "environment",
                    "data": {
                        [data.key]: data.value
                    },
                    "SecretIngestionFor": cicd
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log(`getting error in creating the global cm/cs with name ${name}`);
            console.log(JSON.stringify(await response.json()));
            throw error;
        }
    }
    async getClusterId(token: string, clusterName: string) {
        let response = await this.request.get(this.serverUrl + '/orchestrator/cluster', {
            headers: {
                'argocd.token': token
            }
        })

        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        let clusterid = parsedResponse.result.find(key => {
            return key.cluster_name === clusterName;
        });
        return clusterid;
    }

    async addVirtualCluster(virtualClusterName: string, token: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + '/orchestrator/cluster/virtual', {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "id": null,
                    "clusterName": virtualClusterName,
                    "IsVirtualCluster": true
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in creating the virtual cluster' + JSON.stringify(await response.json()));
            throw error;
        }

    }
    async applyImagePromotionProfile(token: string, appEnvConfig: { appName: string, envName: string }[], policyToApply: string[]) {
        let response = await this.request.post(this.serverUrl + `/orchestrator/global/policy/artifact-promotion/bulk/apply`, {
            headers: {
                token: token
            },
            data: {
                "applicationEnvironments": appEnvConfig,
                "applyToPolicyNames": policyToApply
            }
        })
        if (response.status() != 200) {
            let parsedResponse = await response.json();
            console.log('error in applying the image promotion profile , this is error' + JSON.stringify(parsedResponse));
        }
        expect(response.ok()).toBeTruthy();

    }
    async updateSecurityPolicy(token: string, id: number, action: string) {
        const response = await this.request.post(`${this.serverUrl}/orchestrator/security/policy/update`, {
            headers: {
                'argocd.token': token
            },
            data: {
                action: action,
                id: id
            }
        })
        const responseBody = await response.json();
        expect(response.ok()).toBeTruthy();
        expect(response.status()).toBe(200);
    }

    async saveClusterSecurityPolicy(token, clusterId, action, severity) {
        const response = await this.request.post(`${this.serverUrl}/orchestrator/security/policy/save`, {
            headers: {
                'argocd.token': token
            },
            data: {
                "clusterId": clusterId,
                "action": action,
                "severity": severity
            }
        });
        const responseBody = await response.json();
        return responseBody;

    }

    async saveEnvironmentSecurityPolicy(token, envId, action, severity) {
        const response = await this.request.post(`${this.serverUrl}/orchestrator/security/policy/save`, {
            headers: {
                'argocd.token': token
            },
            data: {
                "envId": envId,
                "action": action,
                "severity": severity
            }
        });
        const responseBody = await response.json();
        return responseBody;

    }

    async saveApplicationSecurityPolicy(token, appId, envId, action, severity) {
        const response = await this.request.post(`${this.serverUrl}/orchestrator/security/policy/save`, {
            headers: {
                'argocd.token': token
            },
            data: {
                "envId": envId,
                "appId": appId,
                "action": action,
                "severity": severity
            }
        });
        const responseBody = await response.json();
        return responseBody;

    }

    async triggerCdPipeline(token: string, pipelineId: number, appId: number, ciArtifactId: number) {
        let response: any;
        console.log()
        try {
            response = await this.request.post(`${this.serverUrl}/orchestrator/app/cd-pipeline/trigger`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "pipelineId": pipelineId,
                    "appId": appId,
                    "ciArtifactId": ciArtifactId,
                    "cdWorkflowType": "DEPLOY",
                    "isRollbackDeployment": false,
                    "deploymentWithConfig": "LAST_SAVED_CONFIG"
                }
            });
            //expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in triggering the cd pipeline' + JSON.stringify(await response.json()));
            throw error;
        }
        const responseBody = await response.json();
        return responseBody;
    }

    async fetchArtifactId(token: string, pipelineId: number) {
        const response = await this.request.get(`${this.serverUrl}/orchestrator/app/cd-pipeline/${pipelineId}/material?offset=0&size=20&stage=DEPLOY`, {
            headers: {
                'argocd.token': token
            }
        });

        const responseBody = await response.json();
        return responseBody;
    }

    async fetchEnvironmentId(token: string, environmentName: string) {

        const response = await this.request.get(`${this.serverUrl}/orchestrator/env`, {
            headers: {
                'argocd.token': token
            }
        })

        const responseBody = await response.json();

        expect(response.ok()).toBeTruthy();
        let id: string = "";
        for (let key in responseBody.result) {
            if (responseBody.result[key].environment_name === environmentName) {
                id = responseBody.result[key].id;
            }
        }
        return id;


    }
    async turnOffGlobalImagePullDigest(token: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/digest-policy`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    'clusterDetails': [],
                    enableDigestForAllClusters: false
                }
            });
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in turning off the global image pull digest' + JSON.stringify(await response.json()));
            throw error;
        }
    }

    async createNewWorkflowOfExternalCi(token: string, appId: number, envId: number, envName: string, deploymentAppType: string = "helm", pushToRegCreds?: { regName: string, repoName: string }) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/app/cd-pipeline`, {
                headers: {
                    'argocd.token': token
                },
                data: payloadForExternalCiWorkflowCreation(deploymentAppType, appId, envId, envName)
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.error('not able to create the workflow external ci , getting error' + JSON.stringify(await response.json()));
            throw error;
        }
    }

    async getObjectDetailsOfExternalCiAndReturnComponentId(token: string, ExternalCiNodeNumber: number, appId: number) {
        let response;
        try {
            response = await this.request.get(this.serverUrl + `/orchestrator/app/external-ci/${appId}`, {
                headers: {
                    'argocd.token': token
                }
            });
            expect(response.ok()).toBeTruthy();
            let parsedResponse = await response.json();
            return parsedResponse.result[ExternalCiNodeNumber].id;
        }
        catch (error) {
            console.log('error in getting the external ci node details' + JSON.stringify(await response.json()));
            throw error;
        }
    }
    async triggerExternalCi(token: string, externalCiNodeId: number, willTriggerBeSuccessfull: boolean = true) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/webhook/ext-ci/${externalCiNodeId}`, {
                headers: {
                    'argocd.token': token,
                    'api-token': token
                },
                data: {
                    dockerImage: "quay.io/devtron/test:e333f7a9-686-7752"
                }
            });
            willTriggerBeSuccessfull ? expect(response.ok()).toBeTruthy() : expect(response.ok()).not.toBeTruthy();
        }
        catch (error) {
            console.log('error in triggering the external ci node' + JSON.stringify(await response.json()));
            throw error;

        }

    }
    async getApiTokenObject(token: string, apiTokenName: string): Promise<undefined | { userId: number, name: string, tokenValue: string }> {
        let response;
        try {
            response = await this.request.get(this.serverUrl + `/orchestrator/api-token`, {
                headers: {
                    'argocd.token': token
                }
            })
            expect(response.ok()).toBeTruthy();
            let parsedResponse = await response.json();
            let Object = parsedResponse.result == null ? undefined : parsedResponse.result.find(key => key.name == apiTokenName);
            let dataToReturn = Object ? { userId: Object.userId, name: Object.name, tokenValue: Object.token } : undefined;
            return dataToReturn;
        }
        catch (error) {
            console.log('getting error in getting the already existing apiTokenDetailObject' + JSON.stringify(await response.json()));
            throw error;
        }
    }
    async createApiTokenWithoutAnyAccess(token: string, apiTokenName: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/api-token`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    name: apiTokenName, description: "", expireAtInMs: 0
                }
            });
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in creating the api token' + JSON.stringify(await response.json()));
            throw error;
        }
    }

    async updateAccessOfApiToken(token: string, isSuperAdmin: boolean, tokenName: string, tokenId: number) {
        let response;
        try {
            response = await this.request.put(this.serverUrl + `/orchestrator/user/v2`, {
                headers: {
                    'argocd.token': token
                },
                data: payloadForSuperAdminOrDevtronAppsAdmin(isSuperAdmin, tokenName, tokenId)
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in updating the api token access' + JSON.stringify(await response.json()));
            throw error;
        }
    }
    /**
     * Synchronizes chart repositories
     * If repoId is provided, syncs only that specific repository, otherwise syncs all repositories
     * @param token The authentication token
     * @param repoId Optional - The ID of a specific chart repository to sync
     * @param isOCIRegistry Whether the repository is an OCI registry (default: false, only used when repoId is provided)
     * @returns True if sync was successful
     */
    async syncChartRepositories(token: string, repoId?: string | number, isOCIRegistry: boolean = false): Promise<boolean> {
        try {
            let response;

            if (repoId !== undefined) {
                // Sync specific chart repository
                response = await this.request.post(`${this.serverUrl}/orchestrator/app-store/chart-provider/sync-chart`, {
                    headers: {
                        'argocd.token': token,
                        'Content-Type': 'application/json'
                    },
                    data: {
                        id: String(repoId),
                        isOCIRegistry: isOCIRegistry
                    }
                });
                console.log(`Sync status for chart repo ID ${repoId}:`, response.status());
            } else {
                // Sync all chart repositories
                response = await this.request.post(`${this.serverUrl}/orchestrator/chart-repo/sync-charts`, {
                    headers: {
                        'argocd.token': token
                    }
                });
                console.log('All chart repositories sync status:', response.status());
            }

            expect(response.ok()).toBeTruthy();
            return true;
        } catch (error) {
            const errorMessage = repoId !== undefined
                ? `Failed to sync chart repository with ID ${repoId}`
                : 'Failed to sync all chart repositories';
            console.error(`${errorMessage}:`, error);
            throw error;
        }
    }
    async HibernateOrUnhibernateDevtronApp(appId: number, envId: number, HibenateTheApp: boolean, token: string) {
        let requestType: string = HibenateTheApp ? "STOP" : "START";
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/app/alpha1/stop-start-app`, {
                headers: {
                    'argocd.token': token
                },
                data: { AppId: appId, EnvironmentId: envId, RequestType: requestType }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('error in hibernating or unhibernating the devtron app' + JSON.stringify(await response.json()));
            throw error;
        }
    }

    async addPublicChartRepository(token: string, repoName: string, repoUrl: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/chart-repo/create`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "id": 0,
                    "name": repoName,
                    "url": repoUrl,
                    "authMode": "ANONYMOUS",
                    "active": true,
                    "allow_insecure_connection": false
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('we were not able to save the required chart repository', JSON.stringify(await response.json()));
            throw error;
        }
    }

    async syncChartsForAChartRepo(token: string, repoId: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/app-store/chart-provider/sync-chart`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "id": String(repoId),
                    "isOCIRegistry": false
                },
                timeout: 45000
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('we were not able to sync the required chart repository', JSON.stringify(await response.json()));
            throw error;
        }
    }


    async getObjectDetailsOfChartRepo(repoName: string, token: string): Promise<{ repoId: string | undefined }> {
        let response;
        response = await this.request.get(this.serverUrl + `/orchestrator/chart-repo/list`, {
            headers: {
                'argocd.token': token
            }
        })
        expect(response.ok()).toBeTruthy();
        let parsedResponse = await response.json();
        let objectReturned = parsedResponse.result.find(key => key.name == repoName);
        if (objectReturned) {
            return { repoId: objectReturned.id };
        }
        return { repoId: undefined };
    }

    async getObjectDetailsOfUser(token: string, emailId: string): Promise<undefined | { id: number }> {
        let response;
        try {
            response = await this.request.get(this.serverUrl + `/orchestrator/user/v2?size=1000&offset=0&sortBy=email_id&sortOrder=ASC`, {
                headers: {
                    'argocd.token': token
                }
            })
            expect(response.ok()).toBeTruthy();
            let parsedResponse = await response.json();

            // ✅ FIX: Use find() instead of filter() and check length properly
            let userFound = parsedResponse.result.users.find(key => key.email_id === emailId);
            console.log('user found value we are getting is', userFound);

            if (!userFound) {
                console.log('User not found, returning undefined');
                return undefined;
            }

            console.log('User found, returning user object with ID:', userFound.id);
            return { id: userFound.id };

        } catch (error) {
            console.log('we were not able to get the user object', JSON.stringify(await response.json()));
            throw error;
        }
    }

    async addUser(userEmail: string, token: string) {
        let response;
        try {
            response = await this.request.post(this.serverUrl + `/orchestrator/user/v2`, {
                headers: {
                    'argocd.token': token
                },
                data: payloadForSuperAdminOrDevtronAppsAdmin(true, userEmail, 0, false)
            })

            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('we were not able to add the user', JSON.stringify(await response.json()));
            throw error;
        }
    }

    async deleteAChartRep(token: string, repoName: string) {
        let response;
        try {
            let repoObject = await this.getObjectDetailsOfChartRepo(repoName, token);
            response = await this.request.delete(this.serverUrl + `/orchestrator/chart-repo`, {
                headers: {
                    'argocd.token': token
                },
                data: {
                    "id": repoObject.repoId,
                    "name": repoName,
                    "url": "https://devtron-labs.github.io/helm-pilot/",
                    "authMode": "ANONYMOUS",
                    "active": true,
                    "allow_insecure_connection": false
                }
            })
            expect(response.ok()).toBeTruthy();
        }
        catch (error) {
            console.log('we were not able to delete the chart repository', JSON.stringify(await response.json()));
            throw error;
        }
    }


    async getResourceRecommendationsForDeployment(
        token: string,
        deploymentName: string,
        namespace: string = "automation",
        clusterId: number = 1
    ) {
        try {
            const response = await this.request.post(
                `${this.serverUrl}/orchestrator/k8s/resource/recommendation/list`,
                {
                    headers: {
                        'argocd.token': token,
                        'accept': 'application/json',
                        'content-type': 'application/json',
                    },
                    data: JSON.stringify({
                        clusterId: clusterId,
                        k8sRequest: {
                            resourceIdentifier: {
                                groupVersionKind: {
                                    Group: "apps",
                                    Version: "v1",
                                    Kind: "Deployment",
                                },
                            },
                        },
                    }),
                }
            );

            if (!response.ok()) {
                throw new Error(`❌ Failed to fetch: ${response.status()}`);
            }

            const parsed = await response.json();
            const deployments = parsed?.result?.data || [];

            const target = deployments.find(
                (d: any) => d.name === deploymentName && d.namespace === namespace
            );

            // ✅ Log just this one object
            console.log(`=== TARGET DEPLOYMENT (${deploymentName}) ===`);
            console.log(JSON.stringify(target, null, 2));
            console.log(`=== END ===`);



            if (!target) {
                console.log(`❌ No match for ${deploymentName} in ${namespace}`);
                return null;
            }

            const format = (val?: string) => !val || val === 'none' ? 'No recommendation' : val;

            console.log(`✅ Found recommendations for deployment: ${target.name}`);
            console.log(`📊 Container: ${target.containerName}`);

            console.log(`💾 Memory Request: ${format(target.memoryRequest?.current?.value)} → ${format(target.memoryRequest?.recommended?.value)}`);
            console.log(`💾 Memory Limit: ${format(target.memoryLimit?.current?.value)} → ${format(target.memoryLimit?.recommended?.value)}`);
            console.log(`🔧 CPU Request: ${format(target.cpuRequest?.current?.value)} → ${format(target.cpuRequest?.recommended?.value)}`);
            console.log(`🔧 CPU Limit: ${format(target.cpuLimit?.current?.value)} → ${format(target.cpuLimit?.recommended?.value)}`);

            if (typeof target.memoryRequest?.delta === 'number') {
                console.log(`💰 Memory Request optimization: ${target.memoryRequest.delta}%`);
            }

            if (typeof target.memoryLimit?.delta === 'number') {
                console.log(`💰 Memory Limit optimization: ${target.memoryLimit.delta}%`);
            }

            return target;

        } catch (error) {
            console.error(`❌ Error fetching recommendations for ${deploymentName}:`, error);
            throw error;
        }
    }

        /**
 * Add a new cluster to Devtron
 * @param token - Authentication token 
 * @param clusterName - Name for the cluster (default: "testprod")
 * @param isProd - Whether this is a production cluster (default: false)
 * @returns Promise<void>
 */
async addCluster(
    token: string, 
    clusterName: string = "testprod", 
    isProd: boolean = false
): Promise<void> {
    let response;
    try {
        response = await this.request.post(`${this.serverUrl}/orchestrator/cluster`, {
            headers: {
                'accept': '*/*',
                'content-type': 'application/json',
                'argocd.token': token,
                'cache-control': 'no-cache'
            },
            data: {
                "id": null,
                "insecureSkipTlsVerify": true,
                "cluster_name": clusterName,
                "config": {
                    "bearer_token": process.env.CLUSTER_TOKEN
                },
                "isProd": isProd,
                "active": true,
                "remoteConnectionConfig": {
                    "connectionMethod": "DIRECT",
                    "proxyConfig": null,
                    "sshConfig": null
                },
                "prometheus_url": "",
                "prometheusAuth": {
                    "userName": "",
                    "password": "",
                    "tlsClientKey": "",
                    "tlsClientCert": "",
                    "isAnonymous": true
                },
                "server_url": process.env.SERVER_URL_TO_ADD_CLUSTER,
                "category": {
                    "id": 0
                }
            }
        });

        expect(response.ok()).toBeTruthy();
        console.log(`✅ Successfully added cluster: ${clusterName}`);
        
    } catch (error) {
        console.error(`❌ Error adding cluster ${clusterName}:`, JSON.stringify(await response?.json()));
        throw error;
    }
}






}
