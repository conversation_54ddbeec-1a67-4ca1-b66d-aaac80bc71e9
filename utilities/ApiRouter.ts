import { Page } from '@playwright/test';

export type MockRoute = {
  url: string;
  method: string;
  body: any;          
  status?: number;
  contentType?: string;
};

export async function mockApis(page: Page, routes: MockRoute[]) {
  for (const route of routes) {
    await page.route(route.url, async (routeHandler) => {
      if (route.method && routeHandler.request().method() !== route.method) {
        return routeHandler.continue();
      }
      const responseBody =
        typeof route.body === 'string' ? route.body : JSON.stringify(route.body,null,2);
      await routeHandler.fulfill({
        status: route.status ?? 200,
        contentType: route.contentType ?? 'application/json',
        body: responseBody,
      });
    });
  }
}
