import { BaseTest } from "../BaseTest";

export function payloadForExternalCiWorkflowCreation( deploymentAppType:string , appId: number, envId: number, envName: string, pushToRegCreds?: { regName: string, repoName: string }) {
    let triggerType = envName == "env9" || envName == "playwright-vir" ? "MANUAL" : "AUTOMATIC";
    deploymentAppType = envName == "env9" || envName == "playwright-vir" ? "manifest_download" : deploymentAppType;
    return {
        "appId": appId,
        "pipelines": [
            {
                "name": BaseTest.generateRandomStringWithCharsOnly(8),
                "appWorkflowId": 0,
                "ciPipelineId": 0,
                "environmentId": envId,
                "namespace": envName,
                "id": 0,
                "strategies": [
                    {
                        "deploymentTemplate": "ROLLING",
                        "defaultConfig": {
                            "deployment": {
                                "strategy": {
                                    "rolling": {
                                        "maxSurge": "25%",
                                        "maxUnavailable": 1
                                    }
                                }
                            }
                        },
                        "config": {
                            "deployment": {
                                "strategy": {
                                    "rolling": {
                                        "maxSurge": "25%",
                                        "maxUnavailable": 1
                                    }
                                }
                            }
                        },
                        "isCollapsed": true,
                        "default": true,
                        "jsonStr": "{\n    \"deployment\": {\n        \"strategy\": {\n            \"rolling\": {\n                \"maxSurge\": \"25%\",\n                \"maxUnavailable\": 1\n            }\n        }\n    }\n}",
                        "yamlStr": "deployment:\n  strategy:\n    rolling:\n      maxSurge: 25%\n      maxUnavailable: 1\n"
                    }
                ],
                "parentPipelineType": "WEBHOOK",
                "parentPipelineId": 0,
                "isClusterCdActive": false,
                "deploymentAppType": deploymentAppType,
                "deploymentAppName": "",
                "releaseMode": "create",
                "deploymentAppCreated": false,
                "triggerType": triggerType,
                "environmentName": envName,
                "preStageConfigMapSecretNames": {
                    "configMaps": [],
                    "secrets": []
                },
                "postStageConfigMapSecretNames": {
                    "configMaps": [],
                    "secrets": []
                },
                "containerRegistryName": "",
                "repoName": "",
                "manifestStorageType": "",
                "runPreStageInEnv": false,
                "runPostStageInEnv": false,
                "preDeployStage": {},
                "postDeployStage": {},
                "customTag": {
                    "tagPattern": "",
                    "counterX": 0
                },
                "customTagStage": "PRE_CD",
                "isDigestEnforcedForPipeline": false,
                "isDigestEnforcedForEnv": false,
                "addType": null
            }
        ]
    }
}