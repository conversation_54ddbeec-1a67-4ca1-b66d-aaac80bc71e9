import { BaseTest } from "../BaseTest";

export function obejctOfCreateAppApi(data: { appName: string, regName: string, repoName: string, repoUrl: string, projName: string, envName: string[], chartRefId: string }) {
    let result = {
        "metadata": {
            "appName": data.appName,
            "projectName": "devtron-demo",
            "labels": []
        },
        "gitMaterials": [
            {
                "gitProviderUrl": "github.com",
                "gitRepoUrl": data.repoUrl,
                "checkoutPath": "./",
                "fetchSubmodules": false
            }
        ],
        "dockerConfig": {
            "dockerRegistry": data.regName,
            "DockerRepository": data.repoName,
            "dockerBuildConfig": {
                "gitCheckoutPath": "./",
                "dockerfileRelativePath": "Dockerfile",
                "targetPlatform": ""
            }
        },
        "globalDeploymentTemplate": {
            "chartRefId": data.chartRefId,
            "template": {
                "ContainerPort": [
                    {
                        "envoyPort": 8799,
                        "idleTimeout": "1800s",
                        "name": "app",
                        "port": 8080,
                        "protocol": "TCP",
                        "servicePort": 80,
                        "supportStreaming": false,
                        "useHTTP2": false
                    }
                ],
                "EnvVariables": [],
                "GracePeriod": 30,
                "LivenessProbe": {
                    "Path": "",
                    "command": [],
                    "failureThreshold": 3,
                    "grpc": {},
                    "httpHeaders": [],
                    "initialDelaySeconds": 20,
                    "periodSeconds": 10,
                    "port": 8080,
                    "scheme": "",
                    "successThreshold": 1,
                    "tcp": false,
                    "timeoutSeconds": 5
                },
                "MaxSurge": 1,
                "MaxUnavailable": 0,
                "MinReadySeconds": 60,
                "ReadinessProbe": {
                    "Path": "",
                    "command": [],
                    "failureThreshold": 3,
                    "grpc": {},
                    "httpHeaders": [],
                    "initialDelaySeconds": 20,
                    "periodSeconds": 10,
                    "port": 8080,
                    "scheme": "",
                    "successThreshold": 1,
                    "tcp": false,
                    "timeoutSeconds": 5
                },
                "Spec": {
                    "Affinity": {
                        "Key": "",
                        "Values": ""
                    }
                },
                "StartupProbe": {
                    "Path": "",
                    "command": [],
                    "failureThreshold": 3,
                    "grpc": {},
                    "httpHeaders": [],
                    "initialDelaySeconds": 20,
                    "periodSeconds": 10,
                    "port": 8080,
                    "successThreshold": 1,
                    "tcp": false,
                    "timeoutSeconds": 5
                },
                "affinity": {
                    "enabled": false,
                    "values": {}
                },
                "ambassadorMapping": {
                    "ambassadorId": "",
                    "cors": {},
                    "enabled": false,
                    "hostname": "devtron.example.com",
                    "labels": {},
                    "prefix": "/",
                    "retryPolicy": {},
                    "rewrite": "",
                    "tls": {
                        "context": "",
                        "create": false,
                        "hosts": [],
                        "secretName": ""
                    }
                },
                "args": {
                    "enabled": false,
                    "value": [
                        "/bin/sh",
                        "-c",
                        "touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600"
                    ]
                },
                "autoscaling": {
                    "MaxReplicas": 2,
                    "MinReplicas": 1,
                    "TargetCPUUtilizationPercentage": 90,
                    "TargetMemoryUtilizationPercentage": 80,
                    "annotations": {},
                    "behavior": {},
                    "containerResource": {
                        "TargetCPUUtilizationPercentage": 90,
                        "TargetMemoryUtilizationPercentage": 80,
                        "enabled": false
                    },
                    "enabled": false,
                    "extraMetrics": [],
                    "labels": {}
                },
                "command": {
                    "enabled": false,
                    "value": [],
                    "workingDir": {}
                },
                "containerSecurityContext": {},
                "containerSpec": {
                    "lifecycle": {
                        "enabled": false,
                        "postStart": {
                            "httpGet": {
                                "host": "example.com",
                                "path": "/example",
                                "port": 90
                            }
                        },
                        "preStop": {
                            "exec": {
                                "command": [
                                    "sleep",
                                    "10"
                                ]
                            }
                        }
                    }
                },
                "containers": [],
                "dbMigrationConfig": {
                    "enabled": false
                },
                "deploymentAnnotations": {},
                "deploymentLabels": {},
                "envoyproxy": {
                    "configMapName": "",
                    "image": "quay.io/devtron/envoy:v1.16.0",
                    "lifecycle": {},
                    "resources": {
                        "limits": {
                            "cpu": "50m",
                            "memory": "50Mi"
                        },
                        "requests": {
                            "cpu": "50m",
                            "memory": "50Mi"
                        }
                    }
                },
                "flaggerCanary": {
                    "addOtherGateways": [],
                    "addOtherHosts": [],
                    "analysis": {
                        "interval": "15s",
                        "maxWeight": 50,
                        "stepWeight": 5,
                        "threshold": 5
                    },
                    "annotations": {},
                    "appProtocol": "http",
                    "corsPolicy": null,
                    "createIstioGateway": {
                        "annotations": {},
                        "enabled": false,
                        "host": null,
                        "labels": {},
                        "tls": {
                            "enabled": false,
                            "secretName": null
                        }
                    },
                    "enabled": false,
                    "gatewayRefs": null,
                    "headers": null,
                    "labels": {},
                    "loadtest": {
                        "enabled": true,
                        "url": "http://flagger-loadtester.istio-system/"
                    },
                    "match": [
                        {
                            "uri": {
                                "prefix": "/"
                            }
                        }
                    ],
                    "portDiscovery": true,
                    "retries": null,
                    "rewriteUri": "/",
                    "serviceport": 8080,
                    "targetPort": 8080,
                    "thresholds": {
                        "latency": 500,
                        "successRate": 90
                    },
                    "timeout": null
                },
                "hostAliases": [],
                "image": {
                    "pullPolicy": "IfNotPresent"
                },
                "imagePullSecrets": [],
                "ingress": {
                    "annotations": {},
                    "className": "",
                    "enabled": false,
                    "hosts": [
                        {
                            "host": "chart-example1.local",
                            "pathType": "ImplementationSpecific",
                            "paths": [
                                "/example1"
                            ]
                        },
                        {
                            "host": "chart-example2.local",
                            "pathType": "ImplementationSpecific",
                            "paths": [
                                "/example2",
                                "/example2/healthz"
                            ]
                        }
                    ],
                    "labels": {},
                    "tls": []
                },
                "ingressInternal": {
                    "annotations": {},
                    "className": "",
                    "enabled": false,
                    "hosts": [
                        {
                            "host": "chart-example1.internal",
                            "pathType": "ImplementationSpecific",
                            "paths": [
                                "/example1"
                            ]
                        },
                        {
                            "host": "chart-example2.internal",
                            "pathType": "ImplementationSpecific",
                            "paths": [
                                "/example2",
                                "/example2/healthz"
                            ]
                        }
                    ],
                    "tls": []
                },
                "initContainers": [],
                "istio": {
                    "authorizationPolicy": {
                        "action": null,
                        "annotations": {},
                        "enabled": false,
                        "labels": {},
                        "provider": {},
                        "rules": []
                    },
                    "destinationRule": {
                        "annotations": {},
                        "enabled": false,
                        "labels": {},
                        "subsets": [],
                        "trafficPolicy": {}
                    },
                    "enable": false,
                    "gateway": {
                        "annotations": {},
                        "enabled": false,
                        "host": "example.com",
                        "labels": {},
                        "tls": {
                            "enabled": false,
                            "secretName": "example-secret"
                        }
                    },
                    "peerAuthentication": {
                        "annotations": {},
                        "enabled": false,
                        "labels": {},
                        "mtls": {
                            "mode": ""
                        },
                        "portLevelMtls": {},
                        "selector": {
                            "enabled": false
                        }
                    },
                    "requestAuthentication": {
                        "annotations": {},
                        "enabled": false,
                        "jwtRules": [],
                        "labels": {},
                        "selector": {
                            "enabled": false
                        }
                    },
                    "virtualService": {
                        "annotations": {},
                        "enabled": false,
                        "gateways": [],
                        "hosts": [],
                        "http": [],
                        "labels": {}
                    }
                },
                "kedaAutoscaling": {
                    "advanced": {},
                    "authenticationRef": {},
                    "enabled": false,
                    "envSourceContainerName": "",
                    "maxReplicaCount": 2,
                    "minReplicaCount": 1,
                    "triggerAuthentication": {
                        "enabled": false,
                        "name": "",
                        "spec": {}
                    },
                    "triggers": []
                },
                "networkPolicy": {
                    "annotations": {},
                    "egress": [],
                    "enabled": false,
                    "ingress": [],
                    "labels": {},
                    "podSelector": {
                        "matchExpressions": [],
                        "matchLabels": {}
                    },
                    "policyTypes": []
                },
                "pauseForSecondsBeforeSwitchActive": 30,
                "podAnnotations": {},
                "podDisruptionBudget": {},
                "podLabels": {},
                "podSecurityContext": {},
                "prometheus": {
                    "release": "monitoring"
                },
                "rawYaml": [],
                "replicaCount": 1,
                "resources": {
                    "limits": {
                        "cpu": "0.05",
                        "memory": "50Mi"
                    },
                    "requests": {
                        "cpu": "0.01",
                        "memory": "10Mi"
                    }
                },
                "restartPolicy": "Always",
                "secret": {
                    "data": {},
                    "enabled": false
                },
                "server": {
                    "deployment": {
                        "image": "",
                        "image_tag": "1-95af053"
                    }
                },
                "service": {
                    "annotations": {},
                    "loadBalancerSourceRanges": [],
                    "type": "ClusterIP"
                },
                "serviceAccount": {
                    "annotations": {},
                    "create": false,
                    "name": ""
                },
                "servicemonitor": {
                    "additionalLabels": {}
                },
                "tolerations": [],
                "topologySpreadConstraints": [],
                "verticalPodScaling": {
                    "enabled": false
                },
                "volumeMounts": [],
                "volumes": [],
                "waitForSecondsBeforeScalingDown": 30,
                "winterSoldier": {
                    "action": "sleep",
                    "annotation": {},
                    "apiVersion": "pincher.devtron.ai/v1alpha1",
                    "enabled": false,
                    "fieldSelector": [
                        "AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())"
                    ],
                    "labels": {},
                    "targetReplicas": [],
                    "timeRangesWithZone": {
                        "timeRanges": [],
                        "timeZone": "Asia/Kolkata"
                    },
                    "type": "Deployment"
                }
            },
            "showAppMetrics": false,
            "isOverride": true,
            "isBasicViewLocked": false,
            "currentViewEditor": "UNDEFINED"
        },
        "workflows": [
            {
                "name": "temp-push-pipeline",
                "ciPipeline": {
                    "name": "temp-build",
                    "isManual": true,
                    "ciPipelineMaterialsConfig": [
                        {
                            "type": "SOURCE_TYPE_BRANCH_FIXED",
                            "value": "main",
                            "checkoutPath": "./",
                            "gitMaterialId": 16
                        }
                    ],
                    "dockerBuildArgs": {},
                    "beforeDockerBuildScripts": null,
                    "afterDockerBuildScripts": null,
                    "vulnerabilitiesScanEnabled": false,
                    "preBuildStage": {
                        "id": null,
                        "type": "PRE_CI",
                        "steps": [
                            {
                                "id": null,
                                "name": "docker login",
                                "description": "this task validates if the tag is created from master branch or hotfix branch",
                                "index": 1,
                                "stepType": "INLINE",
                                "outputDirectoryPath": null,
                                "inlineStepDetail": {
                                    "scriptType": "SHELL",
                                    "script": `docker login -u deep10 -p ${process.env.dockerLoginPass!}`,
                                    "storeScriptAt": "",
                                    "mountDirectoryFromHost": false,
                                    "commandArgsMap": [
                                        {
                                            "command": "",
                                            "args": null
                                        }
                                    ],
                                    "inputVariables": null
                                },
                                "pluginRefStepDetail": null,
                                "triggerIfParentStageFail": false
                            }
                        ]
                    },
                    "isExternal": false,
                    "pipelineType": "CI_BUILD"
                },
                "cdPipelines": cdPipelinesDataObjectForCreateAppApi(data.envName)
            }
        ]
    }
    return result;
}
function cdPipelinesDataObjectForCreateAppApi(envNames: string[]) {
    let resultArray: any = [];
    envNames.forEach((key) => {
        let pipelineName = BaseTest.generateRandomStringWithCharsOnly(5);
        let result = {
            "name": pipelineName,
            "environmentName": key,
            "triggerType": "AUTOMATIC",
            "deploymentAppType": "helm",
            "deploymentType": "ROLLING",
            "deploymentStrategies": [
                {
                    "deploymentType": "ROLLING",
                    "config": {
                        "deployment": {
                            "strategy": {
                                "rolling": {
                                    "maxSurge": "25%",
                                    "maxUnavailable": 0
                                }
                            }
                        }
                    },
                    "isDefault": true
                }
            ],
            "preStage": null,
            "postStage": null,
            "preStageConfigMapSecretNames": {
                "configMaps": [],
                "secrets": []
            },
            "postStageConfigMapSecretNames": {
                "configMaps": [],
                "secrets": []
            },
            "runPreStageInEnv": false,
            "runPostStageInEnv": false,
            "isClusterCdActive": false,
            "userApprovalConfig": {
                "type": "ANY",
                "requiredCount": 2
            },
            "preDeployStage": {
                "id": null,
                "name": "Pre-Deployment",
                "type": "PRE_CD",
                "steps": null,
                "triggerType": "MANUAL"
            }
        }
        resultArray.push(result);
    })
    return resultArray;

}