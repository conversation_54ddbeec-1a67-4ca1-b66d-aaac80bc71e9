import path from "path"

export function payloadForSuperAdminOrDevtronAppsAdmin(superAdmin: boolean, tokenName: string, tokenId: number, isApiToken: boolean = true) {
    let roleFilters = superAdmin ? [] : [
        {
            "entity": "apps",
            "entityName": "",
            "environment": "",
            "team": "devtron-demo",
            "accessType": "devtron-app",
            "approver": false,
            "action": "admin,configApprover",
            "status": "active"
        }
    ]
    let payload = {
        "id": tokenId,
        "superAdmin": superAdmin,
        "email_id": isApiToken ? `API-TOKEN:${tokenName}` : tokenName,
        "userRoleGroups": [],
        "roleFilters": roleFilters,
        "userGroups": [],
        "userStatus": "active"
    }
    return payload

}