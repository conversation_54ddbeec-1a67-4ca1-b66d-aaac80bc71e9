export function updateDeploymentTemplatePayload(id: string, chartRefId: number, chartVersion: string, appId: number) {
    let result = {
        "id": id,
        "refChartTemplate": `deployment-chart_4-20-0`,
        "refChartTemplateVersion": "4.20.0",
        "chartRefId": chartRefId,
        "readme": "\n# Deployment Chart - v4.20.0\n\n## 1. Yaml File -\n\n### Container Ports\n\nThis defines ports on which application services will be exposed to other services\n\n```yaml\nContainerPort:\n  - envoyPort: 8799\n    idleTimeout:\n    name: app\n    port: 8080\n    servicePort: 80\n    nodePort: 32056\n    supportStreaming: true\n    useHTTP2: true\n    protocol: TCP\n```\n\n| Key | Description |\n| :--- | :--- |\n| `envoyPort` | envoy port for the container. |\n| `idleTimeout` | the duration of time that a connection is idle before the connection is terminated. |\n| `name` | name of the port. |\n| `port` | port for the container. |\n| `servicePort` | port of the corresponding kubernetes service. |\n| `nodePort` | nodeport of the corresponding kubernetes service. |\n| `supportStreaming` | Used for high performance protocols like grpc where timeout needs to be disabled. |\n| `useHTTP2` | Envoy container can accept HTTP2 requests. |\n| `protocol` | Protocol for port. Must be UDP, TCP, or SCTP. Defaults to \"TCP\"|\n\n### EnvVariables\n```yaml\nEnvVariables: []\n```\nTo set environment variables for the containers that run in the Pod.\n### EnvVariablesFromSecretKeys\n```yaml\nEnvVariablesFromSecretKeys: \n  - name: ENV_NAME\n    secretName: SECRET_NAME\n    keyName: SECRET_KEY\n\n```\n It is use to get the name of Environment Variable name, Secret name and the Key name from which we are using the value in that corresponding Environment Variable.\n\n ### EnvVariablesFromConfigMapKeys\n```yaml\nEnvVariablesFromConfigMapKeys: \n  - name: ENV_NAME\n    configMapName: CONFIG_MAP_NAME\n    keyName: CONFIG_MAP_KEY\n\n```\n It is use to get the name of Environment Variable name, Config Map name and the Key name from which we are using the value in that corresponding Environment Variable.\n \n### Liveness Probe\n\nIf this check fails, kubernetes restarts the pod. This should return error code in case of non-recoverable error.\n\n```yaml\nLivenessProbe:\n  Path: \"\"\n  port: 8080\n  initialDelaySeconds: 20\n  periodSeconds: 10\n  successThreshold: 1\n  timeoutSeconds: 5\n  failureThreshold: 3\n  httpHeaders:\n    - name: Custom-Header\n      value: abc\n  scheme: \"\"\n  tcp: true\n  grpc:\n    port: 8080\n    service: \"\"\n```\n\n| Key | Description |\n| :--- | :--- |\n| `Path` | It define the path where the liveness needs to be checked. |\n| `initialDelaySeconds` | It defines the time to wait before a given container is checked for liveliness. |\n| `periodSeconds` | It defines the time to check a given container for liveness. |\n| `successThreshold` | It defines the number of successes required before a given container is said to fulfil the liveness probe. |\n| `timeoutSeconds` | It defines the time for checking timeout. |\n| `failureThreshold` | It defines the maximum number of failures that are acceptable before a given container is not considered as live. |\n| `httpHeaders` | Custom headers to set in the request. HTTP allows repeated headers,You can override the default headers by defining .httpHeaders for the probe. |\n| `scheme` | Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.\n| `tcp` | The kubelet will attempt to open a socket to your container on the specified port. If it can establish a connection, the container is considered healthy. |\n| `grpc` | GRPC specifies an action involving a GRPC port. Port is a required field if using gRPC service for health probes. Number must be in the range 1 to 65535. Service (optional) is the name of the service to place in the gRPC HealthCheckRequest. |\n\n\n\n### MaxUnavailable\n\n```yaml\n  MaxUnavailable: 0\n```\nThe maximum number of pods that can be unavailable during the update process. The value of \"MaxUnavailable: \" can be an absolute number or percentage of the replicas count. The default value of \"MaxUnavailable: \" is 25%.\n\n### MaxSurge\n\n```yaml\nMaxSurge: 1\n```\nThe maximum number of pods that can be created over the desired number of pods. For \"MaxSurge: \" also, the value can be an absolute number or percentage of the replicas count.\nThe default value of \"MaxSurge: \" is 25%.\n\n### Min Ready Seconds\n\n```yaml\nMinReadySeconds: 60\n```\nThis specifies the minimum number of seconds for which a newly created Pod should be ready without any of its containers crashing, for it to be considered available. This defaults to 0 (the Pod will be considered available as soon as it is ready).\n\n### Readiness Probe\n\nIf this check fails, kubernetes stops sending traffic to the application. This should return error code in case of errors which can be recovered from if traffic is stopped.\n\n```yaml\nReadinessProbe:\n  Path: \"\"\n  port: 8080\n  initialDelaySeconds: 20\n  periodSeconds: 10\n  successThreshold: 1\n  timeoutSeconds: 5\n  failureThreshold: 3\n  httpHeaders:\n    - name: Custom-Header\n      value: abc\n  scheme: \"\"\n  tcp: true\n  grpc:\n    port: 8080\n    service: \"\"\n```\n\n| Key | Description |\n| :--- | :--- |\n| `Path` | It define the path where the readiness needs to be checked. |\n| `initialDelaySeconds` | It defines the time to wait before a given container is checked for readiness. |\n| `periodSeconds` | It defines the time to check a given container for readiness. |\n| `successThreshold` | It defines the number of successes required before a given container is said to fulfill the readiness probe. |\n| `timeoutSeconds` | It defines the time for checking timeout. |\n| `failureThreshold` | It defines the maximum number of failures that are acceptable before a given container is not considered as ready. |\n| `httpHeaders` | Custom headers to set in the request. HTTP allows repeated headers,You can override the default headers by defining .httpHeaders for the probe. |\n| `scheme` | Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.\n| `tcp` | The kubelet will attempt to open a socket to your container on the specified port. If it can establish a connection, the container is considered healthy. |\n| `grpc` | GRPC specifies an action involving a GRPC port. Port is a required field if using gRPC service for health probes. Number must be in the range 1 to 65535. Service (optional) is the name of the service to place in the gRPC HealthCheckRequest. |\n\n\n### Pod Disruption Budget\n\nYou can create `PodDisruptionBudget` for each application. A PDB limits the number of pods of a replicated application that are down simultaneously from voluntary disruptions. For example, an application would like to ensure the number of replicas running is never brought below the certain number.\n\n```yaml\npodDisruptionBudget: \n     minAvailable: 1\n```\n\nor\n\n```yaml\npodDisruptionBudget: \n     maxUnavailable: 50%\n```\n\nYou can specify either `maxUnavailable` or `minAvailable` in a PodDisruptionBudget and it can be expressed as integers or as a percentage\n\n| Key | Description |\n| :--- | :--- |\n| `minAvailable` | Evictions are allowed as long as they leave behind 1 or more healthy pods of the total number of desired replicas. |\n| `maxUnavailable` | Evictions are allowed as long as at most 1 unhealthy replica among the total number of desired replicas. |\n\n### Ambassador Mappings\n\nYou can create ambassador mappings to access your applications from outside the cluster. At its core a Mapping resource maps a resource to a service.\n\n```yaml\nambassadorMapping:\n  ambassadorId: \"prod-emissary\"\n  cors: {}\n  enabled: true\n  hostname: devtron.example.com\n  labels: {}\n  prefix: /\n  retryPolicy: {}\n  rewrite: \"\"\n  tls:\n    context: \"devtron-tls-context\"\n    create: false\n    hosts: []\n    secretName: \"\"\n```\n\n| Key | Description |\n| :--- | :--- |\n| `enabled` | Set true to enable ambassador mapping else set false.|\n| `ambassadorId` | used to specify id for specific ambassador mappings controller. |\n| `cors` | used to specify cors policy to access host for this mapping. |\n| `weight` | used to specify weight for canary ambassador mappings. |\n| `hostname` | used to specify hostname for ambassador mapping. |\n| `prefix` | used to specify path for ambassador mapping. |\n| `labels` | used to provide custom labels for ambassador mapping. |\n| `retryPolicy` | used to specify retry policy for ambassador mapping. |\n| `corsPolicy` | Provide cors headers on flagger resource. |\n| `rewrite` | used to specify whether to redirect the path of this mapping and where. |\n| `tls` | used to create or define ambassador TLSContext resource. |\n| `extraSpec` | used to provide extra spec values which not present in deployment template for ambassador resource. |\n\n### Autoscaling\n\nThis is connected to HPA and controls scaling up and down in response to request load.\n\n```yaml\nautoscaling:\n  enabled: false\n  MinReplicas: 1\n  MaxReplicas: 2\n  TargetCPUUtilizationPercentage: 90\n  TargetMemoryUtilizationPercentage: 80\n  containerResource:\n    enabled: true\n    TargetCPUUtilizationPercentage: 90\n    TargetMemoryUtilizationPercentage: 80\n\n  extraMetrics: []\n```\n\n| Key | Description |\n| :--- | :--- |\n| `enabled` | Set true to enable autoscaling else set false.|\n| `MinReplicas` | Minimum number of replicas allowed for scaling. |\n| `MaxReplicas` | Maximum number of replicas allowed for scaling. |\n| `TargetCPUUtilizationPercentage` | The target CPU utilization that is expected for a container. |\n| `TargetMemoryUtilizationPercentage` | The target memory utilization that is expected for a container. |\n| `extraMetrics` | Used to give external metrics for autoscaling. |\n| `containerResource` | Used to scale resource as per container resource. |\n\n### Flagger\n\nYou can use flagger for canary releases with deployment objects. It supports flexible traffic routing with istio service mesh as well.\n\n```yaml\nflaggerCanary:\n  addOtherGateways: []\n  addOtherHosts: []\n  analysis:\n    interval: 15s\n    maxWeight: 50\n    stepWeight: 5\n    threshold: 5\n  annotations: {}\n  appProtocol: http\n  corsPolicy:\n    allowCredentials: false\n    allowHeaders:\n      - x-some-header\n    allowMethods:\n      - GET\n    allowOrigin:\n      - example.com\n    maxAge: 24h\n  createIstioGateway:\n    annotations: {}\n    enabled: false\n    host: example.com\n    labels: {}\n    tls:\n      enabled: false\n      secretName: example-tls-secret\n  enabled: false\n  gatewayRefs: null\n  headers:\n    request:\n      add:\n        x-some-header: value\n  labels: {}\n  loadtest:\n    enabled: true\n    url: http://flagger-loadtester.istio-system/\n  match:\n    - uri:\n        prefix: /\n  port: 8080\n  portDiscovery: true\n  retries: null\n  rewriteUri: /\n  targetPort: 8080\n  thresholds:\n    latency: 500\n    successRate: 90\n  timeout: null\n```\n\n| Key | Description |\n| :--- | :--- |\n| `enabled` | Set true to enable canary releases using flagger else set false.|\n| `addOtherGateways` | To provide multiple istio gateways for flagger. |\n| `addOtherHosts` | Add multiple hosts for istio service mesh with flagger. |\n| `analysis` | Define how the canary release should progresss and at what interval. |\n| `annotations` | Annotation to add on flagger resource. |\n| `labels` | Labels to add on flagger resource. |\n| `appProtocol` | Protocol to use for canary. |\n| `corsPolicy` | Provide cors headers on flagger resource. |\n| `createIstioGateway` | Set to true if you want to create istio gateway as well with flagger. |\n| `headers` | Add headers if any. |\n| `loadtest` | Enable load testing for your canary release. |\n\n\n\n### Fullname Override\n\n```yaml\nfullnameOverride: app-name\n```\n`fullnameOverride` replaces the release fullname created by default by devtron, which is used to construct Kubernetes object names. By default, devtron uses {app-name}-{environment-name} as release fullname.\n\n### Image\n\n```yaml\nimage:\n  pullPolicy: IfNotPresent\n```\n\nImage is used to access images in kubernetes, pullpolicy is used to define the instances calling the image, here the image is pulled when the image is not present,it can also be set as \"Always\".\n\n### imagePullSecrets\n\n`imagePullSecrets` contains the docker credentials that are used for accessing a registry.\n\n```yaml\nimagePullSecrets:\n  - regcred\n```\nregcred is the secret that contains the docker credentials that are used for accessing a registry. Devtron will not create this secret automatically, you'll have to create this secret using dt-secrets helm chart in the App store or create one using kubectl. You can follow this documentation Pull an Image from a Private Registry [https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/](https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/) .\n\n### Ingress\n\nThis allows public access to the url, please ensure you are using right nginx annotation for nginx class, its default value is nginx\n\n```yaml\ningress:\n  enabled: false\n  # For K8s 1.19 and above use ingressClassName instead of annotation kubernetes.io/ingress.class:\n  className: nginx\n  annotations: {}\n  hosts:\n      - host: example1.com\n        paths:\n            - /example\n      - host: example2.com\n        paths:\n            - /example2\n            - /example2/healthz\n  tls: []\n```\nLegacy deployment-template ingress format\n\n```yaml\ningress:\n  enabled: false\n  # For K8s 1.19 and above use ingressClassName instead of annotation kubernetes.io/ingress.class:\n  ingressClassName: nginx-internal\n  annotations: {}\n  path: \"\"\n  host: \"\"\n  tls: []\n```\n\n| Key | Description |\n| :--- | :--- |\n| `enabled` | Enable or disable ingress |\n| `annotations` | To configure some options depending on the Ingress controller |\n| `path` | Path name |\n| `host` | Host name |\n| `tls` | It contains security details |\n\n### Ingress Internal\n\nThis allows private access to the url, please ensure you are using right nginx annotation for nginx class, its default value is nginx\n\n```yaml\ningressInternal:\n  enabled: false\n  # For K8s 1.19 and above use ingressClassName instead of annotation kubernetes.io/ingress.class:\n  ingressClassName: nginx-internal\n  annotations: {}\n  hosts:\n      - host: example1.com\n        paths:\n            - /example\n      - host: example2.com\n        paths:\n            - /example2\n            - /example2/healthz\n  tls: []\n```\n\n| Key | Description |\n| :--- | :--- |\n| `enabled` | Enable or disable ingress |\n| `annotations` | To configure some options depending on the Ingress controller |\n| `path` | Path name |\n| `host` | Host name |\n| `tls` | It contains security details |\n\n### additionalBackends\n\nThis defines additional backend path in the ingress .\n\n```yaml\n    hosts:\n    - host: chart-example2.local\n      pathType: \"ImplementationSpecific\"\n      paths:\n        - /example2\n        - /example2/healthz\n      additionalBackends: \n        - path: /example1\n          pathType: \"ImplementationSpecific\"\n          backend:\n            service:\n              name: test-service\n              port:\n                number: 80\n```\n\n### Init Containers\n```yaml\ninitContainers: \n  - reuseContainerImage: true\n    securityContext:\n      runAsUser: 1000\n      runAsGroup: 3000\n      fsGroup: 2000\n    volumeMounts:\n     - mountPath: /etc/ls-oms\n       name: ls-oms-cm-vol\n   args:\n    - sleep 300    \n   command:\n     - flyway\n     - -configFiles=/etc/ls-oms/flyway.conf\n     - migrate\n\n  - name: nginx\n    image: nginx:1.14.2\n    securityContext:\n      privileged: true\n    ports:\n    - containerPort: 80\n    command: [\"/usr/local/bin/nginx\"]\n    args: [\"-g\", \"daemon off;\"]\n```\nSpecialized containers that run before app containers in a Pod. Init containers can contain utilities or setup scripts not present in an app image. One can use base image inside initContainer by setting the reuseContainerImage flag to `true`.\n\n### Istio\n\nIstio is a service mesh which simplifies observability, traffic management, security and much more with it's virtual services and gateways.\n\n```yaml\nistio:\n  enable: true\n  gateway:\n    annotations: {}\n    enabled: false\n    host: example.com\n    labels: {}\n    tls:\n      enabled: false\n      secretName: example-tls-secret\n  virtualService:\n    annotations: {}\n    enabled: false\n    gateways: []\n    hosts: []\n    http:\n      - corsPolicy:\n          allowCredentials: false\n          allowHeaders:\n            - x-some-header\n          allowMethods:\n            - GET\n          allowOrigin:\n            - example.com\n          maxAge: 24h\n        headers:\n          request:\n            add:\n              x-some-header: value\n        match:\n          - uri:\n              prefix: /v1\n          - uri:\n              prefix: /v2\n        retries:\n          attempts: 2\n          perTryTimeout: 3s\n        rewriteUri: /\n        route:\n          - destination:\n              host: service1\n              port: 80\n        timeout: 12s\n      - route:\n          - destination:\n              host: service2\n    labels: {}\n```\n\n### Pause For Seconds Before Switch Active\n```yaml\npauseForSecondsBeforeSwitchActive: 30\n```\nTo wait for given period of time before switch active the container.\n\n### Resources\n\nThese define minimum and maximum RAM and CPU available to the application.\n\n```yaml\nresources:\n  limits:\n    cpu: \"1\"\n    memory: \"200Mi\"\n  requests:\n    cpu: \"0.10\"\n    memory: \"100Mi\"\n```\n\nResources are required to set CPU and memory usage.\n\n#### Limits\n\nLimits make sure a container never goes above a certain value. The container is only allowed to go up to the limit, and then it is restricted.\n\n#### Requests\n\nRequests are what the container is guaranteed to get.\n\n### Service\n\nThis defines annotations and the type of service, optionally can define name also.\n\nSupports \"ClientIP\" and \"None\". Used to maintain session affinity. Enable\n    client IP based session affinity.\n\n```yaml\n  service:\n    type: ClusterIP\n    annotations: {}\n    sessionAffinity:\n      enabled: true\n      sessionAffinityConfig: {}\n```\n\n### Volumes\n\n```yaml\nvolumes:\n  - name: log-volume\n    emptyDir: {}\n  - name: logpv\n    persistentVolumeClaim:\n      claimName: logpvc\n```\n\nIt is required when some values need to be read from or written to an external disk.\n\n### Volume Mounts\n\n```yaml\nvolumeMounts:\n  - mountPath: /var/log/nginx/\n    name: log-volume \n  - mountPath: /mnt/logs\n    name: logpvc\n    subPath: employee  \n```\n\nIt is used to provide mounts to the volume.\n\n### Affinity and anti-affinity\n\n```yaml\nSpec:\n  Affinity:\n    Key:\n    Values:\n```\n\nSpec is used to define the desire state of the given container.\n\nNode Affinity allows you to constrain which nodes your pod is eligible to schedule on, based on labels of the node.\n\nInter-pod affinity allow you to constrain which nodes your pod is eligible to be scheduled based on labels on pods.\n\n#### Key\n\nKey part of the label for node selection, this should be same as that on node. Please confirm with devops team.\n\n#### Values\n\nValue part of the label for node selection, this should be same as that on node. Please confirm with devops team.\n\n### Tolerations\n\n```yaml\ntolerations:\n - key: \"key\"\n   operator: \"Equal\"\n   value: \"value\"\n   effect: \"NoSchedule|PreferNoSchedule|NoExecute(1.6 only)\"\n```\n\nTaints are the opposite, they allow a node to repel a set of pods.\n\nA given pod can access the given node and avoid the given taint only if the given pod satisfies a given taint.\n\nTaints and tolerations are a mechanism which work together that allows you to ensure that pods are not placed on inappropriate nodes. Taints are added to nodes, while tolerations are defined in the pod specification. When you taint a node, it will repel all the pods except those that have a toleration for that taint. A node can have one or many taints associated with it.\n\n### Arguments\n\n```yaml\nargs:\n  enabled: false\n  value: []\n```\n\nThis is used to give arguments to command.\n\n### Command\n\n```yaml\ncommand:\n  enabled: false\n  value: []\n```\n\nIt contains the commands for the server.\n\n| Key | Description |\n| :--- | :--- |\n| `enabled` | To enable or disable the command. |\n| `value` | It contains the commands. |\n\n\n### Containers\nContainers section can be used to run side-car containers along with your main container within same pod. Containers running within same pod can share volumes and IP Address and can address each other @localhost. We can use base image inside container by setting the reuseContainerImage flag to `true`.\n\n```yaml\n    containers:\n      - name: nginx\n        image: nginx:1.14.2\n        ports:\n        - containerPort: 80\n        command: [\"/usr/local/bin/nginx\"]\n        args: [\"-g\", \"daemon off;\"]\n      - reuseContainerImage: true\n        securityContext:\n          runAsUser: 1000\n          runAsGroup: 3000\n          fsGroup: 2000\n        volumeMounts:\n        - mountPath: /etc/ls-oms\n          name: ls-oms-cm-vol\n        command:\n          - flyway\n          - -configFiles=/etc/ls-oms/flyway.conf\n          - migrate\n```\n\n### Prometheus\n\n```yaml\n  prometheus:\n    release: monitoring\n```\n\nIt is a kubernetes monitoring tool and the name of the file to be monitored as monitoring in the given case.It describes the state of the prometheus.\n\n### rawYaml\n\n```yaml\nrawYaml: \n  - apiVersion: v1\n    kind: Service\n    metadata:\n      name: my-service\n    spec:\n      selector:\n        app: MyApp\n      ports:\n        - protocol: TCP\n          port: 80\n          targetPort: 9376\n      type: ClusterIP\n```\nAccepts an array of Kubernetes objects. You can specify any kubernetes yaml here and it will be applied when your app gets deployed.\n\n### Grace Period\n\n```yaml\nGracePeriod: 30\n```\nKubernetes waits for the specified time called the termination grace period before terminating the pods. By default, this is 30 seconds. If your pod usually takes longer than 30 seconds to shut down gracefully, make sure you increase the `GracePeriod`.\n\nA Graceful termination in practice means that your application needs to handle the SIGTERM message and begin shutting down when it receives it. This means saving all data that needs to be saved, closing down network connections, finishing any work that is left, and other similar tasks.\n\nThere are many reasons why Kubernetes might terminate a perfectly healthy container. If you update your deployment with a rolling update, Kubernetes slowly terminates old pods while spinning up new ones. If you drain a node, Kubernetes terminates all pods on that node. If a node runs out of resources, Kubernetes terminates pods to free those resources. It’s important that your application handle termination gracefully so that there is minimal impact on the end user and the time-to-recovery is as fast as possible.\n\n\n### Server\n\n```yaml\nserver:\n  deployment:\n    image_tag: 1-95a53\n    image: \"\"\n```\n\nIt is used for providing server configurations.\n\n#### Deployment\n\nIt gives the details for deployment.\n\n| Key | Description |\n| :--- | :--- |\n| `image_tag` | It is the image tag |\n| `image` | It is the URL of the image |\n\n### Service Monitor\n\n```yaml\nservicemonitor:\n      enabled: true\n      path: /abc\n      scheme: 'http'\n      interval: 30s\n      scrapeTimeout: 20s\n      metricRelabelings:\n        - sourceLabels: [namespace]\n          regex: '(.*)'\n          replacement: myapp\n          targetLabel: target_namespace\n```\n\nIt gives the set of targets to be monitored.\n\n### Db Migration Config\n\n```yaml\ndbMigrationConfig:\n  enabled: false\n```\n\nIt is used to configure database migration.\n\n\n### KEDA Autoscaling\n[KEDA](https://keda.sh) is a Kubernetes-based Event Driven Autoscaler. With KEDA, you can drive the scaling of any container in Kubernetes based on the number of events needing to be processed. KEDA can be installed into any Kubernetes cluster and can work alongside standard Kubernetes components like the Horizontal Pod Autoscaler(HPA).\n\nExample for autosccaling with KEDA using Prometheus metrics is given below:\n```yaml\nkedaAutoscaling:\n  enabled: true\n  minReplicaCount: 1\n  maxReplicaCount: 2\n  idleReplicaCount: 0\n  pollingInterval: 30\n  advanced:\n    restoreToOriginalReplicaCount: true\n    horizontalPodAutoscalerConfig:\n      behavior:\n        scaleDown:\n          stabilizationWindowSeconds: 300\n          policies:\n          - type: Percent\n            value: 100\n            periodSeconds: 15\n  triggers: \n    - type: prometheus\n      metadata:\n        serverAddress:  http://<prometheus-host>:9090\n        metricName: http_request_total\n        query: envoy_cluster_upstream_rq{appId=\"300\", cluster_name=\"300-0\", container=\"envoy\",}\n        threshold: \"50\"\n  triggerAuthentication:\n    enabled: false\n    name:\n    spec: {}\n  authenticationRef: {}\n```\nExample for autosccaling with KEDA based on kafka is given below :\n```yaml\nkedaAutoscaling:\n  enabled: true\n  minReplicaCount: 1\n  maxReplicaCount: 2\n  idleReplicaCount: 0\n  pollingInterval: 30\n  advanced: {}\n  triggers: \n    - type: kafka\n      metadata:\n        bootstrapServers: b-2.kafka-msk-dev.example.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-3.kafka-msk-dev.example.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-msk-dev.example.c2.kafka.ap-southeast-1.amazonaws.com:9092\n        topic: Orders-Service-ESP.info\n        lagThreshold: \"100\"\n        consumerGroup: oders-remove-delivered-packages\n        allowIdleConsumers: \"true\"\n  triggerAuthentication:\n    enabled: true\n    name: keda-trigger-auth-kafka-credential\n    spec:\n      secretTargetRef:\n        - parameter: sasl\n          name: keda-kafka-secrets\n          key: sasl\n        - parameter: username\n          name: keda-kafka-secrets\n          key: username\n  authenticationRef: \n    name: keda-trigger-auth-kafka-credential\n```\n\n### Winter-Soldier\nWinter Soldier can be used to\n- cleans up (delete) Kubernetes resources\n- reduce workload pods to 0\n\n**_NOTE:_** After deploying this we can create the Hibernator object and provide the custom configuration by which workloads going to delete, sleep and many more.   for more information check [the main repo](https://github.com/devtron-labs/winter-soldier)\n\nGiven below is template values you can give in winter-soldier:\n```yaml\nwinterSoldier:\n  enabled: false\n  apiVersion: pincher.devtron.ai/v1alpha1\n  action: sleep\n  timeRangesWithZone:\n    timeZone: \"Asia/Kolkata\"\n    timeRanges: []\n  targetReplicas: []\n  fieldSelector: []\n```\nHere, \n| Key | values | Description |\n| :--- | :--- | :--- |\n| `enabled` | `fasle`,`true` | decide the enabling factor  |\n| `apiVersion` | `pincher.devtron.ai/v1beta1`, `pincher.devtron.ai/v1alpha1` | specific api version  |\n| `action` | `sleep`,`delete`, `scale` | This specify  the action need to perform.  |\n| `timeRangesWithZone`:`timeZone` | eg:- `\"Asia/Kolkata\"`,`\"US/Pacific\"` |  It use to specify the timeZone used. (It uses standard format. please refer [this](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones))  |\n| `timeRangesWithZone`:`timeRanges` | array of [ `timeFrom`, `timeTo`, `weekdayFrom`, `weekdayTo`] |  It use to define time period/range on which the user need to perform the specified action. you can have multiple timeRanges. <br /> These settings will take `action` on Sat and Sun from 00:00 to 23:59:59, |\n| `targetReplicas` | `[n]` : n - number of replicas to scale. | These is mandatory field when the `action` is `scale` <br /> Defalut value is `[]`.  |\n| `fieldSelector` | `- AfterTime(AddTime( ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now()) `  | These value will take a list of methods to select the resources on which we perform specified `action` .  |\n\n\nhere is an example,\n```yaml\nwinterSoldier:\n  apiVersion: pincher.devtron.ai/v1alpha1 \n  enabled: true\n  annotations: {}\n  labels: {}\n  timeRangesWithZone:\n    timeZone: \"Asia/Kolkata\"\n    timeRanges: \n      - timeFrom: 00:00\n        timeTo: 23:59:59\n        weekdayFrom: Sat\n        weekdayTo: Sun\n      - timeFrom: 00:00\n        timeTo: 08:00\n        weekdayFrom: Mon\n        weekdayTo: Fri\n      - timeFrom: 20:00\n        timeTo: 23:59:59\n        weekdayFrom: Mon\n        weekdayTo: Fri\n  action: scale\n  targetReplicas: [1,1,1]\n  fieldSelector: \n    - AfterTime(AddTime( ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '10h'), Now())\n```\nAbove settings will take action on `Sat` and `Sun` from 00:00 to 23:59:59, and on `Mon`-`Fri` from 00:00 to 08:00 and 20:00 to 23:59:59. If `action:sleep` then runs hibernate at timeFrom and unhibernate at `timeTo`. If `action: delete` then it will delete workloads at `timeFrom` and `timeTo`. Here the `action:scale` thus it scale the number of resource replicas to  `targetReplicas: [1,1,1]`. Here each element of `targetReplicas` array is mapped with the corresponding elments of array `timeRangesWithZone/timeRanges`. Thus make sure the length of both array is equal, otherwise the cnages cannot be observed.\n\nThe above example will select the application objects which have been created 10 hours ago across all namespaces excluding application's namespace. Winter soldier exposes following functions to handle time, cpu and memory.\n\n- ParseTime - This function can be used to parse time. For eg to parse creationTimestamp use ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z')\n- AddTime - This can be used to add time. For eg AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '-10h') ll add 10h to the time. Use d for day, h for hour, m for minutes and s for seconds. Use negative number to get earlier time.\n- Now - This can be used to get current time.\n- CpuToNumber - This can be used to compare CPU. For eg any({{spec.containers.#.resources.requests}}, { MemoryToNumber(.memory) < MemoryToNumber('60Mi')}) will check if any resource.requests is less than 60Mi.\n\n\n### Security Context\nA security context defines privilege and access control settings for a Pod or Container.\n\nTo add a security context for main container:\n```yaml\ncontainerSecurityContext:\n  allowPrivilegeEscalation: false\n```\n\nTo add a security context on pod level:\n```yaml\npodSecurityContext:\n  runAsUser: 1000\n  runAsGroup: 3000\n  fsGroup: 2000\n```\n\n### Topology Spread Constraints\nYou can use topology spread constraints to control how Pods are spread across your cluster among failure-domains such as regions, zones, nodes, and other user-defined topology domains. This can help to achieve high availability as well as efficient resource utilization.\n\n```yaml\ntopologySpreadConstraints:\n  - maxSkew: 1\n    topologyKey: zone\n    whenUnsatisfiable: DoNotSchedule\n    autoLabelSelector: true\n    customLabelSelector: {}\n    minDomains: 1\n    nodeAffinityPolicy: Ignore\n```\n\n### Persistent Volume Claim\nYou can use persistent volume claim to mount volume as per your usecase.\n\n```yaml\npersistentVolumeClaim:\n  name: my-pvc\n  storageClassName: default\n  accessMode:\n    - ReadWriteOnce\n  mountPath: /tmp\n```\n\n### Vertical Pod Autoscaling\nThis is connected to VPA and controls scaling up and down in response to request load.\n```yaml\nverticalPodScaling:\n  enabled: true\n  resourcePolicy: {}\n  updatePolicy: {}\n ```\n\n### Scheduler Name\n\nYou can provide you own custom scheduler to schedule your application\n\n```yaml\nschedulerName: \"\"\n```\n\n### Deployment Metrics\n\nIt gives the realtime metrics of the deployed applications\n\n| Key | Description |\n| :--- | :--- |\n| `Deployment Frequency` | It shows how often this app is deployed to production |\n| `Change Failure Rate` | It shows how often the respective pipeline fails. |\n| `Mean Lead Time` | It shows the average time taken to deliver a change to production. |\n| `Mean Time to Recovery` | It shows the average time taken to fix a failed pipeline. |\n\n## 2. Show application metrics\n\nIf you want to see application metrics like different HTTP status codes metrics, application throughput, latency, response time. Enable the Application metrics from below the deployment template Save button. After enabling it, you should be able to see all metrics on App detail page. By default it remains disabled.\n![](../../../.gitbook/assets/deployment_application_metrics%20%282%29.png)\n\nOnce all the Deployment template configurations are done, click on `Save` to save your deployment configuration. Now you are ready to create [Workflow](workflow/) to do CI/CD.\n\n### Helm Chart Json Schema \n\nHelm Chart [json schema](../../../scripts/devtron-reference-helm-charts/reference-chart_4-11-0/schema.json) is used to validate the deployment template values.\n\n### Other Validations in Json Schema\n\nThe values of CPU and Memory in limits must be greater than or equal to in requests respectively. Similarly, In case of envoyproxy, the values of limits are greater than or equal to requests as mentioned below.\n```\nresources.limits.cpu >= resources.requests.cpu\nresources.limits.memory >= resources.requests.memory\nenvoyproxy.resources.limits.cpu >= envoyproxy.resources.requests.cpu\nenvoyproxy.resources.limits.memory >= envoyproxy.resources.requests.memory\n```\n",
        "appId": appId,
        "defaultAppOverride": {
            "ContainerPort": [
                {
                    "envoyPort": 8799,
                    "idleTimeout": "1800s",
                    "name": "app",
                    "port": 8081,
                    "protocol": "TCP",
                    "servicePort": 80,
                    "supportStreaming": false,
                    "useHTTP2": false
                }
            ],
            "EnvVariables": [],
            "GracePeriod": 30,
            "LivenessProbe": {
                "Path": "",
                "command": [],
                "failureThreshold": 3,
                "grpc": {},
                "httpHeaders": [],
                "initialDelaySeconds": 20,
                "periodSeconds": 10,
                "port": 8080,
                "scheme": "",
                "successThreshold": 1,
                "tcp": false,
                "timeoutSeconds": 5
            },
            "MaxSurge": 1,
            "MaxUnavailable": 0,
            "MinReadySeconds": 60,
            "ReadinessProbe": {
                "Path": "",
                "command": [],
                "failureThreshold": 3,
                "grpc": {},
                "httpHeaders": [],
                "initialDelaySeconds": 20,
                "periodSeconds": 10,
                "port": 8080,
                "scheme": "",
                "successThreshold": 1,
                "tcp": false,
                "timeoutSeconds": 5
            },
            "Spec": {
                "Affinity": {
                    "Key": "",
                    "Values": ""
                }
            },
            "StartupProbe": {
                "Path": "",
                "command": [],
                "failureThreshold": 3,
                "grpc": {},
                "httpHeaders": [],
                "initialDelaySeconds": 20,
                "periodSeconds": 10,
                "port": 8080,
                "successThreshold": 1,
                "tcp": false,
                "timeoutSeconds": 5
            },
            "affinity": {
                "enabled": false,
                "values": {}
            },
            "ambassadorMapping": {
                "ambassadorId": "",
                "cors": {},
                "enabled": false,
                "hostname": "devtron.example.com",
                "labels": {},
                "prefix": "/",
                "retryPolicy": {},
                "rewrite": "",
                "tls": {
                    "context": "",
                    "create": false,
                    "hosts": [],
                    "secretName": ""
                }
            },
            "args": {
                "enabled": false,
                "value": [
                    "/bin/sh",
                    "-c",
                    "touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600"
                ]
            },
            "autoscaling": {
                "MaxReplicas": 2,
                "MinReplicas": 1,
                "TargetCPUUtilizationPercentage": 90,
                "TargetMemoryUtilizationPercentage": 80,
                "annotations": {},
                "behavior": {},
                "containerResource": {
                    "TargetCPUUtilizationPercentage": 90,
                    "TargetMemoryUtilizationPercentage": 80,
                    "enabled": false
                },
                "enabled": false,
                "extraMetrics": [],
                "labels": {}
            },
            "command": {
                "enabled": false,
                "value": [],
                "workingDir": {}
            },
            "containerSecurityContext": {},
            "containerSpec": {
                "lifecycle": {
                    "enabled": false,
                    "postStart": {
                        "httpGet": {
                            "host": "example.com",
                            "path": "/example",
                            "port": 90
                        }
                    },
                    "preStop": {
                        "exec": {
                            "command": [
                                "sleep",
                                "10"
                            ]
                        }
                    }
                }
            },
            "containers": [],
            "dbMigrationConfig": {
                "enabled": false
            },
            "deploymentAnnotations": {},
            "deploymentLabels": {},
            "envoyproxy": {
                "configMapName": "",
                "image": "quay.io/devtron/envoy:v1.16.0",
                "lifecycle": {},
                "resources": {
                    "limits": {
                        "cpu": "50m",
                        "memory": "50Mi"
                    },
                    "requests": {
                        "cpu": "50m",
                        "memory": "50Mi"
                    }
                }
            },
            "flaggerCanary": {
                "addOtherGateways": [],
                "addOtherHosts": [],
                "analysis": {
                    "interval": "15s",
                    "maxWeight": 50,
                    "stepWeight": 5,
                    "threshold": 5
                },
                "annotations": {},
                "appProtocol": "http",
                "corsPolicy": null,
                "createIstioGateway": {
                    "annotations": {},
                    "enabled": false,
                    "host": null,
                    "labels": {},
                    "tls": {
                        "enabled": false,
                        "secretName": null
                    }
                },
                "enabled": false,
                "gatewayRefs": null,
                "headers": null,
                "labels": {},
                "loadtest": {
                    "enabled": true,
                    "url": "http://flagger-loadtester.istio-system/"
                },
                "match": [
                    {
                        "uri": {
                            "prefix": "/"
                        }
                    }
                ],
                "portDiscovery": true,
                "retries": null,
                "rewriteUri": "/",
                "serviceport": 8080,
                "targetPort": 8080,
                "thresholds": {
                    "latency": 500,
                    "successRate": 90
                },
                "timeout": null
            },
            "hostAliases": [],
            "image": {
                "pullPolicy": "IfNotPresent"
            },
            "imagePullSecrets": [],
            "ingress": {
                "annotations": {},
                "className": "",
                "enabled": false,
                "hosts": [
                    {
                        "host": "chart-example1.local",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example1"
                        ]
                    },
                    {
                        "host": "chart-example2.local",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example2",
                            "/example2/healthz"
                        ]
                    }
                ],
                "labels": {},
                "tls": []
            },
            "ingressInternal": {
                "annotations": {},
                "className": "",
                "enabled": false,
                "hosts": [
                    {
                        "host": "chart-example1.internal",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example1"
                        ]
                    },
                    {
                        "host": "chart-example2.internal",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example2",
                            "/example2/healthz"
                        ]
                    }
                ],
                "tls": []
            },
            "initContainers": [],
            "istio": {
                "authorizationPolicy": {
                    "action": null,
                    "annotations": {},
                    "enabled": false,
                    "labels": {},
                    "provider": {},
                    "rules": []
                },
                "destinationRule": {
                    "annotations": {},
                    "enabled": false,
                    "labels": {},
                    "subsets": [],
                    "trafficPolicy": {}
                },
                "enable": false,
                "gateway": {
                    "annotations": {},
                    "enabled": false,
                    "host": "example.com",
                    "labels": {},
                    "tls": {
                        "enabled": false,
                        "secretName": "example-secret"
                    }
                },
                "peerAuthentication": {
                    "annotations": {},
                    "enabled": false,
                    "labels": {},
                    "mtls": {
                        "mode": ""
                    },
                    "portLevelMtls": {},
                    "selector": {
                        "enabled": false
                    }
                },
                "requestAuthentication": {
                    "annotations": {},
                    "enabled": false,
                    "jwtRules": [],
                    "labels": {},
                    "selector": {
                        "enabled": false
                    }
                },
                "virtualService": {
                    "annotations": {},
                    "enabled": false,
                    "gateways": [],
                    "hosts": [],
                    "http": [],
                    "labels": {}
                }
            },
            "kedaAutoscaling": {
                "advanced": {},
                "authenticationRef": {},
                "enabled": false,
                "envSourceContainerName": "",
                "maxReplicaCount": 2,
                "minReplicaCount": 1,
                "triggerAuthentication": {
                    "enabled": false,
                    "name": "",
                    "spec": {}
                },
                "triggers": []
            },
            "networkPolicy": {
                "annotations": {},
                "egress": [],
                "enabled": false,
                "ingress": [],
                "labels": {},
                "podSelector": {
                    "matchExpressions": [],
                    "matchLabels": {}
                },
                "policyTypes": []
            },
            "pauseForSecondsBeforeSwitchActive": 30,
            "podAnnotations": {},
            "podDisruptionBudget": {},
            "podLabels": {},
            "podSecurityContext": {},
            "prometheus": {
                "release": "monitoring"
            },
            "rawYaml": [],
            "replicaCount": 1,
            "resources": {
                "limits": {
                    "cpu": "0.05",
                    "memory": "50Mi"
                },
                "requests": {
                    "cpu": "0.01",
                    "memory": "10Mi"
                }
            },
            "restartPolicy": "Always",
            "secret": {
                "data": {},
                "enabled": false
            },
            "server": {
                "deployment": {
                    "image": "",
                    "image_tag": "1-95af053"
                }
            },
            "service": {
                "annotations": {},
                "loadBalancerSourceRanges": [],
                "type": "ClusterIP"
            },
            "serviceAccount": {
                "annotations": {},
                "create": false,
                "name": ""
            },
            "servicemonitor": {
                "additionalLabels": {}
            },
            "tolerations": [],
            "topologySpreadConstraints": [],
            "verticalPodScaling": {
                "enabled": false
            },
            "volumeMounts": [],
            "volumes": [],
            "waitForSecondsBeforeScalingDown": 30,
            "winterSoldier": {
                "action": "sleep",
                "annotation": {},
                "apiVersion": "pincher.devtron.ai/v1alpha1",
                "enabled": false,
                "fieldSelector": [
                    "AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())"
                ],
                "labels": {},
                "targetReplicas": [],
                "timeRangesWithZone": {
                    "timeRanges": [],
                    "timeZone": "Asia/Kolkata"
                },
                "type": "Deployment"
            }
        },
        "isAppMetricsEnabled": false,
        "saveEligibleChanges": false,
        "valuesOverride": {
            "ContainerPort": [
                {
                    "envoyPort": 8799,
                    "idleTimeout": "1800s",
                    "name": "app",
                    "port": 8081,
                    "protocol": "TCP",
                    "servicePort": 80,
                    "supportStreaming": false,
                    "useHTTP2": false
                }
            ],
            "EnvVariables": [],
            "GracePeriod": 30,
            "LivenessProbe": {
                "Path": "",
                "command": [],
                "failureThreshold": 3,
                "grpc": {},
                "httpHeaders": [],
                "initialDelaySeconds": 20,
                "periodSeconds": 10,
                "port": 8080,
                "scheme": "",
                "successThreshold": 1,
                "tcp": false,
                "timeoutSeconds": 5
            },
            "MaxSurge": 1,
            "MaxUnavailable": 0,
            "MinReadySeconds": 60,
            "ReadinessProbe": {
                "Path": "",
                "command": [],
                "failureThreshold": 3,
                "grpc": {},
                "httpHeaders": [],
                "initialDelaySeconds": 20,
                "periodSeconds": 10,
                "port": 8080,
                "scheme": "",
                "successThreshold": 1,
                "tcp": false,
                "timeoutSeconds": 5
            },
            "Spec": {
                "Affinity": {
                    "Key": "",
                    "Values": ""
                }
            },
            "StartupProbe": {
                "Path": "",
                "command": [],
                "failureThreshold": 3,
                "grpc": {},
                "httpHeaders": [],
                "initialDelaySeconds": 20,
                "periodSeconds": 10,
                "port": 8080,
                "successThreshold": 1,
                "tcp": false,
                "timeoutSeconds": 5
            },
            "affinity": {
                "enabled": false,
                "values": {}
            },
            "ambassadorMapping": {
                "ambassadorId": "",
                "cors": {},
                "enabled": false,
                "hostname": "devtron.example.com",
                "labels": {},
                "prefix": "/",
                "retryPolicy": {},
                "rewrite": "",
                "tls": {
                    "context": "",
                    "create": false,
                    "hosts": [],
                    "secretName": ""
                }
            },
            "args": {
                "enabled": false,
                "value": [
                    "/bin/sh",
                    "-c",
                    "touch /tmp/healthy; sleep 30; rm -rf /tmp/healthy; sleep 600"
                ]
            },
            "autoscaling": {
                "MaxReplicas": 2,
                "MinReplicas": 1,
                "TargetCPUUtilizationPercentage": 90,
                "TargetMemoryUtilizationPercentage": 80,
                "annotations": {},
                "behavior": {},
                "containerResource": {
                    "TargetCPUUtilizationPercentage": 90,
                    "TargetMemoryUtilizationPercentage": 80,
                    "enabled": false
                },
                "enabled": false,
                "extraMetrics": [],
                "labels": {}
            },
            "command": {
                "enabled": false,
                "value": [],
                "workingDir": {}
            },
            "containerSecurityContext": {},
            "containerSpec": {
                "lifecycle": {
                    "enabled": false,
                    "postStart": {
                        "httpGet": {
                            "host": "example.com",
                            "path": "/example",
                            "port": 90
                        }
                    },
                    "preStop": {
                        "exec": {
                            "command": [
                                "sleep",
                                "10"
                            ]
                        }
                    }
                }
            },
            "containers": [],
            "dbMigrationConfig": {
                "enabled": false
            },
            "deploymentAnnotations": {},
            "deploymentLabels": {},
            "envoyproxy": {
                "configMapName": "",
                "image": "quay.io/devtron/envoy:v1.16.0",
                "lifecycle": {},
                "resources": {
                    "limits": {
                        "cpu": "50m",
                        "memory": "50Mi"
                    },
                    "requests": {
                        "cpu": "50m",
                        "memory": "50Mi"
                    }
                }
            },
            "flaggerCanary": {
                "addOtherGateways": [],
                "addOtherHosts": [],
                "analysis": {
                    "interval": "15s",
                    "maxWeight": 50,
                    "stepWeight": 5,
                    "threshold": 5
                },
                "annotations": {},
                "appProtocol": "http",
                "corsPolicy": null,
                "createIstioGateway": {
                    "annotations": {},
                    "enabled": false,
                    "host": null,
                    "labels": {},
                    "tls": {
                        "enabled": false,
                        "secretName": null
                    }
                },
                "enabled": false,
                "gatewayRefs": null,
                "headers": null,
                "labels": {},
                "loadtest": {
                    "enabled": true,
                    "url": "http://flagger-loadtester.istio-system/"
                },
                "match": [
                    {
                        "uri": {
                            "prefix": "/"
                        }
                    }
                ],
                "portDiscovery": true,
                "retries": null,
                "rewriteUri": "/",
                "serviceport": 8080,
                "targetPort": 8080,
                "thresholds": {
                    "latency": 500,
                    "successRate": 90
                },
                "timeout": null
            },
            "hostAliases": [],
            "image": {
                "pullPolicy": "IfNotPresent"
            },
            "imagePullSecrets": [],
            "ingress": {
                "annotations": {},
                "className": "",
                "enabled": false,
                "hosts": [
                    {
                        "host": "chart-example1.local",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example1"
                        ]
                    },
                    {
                        "host": "chart-example2.local",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example2",
                            "/example2/healthz"
                        ]
                    }
                ],
                "labels": {},
                "tls": []
            },
            "ingressInternal": {
                "annotations": {},
                "className": "",
                "enabled": false,
                "hosts": [
                    {
                        "host": "chart-example1.internal",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example1"
                        ]
                    },
                    {
                        "host": "chart-example2.internal",
                        "pathType": "ImplementationSpecific",
                        "paths": [
                            "/example2",
                            "/example2/healthz"
                        ]
                    }
                ],
                "tls": []
            },
            "initContainers": [],
            "istio": {
                "authorizationPolicy": {
                    "action": null,
                    "annotations": {},
                    "enabled": false,
                    "labels": {},
                    "provider": {},
                    "rules": []
                },
                "destinationRule": {
                    "annotations": {},
                    "enabled": false,
                    "labels": {},
                    "subsets": [],
                    "trafficPolicy": {}
                },
                "enable": false,
                "gateway": {
                    "annotations": {},
                    "enabled": false,
                    "host": "example.com",
                    "labels": {},
                    "tls": {
                        "enabled": false,
                        "secretName": "example-secret"
                    }
                },
                "peerAuthentication": {
                    "annotations": {},
                    "enabled": false,
                    "labels": {},
                    "mtls": {
                        "mode": ""
                    },
                    "portLevelMtls": {},
                    "selector": {
                        "enabled": false
                    }
                },
                "requestAuthentication": {
                    "annotations": {},
                    "enabled": false,
                    "jwtRules": [],
                    "labels": {},
                    "selector": {
                        "enabled": false
                    }
                },
                "virtualService": {
                    "annotations": {},
                    "enabled": false,
                    "gateways": [],
                    "hosts": [],
                    "http": [],
                    "labels": {}
                }
            },
            "kedaAutoscaling": {
                "advanced": {},
                "authenticationRef": {},
                "enabled": false,
                "envSourceContainerName": "",
                "maxReplicaCount": 2,
                "minReplicaCount": 1,
                "triggerAuthentication": {
                    "enabled": false,
                    "name": "",
                    "spec": {}
                },
                "triggers": []
            },
            "networkPolicy": {
                "annotations": {},
                "egress": [],
                "enabled": false,
                "ingress": [],
                "labels": {},
                "podSelector": {
                    "matchExpressions": [],
                    "matchLabels": {}
                },
                "policyTypes": []
            },
            "pauseForSecondsBeforeSwitchActive": 30,
            "podAnnotations": {},
            "podDisruptionBudget": {},
            "podLabels": {},
            "podSecurityContext": {},
            "prometheus": {
                "release": "monitoring"
            },
            "rawYaml": [
                {
                    "apiVersion": "batch/v1",
                    "kind": "Job",
                    "metadata": {
                        "name": "echo-job-1",
                        "annotations": {
                            "helm.sh/hook": "pre-upgrade,pre-install"
                        },
                        "generateName": "echo-job-",
                        "namepace": "devtron-demo"
                    },
                    "spec": {
                        "ttlSecondsAfterFinished": 10,
                        "template": {
                            "spec": {
                                "containers": [
                                    {
                                        "name": "sleep-container",
                                        "image": "busybox",
                                        "command": [
                                            "sh",
                                            "-c",
                                            "echo 'Hi from hook'"
                                        ]
                                    }
                                ],
                                "restartPolicy": "Never"
                            }
                        },
                        "backoffLimit": 4
                    }
                }
            ],
            "replicaCount": 1,
            "resources": {
                "limits": {
                    "cpu": "0.05",
                    "memory": "50Mi"
                },
                "requests": {
                    "cpu": "0.01",
                    "memory": "10Mi"
                }
            },
            "restartPolicy": "Always",
            "secret": {
                "data": {},
                "enabled": false
            },
            "server": {
                "deployment": {
                    "image": "",
                    "image_tag": "1-95af053"
                }
            },
            "service": {
                "annotations": {},
                "loadBalancerSourceRanges": [],
                "type": "ClusterIP"
            },
            "serviceAccount": {
                "annotations": {},
                "create": false,
                "name": ""
            },
            "servicemonitor": {
                "additionalLabels": {}
            },
            "tolerations": [],
            "topologySpreadConstraints": [],
            "verticalPodScaling": {
                "enabled": false
            },
            "volumeMounts": [],
            "volumes": [],
            "waitForSecondsBeforeScalingDown": 30,
            "winterSoldier": {
                "action": "sleep",
                "annotation": {},
                "apiVersion": "pincher.devtron.ai/v1alpha1",
                "enabled": false,
                "fieldSelector": [
                    "AfterTime(AddTime(ParseTime({{metadata.creationTimestamp}}, '2006-01-02T15:04:05Z'), '5m'), Now())"
                ],
                "labels": {},
                "targetReplicas": [],
                "timeRangesWithZone": {
                    "timeRanges": [],
                    "timeZone": "Asia/Kolkata"
                },
                "type": "Deployment"
            }
        }
    }
    return result;
}