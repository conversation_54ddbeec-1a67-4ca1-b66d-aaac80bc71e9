export function dataForTriggerCi(pipelineId: number, data: { materialId: string, commit: string }[]) {
    let result = {
        "pipelineId": pipelineId,
        "ciPipelineMaterials": objectCreationForCiPipelineMaterials(data),
        "invalidateCache": false,
        "pipelineType": "CI_BUILD",
        "runtimeParams": {
            "runtimePluginVariables": []
        }
    }
    return result;
}
function objectCreationForCiPipelineMaterials(data: { materialId: string, commit: string }[]) {
    let resultArray: any = []
    for (let i = 0; i < data.length; i++) {
        let object = {
            "Id": data[0].materialId,
            "GitCommit": {
                "Commit": data[0].commit
            }
        }
        resultArray.push(object);
    }
    return resultArray;
}


