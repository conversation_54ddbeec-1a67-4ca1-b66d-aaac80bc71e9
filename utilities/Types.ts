import { AddChartRepo } from "../Pages/ApplicationManagement/Configurations/AddChartRepo"
import { AppConfigurationPage } from "../Pages/ApplicationManagement/Applications/AppConfigurationsPage"
import { ApplicationGroupPage } from "../Pages/ApplicationManagement/ApplicationGroups/ApplicationGroupPage"
import { BaseDeploymentTemplatePage } from "../Pages/ApplicationManagement/Applications/BaseDeploymentTemplatePage"
import { BuildConfigurationPage } from "../Pages/ApplicationManagement/Applications/BuildConfigurationPage"
import { BuildHistoryPage } from "../Pages/ApplicationManagement/Applications/BuildHistoryPage"
import { BuildSourceTagAndPR } from "../Pages/ApplicationManagement/Applications/BuildSourceTagAndPR"
import { CatalogFrameworkPage } from "../Pages/ApplicationManagement/Configurations/CatalogFrameworkPage"
import { ChartStoreAppDetailsPage } from "../Pages/ApplicationManagement/ChartStore/ChartStoreAppDetailsPage"
import { chartStoreDeploymentHistory } from "../Pages/ApplicationManagement/ChartStore/chartStoreDeploymentHistory"
import { ChartStorePage } from "../Pages/ApplicationManagement/ChartStore/chartStorePage"
import { DeployChartPage } from "../Pages/ApplicationManagement/ChartStore/DeployChartPage"
import { CiCdAppDetailsPage } from "../Pages/ApplicationManagement/Applications/CiCdAppDetailsPage"
import { CreateAppPage } from "../Pages/ApplicationManagement/Applications/CreateAppPage"
import { DeploymentHistoryPage } from "../Pages/ApplicationManagement/Applications/DeploymentHistoryPage"
import { ExternalCIPage } from "../Pages/ApplicationManagement/Applications/ExternalCIPage"
import { FilterConditionPage } from "../Pages/ApplicationManagement/Policies/FilterConditionPage"
import { GitopsConfigurationPage } from "../Pages/ApplicationManagement/Configurations/GItopsConfigurationPage"
import { GitRepositoryPage } from "../Pages/ApplicationManagement/Applications/GitRepositoryPage"
import { ApiTokenPage } from "../Pages/GlobalConfiguration/Authorization/ApiTokenPage"
import { BuildInfra } from "../Pages/ApplicationManagement/Configurations/BuildInfraPage"
import { ClusterAndEnvironments } from "../Pages/GlobalConfiguration/Clutsers and Environments/Cluster&EnvironmentsPage"
import { ContainerRegistryPage } from "../Pages/GlobalConfiguration/ContainerRegistryPage"
import { LockConfiguration } from "../Pages/ApplicationManagement/Policies/LockConfiguration"
import { NotificationsConfig } from "../Pages/ApplicationManagement/Configurations/NotificationConfigPage"
import { ProjectPage } from "../Pages/ApplicationManagement/ProjectPage"
import { ScopedVariablePage } from "../Pages/ApplicationManagement/Configurations/ScopedVariablePage"
import { UserPermissionPage } from "../Pages/GlobalConfiguration/Authorization/UserPermissionPage"
import { GlobalTag } from "../Pages/ApplicationManagement/Policies/GlobalTag"
import { JobsPage } from "../Pages/AutomationandEnablement/Jobs"
import { MandatoryPluginPage } from "../Pages/ApplicationManagement/Policies/MandatoryPluginpage"
import { OverviewPage } from "../Pages/OverviewPage"
import { PrePostCiCd } from "../Pages/ApplicationManagement/Applications/PrePostCiCd"
import { ImagePullDigest } from "../Pages/ApplicationManagement/Policies/PullImageDigest"
import { ResourceBrowserPage } from "../Pages/InfrastructureManagement/ResourceBrowserPage"
import { Rollback } from "../Pages/ApplicationManagement/Applications/Rollback"
import { TagPropagation } from "../Pages/ApplicationManagement/Applications/TagPropogation"
import { WorkflowPage } from "../Pages/ApplicationManagement/Applications/WorkflowPage"
import { ReleaseConfigurationPage } from "../Pages/SoftwareReleaseManagement/ReleaseConfigurationPage"

import { permissionGroupsPage } from "../Pages/GlobalConfiguration/Authorization/PermissionGroupsPage"
import { SsoLoginServicesPage } from "../Pages/GlobalConfiguration/Authorization/SsoLoginServicesPage"
import { Installation } from "../Pages/SoftwareReleaseManagement/Installations"
import { ReleaseHub } from "../Pages/SoftwareReleaseManagement/ReleaseHub"
import { ReleaseOverviewPage } from "../Pages/SoftwareReleaseManagement/ReleaseOverviewPages"
import { ReleaseTrackConfigurePage } from "../Pages/SoftwareReleaseManagement/ReleaseTrackConfigurePage"
import { Requirements } from "../Pages/SoftwareReleaseManagement/Requirements"
import { Rollout } from "../Pages/SoftwareReleaseManagement/Rollout"
import { RolloutHistoryPage } from "../Pages/SoftwareReleaseManagement/RolloutHistoryPage"
import { Tenants } from "../Pages/SoftwareReleaseManagement/Tenants"
import { SecurityGlobalPage } from "../Pages/SecurityCenter/SecurityPage"
import { ApprovalPolicyPage } from "../Pages/ApplicationManagement/Policies/ApprovalPolicyPage"
import { ExternalLinks } from "../Pages/ApplicationManagement/Configurations/ExternalLinks"
import { ApplicationTemplatePage } from "../Pages/ApplicationManagement/ApplicationTemplatePage"
import { ImagePromotion } from "../Pages/ApplicationManagement/Policies/ImagePromotion"
import { DeploymentWindow } from "../Pages/ApplicationManagement/Policies/DeploymentWindow"
import { DeploymentCharts } from "../Pages/ApplicationManagement/Configurations/DeploymenCharts"
import { ProductDashboardPage } from "../Pages/DevtronLicense/ProductDashboardPage"
import { LicenseDashboardPage } from "../Pages/DevtronLicense/licenseDashboardPage"
import { NavbarPage } from "../Pages/NavbarPage"
import {globalSearchComponentParentEntities } from "../enums/GlobalSearchComponentEnum"
import { GlobalSearchComponent } from "../Pages/GlobalSearchComponent"


export namespace AllTypes {
    export namespace DependencyInjection {
        export interface dependencyVerificationData {
            currentEnvName: string
            verificationData: {
                [keys: number]: { [key: string]: boolean }
            }
        }
        export interface mappingOfEnvironments {
            currentAppEnvName: string
            mappedAppEnvName: string
        }
    }
    export namespace apiTokenPage {
        export interface tokenExpirationDetails {
            type: string, day?: string, month?: string, year?: string
        }
        interface permissionDetails {
            permissionType: string, specificPermission?: { ResourceType: string, permissions: { [key: string]: string[] } }
        }
        export interface apiTokenObject {
            tokenName?: string
            expirationDetails?: tokenExpirationDetails
            permissionData: permissionDetails
        }
    }
    export namespace BaseCredentils {

        export interface securityScanHelmAppData {
            chartName: string;
            imageScanvul: boolean;
            imageScanLicense: boolean;
            kubernetesManifestmisconfig: boolean;
            kubernetesManifestexposedSecret: boolean;
        }
        export interface buildxConfiguration {
            setTargetPlatform?: string;
            sourceOfImage?: string;
            logsVerificationText?: string[]
            configuration?: builderPackConfiguration
            envVariables?: string[]
        }
        export interface builderPackConfiguration {
            language: string;
            version: string;
            builder: string;
        }

        export interface securityScanData {
            gitRepoUrl: string;
            imageScanVul: boolean;
            imageScanLicense: boolean;
            codeScanVul: boolean;
            codeScanLicense: boolean;
            codeScanMisconfig: boolean;
            exposedSecret: boolean;
            kubernetesManifestmisconfig: boolean;
            kubernetesManifestexposedSecret: boolean;
            applicationGroup: string;
            userDefinedGitRepo: string[];
            addChartRepo: AddChartRepo;
        }
        export interface AddChartRepo {
            //Todo add private repousername
            PublicRepositoryURL: string
            privateRepoUsername: string          // Just leaving this and not removing it because it will create some issues in the function.
            privateRepoPassword: string          // Also, It can be used in the Future.
        }
        export interface resourceBrowserParameters {
            SecretUserName: string;
            SecretPassword: string;
            SecretUpdatedUserName: string;
            SecretUpdatedPassword: string;
            SecretDecodedUserName: string;
            SecretDecodedPassword: string;
            DeploymentContainerImage: string;
            DeploymentReplicas: string;
            DeploymentContainerName: string;
            NamespaceForAllResources: string;
        }
        export interface RegistryCredentials {
            EcrRegistry: EcrRegistryCredentials;
            DockerRegistry: DockerRegistryCredentials;
            AzureRegistry: AzureRegistryCredentials;
            GCPRegistry: GCPRegistryCredentials;
            GCRRegistry: GCRRegistryCredentials;
            QuayRegistry: QuayRegistryCredentials;
            HarborRegistry: HarborRegistryCredentials;
        }
        export interface EcrRegistryCredentials {
            registryName: string;
            registryUrl: string;
            registryAccessId: string;
            registrySecretKey: string;
            publicRepoList: string[];
            privateRepoList: string[];
            pushHelmPackages: boolean;
            useAsChartRepo: boolean;
            useAsDefaultRegistry: boolean;
        }

        export interface DockerRegistryCredentials {
            registryName: string;
            registryUrl: string;
            registryUserName: string;
            registryToken: string;
            publicRepoList: string[];
            privateRepoList: string[];
            pushHelmPackages: boolean;
            useAsChartRepo: boolean;
            useAsDefaultRegistry: boolean;

        }
        export interface AzureRegistryCredentials {
            registryName: string;
            registryUrl: string;
            registryUserName: string;
            registryToken: string;
            publicRepoList: string[];
            privateRepoList: string[];
            pushHelmPackages: boolean;
            useAsChartRepo: boolean;
            useAsDefaultRegistry: boolean;

        }
        export interface GCPRegistryCredentials {
            registryName: string;
            registryUrl: string;
            registryServiceAccountFile: string;
            publicRepoList: string[];
            privateRepoList: string[];
            pushHelmPackages: boolean;
            useAsChartRepo: boolean;
            useAsDefaultRegistry: boolean;

        }
        export interface GCRRegistryCredentials {
            registryName: string;
            registryUrl: string;
            registryServiceAccountFile: string;
        }

        export interface QuayRegistryCredentials {
            registryName: string;
            registryUrl: string;
            registryUserName: string;
            registryToken: string;
            publicRepoList: string[];
            privateRepoList: string[];
            pushHelmPackages: boolean;
            useAsChartRepo: boolean;
            useAsDefaultRegistry: boolean;
        }
        export interface HarborRegistryCredentials {
            registryName: string;
            publicRepoList: string[];
            privateRepoList: string[];
            pushHelmPackages: boolean;
            useAsChartRepo: boolean;
            useAsDefaultRegistry: boolean;
            caCertificate: string
        }

        export interface ConfigMapOrSecret {
            resourceName: string;
            mountPath: string;
            setSubPath: boolean;
            setFilePermission: boolean;
            filePermission: string;
            mode: string;
            configmapData: string[];
            secretData: string[];
        }

        export interface HarborInCaCert {
            RegistryURL: string,
            Username: string,
            Password: string,
            CaCertificateTab: string
        }

        export interface VirtualData {
            pipelineType: string,
            VirtualClusterName: string,
            VirtualEnvironment: string,
        }
        export interface NotificationFilter {
            application: string;
            project: string;
            environment: string;
            cluster: string;
        }
        export interface vulnerabilitiesLabel {
            Critical: string,
            High: string,
            Medium: string,
            Low: string,
            unknown: string
        }
        export interface securityPolicyFilter {
            Global: string,
            Cluster: string,
            Environments: string,
            Applications: string,
        }
        export interface TestParameters {
            emailCreateUserArray: string[];
            customChartName: string
            customChartVersion: string
            GitRepoCheckoutPath: string[];
            ProjectName: string;
            BranchName: string[];
            GitAccountName: string;
            ContainerRegistryName: string;
            ContainerRepository: string;
            ContainerRepositoryPlaceholder: string;
            EnvNameForCD: string[]
            ChartName: string
            ChartSource: string
            GlobalApplicationName: string
            envNameForCharts: string
            TerminalScript: string[]
            TerminalExpectedValue: string[]
            customCiTag: string[]
            cdFilterConditions: string[]
            cdFilterEnv: string[]
            skopeoData: { [key: string]: string | number; }[]
            PluginPolicy: string;
            ciTestFixedAutoPipeline: string
            resourceBrowserData: AllTypes.BaseCredentils.resourceBrowserParameters;
            registryData: AllTypes.BaseCredentils.RegistryCredentials;
            GitRepoUrlPhp: string;
            securityScanData: AllTypes.BaseCredentils.securityScanData[];
            securityScanHelmAppData: AllTypes.BaseCredentils.securityScanHelmAppData;
            configMapOrSecretData: { [key: string]: string | string[] | boolean }[];
            applicationGroup: string;
            harborRegistry: AllTypes.BaseCredentils.HarborInCaCert;
            userDefinedGitRepo: string[];
            addChartRepo: AllTypes.BaseCredentils.AddChartRepo;
            githubAccountOwner: string;
            gitRepoName: string;
            InclusterEnv: string;
            buildxData: AllTypes.BaseCredentils.buildxConfiguration[]
            RuntimeParametersData: { [key: string]: string | string[] }[];
            virtualData: AllTypes.BaseCredentils.VirtualData;
            VirtualClusterName: string;
            VirtualEnvironment: string;
            notifiationStatusArray: string[];
            notificationFilter: NotificationFilter;
            notifiationStatusArraySlack: string[];
            vulnerabilitiesLabel: AllTypes.BaseCredentils.vulnerabilitiesLabel;
            securitySidebarImageScan: string[];
            securitySidebarCodeScan: string[];
            securitySidebarKubernetesManifest: string[];
            securityPolicyFilter: AllTypes.BaseCredentils.securityPolicyFilter;
            ephimeralContainer: ephemeralContainers.ephemeralContainer[]
            globalCMCSdata: { name: string, data: { key: string, value: string }, configOrSecret: "CONFIGMAP" | "SECRET", cicd: 'CI/CD' | 'CD' | 'CI' }[]
            ciCdNodeTermination: string

        }
    }
    export namespace ephemeralContainers {
        let imageType = ['Ubuntu: Kubernetes utilites', 'Alpine: Kubernetes utilites'] as const;
        export type ephemeralContainerImageType = (typeof imageType)[number];
        export interface ephemeralContainer {
            containerNamePrefix: string,
            containerImage: ephemeralContainerImageType
            isSuccessfull: boolean
            targetContainerName: string
        }
    }
    export namespace fixtures {
        export interface allPageObjects {
            globalSearchComponent: GlobalSearchComponent
            ssoLoginServicesPage: SsoLoginServicesPage
            permissionGroupPage: permissionGroupsPage
            chartStoreAppDetailsPage: ChartStoreAppDetailsPage,
            chartStoreDeploymentHistoryy: chartStoreDeploymentHistory;
            deployChartPage: DeployChartPage;
            chartStorePage: ChartStorePage;
            navbarPage: NavbarPage
           // globalConfigPage: GlobalConfigurationPage;
            createAppPage: CreateAppPage;
            appConfigurationPage: AppConfigurationPage;
            baseDeploymentTemplatePage: BaseDeploymentTemplatePage;
            buildConfigurationPage: BuildConfigurationPage;
            buildHistoryPage: BuildHistoryPage;
            gitRepositoryPage: GitRepositoryPage;
            workflowPage: WorkflowPage;
            resourceBrowserPage: ResourceBrowserPage;
            clusterAndEnvironmentsPage: ClusterAndEnvironments;
            deploymentHistory: DeploymentHistoryPage
            appDetailsPageCiCd: CiCdAppDetailsPage
            gitopsConfigurationPage: GitopsConfigurationPage;
            externalCIPage: ExternalCIPage;
            containerRegistryPage: ContainerRegistryPage;
            prePostCiCd: PrePostCiCd
            filterConditionPage: FilterConditionPage
            mandatoryPluginPage: MandatoryPluginPage;
            notificationsPage: NotificationsConfig
            apiTokenPage: ApiTokenPage
            jobsPage: JobsPage
            buildInfraPage: BuildInfra
            scopedVariablePage: ScopedVariablePage;
            applicationGroupPage: ApplicationGroupPage;
            imagePullDigest: ImagePullDigest;
            tagPropagatonPage: TagPropagation;
            addChartRepo: AddChartRepo;
            buildSourceTagAndPR: BuildSourceTagAndPR;
            lockConfig: LockConfiguration;
            rollback: Rollback;
            globalTag: GlobalTag;
            projectPage: ProjectPage
            userPermissionPage: UserPermissionPage
            catalogFrameWorkPage: CatalogFrameworkPage
            overViewPage: OverviewPage
            securityGlobalPage: SecurityGlobalPage
            installationPage: Installation
            releaseHubPage: ReleaseHub
            releaseOverviewPage: ReleaseOverviewPage
            releaseTrackConfigurePage: ReleaseTrackConfigurePage
            requirementPage: Requirements
            rolloutPage: Rollout
            rolloutHistoryPage: RolloutHistoryPage
            tenantsPage: Tenants
            releaseConfigurationPage: ReleaseConfigurationPage
            approvalPolicyPage: ApprovalPolicyPage
            externalLinksPage: ExternalLinks
            applicationTemplatePage: ApplicationTemplatePage
            imagePromotionPage: ImagePromotion
            deploymentWindowPage: DeploymentWindow
            deploymentChartPage: DeploymentCharts
            productDashboardPage: ProductDashboardPage
            licenseDashboardPage: LicenseDashboardPage
        }
    }
    export namespace Rbac {
        export interface devtronAppObjectCreation {
            permissionDetails: AllTypes.apiTokenPage.apiTokenObject
            accessibility: acessibilityDevtronApps
        }
        interface passFailCriteriaObject {
            appName: string
            envName: string
            isEligible: boolean
            isTriggerPageVisible?: boolean
        }
        export interface acessibilityDevtronApps {
            create?: {
                isEligible: boolean
                projectNameNotVisible?: string
            }
            edit?: {
                passFailCriteria: passFailCriteriaObject[]
            }
            trigger?: {
                passFailCriteria: passFailCriteriaObject[]
            }
            appGroup?: {
                passFailCriteria: passFailCriteriaObject[]
            }
            delete?: {
                passFailCriteria: passFailCriteriaObject[]
            }
            appLevelConfig?: {
                appName: string
                isEligible: boolean
            }[]
            configApprover?: passFailCriteriaObject[]
            userPermissions?: {
                isPageVisible: boolean
                superAdminPermissionGroupName?: string
                secondApiTokenName?: string
            }
            visibilityOfPages?: {
                pageName: string
                isVisible: boolean
            }
            appListing?: {
                appName: string
                isVisible: boolean
            }[]
            appDetails?: {
                terminalConnection: boolean
                downloadLogs: boolean
                appName: string
                envName: string
            }[]

        }
        export interface helmAppDataCreation {
            eligibleForCreationOfDevtronApps?: boolean
            permissionDetails: AllTypes.apiTokenPage.apiTokenObject
            accessibility: helmAppsAccessibility

        }
        export interface helmAppsAccessibility {
            create?: { isEligible: boolean }
            edit?: { appName: string, isEligible: boolean }[]
            delete?: { appName: string, isEligible: boolean }[]
            appListing?: { appName: string, isEligible: boolean }[]
            appDetails?: {
                terminalConnection: boolean
                downloadLogs: boolean
                appName: string
            }[]
        }
        export interface jobsDataCreation {
            permissionDetails: AllTypes.apiTokenPage.apiTokenObject
            accessibility: jobsAcessibility
        }
        export interface jobsAcessibility {
            run?: { jobName: string, isEligible: boolean }[]
            edit?: { jobName: string, isEligible: boolean }[]
            jobListing?: { jobName: string, isVisible: boolean }[]
            workflowListing?: { jobName: string, workflowVisibility: boolean }[]
        }
        export interface permissionDetailsForK8s {
            cluster: string, namespace: string, api: string, kind: string, resource: string, status: string, role: string
        }
        export interface dataCreationForK8s {
            permissionDetails: permissionDetailsForK8s
            acessibility: k8sAcessibility
        }
        export interface k8sAcessibility {
            visibilityOfResources?: {
                resourceType: string
                isResourceTypeVisible: boolean
                resourceName?: string
                isResourceNameVisible?: boolean
            }[]
            isUnAuthorizedPageVisible?: boolean
            nodeActions?: {
                cordon: boolean
                drain: boolean
                taints: boolean
            }
            resourceLevelOperations?: {
                resourceType: string
                resourceName: string
                delete?: { isEligible: boolean }
                manifest?: { isEligible: boolean }
                terminal?: { isEligible: boolean }
            }[]
        }
        export interface dataForAcessForChartGroups {
            create: boolean,
            editForSpecificChartNames?: string[]
        }
        export interface acessibilityForChartGroups {
            createChartGroup: { isEligible: boolean },
            editChartGroup: { chartGroupName: string, isEligible: boolean }
        }


    }

    export namespace mandatoryPlugin {
        export interface mandatoryPlugin {
            profileCreationData: { isCi: boolean, plugins: { pluginName: string, stage: string }[], consequences: string },
            applyingProfile: { applyingType: string, matchCriteriaData?: { Project: string, Application: string, Cluster: string, Environment: string } }
            viewNonCompliancePipelinesApp?: string
            affectedPipelinesDetails: { appName: string, impactedCiCdNumber: number, nonImpactedCiCdNumber: number }[]
        }
    }
    export namespace downloadLogs {
        export interface downloadLogsData {
            valueToSelectFromDropdown: 'Last 15 minutes' | "Last 30 minutes" | "Last 1 hour" | "Last 2 hours" | "5,000 lines" | "1,000 lines" | "500 lines" | "10,000 lines" | "Custom...",
            customDataSet?: setDuration | setLines | particularDateAndTime | AllAvailableLogs

        }
        export interface setDuration {
            'Set duration': {
                value: string,
                unit: 'Minutes' | 'Hours'
                checkValidation: boolean
                isSuccessfull: boolean
            }
        }
        export interface setLines {
            'Set lines': {
                value: string,
                checkValidationo: boolean,
                isSuccessfull: boolean
            }
        }
        export interface particularDateAndTime {
            'Since date & time': {
                month: string,
                year: string,
                date: string,
                time: string,
                checkDisability?: boolean
                isSuccessfull: boolean
            }
        }
        export interface AllAvailableLogs {
            'All available': {
                isSuccessfull: boolean
            }
        }
    }
    export namespace BuildInfra {
        export const filterType = ['Runner configuration', 'linux/arm64', 'linux/amd64', 'linux/arm/v7'] as const;
        export type RunnerOrTargetPlatformFilterType = (typeof filterType)[number];
        export interface configurationAppliedOnAProfile {
            cpu?: { value: string, overOrInh?: string },
            memory?: { value: string, overOrInh?: string },
            timeout?: { value: string, overOrInh?: string },
            'node selector'?: { value: string, overOrInh?: string },
            tolerance?: { value: string, overOrInh?: string },
            cm?: { value: string, overOrInh?: string },
            cs?: { value: string, overOrInh?: string }

        }
        export interface editOrCreateProfile {
            profileName: string,
            description?: string,
            runnerConfiguration?: {
                buildConfiguration?: buildConfiguration,
                cmcsConfiguration?: cmcsConfiguration[]
            }
            k8sDriverConfiguration?: k8sDriverConfiguration[]


        }
        export interface k8sDriverConfiguration {
            turnOn: boolean,
            targetPlatform?: string,
            configuration?: buildConfiguration
        }
        export interface buildConfiguration {
            cpu?: {
                isInherited: boolean,
                request?: {
                    value: string,
                    units: 'm' | 'Core'
                }
                limit?: {
                    value: string,
                    units: 'm' | 'Core'
                }
            },
            memory?: {
                isInherited: boolean,
                request?: {
                    value: string,
                    units: 'Mi' | 'G'
                }
                limit?: {
                    value: string,
                    units: 'Mi' | 'G'
                }
            }
            timeout?: {
                isInherited: boolean,
                value: string,
                units: 'Seconds' | 'Minutes' | "Hours"
            }
            'node selector'?: {
                isInherited: boolean,
                key: string,
                value: string
            }
            tolerance?: {
                isInherited: boolean,
                key?: string,
                value?: string
            }
        }
        export interface cmcsConfiguration {
            isInherited: boolean,
            cmOrSecret: 'ConfigMaps' | "Secret",
            name: string,
            type: "Kubernetes ConfigMap" | "Kubernetes External ConfigMap" | "Kubernetes Secret" | "Mount Existing Kubernetes Secret",
            configuration?: {
                key: string,
                value: string,
                MountPath?: string
            }
            subType: "Environment Variable" | "Data Volume"
        }
        export interface dataSetForBuildInfra {
            editProfiles: editOrCreateProfile[],
            validationCheck: { filterType: RunnerOrTargetPlatformFilterType, config: configurationAppliedOnAProfile }[]
            valuesToCheckInLogs: string[]
            valuesToCheckInManifest: {
                runnerManifest?: string[],
                'linux/arm64'?: string[],
                'linux/amd64'?: string[],
                'linux/arm/v7'?: string[]
            }
            platformDuringBuild: string[] | undefined
            isCiFailed: boolean
        }
    }
    export namespace appConfiguration {
        export type addCdModule = { envNameForCd: string, helmOrGitops: string, clusterName: string, deploymentStrat: 'ROLLING' | 'RECREATE', autoOrManual: 'Auto' | "Manual" } | { envNameForCd: string, clusterName: string, virtualEnvConfig: { pushOrNot: 'Do not push' } | { pushOrNot: 'Push to registry', regName: string, repoName: string }, deploymentStrat: 'ROLLING' | 'RECREATE', autoOrManual: 'Auto' | "Manual" }
    }
    export namespace deploymentWindow {
        export interface dwConfigForOnceAndDaily {
            type: 'fixed' | 'daily'
            fromDate?: { month: string, year: string, date: string }
            toDate?: { month: string, year: string, date: string }
            fromTime: string
            toTime: string
        }
        export interface dwConfigForWeekly extends Omit<dwConfigForOnceAndDaily, 'type'> {
            type: 'weekly'
            dayOfWeek: string
        }
        export interface dwConfigForWeeklyAndMonthlyRange extends Omit<dwConfigForOnceAndDaily, 'type'> {
            type: 'weekly (range)' | 'monthly'
            fromDay: string,
            toDay: string
        }



    }


}
