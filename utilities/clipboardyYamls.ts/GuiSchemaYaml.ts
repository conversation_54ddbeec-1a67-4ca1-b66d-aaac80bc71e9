export function yamlForCustomGuiSchema() {
    return `
  {
  "type": "object",
  "properties": {
    "ContainerPort": {
      "type": "array",
      "description": "Defines ports on which application services will be exposed to other services",
      "items": {
        "type": "object",
        "properties": {
          "port": {
            "type": "integer",
            "description": "Port at which your application will run"
          }
        }
      },
      "title": "Container Port"
    },
    "Limits": {
      "type": "object",
      "description": "Maximum RAM and CPU available to the application",
      "required": [
        "CPU",
        "Memory"
      ],
      "properties": {
        "CPU": {
          "type": "string",
          "description": "Maximum allowed CPU limit",
          "updatePath": "/resources/limits/cpu"
        },
        "Memory": {
          "type": "string",
          "description": "Maximum allowed memory limit",
          "updatePath": "/resources/limits/memory"
        }
      }
    }
  }
}
    `
}