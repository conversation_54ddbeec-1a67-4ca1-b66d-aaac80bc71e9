/**
 * Returns a YAML configuration for Kubernetes resource requirements
 * @param {string} cpuLimit - CPU limit (e.g. "0.05")
 * @param {string} memoryLimit - Memory limit (e.g. "50Mi")
 * @param {string} cpuRequest - CPU request (e.g. "0.01")
 * @param {string} memoryRequest - Memory request (e.g. "10Mi")
 * @returns {string} The resources YAML configuration
 */
export function getResourcesYaml(
  cpuLimit: string,
  memoryLimit: string,
  cpuRequest: string,
  memoryRequest: string
): string {
  return `resources:
  limits:
    cpu: "${cpuLimit}"
    memory: ${memoryLimit}
  requests:
    cpu: "${cpuRequest}"
    memory: ${memoryRequest}`;
}