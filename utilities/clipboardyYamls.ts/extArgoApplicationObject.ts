import clipboardy from 'clipboardy';

/**
 * Creates an Argo CD Application YAML and writes it to clipboard
 * @param {string} namespace - The namespace to use in the application configuration
 * @returns {Promise<void>}
 */
export async function validArgoApplicationObject(namespace: string): Promise<void> {
  const argoAppYaml = `apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: argodashapp
  namespace: argocd
spec:
  destination:
    namespace: ${namespace}
    server: https://kubernetes.default.svc
  project: default
  source:
    helm:
      valueFiles:
        - _14-values.yaml
    path: deployment-chart_4-20-0/4.20.1
    repoURL: https://github.com/mayankgitops/extwithgitops.git
    targetRevision: master
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true
status:
  controllerNamespace: argocd
  health:
    lastTransitionTime: 2025-06-02T07:14:20Z
    status: Healthy
  history:
    - deployStartedAt: 2025-06-02T07:13:13Z
      deployedAt: 2025-06-02T07:13:15Z
      id: 0
      initiatedBy:
        automated: true
      revision: c27dfb04d41a9e80372145ee8d3099bfedfda3e7
      source:
        helm:
          valueFiles:
            - _14-values.yaml
        path: deployment-chart_4-20-0/4.20.1
        repoURL: https://github.com/mayankgitops/extwithgitops.git
        targetRevision: master
  operationState:
    finishedAt: 2025-06-02T07:13:15Z
    message: successfully synced (all tasks run)
    operation:
      initiatedBy:
        automated: true
      retry:
        limit: 5
      sync:
        revision: c27dfb04d41a9e80372145ee8d3099bfedfda3e7
        syncOptions:
          - CreateNamespace=true
    phase: Succeeded
    startedAt: 2025-06-02T07:13:13Z
    syncResult:
      resources:
        - group: ""
          hookPhase: Running
          kind: Namespace
          message: namespace/${namespace} created
          name: ${namespace}
          namespace: ""
          status: Synced
          syncPhase: PreSync
          version: v1
        - group: ""
          hookPhase: Running
          kind: Service
          message: service/argodashapp-extwithgitops-service created
          name: argodashapp-extwithgitops-service
          namespace: ${namespace}
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/argodashapp-extwithgitops created
          name: argodashapp-extwithgitops
          namespace: ${namespace}
          status: Synced
          syncPhase: Sync
          version: v1
      revision: c27dfb04d41a9e80372145ee8d3099bfedfda3e7
      source:
        helm:
          valueFiles:
            - _14-values.yaml
        path: deployment-chart_4-20-0/4.20.1
        repoURL: https://github.com/mayankgitops/extwithgitops.git
        targetRevision: master
  reconciledAt: 2025-06-02T07:13:15Z
  resources:
    - health:
        status: Healthy
      kind: Service
      name: argodashapp-extwithgitops-service
      namespace: ${namespace}
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: argodashapp-extwithgitops
      namespace: ${namespace}
      status: Synced
      version: v1
  sourceHydrator: {}
  sourceType: Helm
  summary:
    images:
      - quay.io/devtron/test:21609cce-31-53
  sync:
    comparedTo:
      destination:
        namespace: ${namespace}
        server: https://kubernetes.default.svc
      source:
        helm:
          valueFiles:
            - _14-values.yaml
        path: deployment-chart_4-20-0/4.20.1
        repoURL: https://github.com/mayankgitops/extwithgitops.git
        targetRevision: master
    revision: c27dfb04d41a9e80372145ee8d3099bfedfda3e7
    status: Synced`;

  // Write the YAML to clipboard
  await clipboardy.write(argoAppYaml);
}


export async function validArgoApplicationObject1(): Promise<void> {
  const argoAppYaml = `apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: validautomation-1
  namespace: argocd
spec:
  destination:
    namespace: autons-759
    server: https://kubernetes.default.svc
  project: default
  source:
    helm:
      valueFiles:
        - _13-values.yaml
    path: deployment-chart_4-20-0/4.20.1
    repoURL: https://github.com/mayankgitops/forvalidauto-1.git
    targetRevision: master
  syncPolicy:
    automated: {}
    syncOptions:
      - CreateNamespace=true
status:
  controllerNamespace: argocd
  health:
    lastTransitionTime: 2025-06-20T02:31:32Z
    status: Healthy
  history:
    - deployStartedAt: 2025-06-20T02:30:25Z
      deployedAt: 2025-06-20T02:30:27Z
      id: 0
      initiatedBy:
        automated: true
      revision: 123844a74fea6e47725a58fbe248dfa05c32a802
      source:
        helm:
          valueFiles:
            - _13-values.yaml
        path: deployment-chart_4-20-0/4.20.1
        repoURL: https://github.com/mayankgitops/forvalidauto-1.git
        targetRevision: master
  operationState:
    finishedAt: 2025-06-20T02:30:27Z
    message: successfully synced (all tasks run)
    operation:
      initiatedBy:
        automated: true
      retry:
        limit: 5
      sync:
        revision: 123844a74fea6e47725a58fbe248dfa05c32a802
        syncOptions:
          - CreateNamespace=true
    phase: Succeeded
    startedAt: 2025-06-20T02:30:25Z
    syncResult:
      resources:
        - group: ""
          hookPhase: Running
          kind: Namespace
          message: namespace/autons-759 created
          name: autons-759
          namespace: ""
          status: Synced
          syncPhase: PreSync
          version: v1
        - group: ""
          hookPhase: Running
          kind: Service
          message: service/validautomation-1-forvalidauto-1-service created
          name: validautomation-1-forvalidauto-1-service
          namespace: autons-759
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/validautomation-1-forvalidauto-1 created
          name: validautomation-1-forvalidauto-1
          namespace: autons-759
          status: Synced
          syncPhase: Sync
          version: v1
      revision: 123844a74fea6e47725a58fbe248dfa05c32a802
      source:
        helm:
          valueFiles:
            - _13-values.yaml
        path: deployment-chart_4-20-0/4.20.1
        repoURL: https://github.com/mayankgitops/forvalidauto-1.git
        targetRevision: master
  reconciledAt: 2025-06-20T02:41:02Z
  resources:
    - health:
        status: Healthy
      kind: Service
      name: validautomation-1-forvalidauto-1-service
      namespace: autons-759
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: validautomation-1-forvalidauto-1
      namespace: autons-759
      status: Synced
      version: v1
  sourceHydrator: {}
  sourceType: Helm
  summary:
    images:
      - quay.io/devtron/test:21609cce-210-76
  sync:
    comparedTo:
      destination:
        namespace: autons-759
        server: https://kubernetes.default.svc
      source:
        helm:
          valueFiles:
            - _13-values.yaml
        path: deployment-chart_4-20-0/4.20.1
        repoURL: https://github.com/mayankgitops/forvalidauto-1.git
        targetRevision: master
    revision: 123844a74fea6e47725a58fbe248dfa05c32a802
    status: Synced
`;

  // Write the YAML to clipboard
  await clipboardy.write(argoAppYaml);
}