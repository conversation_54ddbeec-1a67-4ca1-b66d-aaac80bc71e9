import yaml from "js-yaml"; // Ensure this module is installed

import clipboardy from "clipboardy"; // Ensure this module is installed

export async function createValidApplicationYamlToClipboard({
  includeNames = [],
  envIds = [],
  global = false,
  deploymentPatch = { addPath: "/defaultAddPath", replacePath: "/defaultReplacePath", value: "defaultValue" },
  configMapPatch = { addPath: "/defaultCMAddPath", replacePath: "/defaultCMReplacePath", key: "defaultKey", value: "defaultValue" },
  secretPatch = { addPath: "/defaultSecretAddPath", replacePath: "/defaultSecretReplacePath", key: "defaultKey", value: "defaultValue" },
  configMapNames = [],
  secretNames = [],
}: {
  includeNames: string[];
  envIds: number[];
  global: boolean;
  deploymentPatch: { addPath: string; replacePath: string; value: string | number };
  configMapPatch: { addPath: string; replacePath: string; key: string; value: string | number };
  secretPatch: { addPath: string; replacePath: string; key: string; value: string | number };
  configMapNames: string[];
  secretNames: string[];
}) {
  // Prepare the YAML object structure
  const yamlObject = {
    apiVersion: "batch/v1beta1",
    kind: "Application",
    spec: {
      includes: {
        names: includeNames,
      },
      envIds: envIds,
      global: global,
      deploymentTemplate: {
        spec: {
          patchJson: JSON.stringify([
            { op: "replace", path: deploymentPatch.replacePath, value: deploymentPatch.value },
          ]),
        },
      },
      configMap: {
        spec: {
          names: configMapNames,
          patchJson: JSON.stringify([
            { op: "add", path: configMapPatch.addPath, value: configMapPatch.value },
            { op: "replace", path: configMapPatch.replacePath, value: configMapPatch.value },
          ]),
        },
      },
      secret: {
        spec: {
          names: secretNames,
          patchJson: JSON.stringify([
            { op: "add", path: secretPatch.addPath, value: secretPatch.value },
            { op: "replace", path: secretPatch.replacePath, value: secretPatch.value },
          ]),
        },
      },
    },
  };

  // Convert the object to YAML with proper formatting
  const yamlString = yaml.dump(yamlObject, {
    lineWidth: -1, // Prevent line breaks in long strings
    quotingType: "'", // Use single quotes for strings
    styles: {
      "!!seq": "block", // Ensure array elements are properly indented
    },
  });

  try {
    // Copy the YAML string to clipboard
    await clipboardy.write(yamlString);
  } catch (error) {
    console.error("Error copying YAML data to clipboard:", error);
  }
}
