import clipboardy from "clipboardy";

export function customHelmCatalogTemplate() {
  const result = `{
  "type": "object",
  "required": [
    "Owners & Pager Duty"
  ],
  "properties": {
    "Documentation": {
      "type": "object",
      "properties": {
        "API Contract": {
          "type": "string",
          "format": "uri"
        },
        "Service Documentation": {
          "type": "string",
          "format": "uri"
        }
      }
    },
    "Service details": {
      "type": "object",
      "properties": {
        "Language": {
          "type": "array",
          "items": {
            "enum": [
              "Java",
              "Python",
              "PHP",
              "Go",
              "Ruby",
              "Node"
            ],
            "type": "string"
          },
          "uniqueItems": true
        },
        "Framework": {
          "type": "array",
          "items": {
            "enum": [
              "Django",
              "Ruby on Rails",
              "Laravel",
              "Angular",
              "React",
              "jQuery",
              "ASP.NET Core",
              "Bootstrap"
            ],
            "type": "string"
          },
          "uniqueItems": true
        },
        "Internet facing": {
          "type": "boolean"
        },
        "Communication method": {
          "enum": [
            "GraphQL",
            "gRPC",
            "Message Queue",
            "NATS",
            "REST API",
            "WebSocket"
          ],
          "type": "string"
        }
      }
    },
    "Owners & Pager Duty": {
      "type": "object",
      "required": [
        "Code owners"
      ],
      "properties": {
        "Code owners": {
          "type": "array",
          "items": {
            "type": "object",
            "refType": "#/references/users"
          },
          "minItems": 1,
          "uniqueItems": true
        },
        "On pager duty": {
          "type": "object",
          "refType": "#/references/users"
        }
      }
    }
  }
}`
  return result;
}

export function ldapSsoCreds(hostUrl: string, password: string) {
  return `host: ${hostUrl}
insecureNoSSL: true
insecureSkipVerify: true
bindDN: cn=admin,dc=example,dc=com
bindPW: ${password}
usernamePrompt: Email Address
userSearch:
  baseDN: ou=people,dc=example,dc=com
  filter: (objectClass=person)
  username: mail
  idAttr: cn
  emailAttr: mail
  nameAttr: cn
  preferredUsernameAttr: cn
groupSearch:
  baseDN: ou=groups,dc=example,dc=com
  filter: (objectClass=groupOfNames)
  userMatchers:
    - userAttr: DN
      groupAttr: member
  nameAttr: cn`
}


export function ephimeralContainerAdvancedConfigurations(targetContainerName: string, prefixContainerName?: string, image?: 'Ubuntu: Kubernetes utilites' | "Alpine: Kubernetes utilites") {
  let prefixContainerNameToEnter: string = prefixContainerName || 'debugger';
  let imageToEnter: string = image || 'quay.io/devtron/ubuntu-k8s-utils:latest';
  let imageMapping = {
    'Ubuntu: Kubernetes utilites': 'quay.io/devtron/ubuntu-k8s-utils:latest',
    'Alpine: Kubernetes utilites': 'quay.io/devtron/alpine-k8s-utils:latest',
    'CentOS: Kubernetes utilities': 'quay.io/devtron/alpine-k8s-utils:latest'
  }
  return `
name: ${prefixContainerNameToEnter}
image: ${imageMapping[imageToEnter]}
targetContainerName: ${targetContainerName}
stdin: true
tty: true
`
}
export let monthMappingForCalendar: { [key: string]: number } = {
  'January': 1,
  'February': 2,
  'March': 3,
  'April': 4,
  'May': 5,
  'June': 6,
  'July': 7,
  'August': 8,
  'September': 9,
  'October': 10,
  'November': 11,
  'December': 12
}
