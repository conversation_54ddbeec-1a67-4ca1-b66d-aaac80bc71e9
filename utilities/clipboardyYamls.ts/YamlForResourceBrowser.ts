import clipboardy from 'clipboardy';

// Function to prepare YAML data for a Secret resource.

export async function dataForSecret(resourceName: string, namespace: string, userName: string) {
  const yamlsecret = `apiVersion: v1
kind: Secret
metadata:
  name: ${resourceName}
  namespace: ${namespace}
type: Opaque
data:
  username: ${userName}`;
  clipboardy.write(yamlsecret);
}

export async function dataForConfigMap(resourceName: string, keyName: string, valueName: string) {
  const yamlConfigMap = `apiVersion: v1
kind: ConfigMap
metadata:
  name: ${resourceName}
  namespace: automation
data:
  ${keyName}: ${valueName}
`;
  clipboardy.write(yamlConfigMap);

}


// Function to prepare YAML data for a Deployment resource.

export async function dataForDeployment(resourceName: string, namespace: string, replicas: string, containerName: string, containerImage: string) {
  const deploymentYaml = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${resourceName}
  namespace: ${namespace}
  labels: # Added labels section here
    app: ${resourceName}
    environment: production
spec:
  replicas: ${replicas}
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels: # Added labels section here
        app: nginx
        environment: production
    spec:
      containers:
      - name: ${containerName}
        image: ${containerImage}
        ports:
        - containerPort: 80

    `
  clipboardy.write(deploymentYaml);
}
export async function dockerfile() {
  const dockerFileData =
    `FROM golang:1.16 as builder

  # Setup the working directory
  WORKDIR /app
  
  # Add source code
  ADD . /app/
  
  # Build the source
  RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main app.go
  
  
  ################################# Prod Container #################################
  
  # Use a minimal alpine image
  FROM alpine:3.7
  #RUN apt update
  #RUN apt install python
  # Add ca-certificates in case you need them
  RUN apk update && apk add ca-certificates && rm -rf /var/cache/apk/* && apk add python
  # Set working directory
  WORKDIR /root
  #COPY hello.py /root/hello.py
  # Copy the binary from builder
  COPY --from=builder /app/. .
  
  # Run the binary
  CMD ["./main"]`
  await clipboardy.write(dockerFileData);
  return dockerFileData;
}
