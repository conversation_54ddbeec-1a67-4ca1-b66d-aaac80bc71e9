import { Page } from "@playwright/test";
import clipboardy from "clipboardy";

// Function to prepare YAML data for a scoped variable-oss.

export function scopeVariableConfigurationForEnterprise() {
  return `
apiVersion: devtron.ai/v1beta1
kind: Variable
spec:
  - notes: Enter any notes for additional details
    shortDescription: Enter a short description here
    isSensitive: false
    name: KAFKA
    values:
      - category: ApplicationEnv
        value: 33
        selectors:
          attributeSelectors:
            ApplicationName: playwright-scope-test
            EnvName: devtron-demo
      - category: Application
        value: 13
        selectors:
          attributeSelectors:
            ApplicationName: playwright-scope-test
      - category: Env
        value: 23
        selectors:
          attributeSelectors:
            EnvName: automation
      - category: Cluster
        value: Cluster-8080
        selectors:
          attributeSelectors:
            ClusterName: prod-cluster
      - category: Global
        value: global
  - notes: Enter any notes for additional details
    shortDescription: Enter a short description here
    isSensitive: false
    name: object
    values:
      - category: Global
        value:
          - name: test1
            value: value1
  - notes: Enter any notes for additional details
    shortDescription: Enter a short description here
    isSensitive: true
    name: sensitive
    values:
      - category: Global
        value: automtest

  `
}

export function scopeVariableConfigurationForOss() {
  return `
apiVersion: devtron.ai/v1beta1
kind: Variable
spec:
  - notes: Enter any notes for additional details
    shortDescription: Enter a short description here
    isSensitive: false
    name: KAFKA
    values:
      - category: Global
        value: 71
  - notes: Enter any notes for additional details
    shortDescription: Enter a short description here
    isSensitive: true
    name: sensitive
    values:
      - category: Global
        value: 61
  `
}