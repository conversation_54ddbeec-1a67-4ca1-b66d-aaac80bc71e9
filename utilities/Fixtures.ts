import { test as base, expect, Page } from '@playwright/test';
import { BaseTest } from "./BaseTest";
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
import { AllTypes } from './Types';
import { ApiUtils } from './ApiUtils';
import { DevtronAllChartVersion, DevtronDeploymentChart } from '../enums/Application Management/Configurations/DeploymentChartsEnum/DeploymentTemplateEnum';
import { createAllObjects } from './CreateObjectsForAllPomClasses';


// we are creating fixtures here , defining their type
export type pomObjects = {
    // define all objects here
    AllObjects: AllTypes.fixtures.allPageObjects
    externalHelmAppCreation: { externalAppName: string }
    devtronAppCreation: {
        number_Workflow: number
        applicationUrl: string[]
        imageBuilt: string[] | null
        appNames: string[]
    }
    repoNumber: number
    isdevtronAppRequired: boolean
    devtronApps: string[]
    devtronEnvs: string[]
    newEnvToCreate: string[]
    numberOfJobs: number
    verifyCdStatus: (string | undefined)[]
    containerRegistryName: string
    containerRepository: string
    triggerCI: boolean[]
    deploymentTemplateChart: (DevtronDeploymentChart | string)[];
    allChartVersion: (DevtronAllChartVersion | string)[];
    helmAppCreation: {
        helmAppUrl: string
        helmAppName: string
    }
    dataForHelmApp: {
        chartName: string,
        chartSource: string,
        projectName: string,
        envName: string
    }
    jobCreation: {
        jobUrl: string,
        jobsNames: string[]
        workflowNames: string[]
        piplineNamesCreated: string[]

    }
    jobRelatedData: {
        customScript: string[]
    }
    virtualEnvCreation: string
    fixWithAI: void
    createExternalCiWorkflow: { nodeId: number[] }
    triggerExternalCi: boolean
    externalCiWorkflowEnvName: string
    chartName: string[],
    chartRefId: string[],
    instantiatePomObjectsForNonSuperAdminPage: { objects: AllTypes.fixtures.allPageObjects, nonSuperAdminPage: Page }
    nonSuperAdminPage: Page | undefined
    instantiatePomObjectsForSuperAdminPage: { objects: AllTypes.fixtures.allPageObjects, SuperAdminPage: Page }
    jobName: string[]

}

export const test = base.extend<pomObjects>({


    repoNumber: 0,
    jobName: ['job' + BaseTest.generateRandomStringWithCharsOnly(4)],
    nonSuperAdminPage: undefined,
    isdevtronAppRequired: false,
    numberOfJobs: 1,
    containerRegistryName: credentials.ContainerRegistryName,
    containerRepository: credentials.ContainerRepository,
    newEnvToCreate: [credentials.VirtualEnvironment],
    triggerCI: [true],
    devtronApps: ["ui-autom" + BaseTest.generateRandomStringWithCharsOnly(4)],
    devtronEnvs: ["automation"],
    verifyCdStatus: [undefined],
    triggerExternalCi: true,
    externalCiWorkflowEnvName: 'automation',
    deploymentTemplateChart: [DevtronDeploymentChart.Deployment],
    allChartVersion: [DevtronAllChartVersion['4.21.0']],
    dataForHelmApp: {
        chartName: 'memcached',
        chartSource: credentials.ChartSource,
        projectName: 'devtron-demo',
        envName: 'automation'
    },
    jobRelatedData: {
        customScript: ['echo "devtron-testing']
    },
    AllObjects: async ({ page }, use) => {
        let objectsOfAllPages = createAllObjects(page);
        await use(objectsOfAllPages);
    },
    //DeploymentChart Carries ChartName and RolloutDeploymentVersion carries its version.
    devtronAppCreation: async ({ page, repoNumber, devtronEnvs, devtronApps, triggerCI, verifyCdStatus, request, deploymentTemplateChart, allChartVersion }, use) => {
        if (!process.env.clusterType?.includes('ea')!) {
            test.setTimeout(18 * 1000 * 60);
            let appNames: string[] = devtronApps;
            let number_Workflow: number = 1;
            let applicationUrl: string[] = [];
            let imageBuilt: string[] = [];
            let apiUtils = new ApiUtils(request);
            let apiToken: string = await apiUtils.login(process.env.PASSWORD!);
            for (var i = 0; i < devtronApps.length!; i++) {
                let appId;
                await apiUtils.createDevtronApp(apiToken, { appName: devtronApps[i], regName: credentials.ContainerRegistryName, repoName: credentials.ContainerRepository, projName: credentials.ProjectName, envName: devtronEnvs, repoUrl: process.env.GIT_REPO_URL?.split(',')[repoNumber]! }, deploymentTemplateChart[i], allChartVersion[i]);
                appId = await apiUtils.getAppIdFromAppName(devtronApps[i], apiToken);
                console.log('printing ci value');
                console.log(triggerCI[i]);
                if (triggerCI[i] == true) {
                    await apiUtils.triggerCi(devtronApps[i], apiToken, 0);
                }
                applicationUrl.push(process.env.BASE_SERVER_URL! + `/app/${appId}/edit/workflow`);
            }
            for (let i = 0; i < devtronApps.length; i++) {
                if (triggerCI[i] == true) {
                    imageBuilt.push(await apiUtils.getArtifactGeneratedAfterCiSuccess(apiToken, 0, 0, devtronApps[i]));
                }
                for (let j = 0; j < verifyCdStatus.length; j++) {
                    if (verifyCdStatus[j] != undefined && triggerCI[i] == true) {
                        await apiUtils.verifyAndWaitForParticularStatusOfCiCd(devtronApps[j], apiToken, j, verifyCdStatus[j]!, false)
                    }
                }
            }
            await page.goto(applicationUrl[0]);
            await use({ number_Workflow, applicationUrl, imageBuilt, appNames });
        }
        else {
            await use({ number_Workflow: 0, applicationUrl: [''], imageBuilt: [''], appNames: [] });
        }


    },
    helmAppCreation: async ({ AllObjects, dataForHelmApp, page }, use) => {
        test.setTimeout(15 * 60 * 1000);
        let appName = BaseTest.generateRandomStringWithCharsOnly(4);
        await AllObjects.globalConfigPage.clickingOncharStore(process.env.BASE_SERVER_URL as string);
        await AllObjects.chartStorePage.SelectingChart(dataForHelmApp.chartName, dataForHelmApp.chartSource);
        // Step 3: Entering details and deploying the chart
        await AllObjects.deployChartPage.deployingChart(appName, dataForHelmApp.projectName, dataForHelmApp.envName, "helm");
        await AllObjects.chartStoreAppDetailsPage.verifyApplicationStatus();
        let appUrl = page.url();
        await use({ helmAppUrl: appUrl, helmAppName: appName });
    },

    jobCreation: async ({ AllObjects, page, jobRelatedData, numberOfJobs, jobName  }, use) => {
        test.setTimeout(12 * 1000 * 60);
        let jobsNames: string[] = jobName;
        let workflowNames: string[] = [];
        let jobUrl: string = "";
        let pipelineNamesCreated: string[] = [];
        for (let i = 0; i < numberOfJobs; i++) {
            await page.goto(process.env.BASE_SERVER_URL!);
            await AllObjects.createAppPage.createCustomAppJob("job", jobName[i], credentials.ProjectName, process.env.BASE_SERVER_URL as string)
            // Add a GitHub repository
            await AllObjects.gitRepositoryPage.addGitHubRepository(process.env.GIT_REPO_URL?.split(',')[0] as string, credentials.GitAccountName);
            // Create a workflow
            let workflowName: string = "workflow-" + BaseTest.generateRandomStringWithCharsOnly(3);
            workflowNames.push(workflowName)
            await AllObjects.jobsPage.createWorkflow(workflowName);
            jobUrl = page.url();
            // Create a pipeline
            let pipelineName = "pipeline-" + BaseTest.generateRandomStringWithCharsOnly(3);
            pipelineNamesCreated.push(pipelineName);
            await AllObjects.jobsPage.createPipeline(pipelineName, credentials.BranchName[0]);
            await AllObjects.jobsPage.clickOnJobNode();
            await expect(async () => {
                // Add a pre/post task
                await AllObjects.prePostCiCd.addPrePostTask("pre", "execute");
                // Execute a custom script
                await AllObjects.jobsPage.executeCustomScript("task-name-" + BaseTest.generateRandomStringWithCharsOnly(3), jobRelatedData.customScript);
                await AllObjects.jobsPage.clickOnJobNode();
                await expect(page.locator('//*[text()="main"]')).toBeVisible({ timeout: 20000 });
            }).toPass({ timeout: 4 * 1000 * 60 });
            await AllObjects.jobsPage.createPipelineButton.click();
        }
        await use({ jobUrl, jobsNames, workflowNames, piplineNamesCreated: pipelineNamesCreated });
    },
    externalHelmAppCreation: async ({ AllObjects }, use) => {
        await AllObjects.resourceBrowserPage.goToResourceBrowser(process.env.BASE_SERVER_URL as string);
        await AllObjects.resourceBrowserPage.goToCluster('default_cluster');
        let chartName = BaseTest.generateRandomStringWithCharsOnly(4);
        await AllObjects.resourceBrowserPage.executeCommandsInDefaultTerminal([{ commandToExecute: 'helm repo add bitnami https://charts.bitnami.com/bitnami', resultOfCommandToVerify: '"bitnami" has been added to your repositories', count: 1 },
        { commandToExecute: `helm install ${chartName} bitnami/memcached -n default`, resultOfCommandToVerify: "REVISION: memcached", count: 0 }
        ]);
        await use({ externalAppName: chartName });
    },
    virtualEnvCreation: async ({ page, AllObjects, newEnvToCreate }, use) => {
        for (let i = 0; i < newEnvToCreate.length; i++) {
            let clusterNameToOperate: string = newEnvToCreate[i] == credentials.VirtualEnvironment ? credentials.VirtualClusterName : "default_cluster";
            console.log('cluster name we get' + clusterNameToOperate);
            console.log(newEnvToCreate[0] + credentials.VirtualEnvironment);
            let envType: string = newEnvToCreate[i] == credentials.VirtualEnvironment ? "virtual" : 'production';
            let clusterType: string = newEnvToCreate[i] == credentials.VirtualEnvironment ? "virtual" : "normal";
            test.setTimeout(5 * 1000 * 60)
            await AllObjects.globalConfigPage.goToGlobalConfigurations(process.env.BASE_SERVER_URL!)
            await AllObjects.clusterAndEnvironmentsPage.goToClustersAndEnvironmentsPage();
            //Adding Virutal Cluster and Env if not added.
            if (!(await AllObjects.clusterAndEnvironmentsPage.isClusterAdded(clusterNameToOperate))) {
                await AllObjects.clusterAndEnvironmentsPage.addVirtualCluster(clusterNameToOperate);
            }
            if (!await AllObjects.clusterAndEnvironmentsPage.searchEnvironment(newEnvToCreate[i], clusterNameToOperate)) {
                await AllObjects.clusterAndEnvironmentsPage.createEnvironment(clusterNameToOperate, clusterType, newEnvToCreate[i], envType);
            }
        }
        await use('');

    },
    createExternalCiWorkflow: async ({ page, devtronApps, devtronEnvs, request, triggerExternalCi, externalCiWorkflowEnvName }, use) => {
        let nodeIds: number[] = [];
        if (!process.env.clusterType?.includes('ea')!) {
            console.log('triggering external ci');
            test.setTimeout(3 * 1000 * 60);
            let apiUtils = new ApiUtils(request);
            let token = await apiUtils.login(process.env.PASSWORD!);
            for (let key of devtronApps) {
                let appId = await apiUtils.getAppIdFromAppName(key, token);
                await apiUtils.deleteAllNodesOfApplication(key, token, false);
                let envObject = await apiUtils.getEnvObject(token, externalCiWorkflowEnvName);
                await apiUtils.createNewWorkflowOfExternalCi(token, appId, envObject.id, externalCiWorkflowEnvName);
                let nodeId = await apiUtils.getObjectDetailsOfExternalCiAndReturnComponentId(token, 0, appId);
                nodeIds.push(nodeId);
                triggerExternalCi ? await apiUtils.triggerExternalCi(token, nodeId) : '';
            }
        }
        await use({ nodeId: nodeIds });
    },
    instantiatePomObjectsForNonSuperAdminPage: async ({ browser, request, nonSuperAdminPage }, use) => {
        let apiUtils = new ApiUtils(request);
        let tokenValue = await apiUtils.getApiTokenObject(await apiUtils.login(process.env.PASSWORD!), 'playwright-non-super').then(key => key?.tokenValue) as string;
        nonSuperAdminPage = await BaseTest.createNewPageWithCustomCookies(browser, tokenValue);
        let objectsOfAllPages = createAllObjects(nonSuperAdminPage);
        await use({ objects: objectsOfAllPages, nonSuperAdminPage });
        await nonSuperAdminPage.close();
    },
    instantiatePomObjectsForSuperAdminPage: async ({ browser, request, page }, use) => {
        let apiUtils = new ApiUtils(request);
        let tokenValue = await apiUtils.getApiTokenObject(await apiUtils.login(process.env.PASSWORD!), 'playwright-super-admin').then(key => key?.tokenValue) as string;
        let SuperAdminPage = await BaseTest.createNewPageWithCustomCookies(browser, tokenValue);
        let objectsOfAllPages = createAllObjects(SuperAdminPage);
        await use({ objects: objectsOfAllPages, SuperAdminPage });
        await SuperAdminPage.close();
    }




})
