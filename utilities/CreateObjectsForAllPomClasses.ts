import { Page } from '@playwright/test';
import { AddChartRepo } from '../Pages/AddChartRepo';
import { AppConfigurationPage } from '../Pages/AppConfigurationsPage';
import { ApplicationGroupPage } from '../Pages/ApplicationManagement/Application Groups/ApplicationGroupPage';
import { ApprovalPolicyPage } from '../Pages/ApprovalPolicyPage';
import { BaseDeploymentTemplatePage } from '../Pages/BaseDeploymentTemplatePage';
import { BuildConfigurationPage } from '../Pages/BuildConfigurationPage';
import { BuildHistoryPage } from '../Pages/BuildHistoryPage';
import { BuildSourceTagAndPR } from '../Pages/ApplicationManagement/Applications/BuildSourceTagAndPR';
import { CatalogFrameworkPage } from '../Pages/CatalogFrameworkPage';
import { ChartStoreAppDetailsPage } from '../Pages/chartStore/ChartStoreAppDetailsPage';
import { chartStoreDeploymentHistory } from '../Pages/ApplicationManagement/Chart Store/chartStoreDeploymentHistory';
import { ChartStorePage } from '../Pages/chartStore/chartStorePage';
import { DeployChartPage } from '../Pages/chartStore/DeployChartPage';
import { CiCdAppDetailsPage } from '../Pages/ApplicationManagement/Applications/CiCdAppDetailsPage';
import { CreateAppPage } from '../Pages/CreateAppPage';
import { DeploymentHistoryPage } from '../Pages/ApplicationManagement/Applications/DeploymentHistoryPage';
import { DeploymentWindow } from '../Pages/DeploymentWindow';
import { ExternalCIPage } from '../Pages/ApplicationManagement/Applications/ExternalCIPage';
import { FilterConditionPage } from '../Pages/ApplicationManagement/Policies/FilterConditionPage';
import { GitopsConfigurationPage } from '../Pages/ApplicationManagement/Configurations/GItopsConfigurationPage';
import { GitRepositoryPage } from '../Pages/ApplicationManagement/Applications/GitRepositoryPage';
import { ApiTokenPage } from '../Pages/Global Configuration/Authorization/ApiTokenPage';
import { ApplicationTemplatePage } from '../Pages/ApplicationManagement/ApplicationTemplatePage';
import { BuildInfra } from '../Pages/GlobalConfigurations/BuildInfraPage';
import { ClusterAndEnvironments } from '../Pages/Global Configuration/Clutsers and Environments/Cluster&EnvironmentsPage';
import { ContainerRegistryPage } from '../Pages/Global Configuration/ContainerRegistryPage';
import { DeploymentCharts } from '../Pages/ApplicationManagement/Configurations/DeploymenCharts';
import { ExternalLinks } from '../Pages/GlobalConfigurations/ExternalLinks';
import { GlobalConfigurationPage } from '../Pages/GlobalConfigurations/GlobalConfigurationPage';
import { LockConfiguration } from '../Pages/GlobalConfigurations/LockConfiguration';
import { NotificationsConfig } from '../Pages/GlobalConfigurations/NotificationConfigPage';
import { ProjectPage } from '../Pages/ApplicationManagement/ProjectPage';
import { ScopedVariablePage } from '../Pages/ApplicationManagement/Configurations/ScopedVariablePage';
import { SecurityGlobalPage } from '../Pages/Security Center/SecurityPage';
import { UserPermissionPage } from '../Pages/GlobalConfigurations/UserPermissionPage';
import { GlobalTag } from '../Pages/GlobalTag';
import { ImagePromotion } from '../Pages/ImagePromotion';
import { JobsPage } from '../Pages/Automation and Enablement/Jobs';
import { LicenseDashboardPage } from '../Pages/Devtron License/licenseDashboardPage';
import { MandatoryPluginPage } from '../Pages/ApplicationManagement/Policies/MandatoryPluginpage';
import { OverviewPage } from '../Pages/OverviewPage';
import { permissionGroupsPage } from '../Pages/PermissionGroupsPage';
import { PrePostCiCd } from '../Pages/PrePostCiCd';
import { ProductDashboardPage } from '../Pages/ProductDashboardPage';
import { ImagePullDigest } from '../Pages/ApplicationManagement/Policies/PullImageDigest';
import { Installation } from '../Pages/Release/Installations';
import { ReleaseConfigurationPage } from '../Pages/Release/ReleaseConfigurationPage';
import { ReleaseHub } from '../Pages/Release/ReleaseHub';
import { ReleaseOverviewPage } from '../Pages/Release/ReleaseOverviewPages';
import { ReleaseTrackConfigurePage } from '../Pages/Release/ReleaseTrackConfigurePage';
import { Requirements } from '../Pages/Release/Requirements';
import { Rollout } from '../Pages/Release/Rollout';
import { RolloutHistoryPage } from '../Pages/Release/RolloutHistoryPage';
import { Tenants } from '../Pages/Release/Tenants';
import { ResourceBrowserPage } from '../Pages/Infrastructure Management/ResourceBrowserPage';
import { Rollback } from '../Pages/Rollback';
import { SsoLoginServicesPage } from '../Pages/Global Configuration/Authorization/SsoLoginServicesPage';
import { TagPropagation } from '../Pages/ApplicationManagement/Applications/TagPropogation';
import { WorkflowPage } from '../Pages/WorkflowPage';
import { AllTypes } from './Types';

export function createAllObjects(page: Page): AllTypes.fixtures.allPageObjects {
    return {
        chartStoreAppDetailsPage: new ChartStoreAppDetailsPage(page),
        chartStoreDeploymentHistoryy: new chartStoreDeploymentHistory(page),
        deployChartPage: new DeployChartPage(page),
        chartStorePage: new ChartStorePage(page),
        globalConfigPage: new GlobalConfigurationPage(page),
        createAppPage: new CreateAppPage(page),
        appConfigurationPage: new AppConfigurationPage(page),
        baseDeploymentTemplatePage: new BaseDeploymentTemplatePage(page),
        buildConfigurationPage: new BuildConfigurationPage(page),
        buildHistoryPage: new BuildHistoryPage(page),
        gitRepositoryPage: new GitRepositoryPage(page),
        workflowPage: new WorkflowPage(page),
        resourceBrowserPage: new ResourceBrowserPage(page),
        clusterAndEnvironmentsPage: new ClusterAndEnvironments(page),
        deploymentHistory: new DeploymentHistoryPage(page),
        appDetailsPageCiCd: new CiCdAppDetailsPage(page),
        gitopsConfigurationPage: new GitopsConfigurationPage(page),
        externalCIPage: new ExternalCIPage(page),
        containerRegistryPage: new ContainerRegistryPage(page),
        prePostCiCd: new PrePostCiCd(page),
        filterConditionPage: new FilterConditionPage(page),
        mandatoryPluginPage: new MandatoryPluginPage(page),
        notificationsPage: new NotificationsConfig(page),
        apiTokenPage: new ApiTokenPage(page),
        jobsPage: new JobsPage(page),
        buildInfraPage: new BuildInfra(page),
        scopedVariablePage: new ScopedVariablePage(page),
        applicationGroupPage: new ApplicationGroupPage(page),
        globalTag: new GlobalTag(page),
        imagePullDigest: new ImagePullDigest(page),
        tagPropagatonPage: new TagPropagation(page),
        buildSourceTagAndPR: new BuildSourceTagAndPR(page),
        rollback: new Rollback(page),
        addChartRepo: new AddChartRepo(page),
        projectPage: new ProjectPage(page),
        userPermissionPage: new UserPermissionPage(page),
        overViewPage: new OverviewPage(page),
        catalogFrameWorkPage: new CatalogFrameworkPage(page),
        permissionGroupPage: new permissionGroupsPage(page),
        ssoLoginServicesPage: new SsoLoginServicesPage(page),
        installationPage: new Installation(page),
        releaseHubPage: new ReleaseHub(page),
        releaseOverviewPage: new ReleaseOverviewPage(page),
        releaseTrackConfigurePage: new ReleaseTrackConfigurePage(page),
        requirementPage: new Requirements(page),
        rolloutPage: new Rollout(page),
        rolloutHistoryPage: new RolloutHistoryPage(page),
        tenantsPage: new Tenants(page),
        releaseConfigurationPage: new ReleaseConfigurationPage(page),
        approvalPolicyPage: new ApprovalPolicyPage(page),
        externalLinksPage: new ExternalLinks(page),
        applicationTemplatePage: new ApplicationTemplatePage(page),
        imagePromotionPage: new ImagePromotion(page),
        deploymentWindowPage: new DeploymentWindow(page),
        securityGlobalPage: new SecurityGlobalPage(page),
        deploymentChartPage: new DeploymentCharts(page),
        productDashboardPage: new ProductDashboardPage(page),
        licenseDashboardPage: new LicenseDashboardPage(page),
        lockConfig: new LockConfiguration(page),
    };
}
