import { Page } from '@playwright/test';
import { AddChartRepo } from '../Pages/ApplicationManagement/Configurations/AddChartRepo';
import { AppConfigurationPage } from '../Pages/ApplicationManagement/Applications/AppConfigurationsPage';
import { ApplicationGroupPage } from '../Pages/ApplicationManagement/ApplicationGroups/ApplicationGroupPage';
import { ApprovalPolicyPage } from '../Pages/ApplicationManagement/Policies/ApprovalPolicyPage';
import { BaseDeploymentTemplatePage } from '../Pages/ApplicationManagement/Applications/BaseDeploymentTemplatePage';
import { BuildConfigurationPage } from '../Pages/ApplicationManagement/Applications/BuildConfigurationPage';
import { BuildHistoryPage } from '../Pages/ApplicationManagement/Applications/BuildHistoryPage';
import { BuildSourceTagAndPR } from '../Pages/ApplicationManagement/Applications/BuildSourceTagAndPR';
import { CatalogFrameworkPage } from '../Pages/ApplicationManagement/Configurations/CatalogFrameworkPage';
import { ChartStoreAppDetailsPage } from '../Pages/ApplicationManagement/ChartStore/ChartStoreAppDetailsPage';
import { chartStoreDeploymentHistory } from '../Pages/ApplicationManagement/ChartStore/chartStoreDeploymentHistory';
import { ChartStorePage } from '../Pages/ApplicationManagement/ChartStore/chartStorePage';
import { DeployChartPage } from '../Pages/ApplicationManagement/ChartStore/DeployChartPage';
import { CiCdAppDetailsPage } from '../Pages/ApplicationManagement/Applications/CiCdAppDetailsPage';
import { CreateAppPage } from '../Pages/ApplicationManagement/Applications/CreateAppPage';
import { DeploymentHistoryPage } from '../Pages/ApplicationManagement/Applications/DeploymentHistoryPage';
import { DeploymentWindow } from '../Pages/ApplicationManagement/Policies/DeploymentWindow';
import { ExternalCIPage } from '../Pages/ApplicationManagement/Applications/ExternalCIPage';
import { FilterConditionPage } from '../Pages/ApplicationManagement/Policies/FilterConditionPage';
import { GitopsConfigurationPage } from '../Pages/ApplicationManagement/Configurations/GItopsConfigurationPage';
import { GitRepositoryPage } from '../Pages/ApplicationManagement/Applications/GitRepositoryPage';
import { ApiTokenPage } from '../Pages/GlobalConfiguration/Authorization/ApiTokenPage';
import { ApplicationTemplatePage } from '../Pages/ApplicationManagement/ApplicationTemplatePage';
import { BuildInfra } from '../Pages/ApplicationManagement/Configurations/BuildInfraPage';
import { ClusterAndEnvironments } from '../Pages/GlobalConfiguration/Clutsers and Environments/Cluster&EnvironmentsPage';
import { ContainerRegistryPage } from '../Pages/GlobalConfiguration/ContainerRegistryPage';
import { DeploymentCharts } from '../Pages/ApplicationManagement/Configurations/DeploymenCharts';
import { ExternalLinks } from '../Pages/ApplicationManagement/Configurations/ExternalLinks';
import { NavbarPage } from '../Pages/NavbarPage';
//import { GlobalConfigurationPage } from '../Pages/ApplicationManagement/Configurations/GlobalConfigurationPage';
import { LockConfiguration } from '../Pages/ApplicationManagement/Policies/LockConfiguration';
import { NotificationsConfig } from '../Pages/ApplicationManagement/Configurations/NotificationConfigPage';
import { ProjectPage } from '../Pages/ApplicationManagement/ProjectPage';
import { ScopedVariablePage } from '../Pages/ApplicationManagement/Configurations/ScopedVariablePage';
import { SecurityGlobalPage } from '../Pages/SecurityCenter/SecurityPage';
import { UserPermissionPage } from '../Pages/GlobalConfiguration/Authorization/UserPermissionPage';
import { GlobalTag } from '../Pages/ApplicationManagement/Policies/GlobalTag';
import { ImagePromotion } from '../Pages/ApplicationManagement/Policies/ImagePromotion';
import { JobsPage } from '../Pages/AutomationandEnablement/Jobs';
import { LicenseDashboardPage } from '../Pages/DevtronLicense/licenseDashboardPage';
import { MandatoryPluginPage } from '../Pages/ApplicationManagement/Policies/MandatoryPluginpage';
import { OverviewPage } from '../Pages/OverviewPage';
import { permissionGroupsPage } from '../Pages/GlobalConfiguration/Authorization/PermissionGroupsPage';
import { PrePostCiCd } from '../Pages/ApplicationManagement/Applications/PrePostCiCd';
import { ProductDashboardPage } from '../Pages/DevtronLicense/ProductDashboardPage';
import { ImagePullDigest } from '../Pages/ApplicationManagement/Policies/PullImageDigest';
import { Installation } from '../Pages/SoftwareReleaseManagement/Installations';
import { ReleaseConfigurationPage } from '../Pages/SoftwareReleaseManagement/ReleaseConfigurationPage';
import { ReleaseHub } from '../Pages/SoftwareReleaseManagement/ReleaseHub';
import { ReleaseOverviewPage } from '../Pages/SoftwareReleaseManagement/ReleaseOverviewPages';
import { ReleaseTrackConfigurePage } from '../Pages/SoftwareReleaseManagement/ReleaseTrackConfigurePage';
import { Requirements } from '../Pages/SoftwareReleaseManagement/Requirements';
import { Rollout } from '../Pages/SoftwareReleaseManagement/Rollout';
import { RolloutHistoryPage } from '../Pages/SoftwareReleaseManagement/RolloutHistoryPage';
import { Tenants } from '../Pages/SoftwareReleaseManagement/Tenants';
import { ResourceBrowserPage } from '../Pages/InfrastructureManagement/ResourceBrowserPage';
import { Rollback } from '../Pages/ApplicationManagement/Applications/Rollback';
import { SsoLoginServicesPage } from '../Pages/GlobalConfiguration/Authorization/SsoLoginServicesPage';
import { TagPropagation } from '../Pages/ApplicationManagement/Applications/TagPropogation';
import { WorkflowPage } from '../Pages/ApplicationManagement/Applications/WorkflowPage';
import { AllTypes } from './Types';
import { GlobalSearchComponent } from '../Pages/GlobalSearchComponent';

export function createAllObjects(page: Page): AllTypes.fixtures.allPageObjects {
    return {
        globalSearchComponent: new GlobalSearchComponent(page),
        chartStoreAppDetailsPage: new ChartStoreAppDetailsPage(page),
        chartStoreDeploymentHistoryy: new chartStoreDeploymentHistory(page),
        deployChartPage: new DeployChartPage(page),
        chartStorePage: new ChartStorePage(page),
        navbarPage: new NavbarPage(page),
        //globalConfigPage: new GlobalConfigurationPage(page),
        createAppPage: new CreateAppPage(page),
        appConfigurationPage: new AppConfigurationPage(page),
        baseDeploymentTemplatePage: new BaseDeploymentTemplatePage(page),
        buildConfigurationPage: new BuildConfigurationPage(page),
        buildHistoryPage: new BuildHistoryPage(page),
        gitRepositoryPage: new GitRepositoryPage(page),
        workflowPage: new WorkflowPage(page),
        resourceBrowserPage: new ResourceBrowserPage(page),
        clusterAndEnvironmentsPage: new ClusterAndEnvironments(page),
        deploymentHistory: new DeploymentHistoryPage(page),
        appDetailsPageCiCd: new CiCdAppDetailsPage(page),
        gitopsConfigurationPage: new GitopsConfigurationPage(page),
        externalCIPage: new ExternalCIPage(page),
        containerRegistryPage: new ContainerRegistryPage(page),
        prePostCiCd: new PrePostCiCd(page),
        filterConditionPage: new FilterConditionPage(page),
        mandatoryPluginPage: new MandatoryPluginPage(page),
        notificationsPage: new NotificationsConfig(page),
        apiTokenPage: new ApiTokenPage(page),
        jobsPage: new JobsPage(page),
        buildInfraPage: new BuildInfra(page),
        scopedVariablePage: new ScopedVariablePage(page),
        applicationGroupPage: new ApplicationGroupPage(page),
        globalTag: new GlobalTag(page),
        imagePullDigest: new ImagePullDigest(page),
        tagPropagatonPage: new TagPropagation(page),
        buildSourceTagAndPR: new BuildSourceTagAndPR(page),
        rollback: new Rollback(page),
        addChartRepo: new AddChartRepo(page),
        projectPage: new ProjectPage(page),
        userPermissionPage: new UserPermissionPage(page),
        overViewPage: new OverviewPage(page),
        catalogFrameWorkPage: new CatalogFrameworkPage(page),
        permissionGroupPage: new permissionGroupsPage(page),
        ssoLoginServicesPage: new SsoLoginServicesPage(page),
        installationPage: new Installation(page),
        releaseHubPage: new ReleaseHub(page),
        releaseOverviewPage: new ReleaseOverviewPage(page),
        releaseTrackConfigurePage: new ReleaseTrackConfigurePage(page),
        requirementPage: new Requirements(page),
        rolloutPage: new Rollout(page),
        rolloutHistoryPage: new RolloutHistoryPage(page),
        tenantsPage: new Tenants(page),
        releaseConfigurationPage: new ReleaseConfigurationPage(page),
        approvalPolicyPage: new ApprovalPolicyPage(page),
        externalLinksPage: new ExternalLinks(page),
        applicationTemplatePage: new ApplicationTemplatePage(page),
        imagePromotionPage: new ImagePromotion(page),
        deploymentWindowPage: new DeploymentWindow(page),
        securityGlobalPage: new SecurityGlobalPage(page),
        deploymentChartPage: new DeploymentCharts(page),
        productDashboardPage: new ProductDashboardPage(page),
        licenseDashboardPage: new LicenseDashboardPage(page),
        lockConfig: new LockConfiguration(page),
    };
}
