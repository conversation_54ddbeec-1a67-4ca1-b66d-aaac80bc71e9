import { Browser, chromium, expect, Page, request } from '@playwright/test';
import { BaseTest } from './utilities/BaseTest';
import { ApiUtils } from './utilities/ApiUtils';
import { setupGmailTesterFiles, writeGmailCredentialsToFile, writeGmailTokenToFile } from './utilities/ThirdPartyClients/GmailNotifications/writeGmailCredentials';
import fs from 'fs'
import { AllTypes } from './utilities/Types';

// This function handles authentication setup for Playwright tests
async function globalSetup() {
  // Launch a non-headless browser for visual debugging
  const browser: Browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const apiRequestContext = await request.newContext();

  // Set a generous timeout for all context operations
  context.setDefaultTimeout(2 * 60 * 1000);

  // Check if we're running tests against staging environment
  var isStaging = process.env.isStaging === 'true';
  if (isStaging) {
    console.log('going to ligin in the staging cluster');
    await context.addCookies([{
      name: 'argocd.token',
      value: process.env.stagingSuperAdminToken!,
      url: 'https://staging.devtron.info/'
    }]);
  }
  const args = process.argv.join(' ');

  // Skip global setup if only DevtronLicense.spec.ts is being run
  if (args.includes('tests/DevtronLicense.spec.ts')) {
    console.log('Skipping global setup for DevtronLicense.spec.ts');
    return;
  }

  const page: Page = await context.newPage();

  // For non-staging environments, perform UI-based login
  if (!isStaging) {
    let isLoginButtonVisible: boolean;
    try {
      // Retry login process until success (with timeout)
      await expect(async () => {
        console.log('env url we are getting is'+process.env.BASE_SERVER_URL);
        await page.goto(process.env.BASE_SERVER_URL as string);
        console.log('page loaded successfully');
        await page.waitForTimeout(3000);

        // Check if login button is visible
        isLoginButtonVisible = await page.getByTestId('login-as-admin').isVisible();
        console.log('loing page loaded successfully' + isLoginButtonVisible);
        await page.getByTestId(`login-as-admin`).click({ delay: 1000, timeout: 8000 });
        await page.getByPlaceholder('Password').click({ delay: 500, timeout: 5000 });
        await page.getByPlaceholder('Password').fill(process.env.PASSWORD as string);

        // Handle different response patterns based on cluster type
        if (process.env.clusterType?.includes('ea')) {
          // For EA clusters, wait for session API response
          await Promise.all([
            page.waitForResponse(response =>
              response.url().includes('/orchestrator/api/v1/session') && response.status() == 200
            ),
            page.getByTestId('login-button').click()
          ]);
        } else {
          // For other clusters, wait for app list response
          await Promise.all([
            page.waitForResponse(response =>
              response.url().includes('/orchestrator/app/list') && response.status() === 200
            ),
            page.getByTestId('login-button').click({ timeout: 10000 })
          ]);
        }
        await BaseTest.clickOnToolTipOkayButton(page);
      }).toPass({ timeout: 4 * 1000 * 60 }); // Allow up to 4 minutes for login process
    } catch (error) {
      // Error handling with diagnostics
      if (isLoginButtonVisible!) {
        console.log('we were able to land on login page but login was not successfull');
      } else {
        console.log('we were not able to load the login page so initial setup failed ');
      }
    }
  }
   // await setupGmailTesterFiles();
  let baseCredentialsObject: AllTypes.BaseCredentils.TestParameters = JSON.parse(fs.readFileSync('TestData/BaseCredentials.json', 'utf-8'));
  baseCredentialsObject.ChartSource = BaseTest.generateRandomStringWithCharsOnly(5);
  fs.writeFileSync(
    'TestData/BaseCredentials.json',
    JSON.stringify(baseCredentialsObject, null, 2)
  );
  await page.context().storageState({ path: './LoginAuth.json' });

  // Clean up resources
  await browser.close();
}

export default globalSetup;

