import { Locator, <PERSON> } from "playwright";
import { verifyAtAGlanceDTO, verifyPotentialSavingsDTO } from "../../DTOs/CostVisibility/OverviewPageDTOs";
import { expect } from "playwright/test";

export class OverviewPage{
    readonly topCostOptimizationOpportunitiesDiv:Locator;
    readonly costVisibilityTimeRangeFilter:Locator;

    constructor(public page :Page){
        this.topCostOptimizationOpportunitiesDiv = this.page.locator(`//*[text()="Top cost optimization opportunities"]/ancestor::div[2]`);
        this.costVisibilityTimeRangeFilter = this.page.locator(`//*[contains(@class,'cost-visibility-time-range-select__control')]`);
    }

    async openOrCloseActionAndInsightSidePanel(openSidePanel:boolean){
        try{
            await this.setLocatorForSidePanelActionButton(openSidePanel).click();
        }
        catch(error){
            console.log('modal is already in desired state');
        }
        openSidePanel ? await this.topCostOptimizationOpportunitiesDiv.waitFor({state:'visible'}): await this.topCostOptimizationOpportunitiesDiv.waitFor({state:'hidden'});
    }

   
    async applyFilterOfDuration(filterValue:string){
        await this.costVisibilityTimeRangeFilter.click();
        await this.page.locator(`//*[@role="listbox"]`).locator(`//*[text()="${filterValue}"]`).click();
    }

    async verifyAtAGlanceSection(verificationData:verifyAtAGlanceDTO){
        let atAGlanceSectionNames = Object.keys(verificationData) as (keyof verifyAtAGlanceDTO)[];
        for (const key of atAGlanceSectionNames) {
            let locatorForSection = this.setLocatorForAtAGlanceSection(key);
            await expect(locatorForSection.locator(`//*[text()="${verificationData[key]?.value}"]`)).toBeVisible();
            await expect(locatorForSection.locator(`//*[text()="${verificationData[key]?.textToVerifyInFooterOfModal}"]`)).toBeVisible();
        }
    }

    async verifyPotentialSavingsSection(verificationData:verifyPotentialSavingsDTO){
        let valuesToVerify:string[]=Object.keys(verificationData) as (keyof verifyPotentialSavingsDTO)[];
        for(let key of valuesToVerify){
            await expect(this.page.locator(`//*[text()="${key}"]/following-sibling::span//*[text()="${verificationData[key]}"]`)).toBeVisible();
        }
    }


    async navigateToAnyClusterFromSidePanel(clusterName:string){
        await this.topCostOptimizationOpportunitiesDiv.locator(`//*[text()="${clusterName}"]`).click();
        await expect(this.topCostOptimizationOpportunitiesDiv).toBeHidden();
    }

    async verifyTopCostOptimizingOpportunitiesDetailsOfClusters(verificationData:{clusterName:string, costToVerify:string,potentialSavings:String}[]){
        for(let key of verificationData){
            await expect(this.setLocatorForSidePanelClusterSpecificDiv(key.clusterName).locator(`//*[text()="${key.costToVerify}"]`)).toBeVisible();
            await expect(this.setLocatorForSidePanelClusterSpecificDiv(key.clusterName).locator(`//*[text()="${key.potentialSavings}"]`)).toBeVisible();
        }

    }

     setLocatorForSidePanelClusterSpecificDiv(clusterName:string){
        return this.topCostOptimizationOpportunitiesDiv.locator(`//*[text()="${clusterName}"]/ancestor::a[1]`);
    }

     setLocatorForAtAGlanceSection(atAGlanceSectionName:string):Locator{
        return this.page.locator(`//*[text()="${atAGlanceSectionName}"]/parent::div`);
    }

     setLocatorForSidePanelActionButton(openSidePanel:boolean) :Locator{
       let locatorToReturn:Locator = openSidePanel ? this.page.getByTestId(`cost-visibility-overview-open-sidebar`) : this.page.getByTestId(`cost-visibility-overview-close-sidebar`);
        return locatorToReturn;
    }

}