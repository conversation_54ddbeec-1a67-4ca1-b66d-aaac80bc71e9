import { Locator, <PERSON> } from "playwright";
import { selectEnvProjectAppProjectEnum } from "../../enums/CostVisibility/CostBreakdownPageEnums";
import { expect } from "playwright/test";
import { verifyCostInVariousSectionsDTO } from "../../DTOs/CostVisibility/CostBreakdownDTOs";
import { BasePage } from "../BasePage";

export class CostBreakdownPage extends BasePage{

    readonly selectEnvProjectAppProjectDiv:Locator;
    readonly top10CostlyFilterButtonForAllResources:Locator;
    readonly clusterAndEnvLevelFilterButton:Locator;
    readonly costVisibilityTimeRangeFilter:Locator;
    readonly headingLocatorOfCompleteDetailsOfAnyResourceSection:Locator;
    readonly valueLocatorOfCompleteDetailsOfAnyResourceSection:Locator;
    
    constructor(public page:Page){
        super(page);
        this.selectEnvProjectAppProjectDiv= this.page.locator(`//*[contains(@class,'segmented-control__container')]`);
        this.top10CostlyFilterButtonForAllResources = this.page.locator(`//*[contains(@class,'top-10-clusters-category-filter__control')]`);
        this.clusterAndEnvLevelFilterButton= this.page.locator(`//*[contains(@class,'prod-non-prod-select__control')]`);
        this.costVisibilityTimeRangeFilter= this.page.locator(`//*[contains(@class,'cost-visibility-time-range-select__control')]`);
        this.headingLocatorOfCompleteDetailsOfAnyResourceSection= this.page.locator(`//*[contains(@class,'sortable-table-header__container')]`);
        this.valueLocatorOfCompleteDetailsOfAnyResourceSection= this.page.locator(`//*[@class=" generic-table__cell"]`);
    }

    async selectEnvProjectAppProjectFromTab(tabToSelect:selectEnvProjectAppProjectEnum){
        await this.selectEnvProjectAppProjectDiv.locator(`//*[text()="${tabToSelect}"]`).click();
    }

   async verifyCostValuesInVariousSections(verificationData:verifyCostInVariousSectionsDTO){
    let sectionsToVerify:(keyof verifyCostInVariousSectionsDTO)[]=Object.keys(verificationData) as (keyof verifyCostInVariousSectionsDTO)[];
    for(let key of sectionsToVerify){
        if(key == 'top 10 costly'){
            await expect(this.setLocatorForValueDivInSections(key).locator(`//*[text()="${verificationData[key].resourceName}"]`).locator(`xpath=parent::div`)).toContainText(verificationData[key].cost);
        }
        else{
             await expect(this.setLocatorForValueDivInSections(key)).toContainText(verificationData[key]!);
        }
    }
   }

   async applyFilterOnTop10CostlyResources(filterValue:string){
    await this.top10CostlyFilterButtonForAllResources.click();
    await this.page.locator(`//*[@role="listbox"]`).locator(`//*[text()="${filterValue}"]`).click();
   }

   async applyFilterOnClusterAndEnvLevel(filterValue:string){
      await this.clusterAndEnvLevelFilterButton.click();
      await this.page.locator(`//*[@role="listbox"]`).locator(`//*[text()="${filterValue}"]`).click();
   }

   async applyFilterOnDuration(filterValue:string){
    await this.costVisibilityTimeRangeFilter.click();
    await this.page.locator(`//*[@role="listbox"]`).locator(`//*[text()="${filterValue}"]`).click();
   }


   async searchAResource(resourceName:string){
        await this.searchBarInputField.fill(resourceName);
        await this.page.keyboard.press('Enter');
   }


   async verifyCompleteDetailsOfAnyResourceOnCostBreakdownPage(){

   }


   async setMappingOfHeadingAndValuesOfAnyResourceOnCostBreakdownPage(headingValue:string,resourceName:string):Promise<Locator | null>{
        await this.searchBarInputField.waitFor();
        let allVisibleHeadings=await this.headingLocatorOfCompleteDetailsOfAnyResourceSection.allTextContents();
        for(let i=0;i<allVisibleHeadings.length;i++){
            if(allVisibleHeadings[i].trim().includes(headingValue.trim())){
             return this.setLocatorForCompleteRowOfAnyResourceOnCostBreakdownPage(resourceName).locator(this.valueLocatorOfCompleteDetailsOfAnyResourceSection.nth(i));
            }
        }
        return null;

   }

   async navigateToAnyResource(resourceName:string){
        await this.searchAResource(resourceName);
        await this.setLocatorForCompleteRowOfAnyResourceOnCostBreakdownPage(resourceName).click();
   }

    setLocatorForValueDivInSections(sectionName: keyof verifyCostInVariousSectionsDTO):Locator{
        if(sectionName=="top 10 costly"){
            return this.page.locator(`//*[text()="Top 10 costly "]/parent::div/following-sibling::div`)
        }
        return this.page.locator(`//*[text()="${sectionName}"]`).locator(`xpath=following-sibling::span`);

    }

    setLocatorForCompleteRowOfAnyResourceOnCostBreakdownPage(resourceName:string):Locator{
       return this.page.locator(`//a[text()="${resourceName}"]/ancestor::div[contains(@class,'generic-table__row')]`)
    }
}