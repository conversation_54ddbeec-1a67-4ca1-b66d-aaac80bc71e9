import { Locator, <PERSON> } from "playwright-core";
import { expect } from "playwright/test";

export class chartStoreDeploymentHistory {

    readonly deploymentHistoryTab: Locator;
    readonly successfullDeployment: Locator;
    readonly stepVerificationGitops: Locator;
    readonly stepsTab: Locator;
    readonly rollbackTriggerButton: Locator;

    constructor(private page: Page) {
        this.successfullDeployment = page.locator('//*[@class="icon-dim-24     dc__no-shrink"]');
        this.deploymentHistoryTab = page.locator('//*[contains(text(),"Deployment history")]');
        this.stepVerificationGitops = page.locator('//*[@data-testid="success-green-tick"]');
        this.stepsTab = page.locator('//*[text()="Steps"]');
        this.rollbackTriggerButton = this.page.getByTestId('re-deployment-button');
    }


    /**
     * 
     * @param count pass count as number of sucessfulldeployments you want to verify on deployment history page 
     */
    async verifyNumberofSucceddedDeployments(count: number) {
        try {
            // we are verifying the successfull deployments count here 
            await expect(async () => {
                await this.page.locator('//*[@data-testid="helm-configure-link" or text()="Configure"]').first().click();
                await this.deploymentHistoryTab.click();
                await expect(this.successfullDeployment).toHaveCount(count, { timeout: 6000 });
            }).toPass({ timeout: 3 * 1000 * 60 });
            if (await this.stepsTab.isVisible({ timeout: 2000 })) {
                //verification for gitops 5 step completion 
                await expect(this.stepVerificationGitops).toHaveCount(5, { timeout: 2 * 1000 * 60 });
            }

        }
        catch (error) {
            console.error(`An error occurred while verifying build logs and status: ${error.message}`);
            throw error;
        }
    }
    async triggerRollback(numberOfDeploymentToTrigger: number) {
        await this.page.locator(`//*[@data-testid="chart-deployment-history-sidebar-row-${numberOfDeploymentToTrigger}"]`).click();
        await this.rollbackTriggerButton.click();
    }
}
