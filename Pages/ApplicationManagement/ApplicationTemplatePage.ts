import { read } from "fs";
import { Locator, Page } from "playwright";
import { expect } from "playwright/test";
import { BasePage } from "../BasePage";

export class ApplicationTemplatePage extends BasePage {
    readonly templateSearchInputField: Locator;
    readonly createTemplateButton: Locator;
    readonly applicationSearchInputField: Locator;
    readonly templateCreationErrorWarning: Locator;
    readonly templateCreationModalDiv: Locator;
    readonly createTemplateNameInputField: Locator;
    readonly createTemplateIdInputField: Locator;
    readonly templateCreationDescriptionInputField: Locator;
    readonly appConfigurationLinkForTemplate: Locator;
    readonly deleteTemplateButton: Locator;
    constructor(public page: Page) {
        super(page);
        this.templateSearchInputField = this.page.getByTestId('template-list-search');
        this.createTemplateButton = this.page.getByTestId('create-template-button');
        this.applicationSearchInputField = this.page.getByTestId('application-list-search');
        this.templateCreationErrorWarning = this.page.locator('#template-display-name-error-msg');
        this.templateCreationModalDiv = this.page.locator('//*[@class="drawer right show"]')
        this.createTemplateNameInputField = this.page.getByTestId('template-display-name');
        this.createTemplateIdInputField = this.page.getByTestId('template-template-id');
        this.templateCreationDescriptionInputField = this.page.getByPlaceholder('Write a description for this template');
        this.appConfigurationLinkForTemplate = this.page.getByTestId('app-template-config-link');
        this.deleteTemplateButton = this.page.locator(`//*[text()='Delete Template']`);
    }



    async searchAndClickOnAnyTemplate(templateName: string, verifyDetails?: { description?: string, createdBy?: string }) {
        await this.templateSearchInputField.fill(templateName);
        await this.page.keyboard.press('Enter');
        verifyDetails ? await this.verifyDetailsOfTemplateAndApplication(templateName, verifyDetails) : console.log();
        await (await this.returnTemplateAndAppSpecificDiv(templateName)).click();
    }
    async clickOnAppConfigurationLinkForTemplate() {
        await this.appConfigurationLinkForTemplate.click();
    }

    async verifyDetailsOfTemplateAndApplication(templateOrAppName: string, verificationDetails: { description?: string, createdBy?: string }) {
        let locatorToClick = await this.returnTemplateAndAppSpecificDiv(templateOrAppName);
        if (verificationDetails.description) {
            await expect(locatorToClick.locator(`//*[text()="${verificationDetails.description}"]`)).toBeVisible();
        }
        if (verificationDetails.createdBy) {
            await expect(locatorToClick.locator(`//*[text()="${verificationDetails.createdBy}"]`)).toBeVisible();
        }
    }

    async createTemplateFromApp(data: { templateRelatedData: { name: string, id: string, desc: string, checkValidation: boolean }, applicationRelatedData: { appName: string, verifyAppDetails?: { description?: string, createdBy?: string } } }) {
        await this.createTemplateButton.click();
        await this.applicationSearchInputField.fill(data.applicationRelatedData.appName);
        await this.page.keyboard.press('Enter');
        data.applicationRelatedData.verifyAppDetails ? await this.verifyDetailsOfTemplateAndApplication(data.applicationRelatedData.appName, data.applicationRelatedData.verifyAppDetails) : console.log();
        await (await this.returnTemplateAndAppSpecificDiv(data.applicationRelatedData.appName)).click();
        if (data.templateRelatedData.checkValidation) {
            await this.templateCreationModalDiv.locator(this.createTemplateButton).click();
            await expect(this.templateCreationErrorWarning).toBeVisible();
        }
        await this.createTemplateNameInputField.fill(data.templateRelatedData.name);
        await this.createTemplateIdInputField.fill(data.templateRelatedData.id);
        await this.templateCreationDescriptionInputField.fill(data.templateRelatedData.desc);
        await this.templateCreationModalDiv.locator(this.createTemplateButton).click();


    }


    async returnTemplateAndAppSpecificDiv(appOrTemplateName: string) {
        return this.page.locator(`//*[text()="${appOrTemplateName}"]/ancestor::div[3]`);
    }

    async deleteTemplate(templateName: string) {
        await this.searchAndClickOnAnyTemplate(templateName);
        await this.deleteTemplateButton.click();
        await this.dialogDeleteConfirmationButton.click();
    }
}