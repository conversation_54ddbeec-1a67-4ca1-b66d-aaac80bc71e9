import { Locator, <PERSON> } from "playwright-core";
import { expect } from "playwright/test";
import { setTimeout } from 'timers/promises';
import { BaseTest } from "../../../utilities/BaseTest";
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
import { BasePage } from "../../BasePage";
import { CanaryPercentage } from "../../../enums/Application Management/Applications/CiCdAppDetailsPageEnum";
import { DeploymentStrategyEnum } from "../../../enums/Application Management/Applications/DeploymentStrategyEnum";
import { DeploymentType, SymbolDeploymentType } from "../../../enums/Application Management/Applications/DeploymentTriggerEnum";

export class CiCdAppDetailsPage extends BasePage {

    readonly envDropdown: Locator;
    readonly envList: Locator;
    readonly commitInfoCard: Locator;
    readonly closeIcon: Locator;
    readonly appDetailsTabButton: Locator;
    readonly detailsLink: Locator;
    readonly scaleUpDownWorkflowButton: Locator;
    readonly hibernateAppButton: Locator;
    readonly confirmationModalPrimaryButton: Locator;
    readonly securityCardHeading: Locator;
    readonly securityVulnerabilitiesDetialsLink: Locator;
    readonly securityCardTotalCount: Locator;
    readonly securityCardTippy: Locator;
    readonly securityVulnerabilityCard: Locator;
    readonly resourceNodeDeployment: Locator;
    readonly resourceNodePod: Locator;
    readonly resourceNodeReplicaSet: Locator;
    readonly nodeResourceDotButton: Locator;
    readonly checkVulOption: Locator;
    readonly threeDotsLocator: Locator;
    readonly resourceDeleteButton: Locator;
    readonly gitCommitInfoCardWrapper: Locator;
    readonly hibernateUnhibernateButton: Locator;
    readonly deploymentWindowConfirmationActionButton: Locator;
    readonly commonModal: Locator;
    readonly vulnerabilityModalButtonResourceTree: Locator;
    readonly appDetailsRotatePodsModalButton: Locator;
    readonly restartWorkloadButton: Locator;
    readonly canaryStrategyCard: Locator;
    readonly strategyTitle: Locator;
    readonly canaryStrategyLiveText: Locator;
    readonly canaryStrategyPercentage: Locator;
    readonly canaryStrategyPausedText: Locator;
    readonly canaryBlueGreenStrategyManageTrafficButton: Locator;
    readonly rollbackButton: Locator;
    readonly manageCanaryTrafficModalTitle: Locator;
    readonly triggerStepButton: Locator;
    readonly triggerNextStepButton: Locator;
    readonly closeManageTraffic: Locator;
    readonly swaptrafficconfirmationinput: Locator;
    readonly swapTrafficText: Locator;
    readonly promotingFullText: Locator;
    readonly rolloutCompleteBlueGreen: Locator;
    readonly blueGreenInProgress: Locator;
    readonly rolloutToNextStepCanary: Locator;
    readonly rolloutToFullCanary: Locator;
    readonly configDriftStatusIcon: Locator
    readonly deploymentStatusDetailsButton: Locator;
    readonly successGreenTick: Locator;
    readonly closeModalHeaderIconButton: Locator;
    readonly deploymentSucceededStatusForTimeline: Locator;
    readonly deploymnetInitiatedBy: Locator;
    readonly pushManifestToGit: Locator;
    readonly applyKubernetesToManifest: Locator;
    readonly propogateManifestToKubernetesResoources: Locator;


    // Constructor to initialize the class with a Playwright Page
    constructor(public page: Page) {
        super(page)
        this.envDropdown = this.page.locator('//*[contains(@class,"app-environment-select")]');
        this.envList = this.page.locator('//*[contains(@class,"app-environment-select__menu-list")]');
        this.commitInfoCard = page.getByTestId("deployed-commit-card");
        this.closeIcon = this.page.locator('//button[@data-testid="visible-modal-close"]');
        this.appDetailsTabButton = this.page.locator(`//*[text()="App details" or text()="App Details"]`);
        this.detailsLink = this.page.locator('//*[text()="Details"]');
        this.scaleUpDownWorkflowButton = page.getByTestId('app-details-hibernate-modal-button');
        this.hibernateAppButton = page.getByTestId('confirmation-modal-primary-button');
        this.confirmationModalPrimaryButton = page.getByTestId('confirmation-modal-primary-button');
        this.securityCardHeading = page.getByTestId('security-card-title');
        this.securityCardTotalCount = page.locator(`//*[@class='fw-6 fs-14 lh-21 cn-9' or  @data-testid="security-card-total-count"]`);
        this.securityVulnerabilitiesDetialsLink = page.locator(`//*[@data-testid='security-card-show-details-button' or @class="fw-6 fs-13 lh-18 cb-5"]`);
        this.securityCardTippy = page.getByTestId("security-card-tippy");
        this.securityVulnerabilityCard = page.getByTestId('security-vulnerability-card');
        this.resourceNodeDeployment = page.getByTestId("resource-node-deployment");
        this.resourceNodePod = page.getByTestId("resource-node-pod");
        this.resourceNodeReplicaSet = page.getByTestId("resource-node-replicaset");
        this.nodeResourceDotButton = page.getByTestId("node-resource-dot-button");
        this.checkVulOption = page.getByText('Check vulnerabilities');
        this.threeDotsLocator = page.getByTestId("node-resource-dot-button");
        this.resourceDeleteButton = page.getByTestId("delete-resource-button");
        this.gitCommitInfoCardWrapper = this.page.locator(`//*[contains(@class,'git-commit-info-generic__wrapper')]`);
        this.hibernateUnhibernateButton = this.page.getByTestId('app-details-hibernate-modal-button');
        this.deploymentWindowConfirmationActionButton = this.page.getByTestId('deployment-window-confirmation-dialog-action-button');
        this.commonModal = page.getByTestId('common-modal');
        this.vulnerabilityModalButtonResourceTree = page.getByTestId('open-vulnerability-modal-button');
        this.appDetailsRotatePodsModalButton = page.getByTestId('app-details-rotate-pods-modal-button');
        this.restartWorkloadButton = page.getByTestId('restart-workloads');

        // Canary Strategy Card locators
        this.configDriftStatusIcon = page.getByTestId('config-drift-status');
        this.rolloutToNextStepCanary = page.getByTestId('rollout-to-next-step')
        this.rolloutToFullCanary = page.getByTestId('Rollout full');
        this.canaryStrategyCard = page.locator(`//*[@class='flexbox-col bg__primary w-220 br-8 border__primary mr-16 dc__overflow-hidden']`);
        this.strategyTitle = page.locator('//div[contains(@class, "canary-strategy-card")]//div[text()="Canary Strategy"]');
        this.canaryStrategyLiveText = page.locator('//div[contains(@class, "canary-strategy-card")]//div[text()="Live for"]');
        this.canaryStrategyPercentage = page.locator('//div[contains(@class, "canary-strategy-card")]//div[contains(text(), "% users")]');
        this.canaryStrategyPausedText = page.locator('//div[contains(@class, "canary-strategy-card")]//div[text()="Paused"]');
        this.canaryBlueGreenStrategyManageTrafficButton = page.getByTestId('strategy-action-button');
        this.rollbackButton = page.getByTestId('rollback-button');
        this.manageCanaryTrafficModalTitle = this.page.locator('//div[text()="Manage Canary Traffic"]');
        this.triggerStepButton = this.page.locator('//button[contains(text(), "Trigger Step")]');
        this.triggerNextStepButton = this.page.locator('//button[contains(text(), "Trigger Next Step")]');
        this.closeManageTraffic = this.page.getByTestId('close-manage-traffic');
        this.swaptrafficconfirmationinput = this.page.getByTestId('swap-traffic-confirmation-input');
        this.swapTrafficText = this.page.locator(`//span[contains(text(), 'Blue Green Strategy')]/following-sibling::div[text()='Awaiting traffic swap']`)
        this.promotingFullText = this.page.locator(`//span[contains(text(), 'Blue Green Strategy')]/following-sibling::div[text()='Promoting']`)
        this.rolloutCompleteBlueGreen = this.page.locator(`//span[contains(text(), 'Blue Green Strategy')]/following-sibling::div[text()='Rollout Complete']`);
        this.blueGreenInProgress = this.page.locator(`//span[contains(text(), 'Blue Green Strategy')]/following-sibling::div[text()='In Progress']`);
        this.deploymentStatusDetailsButton = this.page.getByTestId('deployment-status-details');
        this.successGreenTick = this.page.getByTestId('success-green-tick');
        this.closeModalHeaderIconButton = this.page.getByTestId('close-modal-header-icon-button');
        this.deploymentSucceededStatusForTimeline = this.page.getByTestId('succeeded-status');
        this.deploymnetInitiatedBy = this.page.locator(`//*[contains(text(),'Deployment initiated by')]`);
        this.pushManifestToGit = this.page.locator(`//*[contains(text(),'Push manifest to Git')]`);
        this.applyKubernetesToManifest = this.page.locator(`//*[contains(text(),'Apply manifest to Kubernetes')]`);
        this.applyKubernetesToManifest = this.page.locator(`//*[contains(text(),'Propagate manifest to Kubernetes resources')]`)
    }

    /**
    *  selecting environment from dropdown
    * @param {string} envName - env to be selected
    */
    async selectEnvironment(envName: string) {
        await expect(async () => {
            await this.appDetailsTabButton.click();
            await this.envDropdown.first().click();
            await this.envList.locator(`//*[text()="${envName}"]`).click();
        }).toPass({ timeout: 3 * 1000 * 60 });
    }

    /**
    *  verifying commit info
    * @param {Array} commitInfo - Array of commit information objects
    * @param {string} image - image to be verified (optional)
    */
    async verifyCommitInfo(commitInfo: { branchName: string, commitHash: string, commitCardNumber: number }[], image?: string) {
        await this.appDetailsTabButton.click();
        await this.commitInfoCard.locator(this.detailsLink).click();
        let allCommitCards = await this.gitCommitInfoCardWrapper.all();
        let successfullVerifiedCommits: number = 0;
        for (let key of commitInfo) {
            for (let card of allCommitCards) {
                if (await card.locator(`//*[text()="${key.branchName}"]`).isVisible()) {
                    successfullVerifiedCommits++;
                    break;
                }
            }
        }
        expect(successfullVerifiedCommits).toBe(commitInfo.length);
        if (image) {
            await expect(this.page.locator(`//*[text()="${image}"]`)).toBeVisible({ timeout: 2 * 1000 * 60 });
        }
        await this.closeIcon.click();
    }

    /**
    *  scaling up and down workflow
    * @param {boolean} scalingUp : if we want to scale up then we have to pass true otherwise false , default value is false
    */
    async scaleUpAndDownWorkflow(scalingUp: boolean = false) {
        await this.scaleUpDownWorkflowButton.click();
        if (scalingUp) {
            //await this.confirmationModalPrimaryButton.click();
            await BaseTest.checkToast(this.page, this.confirmationModalPrimaryButton, 'Success');
        }
        else {
            //await this.hibernateAppButton.click();
            await BaseTest.checkToast(this.page, this.hibernateAppButton, 'Success');
        }
    }

    /**
    *  verify security scan card on detail page
    */

    async verifySecurityScanCard(securityscanData: any) {

        await expect
            .poll(
                async () => {
                    await this.page.reload();
                    await setTimeout(5000);
                    const securityCardHeading = await this.securityVulnerabilityCard.isVisible();
                    return securityCardHeading;
                },
                {
                    // Custom error message, optional.
                    message: 'Failed to establish connection to the terminal', // custom error message
                    // Poll for 10 seconds; defaults to 5 seconds. Pass 0 to disable timeout.
                    timeout: 5 * 60 * 1000,
                }
            )
            .toBe(true);
        await expect(this.securityCardHeading).toHaveText("Security");
        await expect(this.securityCardTippy).toBeVisible();
        await expect(this.securityVulnerabilitiesDetialsLink).toBeVisible();
        await expect(this.securityVulnerabilitiesDetialsLink).toHaveText("Details");
        if (securityscanData.imageScanVul) {
            await expect(this.securityCardTotalCount).toBeVisible();
            await expect(this.securityCardTotalCount).toHaveText(/^[a-z0-9A-Z].*$/);
        }




    }
    async getVulnerabilitiesCountOnSecurityCard() {
        // var vulnerabilitiesCount=await this.securityCardTotalCount.textContent() as string;
        // //vulnerabilitiesCount=vulnerabilitiesCount?.substring(0,vulnerabilitiesCount.length-16);
        // var str:string="";
        // for(let i=0;i<vulnerabilitiesCount.length;i++){
        //     if(vulnerabilitiesCount[i]==' ')break;
        //     str+=vulnerabilitiesCount[i];
        // }
        // var totalVulnerabilityCount=Number(str);
        // console.log(totalVulnerabilityCount);
        // return totalVulnerabilityCount;

        for (let key in credentials.vulnerabilitiesLabel) {
            if (await this.page.getByTestId(`segmented-bar-chart-${key}-value`).nth(0).isVisible()) {
                let count: String = await this.page.getByTestId(`segmented-bar-chart-${key}-value`).nth(0).textContent() as string;
                credentials.vulnerabilitiesLabel[key] = count;
            }
        }

    }

    async verifySecurityScanCardAndModal(isImageScanEnabled: boolean, isCodeScanEnabled: boolean, isKubernetesManifestEnabled: boolean, isCheckVulnerabilityOrGlobalSecurityPage: boolean) {

        if (!isCheckVulnerabilityOrGlobalSecurityPage) {
            await this.getVulnerabilitiesCountOnSecurityCard();
            if (await this.page.getByTestId(`security-vulnerability-card`).isVisible({ timeout: 5000 })) {
                this.page.getByTestId(`security-vulnerability-card`).click();
            } else {
                await this.page.locator(`//*[contains(text(),'Image Scan')]/following-sibling::div//*[contains(text(),'vulnerabilities')]`).click()
            }
        }

        if (isImageScanEnabled && process.env.clusterType == "enterprise") {
            for (let i = 0; i < credentials.securitySidebarImageScan.length; i++) {
                if (credentials.securitySidebarImageScan[i] === "Vulnerability" || credentials.securitySidebarImageScan[i] === "License Risks") {
                    await expect(this.page.locator(`//button[@data-testid="tree-view-heading-Image Scan"]/following::button[@data-testid="tree-view-item-${credentials.securitySidebarImageScan[i]}"][1]`)).toBeVisible();
                } else {
                    await expect(this.page.getByTestId(`tree-view-heading-${credentials.securitySidebarImageScan[i]}`)).toBeVisible();
                }
            }
        }

        if (isCodeScanEnabled) {
            for (let i = 0; i < credentials.securitySidebarCodeScan.length; i++) {
                if (credentials.securitySidebarCodeScan[i] === "Code Scan") {
                    await expect(this.page.getByTestId(`tree-view-heading-${credentials.securitySidebarCodeScan[i]}`)).toBeVisible();
                } else {
                    await expect(this.page.locator(`//button[@data-testid="tree-view-heading-Image Scan"]/following::button[@data-testid="tree-view-item-${credentials.securitySidebarCodeScan[i]}"][1]`)).toBeVisible();
                }
            }
        }


        if (isKubernetesManifestEnabled) {
            for (let i = 0; i < credentials.securitySidebarKubernetesManifest.length; i++) {
                if (credentials.securitySidebarKubernetesManifest[i] === "Misconfigurations" || credentials.securitySidebarKubernetesManifest[i] === "Exposed Secrets") {
                    await expect(this.page.locator(`//button[@data-testid="tree-view-heading-Kubernetes Manifest"]/following::button[@data-testid="tree-view-item-${credentials.securitySidebarKubernetesManifest[i]}"][1]`)).toBeVisible();
                } else {
                    await expect(this.page.getByTestId(`tree-view-heading-${credentials.securitySidebarKubernetesManifest[i]}`)).toBeVisible();
                }
            }
        }

        if (await this.page.locator(`//*[@data-testid="security-table-cell-0:0"]`).isVisible()) {
            await this.page.locator(`//*[@data-testid="security-table-cell-0:0"]`).click({ position: { x: 10, y: 10 } });
            if (await this.page.locator(`//*[@data-testid="security-table-cell-0:0"]`).isVisible()) {
                await this.page.locator(`//*[@data-testid="security-table-cell-0:0"]`).click({ position: { x: 10, y: 10 } });
                await expect(this.page.getByText('cve id')).toBeVisible();
                await expect(this.page.getByText('severity').last()).toBeVisible();
                await expect(this.page.getByText('package')).toBeVisible();
                await expect(this.page.getByText('current version')).toBeVisible();
                await expect(this.page.getByText('fixed in version')).toBeVisible();

                for (let key in credentials.vulnerabilitiesLabel) {
                    if (await this.page.getByTestId(`segmented-bar-chart-${key}-value`).last().isVisible()) {
                        var count = Number(await this.page.getByTestId(`segmented-bar-chart-${key}-value`).last().textContent() as string);
                        if (!isCheckVulnerabilityOrGlobalSecurityPage) {
                            if (!(await this.page.getByTestId('app-details-rotate-pods-modal-button').isVisible())) {
                                //expect(count).toBe(Number(credentials.vulnerabilitiesLabel[key]));
                                console.log(count);
                            }
                        }
                        var severity = key.toLowerCase();
                        var severityCount: number = await this.page.locator(`//*[contains(text(),'${severity}')]`).count()
                        console.log(severityCount)
                        //expect(severityCount).toBe(count);
                    }
                }
            }
        }
        await this.page.locator(`//*[@data-testid="close-security-modal"]`).click();
    }

    async verifySecurityScanCardAndModalAtAppDetailsPage(securityscanData: any, isImageScan: boolean = false, isCodeScanEnabled: boolean = false, isKubernetesManifest: boolean = false) {
        await this.verifySecurityScanCard(securityscanData);
        await this.verifySecurityScanCardAndModal(isImageScan, isCodeScanEnabled, isKubernetesManifest, false);
    }



    async goToAppDetails() {
        await this.appDetailsTabButton.click();
        await this.page.waitForTimeout(3000);
    }


    async verifyArgoAndFluxTimelineForDeploymentType(deploymentType: DeploymentType) {
        await this.deploymentStatusDetailsButton.waitFor({ state: "visible" });
        await this.deploymentStatusDetailsButton.click();
        await expect(this.deploymentSucceededStatusForTimeline).toHaveText('Succeeded');
        if (deploymentType != DeploymentType.HELM) {
            const tickCount = (deploymentType == DeploymentType.FLUX) ? 3 : 5;
            await expect(await this.successGreenTick.count()).toBe(tickCount);
            await this.deploymnetInitiatedBy.waitFor({ state: 'visible' });
            await this.pushManifestToGit.waitFor({ state: 'visible' });
            await this.page.locator(`//*[contains(text(),'Synced with ${deploymentType} CD')]`).waitFor({ state: 'visible' });
            if (deploymentType === DeploymentType.ARGO) {
                await this.applyKubernetesToManifest.waitFor({ state: 'visible' });
                await this.applyKubernetesToManifest.waitFor({ state: 'visible' });
            }
        }
        await this.closeModalHeaderIconButton.click()
    }

    async verifyDeploymentTypeSymbolAtAppDetailsPage(deploymentType: SymbolDeploymentType) {
        await this.page.getByTestId(`${deploymentType}-app-logo`).waitFor({ state: "visible" });
    }


    /**
     * for this method, we are assuming that we have already clicked on target resource , if not then call another function open resource present on chartStoreAppDetailsPage
     */
    async deleteAnyResource(isEligible: boolean) {
        await expect(async () => {
            await this.threeDotsLocator.click();
            await this.resourceDeleteButton.click();
            let messageToVerify: string = isEligible ? 'Success' : 'Not authorized'
            await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, messageToVerify);
        }).toPass({ timeout: 4 * 1000 * 60 });
    }

    async clickOnScanModalResourceTree() {
        await this.nodeResourceDotButton.waitFor();
        await this.nodeResourceDotButton.click({ delay: 2000 });
        await this.commonModal.waitFor();
        await this.vulnerabilityModalButtonResourceTree.waitFor();
        await this.vulnerabilityModalButtonResourceTree.click({ delay: 2000 });
        await this.page.getByTestId("close-security-modal").waitFor({ timeout: 10000 });

    }
    async clickOnExternalLinkAndVerifyLink(linkName: string, isNewTab: boolean, linkUrl: string, isLinkVisible: boolean = true) {
        await this.appDetailsTabButton.click();
        let linkPage: Page;

        if (isLinkVisible) {
            if (isNewTab) {
                [linkPage] = await Promise.all([this.page.waitForEvent('popup'), this.page.locator(`//*[@alt="${linkName}"]/parent::button`).click()]);
                console.log('url we get' + linkPage.url());
            }
            else {
                await this.page.locator(`//*[@alt="${linkName}"]/parent::button`).hover();
                let [linkPage] = await Promise.all([this.page.waitForEvent('popup'), this.page.locator(`//*[@alt="${linkName}"]/parent::button/following-sibling::a`).click()]);
                console.log('url we get' + linkPage.url());
            }
            await linkPage!.route(linkUrl, route => {
                route.fulfill({
                    status: 404,
                    contentType: 'text/html',
                    body: '<h1>404 Not Found</h1>',
                });
            });
            await linkPage!.reload();
            expect(linkPage!).toHaveURL(linkUrl);
        }
        else {
            await expect(this.threeDotsLocator).toBeVisible();
            await expect(this.page.locator(`//*[@alt="${linkName}"]/parent::button`)).toBeHidden();
        }

    }
    async triggerHibernationOrUnhibernation(isHibernation: boolean, blackoutWindowData?: { isEligible: boolean }, isHibernationPatchConfigured: boolean = true) {
        await this.appDetailsTabButton.click();
        await this.hibernateUnhibernateButton.waitFor();
        let currentState = await this.hibernateUnhibernateButton.textContent();
        let didWePerformTheAction: boolean = false;
        if (isHibernation == !currentState?.includes('Restore')) {
            await this.hibernateUnhibernateButton.click();
            didWePerformTheAction = true;
        }
        else if (!isHibernation && !currentState?.includes('restore')) {
            await this.hibernateUnhibernateButton.click();
            didWePerformTheAction = true;
        }
        if (blackoutWindowData && didWePerformTheAction) {
            if (blackoutWindowData.isEligible) {
                let textToEnter: string = isHibernation ? "Hibernate" : 'Unhibernate';
                await this.deleteModalInputField.fill(textToEnter);
                await BaseTest.checkToast(this.page, this.deploymentWindowConfirmationActionButton, 'Success');
            }
            else {
                await expect(this.deleteModalInputField).toBeHidden();
                await this.page.reload();
            }
        }
        if (!isHibernationPatchConfigured) {
            await this.dialogDeleteConfirmationButton.click();
        }
        else if (didWePerformTheAction && !blackoutWindowData) {
            await this.dialogDeleteConfirmationButton.click();
        }
    }


    async restartWorkload() {
        this.triggerHibernationOrUnhibernation(false, undefined)
        await this.appDetailsRotatePodsModalButton.waitFor({ timeout: 10000 });
        await this.appDetailsRotatePodsModalButton.click();
        await this.undefinedChkSpan.nth(0).waitFor({ timeout: 10000 });
        await this.undefinedChkSpan.nth(0).click();
        await this.restartWorkloadButton.waitFor();
        await BaseTest.checkToast(this.page, this.restartWorkloadButton, 'Success');

    }

    /**
     * Verify the Canary Strategy Card and its components
     * @param {CanaryVisibilityStatus} visibilityStatus - Whether the canary card should be visible
     * @param {CanaryDeploymentStatus} deploymentStatus - Whether the canary is live or paused
     * @param {string} percentage - The percentage of users for canary (optional)
     * 
     * 
     */


    /**
     * Verify the Canary Strategy Card and its components
     * @param {DeploymentStrategyEnum} strategy - The deployment strategy to verify
     */
    async verifyCanaryStrategyCard(
        strategy: DeploymentStrategyEnum
    ) {
        await this.appDetailsTabButton.click();

        // Verify the card is visible
        let displayStrategy = (strategy === DeploymentStrategyEnum.ADD_STRATEGY_BLUE_GREEN) ? 'Blue Green' : strategy;
        await this.page.locator(`//*[contains(text(),'${displayStrategy} Strategy')]`).waitFor({ timeout: 10000 });

        if (strategy === DeploymentStrategyEnum.ADD_STRATEGY_CANARY) {
            await this.clickManageTrafficButton();
            await this.page.locator(`//*[text()='Rollout to 100% users']/ancestor::[@class='flex dc__gap-4']//*[text()='In Progress']`).waitFor({ state: 'visible' });
            await this.closeManageTraffic.waitFor();
            await this.closeManageTraffic.click()
        }
    }

    /**
     * Click on the Manage Traffic button in the Canary Strategy Card
     * @returns {Promise<void>}
     */
    async clickManageTrafficButton(): Promise<void> {
        await this.appDetailsTabButton.click();
        await this.canaryBlueGreenStrategyManageTrafficButton.click();
    }

    /**
     * Get the current percentage of users for the canary deployment
     * @returns {Promise<string>} The percentage value as a string
     */
    async getCanaryPercentage(): Promise<string> {
        await this.appDetailsTabButton.click();
        await expect(this.canaryStrategyCard).toBeVisible({ timeout: 10000 });
        const percentageText = await this.canaryStrategyPercentage.textContent() || '';
        return percentageText.replace(/[^0-9]/g, '');
    }

    /**
     * Check if the canary deployment matches a specific percentage
     * @param {CanaryPercentage} expectedPercentage - The expected percentage from enum
     * @returns {Promise<boolean>} True if the percentage matches
     */
    async isCanaryPercentageEqual(expectedPercentage: CanaryPercentage): Promise<boolean> {
        const actualPercentage = await this.getCanaryPercentage();
        return actualPercentage === expectedPercentage;
    }

    /**
     * Verifies that canary is rolled out to the specified percentage of users
     * @param {string} percentage - The percentage of users (e.g. "25%")
     * @returns {Promise<void>}
     */
    async verifyCanaryRolloutPercentage(percentage: string): Promise<void> {
        // Ensure we're on the app details tab
        await this.appDetailsTabButton.click();

        // Verify the canary rollout percentage is visible with specified value
        await expect(
            this.page.locator(`//span[contains(text(), 'Canary Strategy')]/following-sibling::div[text()='Live for ${percentage}% users']`)
        ).toBeVisible({ timeout: 5000000 });

        console.log(`✅ Verified canary deployment is live for ${percentage} users`);
    }




    /**
 * Manage the Canary Traffic Modal that appears after clicking the Manage Traffic button
 * @param {string} action - The action to perform: 'rollout', 'pause', 'run-analysis', or 'set-scale'
 * @param {string} percentage - The percentage value for rollout (25, 50, 75, 100)
 * @returns {Promise<void>}
 */
    async manageCanaryTrafficModal(action: 'rollout-to-next-step' | 'rollout-to-users', percentage: string): Promise<void> {
        // Verify modal is open
        await this.clickManageTrafficButton();

        if (action === 'rollout-to-next-step') {
            await BaseTest.checkToast(this.page, this.rolloutToNextStepCanary, 'Triggered rollout to next step');
            await this.verifyCanaryRolloutPercentage(percentage);
        }

        if (action === 'rollout-to-users' && (percentage == '100')) {
            await this.rolloutToFullCanary.click();
            await this.verifyCanaryRolloutPercentage(percentage);
        }

    }



    async clickOnRollbackButton() {
        await this.rollbackButton.waitFor();
        await this.rollbackButton.click();
    }

    async chooseStrategyToRollbackTo(stratagy: string) {
        await this.rollbackButton.waitFor();
        await this.rollbackButton.click();
        await BaseTest.checkToast(this.page, this.page.getByTestId('cd-trigger-deploy-button').nth(1), 'Success');

    }


    /**
     * Gets the count of pods from the deployment details
     * @param {string} podType - Which pods to count: 'old' or 'new'
     * @returns {Promise<number>} The count of pods
     */
    async getPodCount(podType: 'old' | 'new'): Promise<number> {
        // Ensure we're on the app details page
        await this.appDetailsTabButton.click();

        // Select the appropriate locator based on pod type
        const selector = podType === 'old'
            ? '[data-testid="all-pods-old"]'
            : '[data-testid="all-pods-new"]';

        // Get the text content
        const text = await this.page.locator(selector).textContent();

        console.log("exaxt test is " + text);
        // Extract the number using regex
        const match = text?.match(/\d+/);

        // Log error and return 0 if no match found
        if (!match) {
            console.error(`Failed to extract pod count from "${text}" for ${podType} pods`);
            return 0;
        }

        // Parse and return the number
        return parseInt(match[0], 10) % 10;
    }


    /**
     * Waits for a specific distribution of pods to be reached
     * @param expectedDistribution - Expected distribution of pods {old: number, new: number}
     * @param options - Optional timeout and intervals
     */
    async waitForPodDistribution(
        expectedDistribution: { old: number, new: number },
        options?: { timeout?: number, intervals?: number[] }
    ): Promise<void> {
        await expect.poll(
            async () => {
                const oldCount = await this.getPodCount('old');
                const newCount = await this.getPodCount('new');
                console.log(`Pod counts - Old: ${oldCount}, New: ${newCount}`);
                return { old: oldCount, new: newCount };
            },
            {
                message: `Waiting for pod distribution Old: ${expectedDistribution.old}, New: ${expectedDistribution.new}`,
                timeout: options?.timeout || 900000, // Default to 7 minutes
                intervals: options?.intervals || [90000, 120000, 130000, 140000]
            }
        ).toEqual(expectedDistribution);
    }


    /**
     * Manage the Blue-Green deployment modal for traffic operations
     * @param {string} action - The action to perform: 'Swap traffic' or 'Skip & promote full'
     * @param {string} env - Environment name for confirmation
     * @returns {Promise<void>}
     */
    async manageBlueGreenModal(action: 'Swap traffic' | 'Skip & promote full', env: string): Promise<void> {
        // Wait for the appropriate initial state based on action
        if (action === 'Swap traffic') {
            await expect(this.swapTrafficText).toBeVisible({ timeout: 4500000 });
        } else { // 'Skip & promote full'
            await expect(this.blueGreenInProgress).toBeVisible({ timeout: 4000000 });
        }

        // Common steps for both actions
        await this.clickManageTrafficButton();
        await this.swaptrafficconfirmationinput.fill(env);
        await BaseTest.checkToast(this.page, this.confirmationModalPrimaryButton, 'Traffic Swap Initiated');

        // Post-action state check for 'Skip & promote full'
        if (action === 'Skip & promote full') {
            await expect(this.promotingFullText).toBeVisible({ timeout: 4000000 });
        }

        // Final state check for both actions
        await expect(this.rolloutCompleteBlueGreen).toBeVisible({ timeout: 5000000 });
    }


    async openConfigDriftModal() {
        await this.configDriftStatusIcon.click();
        await this.page.waitForTimeout(2000);
    }

    async verifyDriftForSpecificK8sResourceInLiveManifest(resourceType: string, resourceName: string) {
        const page = this.page;
        await this.openConfigDriftModal();

        // 1. Locate the section toggle button for resourceType
        const sectionButton = page.locator(`button:has(span:text("${resourceType}"))`);

        // 2. Read the --rotateBy style from only the first svg inside the button
        const rotateSvg = sectionButton.locator('svg').first();
        const rotation = await rotateSvg.evaluate(el =>
            el?.style?.getPropertyValue('--rotateBy') || ''
        );

        // 3. Click to expand if it's collapsed
        if (rotation.includes('-90deg')) {
            await sectionButton.click();
            await page.waitForTimeout(500); // optional: small wait to allow expansion animation
        }

        // 4. Locate the specific resource within the expanded section
        const resourceButton = page.locator(
            `button.collapsible__item:has(span:text("${resourceName}"))`
        );
        await expect(resourceButton).toBeVisible({ timeout: 5000 });
        await resourceButton.click();

        // 5. Confirm Config Drift indicator
        const configDriftIndicator = page.locator(
            'div.flexbox.dc__gap-4.dc__align-items-center.dc__no-shrink >> text=Config Drift'
        );
        await expect(configDriftIndicator).toBeVisible();
    }

    async getAttachedCmOrCsNameViaAppDetailsPage(resourceType: 'cm' | 'secret') {
        // Step 1: Expand "Config & Storage" only if not already expanded
        const dropdownIcon = this.page.locator('[data-testid="config & storage-dropdown"]');

        const rotateValue = await dropdownIcon.evaluate((el) => {
            return window.getComputedStyle(el).getPropertyValue('--rotateBy').trim();
        });

        // Only click if collapsed (0deg)
        if (rotateValue === '0deg') {
            await this.page.getByTestId('config & storage').click();
        }

        // Step 2: Click the appropriate resource type tab
        const tabTestId = resourceType === 'cm' ? 'resource-node-configmap' : 'resource-node-secret';
        await this.page.getByTestId(tabTestId).click();

        // Step 3: Wait for at least 2 resource names to appear
        const rowsLocator = this.page.locator('[data-testid="resource-node-name"]');
        await rowsLocator.nth(1).waitFor({ state: 'visible', timeout: 5000 });

        // Step 4: Extract the second name
        const secondRow = rowsLocator.nth(1);
        const secondName = await secondRow.innerText();

        // Step 5: Click the copy button
        const copyButton = this.page.locator(`button[aria-label="Copy ${secondName}"]`);
        await copyButton.waitFor({ state: 'visible', timeout: 3000 });
        await copyButton.click();

        return secondName;
    }





}





