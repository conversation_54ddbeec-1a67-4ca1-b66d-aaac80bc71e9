import { Page, Locator, expect } from '@playwright/test';
import exp from 'constants';
import { BaseTest } from '../../../utilities/BaseTest';

export class GitRepositoryPage {
  readonly gitRepositoryTab: Locator;
  readonly gitRepoHeading: Locator;
  readonly selectProjectInputContainer: Locator;
  readonly repoUrlPlaceholder: Locator;
  readonly gitRepoUrlTextBox: Locator;
  readonly gitRepositorySaveButton: Locator;
  readonly nextButton: Locator;
  readonly setCloneDirectoryCheckbox: Locator;
  readonly cloneDirectoryPath: Locator;
  readonly addGitRepositoryButton: Locator; readonly gitRepoPageHeading: Locator;
  readonly alreadyExistingGitMaterial: Locator;
  readonly excludeSpecificFileFolderCheckbox: Locator;
  readonly excludeIncludeCommitTextbox: Locator;

  constructor(private page: Page) {
    // Locators initialization
    this.gitRepositoryTab = page.locator('//*[@data-testid="git-repository-link" or @data-testid="source-code-link"]');
    this.gitRepoHeading = page.locator('//*[@data-testid="git-repositories-heading" or @data-testid="source-code-heading"]')
    this.selectProjectInputContainer = page.locator('.material-view__select-project__input-container');
    this.repoUrlPlaceholder = page.getByPlaceholder('e.g. https://gitlab.com/abc/');
    this.gitRepoUrlTextBox = page.getByTestId('git-repo-url-text-box');
    this.gitRepositorySaveButton = page.getByTestId('git-repository-save-button');
    this.nextButton = page.getByTestId('app-compose-next-button');
    this.gitRepoPageHeading = this.page.locator('//*[@data-testid="git-repositories-heading" or @data-testid="source-code-heading"]');
    this.setCloneDirectoryCheckbox = page.getByTestId("set-clone-directory-checkbox");
    this.cloneDirectoryPath = page.locator("//*[@name='clone-directory-path']");
    this.addGitRepositoryButton = page.getByTestId("add-multi-git-repo");
    this.alreadyExistingGitMaterial = page.getByTestId('already-existing-git-material');
    this.excludeSpecificFileFolderCheckbox = page.locator('[data-testid="undefined-chk-span"]').nth(0);
    this.excludeIncludeCommitTextbox = page.locator('[data-testid="exclude-include-commit-textbox"]');

  }

  /**
   * Adds a GitHub repository with the provided URL and Git account name.
   * @param url - The URL of the GitHub repository.
   * @param gitAccountName - The Git account name for repository selection.
   */
  async addGitHubRepository(url: string, gitAccountName: string, checkoutpath: string = "") {
    try {
      await expect(async () => {
        await this.gitRepositoryTab.click();
        await this.gitRepoHeading.waitFor();
        if (await this.addGitRepositoryButton.isVisible({ timeout: 20000 })) {
          await this.addGitRepositoryButton.click();
        }
        // Click the project input container and select the repository
        await this.gitRepoPageHeading.waitFor({ timeout: 25000 });
        await this.selectProjectInputContainer.click({ timeout: 10000 });
        await this.selectRepositoryFromDropDown(gitAccountName);

        // Click the repo URL placeholder and enter URL
        await this.repoUrlPlaceholder.click();
        if (await this.repoUrlPlaceholder.getAttribute('value') == '') {
          await this.repoUrlPlaceholder.pressSequentially(url);
        }

        if (checkoutpath != "") {
          await this.setCloneDirectoryCheckbox.click();
          await this.cloneDirectoryPath.fill(checkoutpath);
        }

        // Click the Save button for the Git repository
        await this.gitRepositorySaveButton.click();
        await expect(this.page.locator('//*[@data-testid="already-existing-git-material"]').first()).toBeVisible({ timeout: 19000 });
      }).toPass({ timeout: 4 * 1000 * 60 });
      // Click the Next button after saving the Git repository
      await this.clickNextButtonAfterSavingGitRepository();
    } catch (error) {
      console.error(`An error occurred while adding GitHub repository: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * Selects a repository from the dropdown by entering its name.
   * @param repositoryName - The name of the repository to be selected.
   */
  private async selectRepositoryFromDropDown(repositoryName: string) {
    // Wait for the page to reach a stable state
    await this.page.waitForLoadState('load');

    // Click the project input container and enter repository name
    await this.selectProjectInputContainer.click({ timeout: 20000 });
    await this.selectProjectInputContainer.pressSequentially(repositoryName);

    // Click the repository in the dropdown
    await this.page.locator('.material-view__select-project__menu-list').getByText(repositoryName, { exact: true }).click();
  }

  /**
   * Clicks the Next button after saving the Git repository.
   */
  async clickNextButtonAfterSavingGitRepository() {
    await this.page.locator('//*[@data-testid="already-existing-git-material"]').first().waitFor({ timeout: 30000 });
    if (await this.nextButton.isVisible({ timeout: 10000 })) {
      await expect(async () => {
        await this.nextButton.click();
        await expect(this.page.locator('//*[text()="Workflows" or text()="Container Registry"]').first()).toBeVisible({ timeout: 30000 });
      }).toPass({ timeout: 3 * 1000 * 60 });
    }
  }

  async verifyGitAccountAndRepoConfigured(data: { gitAccountName: string, gitRepoUrl: string }[], isCreateAppPage: boolean = true) {
    if (!isCreateAppPage) {
      await this.gitRepositoryTab.click();
      for (let i = 0; i < data.length; i++) {
        await this.alreadyExistingGitMaterial.first().click();
      }
    }
    for (let i = 0; i < data.length; i++) {
      await expect(this.selectProjectInputContainer.nth(i).locator('xpath=preceding-sibling::div')).toContainText(data[i].gitAccountName);
      let urlValue = await this.repoUrlPlaceholder.nth(i).getAttribute('value');
      expect(urlValue).toBe(data[i].gitRepoUrl);
    }

  }

  async excludeSpecificFileFolder(fileOrFolderPath: string) {
    await this.page.getByTestId("app-config-link").click();
    await this.gitRepositoryTab.click();
    const checkboxInput = this.page.locator('input[type="checkbox"]').nth(0);
    try {
      await expect(async () => {
        await this.alreadyExistingGitMaterial.first().click();
        if (!(await checkboxInput.isChecked())) {
          await this.excludeSpecificFileFolderCheckbox.click();
        }
        await this.excludeIncludeCommitTextbox.click();
        await this.excludeIncludeCommitTextbox.fill(fileOrFolderPath);
        await this.gitRepositorySaveButton.click();
      }).toPass({ timeout: 2 * 1000 * 60 });
    } catch (error) {
      console.error(`An error occurred while saving entered file or folder path for GitHub repository: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }





}
