import { expect, Page, Locator } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { dockerfile } from '../../../utilities/clipboardyYamls.ts/YamlForResourceBrowser';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { BasePage } from '../../../utilities/BasePage';
import { JobsPage } from '../../Jobs/Jobs';
import { PrePostCiCd } from './PrePostCiCd';
import { GitopsConfigurationPage } from '../Configurations/GItopsConfigurationPage';
import { BuildxConfigurationDTO } from '../../../DTOs/BuildxConfigurationDTO';
import { AddCdModuleDTO } from '../../../DTOs/AddCdModuleDTO';
import { WorkflowTypeEnum } from '../../../enums/WorkflowTypeEnum';
import { DeploymentTriggerEnum } from '../../../enums/DeploymentTriggerEnum';
import {WorkflowConfigDTO} from '../../../DTOs/WorkflowConfigDTO';
import {VirtualEnvPushConfigDTO} from '../../../DTOs/VirtualEnvPushConfigDTO';
import { LinkedPipelineConfigDTO } from "../../../DTOs/WorkflowConfigDTO";
import {VerifyCdNodesDTO} from "../../../DTOs/VerifyCdNodesDTO";



export class AppConfigurationPage extends BasePage {
  // Locators
  readonly page: Page;
  private ciCdNodesSelector: Locator;
  readonly ciDeletePipelineButton: Locator;
  private deleteJobAppButton: Locator;
  readonly appConfigurationTab: Locator;
  private workflowDiv: Locator;
  private closeModealButton: Locator;
  private prePostDropdown: Locator;
  private customCdTagInputField: Locator;
  private customTagCounterField: Locator;
  readonly updatePipelineButton: Locator;
  readonly appNameForLinkedPipelineDropdown: Locator;
  private deployToButton: Locator;
  private changeSourceIcon: Locator;
  readonly selectLanguageDropdown: Locator;
  readonly appNameForLinkedPipelineInput: Locator;
  readonly appNameForLinkedPipelineMenuList: Locator;
  readonly sourceCiPipelineField: Locator;
  readonly sourceCiPipelineFieldInput: Locator;
  readonly linkedPipelineName: Locator;
  readonly createLinkedPipelineNameButton: Locator;
  readonly linkedBuildPipelineButton: Locator;
  readonly newWorkflowButton: Locator;
  readonly buildDeployByClick: Locator;
  readonly pipelineNameField: Locator;
  readonly ciModal: Locator;
  readonly linkedBuildPipelineModalHeader: Locator;
  readonly deleteLinkedPipelineButton: Locator;
  readonly linkedSymbolHeading: Locator;
  readonly ciPipelineNameField: Locator;
  readonly ciBuildCloseButton: Locator;
  readonly workFlowEditorTab: Locator;
  readonly scanWithVulnerabilities: Locator;
  readonly chartTypeOption: Locator;
  readonly templateLoader: Locator;
  readonly basicArgumentField: Locator;
  readonly advancedYamlButton: Locator;
  readonly closeButtonBuildAndDeploy: Locator;
  readonly deployImageFromExternalCILink: Locator;
  readonly buildPipelineButton: Locator;
  readonly buildDeployFromSourceCodeButton: Locator;
  readonly branchTypeDropdown: Locator;
  readonly branchTypePullRequest: Locator;
  readonly branchTypeTagCreation: Locator;
  readonly createBuildPipelineAdvancedOptionsButton: Locator;
  readonly ciBuildPipelineNameField: Locator;
  readonly cdEnvironmentInput: Locator;
  readonly cdEnvironmentInputContainer: Locator;
  readonly branchNamePlaceholder: Locator;
  readonly gitopsDeploymentSpan: Locator;
  readonly imagePullDigestToggleButton: Locator;
  readonly preDeploymentCustomTagOptionOncd: Locator;
  readonly postDeploymentCustomTagOptionOncd: Locator;
  readonly jobsAndDevtronAppsNewWorkflowButton: Locator;
  readonly linkedCdNode: Locator;
  readonly buildPakcEnvVriableInputField: Locator;
  readonly overrideOptionsText: Locator;
  readonly ciAllowOverrideButton: Locator;
  readonly dockerBuildArgsAddParameterButton: Locator;
  readonly dockerBuildArgsKeyInputField: Locator;
  readonly dockerBuildArgsValueInputField: Locator;
  readonly buildxTargetPlatformDropdown: Locator;
  readonly locatorForPageStabilityVerification: Locator;
  readonly pushToRegistryRadioButton: Locator;
  readonly selectRegistryInput: Locator;
  readonly enterRepository: Locator;
  readonly goToBuildDeployLink: Locator;
  readonly progressing: Locator;
  readonly jobsAndDevtronappsStablePageLocator: Locator;
  readonly enterEnvironmentToDelete: Locator;
  readonly deleteModal: Locator;
  readonly externalLinksButton: Locator;
  readonly removeTargetPlatformIcon: Locator;
  readonly ciNodeNameDiv: Locator;
  readonly ciNodeDiv: Locator;
  readonly typeOfCiNodeDiv: Locator;
  readonly workflowHeader: Locator;
  readonly deploymentStrategyDropdown: Locator;
  readonly jobPage: JobsPage;
  readonly prepostCiCdPage: PrePostCiCd;
  readonly childAddIconToClick: Locator;
  readonly dialogDeleteButton: Locator;
  readonly dialogDeleteConfirmationButton: Locator;
  readonly deleteModalInputField: Locator;

  constructor(page: Page) {
    super(page);
    // Set up locators in the constructor
    this.page = page;
    this.ciCdNodesSelector = page.locator("//div[@class='workflow-node__full-width-minus-Icon p-12']");
    this.ciDeletePipelineButton = this.page.locator("//*[contains(@data-testid,'-delete-pipeline-button') or @data-testid='delete-linked-pipeline']");
    this.dialogDeleteButton = page.locator('//*[@data-testid="dialog-delete" or @data-testid="confirmation-modal-primary-button"]');
    this.deleteJobAppButton = page.locator('[data-testid="delete-job-app-button"]');
    this.appConfigurationTab = page.locator(`//*[contains(@data-testid,'-config-link')]`);
    this.appNameForLinkedPipelineDropdown = page.locator(".link-pipeline-filter-application__control");
    this.appNameForLinkedPipelineInput = page.locator(".link-pipeline-filter-application__input");
    this.appNameForLinkedPipelineMenuList = page.locator(".link-pipeline-filter-application__menu-list");
    this.sourceCiPipelineField = page.getByTestId("source-ci-pipeline-container");
    this.sourceCiPipelineFieldInput = page.getByTestId("source-ci-pipeline-input");
    this.linkedPipelineName = page.getByTestId("pipeline-name-for-linked");
    this.createLinkedPipelineNameButton = page.getByTestId("create-linked-ci-button");
    this.linkedBuildPipelineButton = page.getByTestId("linked-build-pipeline-button");
    this.newWorkflowButton = page.getByTestId('new-workflow-button');
    this.buildDeployByClick = page.getByText("Build & Deploy", { exact: true });
    this.pipelineNameField = page.locator("//input[@placeholder='Enter pipeline name']");
    this.ciModal = page.locator("//div[contains(@class,'modal__body--ci')]");
    this.linkedBuildPipelineModalHeader = page.getByText("Linked build pipeline", { exact: true });
    this.deleteLinkedPipelineButton = page.getByTestId("delete-linked-pipeline");
    this.linkedSymbolHeading = page.getByTestId("linked-symbol");
    this.ciPipelineNameField = page.locator("//*[@placeholder='e.g. my-first-pipeline']");
    this.workflowDiv = this.page.locator('//*[contains(@class,"workflow__body")]');
    this.childAddIconToClick = this.page.locator('//*[@class="add-cd-edge-btn"]');
    this.closeModealButton = this.page.getByTestId("close-build-deploy-button");
    this.prePostDropdown = this.page.locator('//*[contains(@class,"select-custom-image-tag-cd")]').first();
    this.customCdTagInputField = this.page.locator('//*[@name="image_tag"]');
    this.customTagCounterField = this.page.locator('//*[@name="image_counter"]');
    this.updatePipelineButton = this.page.locator("//*[contains(@data-testid,'build-pipeline-button') or contains(@data-testid,'linked-cd')]");
    this.newWorkflowButton = page.getByTestId('new-workflow-button');
    this.deployToButton = page.getByTestId('sync-env-deploy-to-btn');
    this.changeSourceIcon = this.page.locator("//*[contains(@class,' workflow-header-action-btn ')]");
    this.ciBuildCloseButton = this.page.locator("//*[@class='dc__transparent flex icon-dim-24']");
    this.workFlowEditorTab = this.page.getByTestId("workflow-editor-link");
    this.scanWithVulnerabilities = page.getByTestId('create-build-pipeline-scan-vulnerabilities-toggle');
    this.selectLanguageDropdown = this.page.locator('//*[contains(@class,"build-pack-language-dropdown__")]');
    this.chartTypeOption = this.page.locator('//*[@class="chart-type-options"]')
    this.basicArgumentField = page.locator('//*[text()="Arguments"]')
    this.templateLoader = page.locator('//*[@class="loader__svg"]')
    this.advancedYamlButton = page.getByTestId("base-deployment-template-advanced-button");
    this.closeButtonBuildAndDeploy = page.getByTestId("close-build-deploy-button");
    this.deployImageFromExternalCILink = page.getByTestId("deploy-image-external-service-link");
    this.buildPipelineButton = page.getByTestId('build-pipeline-button');
    this.buildDeployFromSourceCodeButton = page.getByTestId('build-deploy-from-source-code-button');
    this.branchTypeDropdown = page.locator("//*[contains(@class,'select-build-pipeline-sourcetype-0__control')]");
    this.branchTypeTagCreation = page.getByText("Tag Creation");
    this.branchTypePullRequest = page.getByText("Pull Request");
    this.createBuildPipelineAdvancedOptionsButton = page.getByTestId("create-build-pipeline-advanced-options-button");
    this.ciBuildPipelineNameField = page.getByPlaceholder('e.g. my-first-pipeline');
    this.branchNamePlaceholder = page.locator('//*[@data-testid="branchName" or @data-testid="branchRegex"]')
    this.gitopsDeploymentSpan = page.getByTestId('gitops-deployment-span');
    this.cdEnvironmentInputContainer = page.locator('.cd-pipeline-environment-dropdown__input-container');
    this.cdEnvironmentInput = page.locator('.cd-pipeline-environment-dropdown__input');
    this.imagePullDigestToggleButton = this.page.getByTestId('create-build-pipeline-image-pull-digest-toggle');
    this.preDeploymentCustomTagOptionOncd = this.page.locator('//*[text()="Pre-deployment stage"]');
    this.postDeploymentCustomTagOptionOncd = this.page.locator('//*[text()="Post-deployment stage"]');
    this.jobsAndDevtronAppsNewWorkflowButton = this.page.locator('//*[@data-testid="new-workflow-button" or @data-testid="job-pipeline-button" or text()=" Completed" ]');
    this.linkedCdNode = this.page.locator('//*[contains(@data-testid,"workflow-editor-link-cd")]');
    this.buildPakcEnvVriableInputField = this.page.getByTestId('build-pack-build-env-value0');
    this.overrideOptionsText = this.page.locator('//*[text()="Override Options"]');
    this.ciAllowOverrideButton = this.page.getByTestId('create-build-pipeline-allow-override-button');
    this.dockerBuildArgsAddParameterButton = this.page.getByTestId('create-build-pipeline-docker-args-add-parameter-button');
    this.dockerBuildArgsKeyInputField = this.page.locator('//*[@name="arg-key"]');
    this.dockerBuildArgsValueInputField = this.page.locator('//*[@placeholder="Value"]');
    this.buildxTargetPlatformDropdown = this.page.locator('//*[contains(@class,"target-platform__select__indicator target-platform__select__dropdown-indicator")]');
    this.locatorForPageStabilityVerification = this.page.locator("//*[contains(@class,'workflow-action-header') or contains(@class,'app-compose__main')]").first();
    this.pushToRegistryRadioButton = this.page.getByText('Push to registry');
    this.selectRegistryInput = this.page.locator(".cd-trigger__select-container-registry__input");
    this.enterRepository = this.page.locator('//*[@name="repository_name"]');
    this.goToBuildDeployLink = this.page.getByTestId('go-to-build-deploy-link');
    this.progressing = this.page.getByTestId('progressing');
    this.jobsAndDevtronappsStablePageLocator = this.page.locator(`//*[contains(@class,"workflow-node") or text()="Workflows"]`);
    this.enterEnvironmentToDelete = this.page.getByPlaceholder('Type to confirm');
    this.deleteModal = this.page.getByTestId('confirmation-modal-primary-button');
    this.externalLinksButton = this.page.locator(`//*[text()="External Links"]`);
    this.removeTargetPlatformIcon = this.page.locator(`//*[contains(@aria-label,'Remove')]`);
    this.ciNodeDiv = this.page.locator(`//*[contains(@data-testid,'workflow-editor-ci-node')]`);
    this.typeOfCiNodeDiv = this.page.getByTestId('linked-indication-name');
    this.ciNodeNameDiv = this.ciNodeDiv.locator(this.typeOfCiNodeDiv).locator('xpath=following-sibling::div')
    this.workflowHeader = this.page.getByTestId('workflow-header');
    this.deploymentStrategyDropdown = this.page.locator(`//*[contains(@class,'deployment-strategy-dropdown__control')]`);
    this.jobPage = new JobsPage(page);
    this.prepostCiCdPage = new PrePostCiCd(page);
    this.dialogDeleteConfirmationButton = this.page.getByTestId('confirmation-modal-primary-button');
  }


  async createNewLinkedPipeline(linkedPipelineConfigDTO:LinkedPipelineConfigDTO) {
    await this.appNameForLinkedPipelineDropdown.click();
    await this.appNameForLinkedPipelineInput.fill(linkedPipelineConfigDTO.appName);
    await expect(this.appNameForLinkedPipelineMenuList.getByText(linkedPipelineConfigDTO.appName)).toBeVisible({ timeout: 25000 });
    await this.appNameForLinkedPipelineMenuList.getByText(linkedPipelineConfigDTO.appName).click();
    await this.sourceCiPipelineField.click();
    await this.sourceCiPipelineFieldInput.fill(linkedPipelineConfigDTO.sourcePipelineName);
    await this.page.getByTestId(`source-ci-pipeline-menu-list-${linkedPipelineConfigDTO.sourcePipelineName}`).click();
    if (linkedPipelineConfigDTO.checkValidation) {
      await BaseTest.checkToast(this.page, this.createLinkedPipelineNameButton, "Error");
    }
    await this.pipelineNameField.fill(linkedPipelineConfigDTO.linkedPipelineName);
    await this.createLinkedPipelineNameButton.click();
  }


  async fetchPipelineIdOfAnyCiNode(workflowNumber: number = 0) {
    await this.appConfigurationTab.click();
    await this.workflowDiv.nth(workflowNumber).locator(this.ciNodeNameDiv).waitFor();
    return await this.workflowDiv.nth(workflowNumber).locator(this.ciNodeNameDiv).textContent();
  }

/**
 * Deletes all CI/CD nodes and the application.
 * 
 * 
 */
  async deleteNodesFromWorkflow(workflowLocator: Locator) {
    const applicationUrl = await this.page.url();
    const nodeCount = await workflowLocator.locator(`//*[@class="data-hj-whitelist"]`).count();
    for (let node = nodeCount; node >= 0; node--) {
      if (await workflowLocator.locator(`//*[@class="data-hj-whitelist"]`).nth(node).isVisible()) {
        //await this.page.locator(`//*[contains(@class,'workflow--create')][${i}]//*[@class="data-hj-whitelist"]`).nth(node).click({delay:3000});
        await workflowLocator.locator(`//*[@class="data-hj-whitelist"]`).nth(node).click({ delay: 3000 });
        await this.ciDeletePipelineButton.waitFor({ state: 'visible' });
        await this.ciDeletePipelineButton.click({ delay: 3000 });
        //
        try {
          await this.page.locator('//*[@placeholder="Type to confirm"]').waitFor({ timeout: 7000 });
          let text = await this.page.locator(`//*[contains(@class,'confirmation-modal')]//*[@data-testid="delete-cd-node-input-label"]`).textContent();
          console.log('mine value is ' + text!.split("‘")[1].split("’")[0].trim());
          await this.page.locator(`//*[@name="delete-cd-node-input"]`).fill(text!.split("‘")[1].split("’")[0].trim());

        } catch (error) {
          console.log('modal did not come');
        }
        await this.dialogDeleteButton.click();
        if (await this.page.locator(`//*[contains(text(),'Please delete deployment pipelines for this workflow first and try again')]`).isVisible({ timeout: 7000 })) {
          await this.page.goto(applicationUrl);
          break;
        }
        try {
          await this.page.locator('//*[text()="Force Delete"]').waitFor({ timeout: 3000 });
          await this.page.locator(`//*[text()="Force Delete"]`).click({ delay: 3000 });
        }
        catch (error) {
          console.log(error)
        }
      }
      await this.page.waitForTimeout(5000);
    }
  }

  async deleteAppsFromUI(apiUtils: ApiUtils) {
    const TESTID_TRIGGER_JOB = 'trigger-job-link';
  
    const isJob = await this.page.locator(`//*[@data-testid="${TESTID_TRIGGER_JOB}"]`).isVisible({ timeout: 7000 });
    const appOrJob = isJob ? 'job' : 'app';
  
    await this.navigateToConfig(appOrJob);
    const appId = await this.extractAppIdFromUrl();
    const responseBody = await apiUtils.fetchEnvOnAppConfigurationsPage(appId);
  
    await this.deleteLinkedCDs(responseBody);
    await this.deleteLinkedCIs();
    await this.deleteStandaloneCDs(responseBody);
    await this.deleteRemainingWorkflows();
  
    await this.confirmDeletion();
  
    console.log('Deletion of all elements completed successfully.');
  }
  
   async navigateToConfig(appOrJob: string) {
    await this.page.getByTestId(`${appOrJob.toLowerCase()}-config-link`).click();
    await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
  }
  
   async extractAppIdFromUrl(): Promise<number> {
    const applicationUrl = await this.page.url();
    return Number(applicationUrl.split('/')[5]);
  }
  
   async deleteLinkedCDs(responseBody: any) {
    for (const key in responseBody.result) {
      const env = responseBody.result[key].environmentName;
      const cdLink = this.page.getByTestId(`workflow-editor-link-cd-${env}`);
      if (await cdLink.isVisible()) {
        const workflowLocator = this.page.locator(`//*[@data-testid="workflow-editor-link-cd-${env}"]/ancestor::*[contains(@class,'workflow--create')]`);
        await this.deleteNodesFromWorkflow(workflowLocator);
      }
    }
  }
  
   async deleteLinkedCIs() {
    await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
    const workflows = this.page.locator(`//*[contains(@class,'workflow--create')]`);
    const count = await workflows.count();
  
    for (let i = count; i > 0; i--) {
      const workflow = workflows.nth(i - 1);
      const hasLinkedSymbol = await workflow.locator(`.//*[@data-testid="linked-symbol"]`).isVisible();
      if (!hasLinkedSymbol) {
        await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
        await this.deleteNodesFromWorkflow(workflow);
      }
    }
  }
  
  async deleteStandaloneCDs(responseBody: any) {
    for (const key in responseBody.result) {
      const env = responseBody.result[key].environmentName;
      const cdNode = this.page.getByTestId(`workflow-editor-cd-node-${env}`);
      if (await cdNode.isVisible()) {
        await this.deleteParticularCd(env);
      }
    }
  }
  
  private async deleteRemainingWorkflows() {
    await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
    const workflows = this.page.locator(`//*[contains(@class,'workflow--create')]`);
    const count = await workflows.count();
  
    for (let i = count; i > 0; i--) {
      const workflow = workflows.nth(i - 1);
      await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
      await this.deleteNodesFromWorkflow(workflow);
    }
  }
  
  private async confirmDeletion() {
    await this.deleteJobAppButton.click({ delay: 3000 });
    await this.dialogDeleteButton.click();
    await this.page.waitForTimeout(5000);
  }
  

  async deleteAllCiCdNodesAndApplication(appOrJob: string, linkedci: boolean = false, linkedCd: boolean = false) {
    try {
      await this.page.getByTestId(`${appOrJob.toLowerCase()}-config-link`).click();
      await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
      await this.page.waitForTimeout(5000); // Optional: replace with more targeted wait
  
      let cdNodes = await this.ciCdNodesSelector.all();
      let cdCount = cdNodes.length - 1;
      let count = 0;
  
      while (cdCount >= 0) {
        const ciCdNodeList = await this.ciCdNodesSelector.all();
        console.log('Number of elements to be deleted:', ciCdNodeList.length);
  
        await this.page.waitForTimeout(2000); // Optional: replace with wait for specific UI change
  
        if (linkedCd && count === 1) {
          console.log('Deleting linked CD node...');
          await this.linkedCdNode.click();
          await this.ciDeletePipelineButton.click();
          await this.dialogDeleteConfirmationButton.click();
        }
  
        // Select the current node for deletion
        await this.ciCdNodesSelector.nth(cdCount).click();
  
        if (linkedci) {
          await expect(this.ciModal).toBeVisible();
          await expect(this.linkedBuildPipelineModalHeader).toBeVisible();
          await this.deleteLinkedPipelineButton.click();
          linkedci = false; // Unset after one-time use
        } else {
          await this.ciDeletePipelineButton.click();
        }
  
        // Handle confirmation modal if present
        try {
          await this.deleteModalInputField.waitFor({ timeout: 7000 });
          const modalText = await this.page
            .locator(`//*[contains(@class,'confirmation-modal')]//*[contains(@data-testid,'input-label')]`)
            .textContent();
          const confirmValue = modalText!.split("‘")[1].split("’")[0].trim();
          await this.deleteModalInputField.fill(confirmValue);
        } catch {
          console.log('Confirmation modal input did not appear.');
        }
  
        await this.dialogDeleteConfirmationButton.click();
  
        // Handle optional Force Delete
        try {
          const forceDeleteBtn = this.page.locator('//*[text()="Force Delete"]');
          await forceDeleteBtn.waitFor({ timeout: 3000 });
          await forceDeleteBtn.click();
        } catch {
          // No force delete button shown — continue
        }
  
        // Wait for deletion to complete
        await this.dialogDeleteConfirmationButton.waitFor({ state: 'hidden', timeout: 45000 });
        await this.jobsAndDevtronappsStablePageLocator.first().waitFor({ timeout: 60000 });
        await this.ciCdNodesSelector.nth(cdCount).waitFor({ state: 'hidden' });
  
        count += 1;
        cdNodes = await this.ciCdNodesSelector.all();
        cdCount = cdNodes.length - 1;
        console.log('Updated CD count:', cdCount);
      }
  
      // Final delete for the app/job itself
      await this.deleteJobAppButton.click();
      await this.dialogDeleteConfirmationButton.click();
      await this.page.waitForTimeout(5000);
  
      console.log('Deletion of all elements completed successfully.');
    } catch (error) {
      console.error('Error during deleteAllCiCdNodesAndApplication:', error);
      throw error; // Rethrow to ensure test fails
    }
  }
  

  /**
  * Select first ci Node to open build configuration -
  */
  async selectCiNode() {
    await this.appConfigurationTab.click();
    await this.page.waitForTimeout(2000)
    await this.workFlowEditorTab.click({ delay: 300 });
    await this.page.waitForTimeout(3000)
    await this.ciCdNodesSelector.first().click();

  }

  /**
      *  adding new cd's
      * @param {string} workflowNumber - in which workflow user want to operate
      * @param {string} parentCd - parent cd from which we want to add another cd
      * @param {string} isMiddleCdOrCi -if it is ci or middle cd , then we give two options to user(series,parallel)
      * @param {string} seriesOrParallel - we again give option to user for series or parallel
      */
  async clickOnAddIconToAddCd(workflowNumber: number, nodeName: string, childAddIconToClick: number) {
    await this.appConfigurationTab.click();
    await this.workflowDiv.nth(workflowNumber).locator(`//*[contains(@data-testid,'${nodeName}') and contains(@data-testid,'add-button')]`).click();
    if (await this.childAddIconToClick.first().isVisible()) {
      await this.childAddIconToClick.nth(childAddIconToClick).locator(`xpath=parent::*[local-name()='svg']`).click();
    }
  }


  /**
     *  configuring ci-cd's
     * @param {string} workflowNumber - in which workflow user want to operate
     * @param {string} parentCd - parent cd from which we want to add another cd or which we want to configure
     * just pass the name for ci pass "ci" else environment name
     */
  async clickOnSpecificCiCdNode(workflowNumber: number, parentCd: string) {
    await this.appConfigurationTab.click();
    await this.workflowDiv.nth(workflowNumber).locator(`//*[contains(@data-testid,"-${parentCd}")]`).first().click();
  }

  async addBranchNameAndUpdate(branchName: string) {
    await this.branchNamePlaceholder.last().fill(branchName);
    await this.buildPipelineButton.click();
  }
  async closeUpdationModal() {
    try {
      await this.closeModealButton.click({ timeout: 8000 });
    }
    catch (error) {
      console.log('modal did not come');
    }

  }

  /**
     *  setting up customTag for pre-post cd
     * @param {string} prePostStage - in which stage user want to operate
     * @param {string} valueToEnter - tag value
     */

  async setupCustomTagForCd(prePostStage: string, valueToEnter: string, counterValue: string = "0") {
    await this.selectDeploymentTag(prePostStage);
    await this.customCdTagInputField.clear();
    await this.customCdTagInputField.fill(valueToEnter);
    await this.setCustomTagCounter(counterValue);
    await this.updatePipelineButton.click();
  }
  
  private async selectDeploymentTag(prePostStage: string) {
    await this.prePostDropdown.click();
    if (prePostStage.toLowerCase() === "pre") {
      await this.preDeploymentCustomTagOptionOncd.first().click();
    } else {
      await this.postDeploymentCustomTagOptionOncd.first().click();
    }
  }
  
  private async setCustomTagCounter(counterValue: string) {
    await this.customTagCounterField.clear();
    await this.customTagCounterField.fill(counterValue);
  }
  
  async goToWorkflowEditorTab() {
    await this.workFlowEditorTab.click();
  }

  async goToAppConfigurationPage(buildAndDeployUrl: string) {
    await this.page.goto(buildAndDeployUrl);
    await this.appConfigurationTab.waitFor({ state: 'visible' });
    await this.appConfigurationTab.click();
  }

  /**
   * this method can be used for entry point for creating workflows of any type , rest you have to configure separetely 
   * @param workfloyType 
   */

  async CreateWorkflowAnyType(workfloyType: string) {
    await this.appConfigurationTab.click();
    await this.newWorkflowButton.click();
    await this.page.locator(`//*[@data-pipeline-type="${workfloyType}"]`).click();
  }


  /**
   * this method is used to setup a linked cd workflow
   * @param imageSrcEnv parent env that you want to make src 
   * @param deployToEnv env name on which u want to deploy
   */
  async configureLinkedCdWorkflow(imageSrcEnv: string, deployToEnv: string) {
    await this.page.locator(`//*[@class="drawer right show"]//*[text()="${imageSrcEnv}"]`).click();
    if (await this.deployToButton.isVisible()) {
      await this.deployToButton.click();
      await this.page.locator(`//*[@class="drawer right show"]//*[text()="${deployToEnv}"]`).click();
      await this.updatePipelineButton.click();
      if (await this.page.locator("//*[text()='GitOps repository is not configured for the app']").isVisible()) {
        expect(this.page.locator('//*[@aria-label="close-modal"]')).toBeVisible();
        await this.page.locator('//*[@aria-label="close-modal"]').click();
        const gitopsConfigurationPage = new GitopsConfigurationPage(this.page);
        gitopsConfigurationPage.ClickOnSaveButton();
      }
      await this.closeUpdationModal();
    }
    else {
      await this.updatePipelineButton.click();
    }

  }


  /**
   * this method is used to change the image soruce of any workflow
   * @param workflowNumber workflow number that ypu want to change 
   * @param workfloyType type of workflow that u want to set
   */
  async changeImageSourceOfAnyWorkflow(workflowNumber: number, workfloyType: string) {
    await this.appConfigurationTab.click();
    await this.page.locator('//*[@data-testid="workflow-header"]').nth(workflowNumber).hover();
    await this.changeSourceIcon.nth(workflowNumber).click();
    await this.page.locator(`//*[@data-pipeline-type="${workfloyType}"]`).click();
  }


  /**
   * this method is used to check whether the button has been locked or not
   * @param locator 
   * @param isBlocked 
   */
  async checkIfButtonIsBlockedOrNot(locator: Locator, isBlocked: boolean) {
    await expect(locator).toBeDisabled();
  }


  /**
   * turn on image scan 
   */
  async clickOnScanninWithVulnerabilities() {
    await this.scanWithVulnerabilities.click();
    await this.updatePipelineButton.click();
  }

  /**
   * this method is used to configure buildx without dockerfile
   */
  async BuildWithoutDockerFile() {
    //reverting new code editor
    if (await this.codeMirrorEditorTextArea.textContent() != "") {
      await this.page.locator('//*[text()="FROM"]').first().dblclick();
      for (var i = 0; i < 12; i++) {
        if (process.env.OS as string == "Mac") {
          await this.page.keyboard.press('Meta+a');
        }
        else {
          await this.page.keyboard.press('Control+a');
        }
        await this.page.waitForTimeout(1000);
      }
      await this.page.keyboard.press('Backspace');
    }

    await dockerfile();
    //reverting new code editor 
    await this.codeMirrorEditorTextArea.click({ delay: 2000 });
    await this.page.keyboard.press("Shift+Insert");
    try {
      await expect(this.page.locator('//*[text()="CMD"]')).toBeVisible({ timeout: 15000 });
    }
    catch (error) {
      console.error(error);
      throw error;
    }

  }


  /**
   * confivure buildx with builder packs
   * @param buildxConfigurationDTO 
   */
  async buildWithBuilderPack(buildxConfigurationDTO: BuildxConfigurationDTO) {
    const keys = Object.keys(buildxConfigurationDTO.builderPackConfigurationDTO!);
    for (var i = 0; i < keys.length; i++) {
      const key = keys[i];
      const value = buildxConfigurationDTO.builderPackConfigurationDTO![key];
      await this.page.locator(`//*[contains(@class,'build-pack') and contains(@class,'-${key}-dropdown__indicators')]`).click();
      await this.page.locator(`//*[contains(@class,'build-pack') and contains(@class,'-${key}-dropdown__menu-list')]//*[text()="${value}"]`).click();
    }
    var value: string | null = "";
    if (buildxConfigurationDTO.builderPackConfigurationDTO?.version != "Auto detect") {
      value = await this.buildPakcEnvVriableInputField.textContent();
    }
    expect(value).toBe(`${buildxConfigurationDTO.builderPackConfigurationDTO?.version}`);
  }

  async clickOnAllowOverrideOnCiNode() {
    await this.overrideOptionsText.waitFor({ timeout: 40000 });
    if (await this.ciAllowOverrideButton.isVisible()) {
      await this.ciAllowOverrideButton.click();
    }
  }

  /**
   * this is the parent method that we call to configure buildx 
   * @param buildxConfigurationDto 
   */

  async configureBuildx(buildxConfigurationDto: BuildxConfigurationDTO) {
    // Ensure necessary actions before processing
    await this.clickOnAllowOverrideOnCiNode();
  
    if (!buildxConfigurationDto.sourceOfImage) {
      console.error('Source of image is not provided');
      return;
    }
  
    // Select image source in UI
    await this.page.locator(`//*[contains(text(),"${buildxConfigurationDto.sourceOfImage}")]`).click();
  
    // Define helper function for target platform setting
    const setTargetPlatformIfNeeded = async () => {
      if (buildxConfigurationDto.setTargetPlatform) {
        await this.setTargetPlatformForBuild([buildxConfigurationDto.setTargetPlatform]);
      }
    };
  
    // Process different image sources based on `sourceOfImage`
    switch (buildxConfigurationDto.sourceOfImage) {
      case 'Create Dockerfile': {
        await setTargetPlatformIfNeeded();
        await this.BuildWithoutDockerFile();
        break;
      }
  
      case 'Build without Dockerfile': {
        // Handle build with builder pack
        if (buildxConfigurationDto.builderPackConfigurationDTO) {
          await this.buildWithBuilderPack(buildxConfigurationDto);
        } else {
          console.error('Builder pack configuration is missing');
        }
        break;
      }
  
      case 'have a Dockerfile': {
        await setTargetPlatformIfNeeded();
        if (buildxConfigurationDto.envVariables && buildxConfigurationDto.envVariables.length > 0) {
          await this.addEnvVariables(buildxConfigurationDto.envVariables);
        }
        break;
      }
  
      default: {
        console.error('Unexpected source of image:', buildxConfigurationDto.sourceOfImage);
      }
    }
  
    // Finalize configuration update
    await this.updatePipelineButton.click();
  }
  
  async addEnvVariables(data: string[]) {
    for (var i = 0; i < data.length; i++) {
      await this.dockerBuildArgsAddParameterButton.click();
      await this.dockerBuildArgsKeyInputField.first().fill(data[i].split(':')[0]);
      await this.dockerBuildArgsValueInputField.first().fill(data[i].split(':')[1]);
    }
  }

  async setTargetPlatformForBuild(targetPlatform: string[]) {

    await this.buildxTargetPlatformDropdown.click();
    let alreadyexistedPlatforms = await this.removeTargetPlatformIcon.count();
    console.log('count value is' + alreadyexistedPlatforms);
    for (let i = alreadyexistedPlatforms - 1; i >= 0; i--) {
      await this.removeTargetPlatformIcon.nth(i).click();
    }
    for (let key of targetPlatform) {
      await this.page.locator(`//*[text()="${key}"]`).click();
    }
  }


  /**
   * Creates a External CI workflow with CI
   * @param envNameForCD - The environment name for CD.
   */
  async configureDetailsForExternalCI(addCdModuleDTO: AddCdModuleDTO) {
    try {
      await this.addCdModule(addCdModuleDTO);
    } catch (error) {
      console.error(`Error during createNewWorkflow: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async closeBuildAndDeployButtonModal() {
    await this.closeButtonBuildAndDeploy.click();
  }

  async createNewWorkflowCiJobs(pipelineName: string) {
    await this.newWorkflowButton.click();
    await this.buildDeployFromSourceCodeButton.waitFor({ state: 'visible' });
    if (!this.page.getByTestId('job-ci-pipeline-button').isVisible()) {
      console.log("CI-Jobs Pipeline Button is not visible, May be Enable_CI_Job is false");
    } else {
      await this.page.getByTestId('job-ci-pipeline-button').click();
      await this.ciPipelineNameField.waitFor({ state: 'visible' });
      await this.ciPipelineNameField.fill(pipelineName);
      await this.branchNamePlaceholder.fill('main');
      await this.buildPipelineButton.click();
    }

  }

  /**
   * this method is used to set ci on auto or manually , you have to reach to auto-manual page first 
   * @param stage use Automatically or Manually
   */
  async setCiCdAutoOrManual(autoOrManual: 'Auto' | 'Manual') {
    await this.createBuildPipelineAdvancedOptionsButton.isVisible() ? await this.createBuildPipelineAdvancedOptionsButton.click() : console.log('it is a webhook');
    await this.page.locator(`//span[contains(text(),"${autoOrManual}")]`).click();
  }


  /**
   * Adds CI module to the workflow.
   * @param branchName - The name of the branch.
   * @param pipelineName - optional param for name of workflow to be created.
   */

  async addCiModule(
    workflowConfigDTO: WorkflowConfigDTO,
    deploymentTriggerEnum: DeploymentTriggerEnum
  ) {
    try {
      await expect(async () => {
        const buildConfig = workflowConfigDTO.buildDeployFromSourceCodeConfigDTO;
  
        // Check if build config is defined before using it
        if (buildConfig) {
          // Open branch type dropdown and select the sourceType
          await this.branchTypeDropdown.click();
          await this.page
            .locator('//*[@role="option"]')
            .locator(`//*[text()="${buildConfig.sourceTypeEnum}"]`)
            .click();
  
          // Fill branch names if any exist
          if (buildConfig.branchNamesList && buildConfig.branchNamesList.length > 0) {
            for (let i = 0; i < buildConfig.branchNamesList.length; i++) {
              await this.branchNamePlaceholder.nth(i).fill(buildConfig.branchNamesList[i]);
            }
          }
        }
  
        // Set CI/CD behavior and click build
        await this.setCiCdAutoOrManual(deploymentTriggerEnum);
        await this.buildPipelineButton.click();
  
        // Wait until the build pipeline button is hidden
        await expect(this.buildPipelineButton).toBeHidden({ timeout: 30000 });
      }).toPass({ timeout: 3 * 60 * 1000 }); // 3-minute retry window
    } catch (error) {
      console.error(`Error during addCiModule: ${error}`);
      throw error;
    }
  }
  
  /**
   * this method is used to fill ci branch name 
   * @param branchName 
   */
  async fillCiBranchName(branchName: string) {

    await this.branchNamePlaceholder.click();
    await this.branchNamePlaceholder.pressSequentially(branchName);
    await this.buildPipelineButton.click();
  }

  /**
   * Adds CD module to the workflow.
   * @param envNameForCD - The environment name for CD.
   */

  async addCdModule(addCdModuleDTO: AddCdModuleDTO) {
    try {
      await expect(async () => {
        await this.selectEnvironmentFromDropDown(addCdModuleDTO.envNameForCd, addCdModuleDTO.clusterName);
  
        if ('helmOrGitops' in addCdModuleDTO) {
          await this.gitopsDeploymentSpan.locator('span').first().click();
          await this.page.locator(`//*[@data-testid="${addCdModuleDTO.helmOrGitops}-deployment-span"]`).click();
        }
  
        if (addCdModuleDTO.deploymentStrategy) {
          await this.deploymentStrategyDropdown.click();
          await this.page.locator('//*[@role="option"]').locator(`//*[text()="${addCdModuleDTO.deploymentStrategy}"]`).click();
        }
  
        if ('virtualEnvConfig' in addCdModuleDTO) {
          // baseCdModuleDTO has virtualEnvConfig property, so we can safely pass it
          await this.configureDetailsForVirtualCd(addCdModuleDTO.virtualEnvConfig as VirtualEnvPushConfigDTO);
        }
        
  
        if (addCdModuleDTO.deploymentTrigger === DeploymentTriggerEnum.AUTO || addCdModuleDTO.deploymentTrigger === DeploymentTriggerEnum.MANUAL) {
          await this.setCiCdAutoOrManual(addCdModuleDTO.deploymentTrigger);
        }
  
        await this.buildPipelineButton.click();
  
        await expect(
          this.page.locator(`//*[@data-testid="workflow-editor-page"]//*[text()="${addCdModuleDTO.envNameForCd}"]`)
        ).toBeVisible({ timeout: 25000 });
  
        await this.closeUpdationModal();
      }).toPass({ timeout: 2 * 1000 * 60 });
  
    } catch (error) {
      console.error(`Error during addCdModule: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async configureDetailsForVirtualCd(virtualEnvPushConfig: VirtualEnvPushConfigDTO) {
    await expect(this.pushToRegistryRadioButton).toBeVisible();
    await this.page.locator(`//*[text()="${virtualEnvPushConfig.pushOrNot}"]`).click();
    if ('regName' in virtualEnvPushConfig) {
      await this.selectRegistryInput.click();
      await this.selectRegistryInput.fill(virtualEnvPushConfig.regName || '');
      await this.page.keyboard.press('Enter');
      console.log('repo name we got is' + virtualEnvPushConfig.repoName);
      await this.enterRepository.fill(virtualEnvPushConfig.repoName || '');
      console.log('filled');
    }
  }


  /**
   * Selects environment from CD module dropdown.
   * @param envName - The environment name.
   */
  private async selectEnvironmentFromDropDown(envName: string, clusterName: string = 'default_cluster') {
    try {
      await this.page.waitForLoadState('load');
      await this.cdEnvironmentInputContainer.click({ delay: 400 });
      await this.cdEnvironmentInput.pressSequentially(envName);
      await this.page.locator(`//*[contains(text(),"${clusterName}")]/parent::div//*[text()="${envName}"]`).click();
    } catch (error) {
      console.error(`Error during selectEnvironmentFromDropDown: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * verify that toglle button is checked
   */
  async verifyImagePullToggleIsLocked() {
    await expect(this.imagePullDigestToggleButton).toBeChecked({ timeout: 25000 });
  }


  /**
   * this is the function if you want to verify the visibility/invisibility of nodes and text inside the nodes 
   * @param data -> we will be using 2 loops one to traverse nodes and one to traverse the text inside the nodes 
   */
  async verifyCdNodes(cdNodesDTO: VerifyCdNodesDTO) {
    await this.locatorForPageStabilityVerification.waitFor({ timeout: 12000 });
    for (const text of cdNodesDTO.textToVerify) {
      await this.workflowDiv.first().waitFor();
      let isVisible = await this.page.locator(`//*[contains(@class,"workflow__body")]//*[contains(@data-testid,"-${cdNodesDTO.cdName}")]//*[contains(text(),"${text.text}")]`).isVisible();
      expect(isVisible).toBe(true);
      console.log(isVisible);
      expect(isVisible).toBe(text.isVisible);
    }
  }

  async deleteParticularCd(environmentName: string) {
    await this.appConfigurationTab.waitFor({ state: 'visible' });
    await this.appConfigurationTab.click();
    await this.page.locator(`//*[@data-testid="workflow-editor-cd-node-${environmentName}"]`).waitFor({ state: 'visible' });
    await this.page.locator(`//*[@data-testid="workflow-editor-cd-node-${environmentName}"]`).click();
    await this.progressing.waitFor({ state: 'hidden' });
    await this.ciDeletePipelineButton.waitFor({ state: 'visible' });
    await this.ciDeletePipelineButton.click({ delay: 2000 });
    await this.enterEnvironmentToDelete.waitFor({ state: 'visible' });
    await this.enterEnvironmentToDelete.click();
    await this.enterEnvironmentToDelete.fill(environmentName);
    await this.deleteModal.waitFor({ state: 'visible' });
    await BaseTest.checkToast(this.page, this.deleteModal, 'Pipeline Deletion Initiated');
  }

  async turnOnImageScanToggleButton() {
    await this.scanWithVulnerabilities.waitFor({ state: 'visible' });
    await this.scanWithVulnerabilities.click();
    await BaseTest.checkToast(this.page, this.updatePipelineButton, "Pipeline Updated");
  }

  async createWorkflows(
    workflowType: WorkflowTypeEnum,
    workflowConfigDTO: WorkflowConfigDTO,
    addCdModuleDTO?: AddCdModuleDTO
  ) {
    await this.appConfigurationTab.click();
    const existingWorkflows = await this.fetchExistingWorkflowNames();
    await this.newWorkflowButton.first().click();
    await this.page.locator(`//*[contains(@data-testid,'${workflowType}')]`).click();
  
    // Build & Deploy from Source Code workflow
    if (
      workflowType === WorkflowTypeEnum.BuildDeployFromSourceCode &&
      addCdModuleDTO &&
      workflowConfigDTO.buildDeployFromSourceCodeConfigDTO
    ) {
      await this.addCiModule(workflowConfigDTO, addCdModuleDTO.deploymentTriggerEnum);
    }
  
    // Linked Build workflow
    if (
      workflowType === WorkflowTypeEnum.LinkedBuild &&
      addCdModuleDTO &&
      workflowConfigDTO.linkedBuildPipelineConfigDTO
    ) {
      await this.createNewLinkedPipeline(workflowConfigDTO.linkedBuildPipelineConfigDTO);
    }
  
    // External CI workflow
    if (
      workflowType === WorkflowTypeEnum.DeployImageFromExternalLink &&
      addCdModuleDTO
    ) {
      await this.configureDetailsForExternalCI(addCdModuleDTO);
    }
  
    // Linked CD workflow
    if (
      workflowType === WorkflowTypeEnum.LinkedCd &&
      workflowConfigDTO.linkedCdConfigDTO
    ) {
      await this.configureLinkedCdWorkflow(
        workflowConfigDTO.linkedCdConfigDTO.sourceEnv,
        workflowConfigDTO.linkedCdConfigDTO.destEnv
      );
    }
  
    // Job CI workflow
    if (
      workflowType === WorkflowTypeEnum.JobCi &&
      workflowConfigDTO.jobCiConfigDTO
    ) {
      await this.jobPage.fillJobPipelineDetails(
        workflowConfigDTO.jobCiConfigDTO.pipelineName,
        workflowConfigDTO.jobCiConfigDTO.branchName
      );
      await this.prepostCiCdPage.addPrePostTask("pre", "execute");
      await this.jobPage.executeCustomScript(
        "task-name-" + BaseTest.generateRandomStringWithCharsOnly(3),
        workflowConfigDTO.jobCiConfigDTO.script[0],
        true
      );
    }
  
    // Add CD Module if required (except for BuildDeployFromSourceCode)
    if (
      addCdModuleDTO &&
      workflowType !== WorkflowTypeEnum.BuildDeployFromSourceCode
    ) {
      await this.clickOnAddIconToAddCd(existingWorkflows.length, 'ci', 0);
      await this.addCdModule(addCdModuleDTO);
    }
  }
  
  
  async fetchExistingWorkflowNames(): Promise<string[]> {
    await this.jobsAndDevtronAppsNewWorkflowButton.first().waitFor({ timeout: 25000 });
    let existingWorkflows: string[] = [];
    let workflowHeaderNames = await this.workflowHeader.all();
    console.log('workflows found by us' + workflowHeaderNames.length);
    for (let key of workflowHeaderNames) {
      let name = await key.textContent();
      existingWorkflows.push(name!);
    }
    return existingWorkflows;
  }

  async verifyDetailsOfWorkflow(data: { workflowNumber: number, nodeNumber: number, textToVerify: string[] }) {
    await this.appConfigurationTab.click();
    for (let i = 0; i < data.textToVerify.length; i++) {
      await expect(this.workflowDiv.nth(data.workflowNumber).locator(`//*[contains(@class,'workflow-node ') or contains(@data-testid,'workflow-editor-link-cd')]`).nth(data.nodeNumber)).toContainText(data.textToVerify[i]);
    }

  }

  async configureBranchForMultiGitWithExistingWorkflows(branchName: string[]) {
    await this.workFlowEditorTab.click();
    await this.page.reload();
    await this.jobsAndDevtronAppsNewWorkflowButton.first().waitFor({ timeout: 25000 });
    let allUncofiguredPipelines = await this.page.locator(`//*[text()="Not Configured"]`).all();
    console.log('total length we got is ' + allUncofiguredPipelines.length);
    for (let j = allUncofiguredPipelines.length - 1; j >= 0; j--) {
      await allUncofiguredPipelines[j].click();
      for (let i = 1; i > 0; i--) {
        try {
          await this.branchNamePlaceholder.nth(i).fill(branchName[i - 1], { timeout: 8000 });
        }
        catch (error) {
          console.log('no more branch to fill');
        }
      }
      await this.updatePipelineButton.click();
    }
  }

  async checkAppliedTargetPlatform(targetPlatform: string[]) {
    let completeTextContent = await this.page.locator(`//*[contains(@class,'target-platform__select__multi-value')]`).first().textContent();
    for (let key of targetPlatform) {
      expect(completeTextContent).toContain(key);
    }
  }

  async verifyHelmOrGitopsSelectedInCd(isGitopsSelected: boolean) {
    if (isGitopsSelected) {
      await expect(this.gitopsDeploymentSpan.locator('xpath=preceding-sibling::input')).toBeChecked();
    }
  }
  async verifydeployStratConfiguredOnCd(strat: string) {
    await expect(this.page.locator(`//*[@class="deployment-strategy__info"]`)).toContainText(strat);
  }
}
