import { Locator, Page } from "playwright-core";
import { BaseTest } from "../../../utilities/BaseTest";
import { expect } from "playwright/test";
import { BasePage } from "../../BasePage";

export class PrePostCiCd extends BasePage {

    readonly prebuildButton: Locator;
    readonly postBuildButton: Locator;
    readonly addTaskButton: Locator;
    readonly updatePipelineButton: Locator;
    readonly inputVaiablesDropdown: Locator;
    readonly destinationInfoInputField: Locator;
    readonly outputVariableAddVariableButton: Locator;
    readonly inputVariableAddVariableButton: Locator;
    readonly outputAndInputVariableInputField: Locator;
    readonly passFailureContionButton: Locator;
    readonly passFailureKeyField: Locator;
    readonly passFailValueField: Locator;
    readonly passFailConditionRadioButton: Locator;
    readonly buildPipelineButton: Locator;
    readonly addConditionButton: Locator;
    readonly taskNamesModal: Locator;
    readonly taskOptions: Locator;
    readonly taskRemoveButton: Locator;
    readonly executeCustomScriptPluginButton: Locator;
    readonly searchBar: Locator;
    readonly passFailureKeyMenu: Locator;
    readonly warningIconOnTabLevel: Locator;
    readonly viewPluginsButton: Locator;
    readonly applyMandatoryPluginsButton: Locator;
    readonly containerImageConfigurationButton: Locator;
    readonly entityListDiv: Locator;
    readonly commandInputField: Locator;
    readonly argsInputField: Locator;
    readonly mountCodeToContainerCheckboxNo: Locator;
    readonly mountCodeToContainerCheckboxYes: Locator;
    readonly mountCodeToContainerInputField: Locator;
    readonly mountDirectoryFromHostCheckboxYes: Locator;
    readonly mountDirectoryFromHostCheckboxNo: Locator;
    readonly filePathOnHost: Locator;
    readonly filePathOnContainer: Locator;
    readonly addMappingButton: Locator;
    readonly outputDirectoryAddPathButton: Locator;
    readonly addOutputPathInputField: Locator;
    readonly saveAsPluginButton: Locator;
    readonly pluginNameInputField: Locator;
    readonly pluginIdInputField: Locator;
    readonly pluginVersionInputField: Locator;
    readonly documentationLink: Locator;
    readonly addTagsInPluginDropdownControl: Locator;
    readonly editImageIcon: Locator;
    readonly editImageInputField: Locator;
    readonly previewButton: Locator;
    readonly replaceTaskWithPluginCheckbox: Locator;
    readonly newVersionOfExistingPlugin: Locator;
    readonly selectExistingPluginDropdown: Locator;
    readonly createPluginButton: Locator;
    readonly addOtherVariableButton: Locator;
    readonly setTypeOfVariableDropdown: Locator;
    readonly configButtonForValuesOfVariables: Locator;
    readonly fileMountPathInputFieldInInputOutputVariables: Locator;
    readonly restrictFileTypesInOutputAndInputVariables: Locator;
    readonly enterSizeOfFileInOutputAndInputVariables: Locator;
    readonly fileMaxSizeUnitDropdownInOutputAndInputVariables: Locator;
    readonly addChoiceButton: Locator;
    readonly addChoiceInputFieldInOutputAndInputVariables: Locator;
    readonly envVariableValuConfigurationModalCloseButton: Locator;
    readonly inputVariablesValueDropdown: Locator;
    readonly mountAboveCodeAtInputField: Locator;
    readonly createPluginSaveButton: Locator;
    readonly versionChangeDropdown: Locator;
    readonly pluginTagDropdown: Locator;
    readonly applyButton: Locator;

    constructor(public page: Page) {
        super(page);
        this.prebuildButton = this.page.getByTestId('pre-build-button');
        this.postBuildButton = this.page.getByTestId('post-build-button');
        this.addTaskButton = this.page.getByTestId("sidebar-add-task-button");
        this.updatePipelineButton = this.page.getByTestId('build-pipeline-button');
        this.inputVaiablesDropdown = this.page.getByTestId('input-variable-value-dropdown');
        this.destinationInfoInputField = this.page.locator(`//*[text()="Enter value or variable"]/ancestor::div[contains(@class,'dynamic-data-table__cell__select-picker__control ')]`);
        this.outputVariableAddVariableButton = page.getByTestId("add-io-variable-row").nth(1);
        this.outputAndInputVariableInputField = page.getByPlaceholder('Variable name');
        this.passFailureContionButton = page.getByText('Pass/Failure');
        this.passFailureKeyField = page.locator(`//*[text()="Select variable"]/parent::div`);
        this.passFailValueField = page.getByPlaceholder('Enter value');
        this.passFailConditionRadioButton = page.getByTestId("undefined-span");
        this.buildPipelineButton = page.getByTestId("build-pipeline-button");
        this.addConditionButton = page.getByText('Add condition');
        this.taskNamesModal = page.locator('//*[contains(@class,"task-item fw-4 fs-13 pointer flex-justify ")]')
        this.taskOptions = page.locator('//*[contains(@class,"popup-button-kebab")]')
        this.taskRemoveButton = page.getByText('Remove');
        this.executeCustomScriptPluginButton = this.page.getByTestId('execute-custom-script-button');
        this.searchBar = this.page.getByTestId('search-bar');
        this.passFailureKeyMenu = this.page.locator('//*[@role="listbox"]');
        this.warningIconOnTabLevel = this.page.locator(`//*[@class="drawer right show"]//*[@role="tablist"]//*[name()='svg']`);
        this.viewPluginsButton = this.page.locator(`//*[text()="View plugins"]`);
        this.applyMandatoryPluginsButton = this.page.getByTestId(`apply-mandatory-plugin-button`);
        this.containerImageConfigurationButton = this.page.getByTestId('custom-script-task-name-container-image');
        this.entityListDiv = this.page.locator('//*[@role="option"]');
        this.commandInputField = this.page.getByTestId('command');
        this.argsInputField = this.page.getByTestId('args');
        this.mountCodeToContainerCheckboxNo = this.page.getByTestId('build-stage-script-mount-container-false-span');
        this.mountCodeToContainerCheckboxYes = this.page.getByTestId('build-stage-script-mount-container-true-span');
        this.mountCodeToContainerInputField = this.page.getByTestId('mountCodeToContainerPath');
        this.mountDirectoryFromHostCheckboxYes = this.page.getByTestId('build-stage-script-mount-host-true-span');
        this.mountDirectoryFromHostCheckboxNo = this.page.getByTestId('build-stage-script-mount-host-false-span');
        this.filePathOnHost = this.page.getByTestId('filePathOnDisk');
        this.filePathOnContainer = this.page.getByTestId('filePathOnContainer');
        this.addMappingButton = this.page.locator(`//*[text()="Add mapping"]`);
        this.outputDirectoryAddPathButton = this.page.getByTestId('output-directory-path-add-path-button');
        this.addOutputPathInputField = this.page.getByTestId('directory-path');
        this.saveAsPluginButton = this.page.getByTestId('open-create-plugin-modal-button');
        this.pluginNameInputField = this.page.getByTestId('updateNewPluginName');
        this.pluginIdInputField = this.page.getByTestId('updatePluginIdentifier');
        this.pluginVersionInputField = this.page.getByTestId('updatePluginVersion');
        this.documentationLink = this.page.getByTestId('updatePluginDocumentationLink');
        this.addTagsInPluginDropdownControl = this.page.locator(`//*[contains(@class,'select-tags-for-plugin__control')]`);
        this.editImageIcon = this.page.locator(`//*[contains(@class,'--edit-image-icon')]`);
        this.editImageInputField = this.page.getByTestId('Edit plugin icon-url-input');
        this.previewButton = this.page.locator('//*[text()="Preview"]');
        this.replaceTaskWithPluginCheckbox = this.page.getByTestId('replace-custom-task-checkbox-chk-span');
        this.newVersionOfExistingPlugin = this.page.locator(`//*[text()="New version of existing plugin"]`);
        this.selectExistingPluginDropdown = this.page.locator(`//*[contains(@class,'select-existing-plugin__control')]`);
        this.createPluginButton = this.page.getByTestId(`create-plugin-cta`);
        this.inputVariableAddVariableButton = page.getByTestId("add-io-variable-row").nth(0);
        this.addOtherVariableButton = this.page.getByTestId('data-table-add-row-button');
        this.setTypeOfVariableDropdown = this.page.locator(`//*[contains(@class,'dynamic-data-table__cell__select-picker__control')]`);
        this.configButtonForValuesOfVariables = this.page.locator(`//*[@aria-label="Close Popup"]`).nth(1);
        this.fileMountPathInputFieldInInputOutputVariables = this.page.locator('//*[@placeholder="Enter file mount path"]');
        this.restrictFileTypesInOutputAndInputVariables = this.page.getByTestId('file-allowed-extension');
        this.fileMaxSizeUnitDropdownInOutputAndInputVariables = this.page.locator(`//*[contains(@class,'file-max-size-unit-selector__control')]`);
        this.addChoiceButton = this.page.getByTestId(`add-choice-button`);
        this.addChoiceInputFieldInOutputAndInputVariables = this.page.locator(`//*[@placeholder="Enter choice"]`);
        this.envVariableValuConfigurationModalCloseButton = this.page.getByTestId('popup-close-button');
        this.inputVariablesValueDropdown = this.page.locator(`//*[@class="dynamic-data-table__cell__select-picker-text-area__input"]`);
        this.mountAboveCodeAtInputField = this.page.getByTestId('storeScriptAt');
        this.createPluginSaveButton = this.page.getByTestId('create-plugin-cta');
        this.versionChangeDropdown = this.page.locator(`//*[contains(@class,'plugin-detail-header__version-select__control')]`);
        this.pluginTagDropdown = this.page.locator(`//*[contains(@class,'plugin-tag-select__control')]`);
        this.applyButton = this.page.getByTestId('filter-select-picker-apply');

    }


    async addPrePostTask(
        stage: string,
        taskName: string,
        autoOrManul?: string,
        executeInAppEnv: boolean = false
    ) {
        if (stage.toLocaleLowerCase() == "pre") {
            await this.prebuildButton.click({ delay: 800 });
        }
        else {
            await this.postBuildButton.click({ delay: 800 });
        }

        // Only check the "Execute tasks in application environment" checkbox if executeInAppEnv is true
        if (executeInAppEnv) {
            await this.page.locator('div:has-text("Execute tasks in application environment") input[type="checkbox"]').check();
        }

        autoOrManul ? await this.page.locator(`//span[contains(text(),"${autoOrManul}")]`).click() : '';
        await this.updatePipelineButton.waitFor();
        await this.addTaskButton.click();
        if (taskName.includes('execute')) {
            await this.executeCustomScriptPluginButton.click();
        }
        else {
            await this.searchAndSelectPluginorTaskFromList(taskName);
        }
    }

    async searchAndSelectPluginorTaskFromList(pluginName: string) {
        await this.searchBar.fill(pluginName);
        await this.page.keyboard.press('Enter');
        await this.page.locator(`//*[@role="button"]//*[text()="${pluginName}"]`).click();
    }
    async applyTagFilter(tagName: string) {
        await this.pluginTagDropdown.click();
        await this.page.locator(`//*[@role='listbox']`).locator(`//*[text()="${tagName}"]`).click();
        await this.applyButton.click();
    }




    /**
     * this method is used to add output variables in pre-post ci-cd
     * @param destinationPath this is input field where you want to copy the container image 
     * use this method in combination of add pre-post task to reach upto this page 
     */
    async configureSkopeoPlugin(destinationPath: string) {
        await this.inputVaiablesDropdown.waitFor({ timeout: 25000 });
        // if (!await this.destinationInfoInputField.first().isVisible()) {
        //     await this.inputVaiablesDropdown.click();
        // }
        await this.page.locator(`//*[@class="dynamic-data-table__cell__select-picker-text-area__input"]`).first().fill(destinationPath);
        // await this.page.keyboard.insertText(destinationPath);
        await this.page.keyboard.press('Enter');
        await this.updatePipelineButton.click();
        await this.page.waitForLoadState('load');
    }



    /**
     * this method is used to add output variables in pre-post ci-cd
     * @param variable 
     * @param variableType we can also set variable type like string bool etc
     */
    async addOutputVariables(variable: string[] | string, variableType: string[]) {
        await this.outputVariableAddVariableButton.click();
        for (let i = 0; i < variable.length; i++) {
            var key = (await BaseTest.splitKeyValuePair(variable[i])).key;
            await this.outputAndInputVariableInputField.fill(key);
            let typeOfVariable: "String" | "Number" | "Boolean" | "Date" | "File" = variableType[i] as "String" | "Number" | "Boolean" | "Date" | "File";
            await this.setTypeOfVariables('output', typeOfVariable);
        }
    }


    /**
     * this method is used to set pass -failure conditions 
     * @param condition pass or fail
     * @param keyValuePair  key that you want to select from dropdown and respected related value that you want to add there 
     */
    async setPassFailureCondition(condition: string, keyValuePair: string, saveTheConfiguration: boolean = true) {
        await this.passFailureContionButton.click();
        if (condition == "pass")
            await this.passFailConditionRadioButton.first().click()
        else {
            await this.passFailConditionRadioButton.last().click();
            await this.addConditionButton.click();
        }
        let pairArray = keyValuePair;
        var key = (await BaseTest.splitKeyValuePair(pairArray)).key;
        var value = (await BaseTest.splitKeyValuePair(pairArray)).value;

        await this.passFailureKeyField.click();
        await this.passFailureKeyMenu.getByText(key).click();
        await this.passFailValueField.fill(value);
        saveTheConfiguration ? await this.buildPipelineButton.click() : console.log('skipping');
    }



    /**
     * this method is used to remove tasks of any stage pre or post whether ci or cd
     * @param stage 
     */
    async deleteTask(stage: string) {
        if (stage.toLocaleLowerCase() == "pre") {
            await this.prebuildButton.click();
        }
        else {
            await this.postBuildButton.click();
        }
        await this.buildPipelineButton.waitFor({ timeout: 10000 })
        let count = await this.taskNamesModal.all();
        for (let i = 0; i < count.length; i++) {
            await this.taskOptions.nth(0).click();
            await this.taskRemoveButton.click();
        }
    }



    /**
     * use this method to configure mandat plugins which have been set through policy
     */
    async configureMandatPlugins() {
        await this.warningIconOnTabLevel.click();
        await this.viewPluginsButton.click();
        await this.applyMandatoryPluginsButton.click();
        await this.updatePipelineButton.click();

    }

    async setConfigurationForContainerImage(data: { containerImage: 'alpine:latest', command?: string, args?: string, mountCodeToContainer?: string, mountDirectoryFromHost?: { hostFilePath: string, containerFilePath: string }[], outputDirectoryPath?: string[], mountCustomCode?: { script: string[], mountCustomCodePath: string } }) {
        let commandToEnter: string = data.command || "";
        let argsToEnter: string = data.args || "";
        await this.containerImageConfigurationButton.click();
        await this.page.locator(`//*[contains(@class,'select__control')]`).click();
        await this.entityListDiv.locator(`//*[text()="${data.containerImage}"]`).click();
        await this.commandInputField.fill(commandToEnter);
        await this.argsInputField.fill(argsToEnter);
        if (data.mountCodeToContainer) {
            await this.mountCodeToContainerCheckboxYes.click();
            await this.mountCodeToContainerInputField.fill(data.mountCodeToContainer);
        }
        else {
            await this.mountCodeToContainerCheckboxNo.click();
        }
        if (data.mountDirectoryFromHost) {
            for (let i = 0; i < data.mountDirectoryFromHost.length; i++) {
                i > 0 ? await this.addMappingButton.click() : '';
                await this.mountDirectoryFromHostCheckboxYes.click();
                await this.filePathOnHost.nth(0).fill(data.mountDirectoryFromHost[i].hostFilePath);
                await this.filePathOnContainer.nth(0).fill(data.mountDirectoryFromHost[i].containerFilePath);
            }
        } else {
            await this.mountDirectoryFromHostCheckboxNo.click();
        }
        if (data.outputDirectoryPath) {
            for (let key of data.outputDirectoryPath) {
                await this.outputDirectoryAddPathButton.click();
                await this.addOutputPathInputField.fill(key);
            }
        }
        if (data.mountCustomCode) {
            await this.page.locator(`//*[text()="Mount custom code"]`).click();
            await this.codeMirrorEditorTextArea.click();
            for (let key of data.mountCustomCode.script) {
                await this.page.keyboard.type(key);
                await this.page.keyboard.press('Enter');
            }
            await this.mountAboveCodeAtInputField.fill(data.mountCustomCode.mountCustomCodePath);
        }

    }
    async saveACustomPlugin(data: { pluginName?: string, pulginId?: string, pluginVersion?: string, documentationLink?: string, tags?: string[], mandatVariableConfig?: { varName: string, isMandat: boolean }[], newVersionOfExistingPlugin?: { existingPluginName: string, newPluginVersion: string }, imageUrl?: string, replaceTaskWithPlugin?: boolean }) {
        await this.saveAsPluginButton.click();
        if (data.newVersionOfExistingPlugin) {
            await this.newVersionOfExistingPlugin.click();
            await this.selectExistingPluginDropdown.click();
            await this.page.locator(`//*[@role="option"]`).locator(`//*[text()="${data.newVersionOfExistingPlugin.existingPluginName}"]`).click();
            await this.pluginVersionInputField.fill(data.newVersionOfExistingPlugin.newPluginVersion);
        }
        else {
            let documentationLink = data.documentationLink || "";
            await this.pluginNameInputField.fill(data.pluginName!);
            await this.pluginIdInputField.fill(data.pulginId!);
            await this.pluginVersionInputField.fill(data.pluginVersion!);
            data.documentationLink ? await this.documentationLink.fill(documentationLink) : console.log('skipping');
        }
        if (data.tags) {
            await this.addTagsInPluginDropdownControl.click();
            for (let key of data.tags) {
                await this.page.locator('//*[@role="option"]').locator(`//*[text()="${key}"]`).click();
            }
            await this.addTagsInPluginDropdownControl.click();
        }
        if (data.imageUrl) {
            await this.editImageIcon.click();
            await this.editImageInputField.fill(data.imageUrl);
            await this.previewButton.click();
            await expect(this.page.locator(`//img[contains(@src,'DEL')]`)).toBeVisible({ timeout: 25000 });
        }
        if (data.mandatVariableConfig) {
            for (let key of data.mandatVariableConfig) {
                if (await this.page.locator(`//*[@name="toggle-is-${key.varName}-mandatory"]`).isChecked() != key.isMandat) {
                    await this.page.getByTestId(`toggle-is-${key.varName}-mandatory`).click();
                }
            }
        }
        data.replaceTaskWithPlugin ? await this.replaceTaskWithPluginCheckbox.click() : '';
        await this.createPluginSaveButton.click();

    }
    async addInputVariables(data: {
        varName: string;
        varType: 'String' | 'Number' | 'Boolean' | 'Date' | 'File';
        value: string;
        valueConfiguration?:
        | { fileMountPath: string; restrictFileType?: string; restrictFileSize: { size: string; unit: "KB" | "MB" }; askValueAtRuntime: boolean }
        | { choices: string[]; dontAllowCustomInput: boolean; askValueAtRuntime: boolean }
    }[]) {
        await this.inputVariableAddVariableButton.click();

        for (let i = 0; i < data.length; i++) {
            if (i !== 0) {
                await this.addOtherVariableButton.first().click();
            }

            await this.outputAndInputVariableInputField.nth(0).fill(data[i].varName);
            await this.setTypeOfVariables('input', data[i].varType);

            const valueConfig = data[i].valueConfiguration;

            if (valueConfig) {
                await this.setConfigurationForValuesOfVariables(valueConfig);
            }

            await this.inputVariablesValueDropdown.nth(0).click();
            if (valueConfig && "choices" in valueConfig && valueConfig.dontAllowCustomInput) {
                await this.validateChoicesInValueDropdown(valueConfig.choices);
            }
            if (data[i].value) {
                await this.inputVariablesValueDropdown.nth(0).click();
                await this.page.keyboard.type(data[i].value);
                await this.page.keyboard.press('Enter');
            }

        }
    }

    async validateChoicesInValueDropdown(choices: string[]) {
        await expect(this.page.locator(`//*[text()="Choices"]`)).toBeVisible();
        for (let key of choices) {
            await expect(this.page.locator(`//*[@role="option"]`).locator(`//*[text()="${key}"]`)).toBeVisible();
        }
        let choicesCount = await this.page.locator(`//*[@role="option"]`).count();
        expect(choicesCount).toBe(choices.length);

    }

    async setTypeOfVariables(inputVariablesOrOutputVariables: 'input' | 'output', variableType: 'String' | "Number" | "Boolean" | "Date" | "File") {
        let totalSetTypeDropdownsAvailable: Number = await this.setTypeOfVariableDropdown.count();
        let locatorNumber: number = inputVariablesOrOutputVariables == "input" || totalSetTypeDropdownsAvailable == 1 ? 0 : 1;
        await this.setTypeOfVariableDropdown.nth(locatorNumber).click();
        await this.page.locator('//*[@role="option"]').locator(`//*[text()="${variableType}"]`).click();
    }
    async setConfigurationForValuesOfVariables(data: { fileMountPath: string, restrictFileType?: string, restrictFileSize?: { size: string, unit: "KB" | "MB" }, askValueAtRuntime: boolean } | { choices: string[], dontAllowCustomInput: boolean, askValueAtRuntime: boolean }) {
        await this.configButtonForValuesOfVariables.nth(0).click();
        if ('fileMountPath' in data) {
            await this.fileMountPathInputFieldInInputOutputVariables.fill(data.fileMountPath);
            data.restrictFileType ? await this.restrictFileTypesInOutputAndInputVariables.fill(data.restrictFileType) : '';
            if (data.restrictFileSize) {
                await this.enterSizeOfFileInOutputAndInputVariables.fill(data.restrictFileSize.size);
                await this.fileMaxSizeUnitDropdownInOutputAndInputVariables.click();
                await this.page.locator(`//*[@role="option"]`).locator(`//*[text()="${data.restrictFileSize.unit}"]`).click();
            }
        }
        else {
            for (let key of data.choices) {
                await this.addChoiceButton.click();
                await this.addChoiceInputFieldInOutputAndInputVariables.nth(0).fill(key);
            }
            data.dontAllowCustomInput ? await this.page.locator(`//*[text()="Allow Custom input"]`).click() : console.log('deep');
            data.askValueAtRuntime ? await this.page.locator(`//*[text()="Ask value at runtime"]`).click() : console.log('deep');
            await this.envVariableValuConfigurationModalCloseButton.click();
        }

    }
    async selectVersionOfPlugin(version: string) {
        await this.versionChangeDropdown.click();
        await this.page.locator('//*[@role="listbox"]').locator(`//*[text()="${version}"]`).click();
    }

}