import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../BasePage';
import { BaseTest } from '../../../utilities/BaseTest';

export class ExternalCIPage extends BasePage {

  //Locators
  readonly externalSourceConfigButton: Locator;
  readonly selectTokenWithRequiredPermission: Locator;
  readonly generateTokenWithRequiredPermissions: Locator;
  readonly autoGenerateTokenButton: Locator;
  readonly generateTokenLink: Locator;
  readonly tokenNameField: Locator;
  readonly generatedAPIToken: Locator;
  readonly selectAPITokenButton: Locator;
  readonly selectAPITokenDropdown: Locator;
  readonly selectAPITokenMenulist: Locator;
  readonly tryItOutButtonTab: Locator;
  readonly apiTokenField: Locator;
  readonly executeButton: Locator;
  readonly statusCode: Locator;
  readonly crossButton: Locator;
  readonly monacoEditor: Locator;
  readonly tokenNameInputField: Locator;
  readonly generateTokenButton: Locator;
  readonly errorMessage: Locator;
  readonly autoTokenGeneration: Locator;
  readonly closeWebhookTippy: Locator;
  readonly closeTryWithOutModalButton: Locator;
  constructor(public page: Page) {
    super(page);
    this.externalSourceConfigButton = page.getByText('External source', { exact: true });
    this.selectTokenWithRequiredPermission = page.getByText("Select or auto-generate token with required permissions", { exact: true });
    this.generateTokenWithRequiredPermissions = page.getByTestId("select-or-generate-token")
    this.generateTokenLink = page.getByTestId("generate-token")
    this.tokenNameField = page.getByTestId("token-name")
    this.generatedAPIToken = page.getByTestId("generated-api-token");
    this.selectAPITokenButton = page.getByText('Select API token', { exact: true });
    this.selectAPITokenDropdown = page.locator("//div[contains(@class,'select-token__')]").first();
    this.selectAPITokenMenulist = page.locator('//*[@role="listbox"]');
    this.tryItOutButtonTab = page.getByText("Try it out");
    this.apiTokenField = page.locator("//input[@name='api-token']");
    this.executeButton = page.getByText("Execute", { exact: true });
    this.statusCode = page.locator("//pre[@data-testid='sample-script']/code");
    this.crossButton = page.getByTestId('close-webhook-details-modal')
    //old code editor
    this.monacoEditor = page.locator("//*[@class='view-lines monaco-mouse-cursor-text']");
    //todo: to replace with a better locator.
    this.tokenNameField = page.getByTestId('token-name');
    this.autoGenerateTokenButton = this.page.locator(`//*[@data-key="autoToken"]`);
    this.generateTokenButton = this.page.locator(`//*[text()="Generate token"]`);
    this.errorMessage = this.page.locator(`//*[@id="api-token-error-msg"]`);
    this.closeWebhookTippy = this.page.locator(`//*[@class="webhook-tippy-card text__white p-20 br-8 fs-13 "]//*[local-name()="svg"]`).nth(1);
    this.closeTryWithOutModalButton = this.page.locator(`//*[text()="Webhook Details"]/following-sibling::button`);
  }

  /**
 * click on external cd
 */
  async clickOnExternalSourceConfigButton() {
    await this.externalSourceConfigButton.click();
  }

  /**
   * click on try it out and trigger the external cd with token named as dontdeleteit
   */
  async externalCITriggerWithTryItOut(autoGenerateToken: boolean = false, checkUnathorized: boolean = false) {
    let token: string = '';

    if (process.env.HIDE_API_TOKEN === "false") {
      await this.selectTokenWithRequiredPermission.click();
      if (!autoGenerateToken) {
        await this.selectAPITokenDropdown.click();
        await this.selectAPITokenMenulist
          .getByText("playwright-super-admin")
          .first()
          .click();
        //todo: to make this token selection dynamic.
      } else {
        await this.autoGenerateTokenButton.click();
        await this.tokenNameField.fill(
          BaseTest.generateRandomStringWithCharsOnly(4)
        );
        await this.generateTokenButton.click();
      }
    } else if (process.env.HIDE_API_TOKEN === "true") {
      await this.generateTokenWithRequiredPermissions.click();
      await this.autoGenerateTokenButton.click();
      await this.tokenNameField.fill(
        BaseTest.generateRandomStringWithCharsOnly(5)
      );
      await this.generateTokenLink.click();
    }

    token = await this.generatedAPIToken.textContent() as string;
    await this.tryItOutButtonTab.click();
    if (checkUnathorized) {
      await this.executeButton.click();
      await expect(this.errorMessage).toBeVisible({ timeout: 1 * 1000 * 60 });
      await this.apiTokenField.fill('err');
      await this.executeButton.click();
      await expect(this.page.locator('//*[text()="401"]')).toBeVisible();
    }
    await this.apiTokenField.fill(token);
    await expect(async () => {
      await this.codeMirrorEditorTextArea.click();
      if (process.env.OS as string == "Mac") {
        await this.page.keyboard.press('Meta+a');
      }
      else {
        await this.page.keyboard.press('Control+a');
      }
      await this.codeMirrorEditorTextArea.press('Backspace');
      expect(await this.codeMirrorEditorTextArea.textContent()).toEqual('');
    }).toPass({ timeout: 3 * 1000 * 60 });
    await this.codeMirrorEditorTextArea.pressSequentially('{"dockerImage": "quay.io/devtron/test:e333f7a9-686-7752"}');
    // old code editor ends
    await this.executeButton.click();
    await expect(this.statusCode.nth(0)).toContainText("200", { timeout: 1 * 1000 * 60 });
    await this.crossButton.click();
  }
}