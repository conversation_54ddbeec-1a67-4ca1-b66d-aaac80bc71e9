import { Page, Locator, expect } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';

// Class representing a build configuration page
export class BuildConfigurationPage {
  // Locators
  readonly saveAndNextButton: Locator;
  readonly selectContainerRegistryMenuList: Locator;
  readonly selectContainerRegistryInputBox: Locator;
  readonly selectContainerRegistryInputContainer: Locator;
  readonly repositoryNameInput: Locator;
  readonly warningMessage: Locator;
  readonly updatePipeline: Locator;
  readonly customCiTagInputField: Locator;
  readonly toggleCustomCiTag: Locator;
  readonly customCiTagEditIcon: Locator;
  readonly customCiTagCounterField: Locator;
  readonly preBuildButton: Locator;
  readonly viewPluginMandatoryPluginButton: Locator;
  readonly applyMandatoryPluginButton: Locator;
  readonly buildConfigurationTab: Locator;

  // Constructor that takes a Playwright Page instance
  constructor(private page: Page) {
    this.saveAndNextButton = page.getByTestId('build_config_save_and_next_button');
    this.selectContainerRegistryMenuList = page.locator('.build-config__select-container-registry__menu-list');
    this.selectContainerRegistryInputBox = page.locator('.build-config__select-container-registry__input');
    this.selectContainerRegistryInputContainer = page.locator('.build-config__select-container-registry__input-container');
    this.repositoryNameInput = page.locator('input[name="repository_name"]');
    this.warningMessage = this.page.locator('#image_tag-error-msg');
    this.updatePipeline = this.page.getByTestId('build-pipeline-button');
    this.customCiTagInputField = this.page.locator('//*[@name="image_tag"]');
    this.customCiTagEditIcon = this.page.locator(`//*[contains(text(),"{x}")]/following-sibling::*[local-name()='svg']`);
    this.toggleCustomCiTag = this.page.getByTestId('create-build-pipeline-custom-tag-enabled-toggle');
    this.customCiTagCounterField = this.page.locator('//*[@name="image_counter"]');
    this.preBuildButton = this.page.getByTestId('pre-build-button');
    this.viewPluginMandatoryPluginButton = this.page.getByText('View plugin');
    this.applyMandatoryPluginButton = this.page.getByText('Apply', { exact: true });
    this.buildConfigurationTab = this.page.getByTestId('build-configuration-link');
  }
  /**
   * Saves build configurations.
   * @param {string} ContainerRegistryName - The name of the container registry to be selected.
   * @param {string} ContainerRepository - The repository name to be filled in the input field.
   */
  async saveBuildConfigurations(ContainerRegistryName: string, ContainerRepository: string) {
    try {
      await expect(async () => {
        await this.selectRegistryFromDropDown(ContainerRegistryName);
        await this.repositoryNameInput.click();
        await this.repositoryNameInput.fill(ContainerRepository);
        expect(await this.repositoryNameInput.getAttribute('value')).toEqual(ContainerRepository);
        expect(await this.page.locator(`//*[contains(@class,'build-config__select-container-registry__control')]`).textContent()).toContain(ContainerRegistryName);
        await this.saveAndNextButton.click();
      }).toPass({ timeout: 3 * 1000 * 60 });

    } catch (error) {
      console.error(`An error occurred during saveBuildConfigurations: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async verifySavedRegistry(registryName: string, repoName: string) {
    await expect(this.selectContainerRegistryInputContainer.locator('xpath=preceding-sibling::div')).toContainText(registryName);
    let repoNameConfigured = await this.repositoryNameInput.getAttribute('value');
    expect(repoNameConfigured).toEqual(repoName);
  }



  /**
   * Selects a container registry from the dropdown.
   * @param {string} registryName - The name of the container registry to be selected.
   */
  async selectRegistryFromDropDown(registryName: string) {
    try {
      await this.selectContainerRegistryInputContainer.waitFor();
      await this.selectContainerRegistryInputContainer.click({ delay: 500 });
      await this.selectContainerRegistryInputBox.pressSequentially(registryName);
      await this.selectContainerRegistryMenuList.getByText(registryName, { exact: true }).click();
    } catch (error) {
      console.error(`An error occurred during selectRegistryFromDropDown: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * Selects a container registry from the dropdown.
   * @param {string} wrongInputValue - Array of wrong values 
   * @param {string} inputValue  - right value to create tag
   */
  async cutomCiTagSetup(wrongInputValue: string[], inputValue: string) {
    await this.togglingCustomCiTag();
    await this.verifyWrongCustomCiTag(wrongInputValue);
    await this.enterCustomTagValue(inputValue);
    await this.updatePipeline.click();
  }

  /**
   * Selects a container registry from the dropdown.
   * @param {string} wrongInputValue - Array of wrong values 
   */
  async verifyWrongCustomCiTag(wrongInputValue: string[]) {
    for (let number of wrongInputValue) {
      await this.enterCustomTagValue(number);
      await this.warningMessage.first().waitFor({ state: "visible" });
      await expect(this.updatePipeline).toBeDisabled();
    }
  }


  async clickOnupdateCIPipelineButton() {
    // Clicks on the 'updatePipeline' button
    await this.updatePipeline.click();
  }



  async configurePlugin() {
    // Clicks on the 'preBuildButton' to configure the plugin
    await this.preBuildButton.click();
    // Clicks on the 'viewPluginMandatoryPlugin' to view mandatory plugin details
    await this.viewPluginMandatoryPluginButton.click();
    // Clicks on the 'applyMandatoryPlugin' button to apply mandatory plugin settings
    await this.applyMandatoryPluginButton.click();
    // Clicks on the 'updatePipeline' button to update the CI pipeline after plugin configuration
    await this.updatePipeline.click();

  }




  /**
  * toggle custom ci-tag and open the input field
  */
  async togglingCustomCiTag() {
    await this.toggleCustomCiTag.waitFor({ timeout: 1 * 60 * 1000 });
    if (await this.customCiTagEditIcon.isVisible()) {
      await this.customCiTagEditIcon.click();
    }
    else {
      await this.toggleCustomCiTag.click();
    }
  }

  /**
   * enter value in tag field
   * @param {string} inputValue - provided value 
   */
  async enterCustomTagValue(inputValue: string) {
    await this.customCiTagInputField.clear();
    await this.customCiTagInputField.fill(inputValue);
  }

  /**
   * enter value in tag field and verify that existing value in counter should be accurate 
   * @param {string} inputValue - provided value 
   * @param {string} existingValue - already present value in counter field 
   */
  async enterAndVerifyCiTagCounterValue(inputValue: string, existingValue: string) {
    await this.verifyExistingCounterValue(existingValue);
    await this.customCiTagCounterField.clear();
    await this.customCiTagCounterField.fill(inputValue);
    await this.updatePipeline.click();
  }

  /**
   *  verify that existing value in counter should be accurate  
   * @param {string} existingValue - value to be checked
   */
  async verifyExistingCounterValue(existingValue: string) {
    await this.customCiTagCounterField.waitFor()
    var value: string | null = await this.customCiTagCounterField.getAttribute('value');
    expect.soft(value).toEqual(existingValue);
  }

}
