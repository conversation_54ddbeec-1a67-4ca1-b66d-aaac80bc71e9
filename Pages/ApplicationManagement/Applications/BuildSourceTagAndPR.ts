
import { Page, Locator, expect } from '@playwright/test';
import clipboard from 'clipboardy';

export class BuildSourceTagAndPR {

    readonly PRTagPipelineName: Locator;
    readonly buildCopyWebhookURLButton: Locator;
    readonly buildCopySecretKeyButton: Locator;
    readonly closeButton: Locator;

    constructor(private page: Page) {
        this.PRTagPipelineName = page.getByText("prtagpipelinename");
        this.buildCopyWebhookURLButton = page.getByTestId("build-copy-webhook-url-button");
        this.buildCopySecretKeyButton = page.getByTestId("build-copy-secret-key-button");
        //todo: to add a better locator for the below close button web element
        this.closeButton = page.locator("//*[@class='dc__transparent flex icon-dim-24']");
    }

    async getPRWebhookCredentials(): Promise<{ webhookURL: string, secretKey: string }> {
        await expect(this.buildCopyWebhookURLButton).toBeVisible();
        await this.buildCopyWebhookURLButton.click();
        const webhookURL = await clipboard.read();
        await expect(this.buildCopySecretKeyButton).toBeVisible();
        await this.buildCopySecretKeyButton.click();
        const secretKey = await clipboard.read();
        await this.closeButton.click();
        return { webhookURL, secretKey }
    }


}