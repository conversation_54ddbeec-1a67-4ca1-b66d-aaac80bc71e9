import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../BasePage';  // Import the BasePage class
import { BaseTest } from '../../../utilities/BaseTest';

/**
 * Page Object Model for the Create App / Job page.
 * Contains methods and locators used to create applications or jobs within the system.
 */
export class CreateAppPage extends BasePage {
  // ---------- Locators ----------
  readonly createAppButtonOnHeader: Locator;
  readonly customAppLink: Locator;
  readonly appNameTextbox: Locator;
  readonly descriptionTextbox: Locator;
  readonly createAppButtonOnDrawer: Locator;
  readonly selectProjectInputContainer: Locator;
  readonly visibleModalClose: Locator;
  readonly appSearchBar: Locator;
  readonly singleAppSelectionModal: Locator;
  readonly cloneExistingApp: Locator;
  readonly cloneExistingInputField: Locator;
  readonly appConfigurationLink: Locator;
  readonly gitRepoTab: Locator;
  readonly clickOnApplication: Locator;
  readonly cloneAppSourceAppDropdownButton: Locator;
  readonly createJobButton: Locator;
  readonly createJobButtonOnDrawer: Locator;
  readonly helmAppListButton: Locator;
  readonly formError: Locator;
  readonly helpButton: Locator;
  readonly appNameTextFieldInAppListRows: Locator;
  readonly clearAllFiltersButton: Locator;
  readonly appNameSortingButton: Locator;
  readonly applyFiltersButton: Locator;
  readonly genericEmptyState: Locator;
  readonly syncButton: Locator;
  readonly profileButton: Locator;
  readonly createAppFromTemplateButton: Locator;
  readonly appTemplateWorkflowDivs: Locator;
  readonly addTagsButton: Locator;
  readonly pageIsStabled: Locator;
  private static readonly PAGE_STABILITY_TIMEOUT = 40000; // Max wait time for app list to stabilize

  constructor(page: Page) {
    super(page); // Call the parent class constructor to initialize the page
    // Assign all locators specific to CreateAppPage
    this.createAppButtonOnHeader = page.getByTestId('create-app-button-on-header');
    this.addTagsButton = page.locator(`//*[contains(text(),'Add tags')]`);
    this.customAppLink = page.getByText('Custom app', { exact: true });
    this.appNameTextbox = page.locator('//*[@name="name"]');
    this.descriptionTextbox = page.getByTestId('description');
    this.createAppButtonOnDrawer = page.getByTestId('create');
    this.selectProjectInputContainer = page.locator(`//*[contains(@class,'project__control')]`).first();
    this.visibleModalClose = page.getByTestId('visible-modal-close');
    this.appSearchBar = page.getByTestId('Search-by-app-name');
    this.singleAppSelectionModal = page.locator(`//*[contains(@data-testid,'app-list-row') or contains(@data-testid,'app-list__row')]`);
    this.cloneExistingApp = page.locator(`//*[@aria-label="Creation method: Clone application"]`);
    this.cloneExistingInputField = page.locator("//*[@class= 'app-name-for-clone__input']");
    this.appConfigurationLink = page.getByTestId("app-config-link");
    this.gitRepoTab = page.getByTestId("git-repository-link");
    this.clickOnApplication = page.getByTestId("click-on-application");
    this.cloneAppSourceAppDropdownButton = page.locator(`//*[contains(@class,'app-name-for-clone__control')]`);
    this.createJobButton = page.getByTestId("create-job-button-in-dropdown");
    this.createJobButtonOnDrawer = page.getByTestId("create");
    this.helmAppListButton = page.getByTestId("helm-app-list-button");
    this.formError = page.locator("//*[contains(@class,'form__input--error')]");
    this.helpButton = page.getByTestId("go-to-get-started");
    this.pageIsStabled = page.locator(`//*[@data-testid="app-list-row" or text()="No results"]`).first();
    this.appNameTextFieldInAppListRows = page.locator(`//*[@data-testid="app-list-for-sort"]`);
    this.clearAllFiltersButton = page.locator(`//*[text()="Clear All Filters"]`);
    this.appNameSortingButton = page.locator('//*[text()="APP NAME"]');
    this.applyFiltersButton = page.getByTestId("filter-select-picker-apply");
    this.genericEmptyState = page.getByTestId('generic-empty-state');
    this.syncButton = page.getByTestId(`sync-now-button`);
    this.profileButton = page.getByTestId(`profile-button`);
    this.createAppFromTemplateButton = page.locator('//*[@aria-label="Creation method: From template"]');
    this.appTemplateWorkflowDivs = page.getByTestId('rf__wrapper');
  }

  /**
   * Fills in the app name and description fields in the form.
   */
  private async fillAppForm(appName: string, description: string) {
    await this.appNameTextbox.pressSequentially(appName, { delay: 20 });
    await this.descriptionTextbox.click();
    await this.descriptionTextbox.fill(description);
  }

  /**
   * Handles Role-Based Access Control logic by simulating API response.
   */
  private async handleRBAC(isSuccessfull: boolean, createButton: Locator) {
    const statusCode = isSuccessfull ? 200 : 403;
    await BaseTest.waitForApiResponse(this.page, '/app', statusCode, async () => {
      await createButton.click();
    });
  }

  /**
   * Selects a project from the dropdown. Uses fallback project if provided.
   */
  async selectProject(projectName: string, fallbackProjectName?: string) {
    const project = fallbackProjectName || projectName;
    await this.selectProjectInputContainer.click();
    await this.page.getByText(project, { exact: true }).click();
  }

  /**
   * Checks if the entity already exists and navigates to it if it does.
   */
  private async handleEntityAlreadyExists(entityName: string, entityType: string): Promise<boolean> {
    const exists = await this.page.locator(`//*[contains(text()," [${entityName}] already exists")]`).isVisible();
    if (exists) {
      await this.page.locator(`//*[contains(@data-testid,"close-create-${entityType}")]`).click();
      await this.searchAndSelectAppsFromList(entityName, false, entityType);
      return true;
    }
    return false;
  }

  /**
   * Navigates to the application list page and waits for it to load.
   */
  async navigateToAppList(baseUrl: string) {
    await this.page.goto(`${baseUrl}/application`);
    await this.waitForPageToStabilize();
  }

  /**
   * Searches for an app in the list and selects it.
   * If `isToBeCreated` is false, navigates to the config page.
   */
  async searchAndSelectAppsFromList(appName: string, isToBeCreated: boolean, appOrJob: string) {
    await this.appSearchBar.fill(appName);
    await this.page.waitForSelector(`[data-testid="app-list-row"]`, { state: 'visible' });

    const appRows = await this.singleAppSelectionModal.all();
    for (const row of appRows) {
      const text = await row.textContent();
      if (text?.includes(appName)) {
        await row.click();
        break;
      }
    }

    if (!isToBeCreated) {
      await this.page.locator(`//*[@data-testid="${appOrJob}-config-link"]`).click();
    }
  }

  /**
   * Returns the correct "Create" button based on entity type.
   */
  private getCreateButton(appOrJob: string): Locator {
    return appOrJob === 'app' ? this.createAppButtonOnDrawer : this.createJobButtonOnDrawer;
  }

  /**
 * Waits until the app list page is stabilized (either apps are shown or "No results" is displayed).
 */
private async waitForPageToStabilize(): Promise<void> {
  await expect(this.pageIsStabled).toBeVisible({
    timeout: CreateAppPage.PAGE_STABILITY_TIMEOUT
  });
}

  /**
   * Clicks the appropriate "Create" or "New" button and opens the form.
   */
  private async initiateAppCreationFlow(appOrJob: string) {
    await this.createAppButtonOnHeader.click();
    if (appOrJob === 'app') {
      await this.customAppLink.click();
    } else {
      await this.createJobButton.click();
    }
    await this.page.waitForLoadState('load');
  }

  /**
   * Public method to create a new custom app or job.
   */
  async createDevtronAppOrJob(
    appOrJob: string,
    appName: string,
    projectName: string,
    baseServerURL: string,
    isSuccessfull?: boolean,
    hiddenProjectName?: string
  ) {
    try {
      await BaseTest.clickOnDarkMode(this.page);
      await BaseTest.clickOnToolTipOkayButton(this.page);

      const alreadyExists = await this.handleEntityAlreadyExists(appName, appOrJob);
      if (alreadyExists) return;

      await expect(async () => {
        await this.navigateToAppList(baseServerURL);
        await this.initiateAppCreationFlow(appOrJob);
        await this.fillAppForm(appName, 'this is for testing purpose only');
        await this.selectProject(projectName, hiddenProjectName);
        const createButton = this.getCreateButton(appOrJob);

        if (isSuccessfull !== undefined) {
          await this.handleRBAC(isSuccessfull, createButton);
        } else {
          await createButton.click();
          await expect(this.page.locator('//*[text()="Git Account"]')).toBeVisible({ timeout: 50000 });
        }
      }).toPass({ timeout: 3 * 60 * 1000 });

    } catch (error) {
      console.error(`An error occurred during ${appOrJob} creation for "${appName}":`, error);
      throw error;
    }
  }
}
