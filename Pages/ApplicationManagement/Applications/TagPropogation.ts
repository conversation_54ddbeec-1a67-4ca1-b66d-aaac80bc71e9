// TAG PROPAGATION

import { Page, Locator, expect } from '@playwright/test'; // Importing necessary modules
import { BaseTest } from '../../../utilities/BaseTest'; // Importing BaseTest utility



export class TagPropagation {
    readonly applicationButton: Locator;
    readonly createButton: Locator;
    readonly customAppButton: Locator;
    readonly appName: Locator;
    readonly description: Locator;
    readonly createFromScratch: Locator;
    readonly cloneExistingApp: Locator;
    readonly selectProject: Locator;
    readonly overview: Locator;
    readonly addTagIcon: Locator;
    readonly addTag: Locator;
    readonly enterKey: Locator;
    readonly enterValue: Locator;
    readonly pressOnIcon: Locator;
    readonly saveTag: Locator;
    readonly appConfigurationsTab: Locator;
    readonly buildConfigurationTab: Locator;
    readonly podName: Locator;

    constructor(private page: Page) {

        // Checking any application from the Applications

        this.applicationButton = page.getByTestId('click-on-application')
        this.createButton = page.getByTestId('create-app-button-on-header')
        this.customAppButton = page.getByTestId('create-custom-app-button-in-dropdown')
        this.appName = page.locator('//*[@name="app-name"]')
        this.description = page.getByTestId('description-textbox')
        this.createFromScratch = page.getByTestId('create-from-scratch-radio-button-span')
        this.cloneExistingApp = page.getByTestId('clone-existing-application-radio-button-span')


        this.overview = page.getByTestId('overview-click')
        this.addTagIcon = page.getByText('Add Tags')
        this.addTag = page.getByText('Add tag')
        this.enterKey = page.locator(`//*[contains(@data-testid,'-tagKey') or @class="dynamic-data-table__cell__select-picker-text-area__input"]`);
        this.enterValue = page.locator(`//*[contains(@data-testid,'-tagValue')]`)


        this.pressOnIcon = page.locator(`//button[contains(@class,'pointer')]`)
        this.saveTag = page.getByTestId('overview-tag-save-button')
        this.appConfigurationsTab = page.getByTestId("app-config-link");
        this.buildConfigurationTab = page.getByTestId("build-configuration-link");
        this.podName = page.getByTestId("resource-node-name")








    }

    async addTagToApp(key: string, value: string) {
        await this.overview.click();
        await this.addTagIcon.click();
        await this.addTagDetails(key, value);
        await this.saveTag.click();
    }
    async addTagDetails(key: string, value: string, propagate: boolean = true) {
        await this.enterKey.fill(key);
        if (await this.page.locator(`//*[contains(@data-testid,'-tagKey')]`).isHidden())
            await this.page.keyboard.press('Enter');
        await this.enterValue.fill(value);
        await this.pressOnIcon.click();
    }
    async hoverOnPodName() {
        await this.podName.hover();                // this function helps in opening the option of manifest after hovering on podName
    }

}

