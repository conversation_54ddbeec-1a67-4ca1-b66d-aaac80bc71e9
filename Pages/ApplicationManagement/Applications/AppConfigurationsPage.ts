import { expect, Page, Locator } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { AllTypes } from '../../../utilities/Types';
import { dockerfile } from '../../../utilities/clipboardyYamls.ts/YamlForResourceBrowser';
import { ApiUtils } from '../../../utilities/ApiUtils';
import { BasePage } from '../../BasePage';
import { JobsPage } from '../../Automation and Enablement/Jobs.ts';
import { PrePostCiCd } from './PrePostCiCd';
import { GitopsConfigurationPage } from '../Configurations/GItopsConfigurationPage';
import { DeploymentStrategyEnum } from '../../../enums/Application Management/Applications/DeploymentStrategyEnum.ts';
import { getCanaryStrategyYaml } from '../../../utilities/clipboardyYamls.ts/customCanaryStrategy.ts';
import { getBlueGreenStrategyYaml } from '../../../utilities/clipboardyYamls.ts/customBlueGreenStrategy.ts';
import { SourceTypeEnum } from "../../../enums/Application Management/Applications/SourceTypeEnum.ts"
import { DeploymentTypeEnum } from "../../../enums/Application Management/Applications/DeploymentTypeEnum.ts"


//const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class AppConfigurationPage extends BasePage {
  // Locators
  readonly page: Page;
  private ciCdNodesSelector: Locator;
  readonly ciDeletePipelineButton: Locator;
  private deleteJobAppButton: Locator;
  readonly appConfigurationTab: Locator;
  private workflowDiv: Locator;
  private closeModealButton: Locator;
  private prePostDropdown: Locator;
  private customCdTagInputField: Locator;
  private customTagCounterField: Locator;
  readonly updatePipelineButton: Locator;
  readonly appNameForLinkedPipelineDropdown: Locator;
  private deployToButton: Locator;
  private changeSourceIcon: Locator;
  readonly selectLanguageDropdown: Locator;
  readonly appNameForLinkedPipelineInput: Locator;
  readonly appNameForLinkedPipelineMenuList: Locator;
  readonly sourceCiPipelineField: Locator;
  readonly sourceCiPipelineFieldInput: Locator;
  readonly linkedPipelineName: Locator;
  readonly createLinkedPipelineNameButton: Locator;
  readonly linkedBuildPipelineButton: Locator;
  readonly newWorkflowButton: Locator;
  readonly buildDeployByClick: Locator;
  readonly pipelineNameField: Locator;
  readonly ciModal: Locator;
  readonly linkedBuildPipelineModalHeader: Locator;
  readonly deleteLinkedPipelineButton: Locator;
  readonly linkedSymbolHeading: Locator;
  readonly ciPipelineNameField: Locator;
  readonly ciBuildCloseButton: Locator;
  readonly workFlowEditorTab: Locator;
  readonly scanWithVulnerabilities: Locator;
  readonly chartTypeOption: Locator;
  readonly templateLoader: Locator;
  readonly basicArgumentField: Locator;
  readonly advancedYamlButton: Locator;
  readonly closeButtonBuildAndDeploy: Locator;
  readonly deployImageFromExternalCILink: Locator;
  readonly buildPipelineButton: Locator;
  readonly buildDeployFromSourceCodeButton: Locator;
  readonly branchTypeDropdown: Locator;
  readonly branchTypePullRequest: Locator;
  readonly branchTypeTagCreation: Locator;
  readonly createBuildPipelineAdvancedOptionsButton: Locator;
  readonly ciBuildPipelineNameField: Locator;
  readonly cdEnvironmentInput: Locator;
  readonly cdEnvironmentInputContainer: Locator;
  readonly branchNamePlaceholder: Locator;
  readonly gitopsDeploymentSpan: Locator;
  readonly imagePullDigestToggleButton: Locator;
  readonly preDeploymentCustomTagOptionOncd: Locator;
  readonly postDeploymentCustomTagOptionOncd: Locator;
  readonly jobsAndDevtronAppsNewWorkflowButton: Locator;
  readonly linkedCdNode: Locator;
  readonly buildPakcEnvVriableInputField: Locator;
  readonly overrideOptionsText: Locator;
  readonly ciAllowOverrideButton: Locator;
  readonly dockerBuildArgsAddParameterButton: Locator;
  readonly dockerBuildArgsKeyInputField: Locator;
  readonly dockerBuildArgsValueInputField: Locator;
  readonly buildxTargetPlatformDropdown: Locator;
  readonly locatorForPageStabilityVerification: Locator;
  readonly pushToRegistryRadioButton: Locator;
  readonly selectRegistryInput: Locator;
  readonly enterRepository: Locator;
  readonly goToBuildDeployLink: Locator;
  readonly progressing: Locator;
  readonly jobsAndDevtronappsStablePageLocator: Locator;
  readonly enterEnvironmentToDelete: Locator;
  readonly deleteModal: Locator;
  readonly externalLinksButton: Locator;
  readonly removeTargetPlatformIcon: Locator;
  readonly ciNodeNameDiv: Locator;
  readonly ciNodeDiv: Locator;
  readonly typeOfCiNodeDiv: Locator;
  readonly workflowHeader: Locator;
  readonly deploymentStrategyDropdown: Locator;
  readonly jobPage: JobsPage;
  readonly prepostCiCdPage: PrePostCiCd;
  readonly childAddIconToClick: Locator;
  readonly addStrategyDropdown: Locator;
  readonly dialogDeleteButton: Locator;
  private configButton: Locator;
  readonly buildAndDeployButton: Locator;
  readonly heading: Locator;
  readonly closeButton: Locator;
  readonly sourceTypeLabel: Locator;
  readonly sourceTypeCombobox: Locator;
  readonly branchNameInput: Locator;
  readonly createWorkFlowButton: Locator;
  readonly appOverviewTabButton: Locator;
  //migratetodevtron locators
  //migratetodevtron locators
  readonly migrateToDevtronTab: Locator;
  readonly helmReleaseRadioButton: Locator;
  readonly argoCdApplicationRadioButton: Locator;

  readonly clusterContainingHelmArgoDropdown: Locator;
  readonly clusterContainingHelmArgoInput: Locator;
  readonly clusterContainingHelmArgoMenuList: Locator;

  readonly externalHelmArgoApplicatioDropdown: Locator;
  readonly externalHelmArgoApplicatioInput: Locator;
  readonly externalHelmArgoApplicatioMenuList: Locator;
  readonly fluxCdRadioButton: Locator;

  constructor(page: Page) {
    super(page);
    // Set up locators in the constructor
    this.page = page;
    // todo: to replace the below locator in future.
    this.ciCdNodesSelector = page.locator("//div[@class='workflow-node__full-width-minus-Icon p-12']");
    //this.ciDeletePipelineButton = page.locator('[data-testid="ci-delete-pipeline-button"]');
    this.ciDeletePipelineButton = this.page.locator("//*[contains(@data-testid,'-delete-pipeline-button') or @data-testid='delete-linked-pipeline']");
    this.dialogDeleteButton = page.locator('//*[@data-testid="dialog-delete" or @data-testid="confirmation-modal-primary-button"]');
    this.deleteJobAppButton = page.locator('[data-testid="delete-job-app-button"]');
    this.appConfigurationTab = page.locator(`//*[contains(@data-testid,'-config-link')]`);
    this.appNameForLinkedPipelineDropdown = page.locator(".link-pipeline-filter-application__control");
    this.appNameForLinkedPipelineInput = page.locator(".link-pipeline-filter-application__input");
    this.appNameForLinkedPipelineMenuList = page.locator(".link-pipeline-filter-application__menu-list");
    this.sourceCiPipelineField = page.getByTestId("source-ci-pipeline-container");
    this.sourceCiPipelineFieldInput = page.getByTestId("source-ci-pipeline-input");
    this.linkedPipelineName = page.getByTestId("pipeline-name-for-linked");
    this.createLinkedPipelineNameButton = page.getByTestId("create-linked-ci-button");
    this.linkedBuildPipelineButton = page.getByTestId("linked-build-pipeline-button");
    this.newWorkflowButton = page.getByTestId('new-workflow-button');
    this.buildDeployByClick = page.getByText("Build & Deploy", { exact: true });
    this.pipelineNameField = page.locator("//input[@placeholder='Enter pipeline name']");
    this.ciModal = page.locator("//div[contains(@class,'modal__body--ci')]");
    this.linkedBuildPipelineModalHeader = page.getByText("Linked build pipeline", { exact: true });
    this.deleteLinkedPipelineButton = page.getByTestId("delete-linked-pipeline");
    this.linkedSymbolHeading = page.getByTestId("linked-symbol");
    this.ciPipelineNameField = page.locator("//*[@placeholder='e.g. my-first-pipeline']");
    this.workflowDiv = this.page.locator('//*[contains(@class,"workflow__body")]');
    this.childAddIconToClick = this.page.locator('//*[@class="add-cd-edge-btn"]');
    this.closeModealButton = this.page.getByTestId("close-build-deploy-button");
    this.prePostDropdown = this.page.locator('//*[contains(@class,"select-custom-image-tag-cd")]').first();
    this.customCdTagInputField = this.page.locator('//*[@name="image_tag"]');
    this.customTagCounterField = this.page.locator('//*[@name="image_counter"]');
    this.updatePipelineButton = this.page.locator("//*[contains(@data-testid,'build-pipeline-button') or contains(@data-testid,'linked-cd')]");
    this.newWorkflowButton = page.getByTestId('new-workflow-button');
    this.deployToButton = page.getByTestId('sync-env-deploy-to-btn');
    this.changeSourceIcon = this.page.locator("//*[contains(@class,' workflow-header-action-btn ')]");
    this.ciBuildCloseButton = this.page.locator("//*[@class='dc__transparent flex icon-dim-24']");
    this.workFlowEditorTab = this.page.getByTestId("workflow-editor-link");
    this.scanWithVulnerabilities = page.getByTestId('create-build-pipeline-scan-vulnerabilities-toggle');
    this.selectLanguageDropdown = this.page.locator('//*[contains(@class,"build-pack-language-dropdown__")]');
    this.chartTypeOption = this.page.locator('//*[@class="chart-type-options"]')
    this.basicArgumentField = page.locator('//*[text()="Arguments"]')
    this.templateLoader = page.locator('//*[@class="loader__svg"]')
    this.advancedYamlButton = page.getByTestId("base-deployment-template-advanced-button");
    this.closeButtonBuildAndDeploy = page.getByTestId("close-build-deploy-button");
    this.deployImageFromExternalCILink = page.getByTestId("deploy-image-external-service-link");
    this.buildPipelineButton = page.getByTestId('build-pipeline-button');
    this.buildDeployFromSourceCodeButton = page.getByTestId('build-deploy-from-source-code-button');
    this.branchTypeDropdown = page.locator("//*[contains(@class,'ci-pipeline-sourceType__control')]").first();
    this.branchTypeTagCreation = page.getByText("Tag Creation");
    this.branchTypePullRequest = page.getByText("Pull Request");
    this.createBuildPipelineAdvancedOptionsButton = page.getByTestId("create-build-pipeline-advanced-options-button");
    this.ciBuildPipelineNameField = page.getByPlaceholder('e.g. my-first-pipeline');
    this.branchNamePlaceholder = page.locator('//*[@data-testid="branchName" or @data-testid="branchRegex"]')
    this.gitopsDeploymentSpan = page.getByTestId('gitops-deployment-span');
    this.cdEnvironmentInputContainer = page.locator('.cd-pipeline-environment-dropdown__input-container');
    this.cdEnvironmentInput = page.locator('.cd-pipeline-environment-dropdown__input');
    this.imagePullDigestToggleButton = this.page.getByTestId('create-build-pipeline-image-pull-digest-toggle');
    this.preDeploymentCustomTagOptionOncd = this.page.locator('//*[text()="Pre-deployment stage"]');
    this.postDeploymentCustomTagOptionOncd = this.page.locator('//*[text()="Post-deployment stage"]');
    this.jobsAndDevtronAppsNewWorkflowButton = this.page.locator('//*[@data-testid="new-workflow-button" or @data-testid="job-pipeline-button" or text()=" Completed" ]');
    this.linkedCdNode = this.page.locator('//*[contains(@data-testid,"workflow-editor-link-cd")]');
    this.buildPakcEnvVriableInputField = this.page.locator('//*[@placeholder="Enter value"]');
    this.overrideOptionsText = this.page.locator('//*[text()="Override Options"]');
    this.ciAllowOverrideButton = this.page.getByTestId('create-build-pipeline-allow-override-button');
    this.dockerBuildArgsAddParameterButton = this.page.locator(`//*[@aria-label="Add"]`);
    this.dockerBuildArgsKeyInputField = this.page.locator('//*[@placeholder="Enter key"]');
    this.dockerBuildArgsValueInputField = this.page.locator('//*[@placeholder="Enter value"]');
    this.buildxTargetPlatformDropdown = this.page.locator('//*[contains(@class,"target-platform__select__indicator target-platform__select__dropdown-indicator")]');
    this.locatorForPageStabilityVerification = this.page.locator("//*[contains(@class,'workflow-action-header') or contains(@class,'app-compose__main')]").first();
    this.pushToRegistryRadioButton = this.page.getByText('Push to registry');
    this.selectRegistryInput = this.page.locator(".cd-trigger__select-container-registry__input");
    this.enterRepository = this.page.locator('//*[@name="repository_name"]');
    this.goToBuildDeployLink = this.page.getByTestId('go-to-build-deploy-link');
    this.progressing = this.page.getByTestId('progressing');
    this.jobsAndDevtronappsStablePageLocator = this.page.locator(`//*[contains(@class,"workflow-node") or text()="Workflows"]`);
    this.enterEnvironmentToDelete = this.page.getByPlaceholder('Type to confirm');
    this.deleteModal = this.page.getByTestId('confirmation-modal-primary-button');
    this.externalLinksButton = this.page.locator(`//*[text()="External Links"]`);
    this.removeTargetPlatformIcon = this.page.locator(`//*[contains(@aria-label,'Remove')]`);
    this.ciNodeDiv = this.page.locator(`//*[contains(@data-testid,'workflow-editor-ci-node')]`);
    this.typeOfCiNodeDiv = this.page.getByTestId('linked-indication-name');
    this.ciNodeNameDiv = this.ciNodeDiv.locator(this.typeOfCiNodeDiv).locator('xpath=following-sibling::div')
    this.workflowHeader = this.page.getByTestId('workflow-header');
    this.deploymentStrategyDropdown = this.page.locator(`//*[contains(@class,'deployment-strategy-dropdown__control')]`);
    this.addStrategyDropdown = this.page.locator(`//*[contains(@class,'deployment-strategy-dropdown')]`);
    this.jobPage = new JobsPage(page);
    this.prepostCiCdPage = new PrePostCiCd(page);
    this.configButton = this.page.locator('//*[@alt="config"]');
    //this.durationElements = this.page.locator('//*[text()="duration"]');
    this.buildAndDeployButton = page.getByTestId('build-and-deploy-from-source-code-button');
    this.heading = page.getByTestId('create-ci-cd-pipeline-modal-heading');
    this.closeButton = page.getByTestId('create-ci-cd-pipeline-modal-close-button');
    this.sourceTypeLabel = page.getByTestId('sourceType-getting-started-nodejs-label');
    this.sourceTypeCombobox = page.getByRole('combobox', { name: 'Source Type' });
    this.branchNameInput = page.getByTestId('branchName');
    this.createWorkFlowButton = page.getByTestId('ci-cd-workflow-create-button');
    this.appOverviewTabButton = this.page.getByTestId('overview-click');
    this.fluxCdRadioButton = this.page.getByTestId(`flux_cd-radio-item-span`);

    this.migrateToDevtronTab = page.locator(
      '//*[@data-testid="migrate-to-devtron-tab"]'
    );
    this.helmReleaseRadioButton = page.locator(
      '//*[@data-testid="helm-radio-item-span"]'
    );
    this.argoCdApplicationRadioButton = page.locator(
      '//*[@data-testid="argo_cd-radio-item-span"]'
    );

    this.clusterContainingHelmArgoDropdown = page.locator(
      ".migrate-from-source-cluster-select__control"
    );
    this.clusterContainingHelmArgoInput = page.locator(
      ".migrate-from-source-cluster-select__input"
    );
    this.clusterContainingHelmArgoMenuList = page.locator(
      ".migrate-from-source-cluster-select__menu-list"
    );

    this.externalHelmArgoApplicatioDropdown = page.locator(
      ".migrate-from-source-app-select__control"
    );
    this.externalHelmArgoApplicatioInput = page.locator(
      ".migrate-from-source-app-select__input"
    );
    this.externalHelmArgoApplicatioMenuList = page.locator(
      ".migrate-from-source-app-select__menu-list"
    );


  }


  async createNewLinkedPipeline(data: { linkedPipelineName: string, sourcePipelineName: string, appName: string, checkValidation: boolean }) {
    await expect(async () => {
      await this.appNameForLinkedPipelineDropdown.click();
      await this.appNameForLinkedPipelineInput.fill(data.appName);
      await this.appNameForLinkedPipelineMenuList.getByText(data.appName).click();
      await this.sourceCiPipelineField.click();
      await this.sourceCiPipelineFieldInput.fill(data.sourcePipelineName);
      await this.page.getByTestId(`source-ci-pipeline-menu-list-${data.sourcePipelineName}`).click();
    }).toPass({ timeout: 2 * 1000 * 60 });
    if (data.checkValidation) {
      await BaseTest.checkToast(this.page, this.createLinkedPipelineNameButton, "Error");
    }
    await this.pipelineNameField.fill(data.linkedPipelineName);
    await this.createLinkedPipelineNameButton.click();
  }
  async fetchPipelineIdOfAnyCiNode(workflowNumber: number = 0) {
    await this.appConfigurationTab.click();
    await this.workflowDiv.nth(workflowNumber).locator(this.ciNodeNameDiv).waitFor();
    return await this.workflowDiv.nth(workflowNumber).locator(this.ciNodeNameDiv).textContent();
  }

  /**
 * Deletes all CI/CD nodes and the application.
 * 
 * 
 */
  async deleteNodesFromWorkflow(workflowLocator: Locator) {
    const applicationUrl = await this.page.url();
    const nodeCount = await workflowLocator.locator(`//*[@class="data-hj-whitelist"]`).count();
    for (let node = nodeCount; node >= 0; node--) {
      if (await workflowLocator.locator(`//*[@class="data-hj-whitelist"]`).nth(node).isVisible()) {
        //await this.page.locator(`//*[contains(@class,'workflow--create')][${i}]//*[@class="data-hj-whitelist"]`).nth(node).click({delay:3000});
        await workflowLocator.locator(`//*[@class="data-hj-whitelist"]`).nth(node).click({ delay: 3000 });
        await this.ciDeletePipelineButton.waitFor({ state: 'visible' });
        await this.ciDeletePipelineButton.click({ delay: 3000 });
        //
        try {
          await this.page.locator('//*[@placeholder="Type to confirm"]').waitFor({ timeout: 7000 });
          let text = await this.page.locator(`//*[contains(@class,'confirmation-modal')]//*[@data-testid="delete-cd-node-input-label"]`).textContent();
          console.log('mine value is ' + text!.split("‘")[1].split("’")[0].trim());
          await this.page.locator(`//*[@name="delete-cd-node-input"]`).fill(text!.split("‘")[1].split("’")[0].trim());

        } catch (error) {
          console.log('modal did not come');
        }
        await this.dialogDeleteButton.click();
        if (await this.page.locator(`//*[contains(text(),'Please delete deployment pipelines for this workflow first and try again')]`).isVisible({ timeout: 7000 })) {
          await this.page.goto(applicationUrl);
          break;
        }
        try {
          await this.page.locator('//*[text()="Force Delete"]').waitFor({ timeout: 3000 });
          await this.page.locator(`//*[text()="Force Delete"]`).click({ delay: 3000 });
        }
        catch (error) {
          console.log(error)
        }
      }
      await this.page.waitForTimeout(5000);
    }
  }

  async deleteAppsFromUI(apiUtils: ApiUtils) {
    let appOrJob = 'app';
    if (await this.page.locator(`//*[@data-testid="trigger-job-link"]`).isVisible({ timeout: 7000 })) {
      appOrJob = 'job'
    }

    await this.page.getByTestId(`${appOrJob.toLowerCase()}-config-link`).click();

    await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
    const applicationUrl = await this.page.url();
    const appId = Number(applicationUrl.split('/')[5]);
    console.log(appId);
    const responseBody = await apiUtils.fetchEnvOnAppConfigurationsPage(appId);
    //Deleting Linked CD
    for (let key in responseBody.result) {
      const env = responseBody.result[key].environmentName;
      if (await this.page.getByTestId(`workflow-editor-link-cd-${env}`).isVisible()) {
        var workflowLocator = this.page.locator(`//*[@data-testid="workflow-editor-link-cd-${env}"]/ancestor::*[contains(@class,'workflow--create')]`);
        await this.deleteNodesFromWorkflow(workflowLocator);
      }

    }
    //Deleting LinkCI
    await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
    var workflowCount = await this.page.locator(`//*[contains(@class,'workflow--create')]`).count();
    for (let i = workflowCount; i > 0; i--) {
      await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
      if (!(await this.page.locator(`//*[contains(@class,'workflow--create')][${i}]//*[@data-testid="linked-symbol"]`).isVisible())) {
        console.log(`print ${i}`);
        workflowLocator = await this.page.locator(`//*[contains(@class,'workflow--create')][${i}]`);
        await this.deleteNodesFromWorkflow(workflowLocator);

      }
    }
    //Deleting Only CD 
    for (let key in responseBody.result) {
      const env = responseBody.result[key].environmentName;
      if (await this.page.getByTestId(`workflow-editor-cd-node-${env}`).isVisible())
        await this.deleteParticularCd(env);
    }

    await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
    workflowCount = await this.page.locator(`//*[contains(@class,'workflow--create')]`).count();
    for (let i = workflowCount; i > 0; i--) {
      await this.jobsAndDevtronAppsNewWorkflowButton.waitFor({ timeout: 25000 });
      workflowLocator = await this.page.locator(`//*[contains(@class,'workflow--create')][${i}]`);
      await this.deleteNodesFromWorkflow(workflowLocator);
    }
    await this.deleteJobAppButton.click({ delay: 3000 });
    await this.dialogDeleteButton.click();
    await this.page.waitForTimeout(5000);
    console.log('Deletion of all elements completed successfully.');

  }

  async deleteAllCiCdNodesAndApplication(appOrJob: string, linkedci: boolean = false, linkedCd: boolean = false) {
    try {
      await this.page.getByTestId(`${appOrJob.toLowerCase()}-config-link`).click();
      // Adding a delay to ensure proper element loading (Replace with more robust waits if needed)
      await this.jobsAndDevtronAppsNewWorkflowButton.first().waitFor({ timeout: 25000 });
      // Flag to indicate whether there are still nodes to delete
      let nodesExist = true;
      await this.page.waitForTimeout(5000);
      var cdNodes = await this.ciCdNodesSelector.all();
      var cdCount = cdNodes.length - 1;
      var count = 0;
      // Continue deleting nodes until none are left
      while (cdCount >= 0) {
        // Find all elements with the given locator
        const ciCdNodeList = await this.ciCdNodesSelector.all();
        // Log the number of elements to be deleted
        console.log('Number of elements to be deleted:', ciCdNodeList.length);

        // If there are no nodes left, exit the loop

        // Iterate over the elements in reverse order and perform actions
        // Adding a delay to ensure proper element loading (Replace with more robust waits if needed)
        await this.page.waitForTimeout(2000);
        if (linkedCd) {
          if (count == 1) {
            await this.linkedCdNode.click();
            await this.ciDeletePipelineButton.click();
            await this.dialogDeleteConfirmationButton.click();
          }
        }
        await this.ciCdNodesSelector.nth(cdCount).click();
        if (linkedci) {
          await expect(this.ciModal).toBeVisible();
          await expect(this.linkedBuildPipelineModalHeader).toBeVisible();
          await this.deleteLinkedPipelineButton.click();
          linkedci = false;
        } else {
          await this.ciDeletePipelineButton.click();
        }
        try {
          await this.deleteModalInputField.waitFor({ timeout: 7000 });
          let text = await this.page.locator(`//*[contains(@class,'confirmation-modal')]//*[contains(@data-testid,'input-label')]`).textContent();
          console.log('mine value is ' + text!.split("‘")[1].split("’")[0].trim());
          await this.deleteModalInputField.fill(text!.split("‘")[1].split("’")[0].trim());

        } catch (error) {
          console.log('modal did not come');
        }
        await this.dialogDeleteConfirmationButton.click();
        try {
          await this.page.locator('//*[text()="Force Delete"]').waitFor({ timeout: 3000 });
          await this.page.locator(`//*[text()="Force Delete"]`).click();
        }
        catch (error) {
          console.log()
        }
        await this.dialogDeleteConfirmationButton.waitFor({ state: 'hidden', timeout: 45000 });
        await this.jobsAndDevtronappsStablePageLocator.first().waitFor({ timeout: 1 * 1000 * 60 });
        await this.ciCdNodesSelector.nth(cdCount).waitFor({ state: 'hidden' });
        count = count + 1;
        cdNodes = await this.ciCdNodesSelector.all();
        cdCount = cdNodes.length - 1;
        console.log('updted cd count' + cdCount);
        // Click on the delete job/app button and confirm deletion
      }
      await this.deleteJobAppButton.click();
      // await this.dialogDeleteButton.click();
      await this.dialogDeleteConfirmationButton.click();
      await this.page.waitForTimeout(5000);
      console.log('Deletion of all elements completed successfully.');
    } catch (error) {
      console.error('Error during deleteAllCiCdNodesAndApplication:', error);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
      *  adding new cd's
      * @param {string} workflowNumber - in which workflow user want to operate
      * @param {string} parentCd - parent cd from which we want to add another cd
      * @param {string} isMiddleCdOrCi -if it is ci or middle cd , then we give two options to user(series,parallel)
      * @param {string} seriesOrParallel - we again give option to user for series or parallel
      */
  async clickOnAddIconToAddCd(workflowNumber: number, nodeName: string, childAddIconToClick: number) {
    await this.appConfigurationTab.click();
    await this.workflowDiv.nth(workflowNumber).locator(`//*[contains(@data-testid,'${nodeName}') and contains(@data-testid,'add-button')]`).click();
    if (await this.childAddIconToClick.first().isVisible()) {
      await this.childAddIconToClick.nth(childAddIconToClick).locator(`xpath=parent::*[local-name()='svg']`).click();
    }
  }


  /**
     *  configuring ci-cd's
     * @param {string} workflowNumber - in which workflow user want to operate
     * @param {string} parentCd - parent cd from which we want to add another cd or which we want to configure
     * just pass the name for ci pass "ci" else environment name
     */
  async clickOnSpecificCiCdNode(workflowNumber: number, parentciCd: string) {
    await expect(async () => {
      await this.appConfigurationTab.click({ delay: 1000 });
      await this.jobsAndDevtronAppsNewWorkflowButton.first().waitFor({ timeout: 7000 });
    }).toPass({ timeout: 1 * 1000 * 60 });
    await this.workflowDiv.nth(workflowNumber).locator(`//*[contains(@data-testid,"-${parentciCd}")]`).first().click();
  }

  async addBranchNameAndUpdate(branchName: string) {
    await this.branchNamePlaceholder.last().fill(branchName);
    await this.buildPipelineButton.click();
  }
  async closeUpdationModal() {
    try {
      await this.closeModealButton.click({ timeout: 8000 });
    }
    catch (error) {
      console.log('modal did not come');
    }

  }

  /**
     *  setting up customTag for pre-post cd
     * @param {string} prePostStage - in which stage user want to operate
     * @param {string} valueToEnter - tag value
     */
  async setupCustomTagForCd(prePostStage: string, valueToEnter: string, counterValue: string = "0") {

    await this.prePostDropdown.click();
    if (prePostStage.toLowerCase() == "pre") {
      await this.preDeploymentCustomTagOptionOncd.first().click();
    }
    else {
      await this.postDeploymentCustomTagOptionOncd.first().click();
    }
    await this.customCdTagInputField.clear();
    await this.customCdTagInputField.fill(valueToEnter);
    await this.customTagCounterField.clear();
    await this.customTagCounterField.fill("0");
    await this.updatePipelineButton.click();
  }
  async goToWorkflowEditorTab() {
    await this.workFlowEditorTab.click();
  }

  async goToAppConfigurationPage(buildAndDeployUrl: string) {
    await this.page.goto(buildAndDeployUrl);
    await this.appConfigurationTab.waitFor({ state: 'visible' });
    await this.appConfigurationTab.click();
  }

  /**
   * this method is used to setup a linked cd workflow
   * @param imageSrcEnv parent env that you want to make src 
   * @param deployToEnv env name on which u want to deploy
   */
  async configureLinkedCdWorkflow(imageSrcEnv: string, deployToEnv: string) {
    await this.page.locator(`//*[@class="drawer right show"]//*[text()="${imageSrcEnv}"]`).click();
    if (await this.deployToButton.isVisible()) {
      await this.deployToButton.click();
      await this.page.locator(`//*[@class="drawer right show"]//*[text()="${deployToEnv}"]`).click();
      await BaseTest.checkToast(this.page, this.updatePipelineButton, "Success");
      await this.closeUpdationModal();
    }
    else {
      await this.updatePipelineButton.click();
    }
  }


  /**
   * this method is used to change the image soruce of any workflow
   * @param workflowNumber workflow number that ypu want to change 
   * @param workfloyType type of workflow that u want to set
   */
  async clickOnChangeImageSourceButtonOfWorkflow(workflowNumber: number) {
    await this.appConfigurationTab.click();
    await this.page.locator('//*[@data-testid="workflow-header"]').nth(workflowNumber).hover();
    await this.changeSourceIcon.nth(workflowNumber).click();
  }


  /**
   * this method is used to check whether the button has been locked or not
   * @param locator 
   * @param isBlocked 
   */
  async checkIfButtonIsBlockedOrNot(locator: Locator, isBlocked: boolean) {
    await expect(locator).toBeDisabled();
  }


  /**
   * turn on image scan 
   */
  async clickOnScanninWithVulnerabilities() {
    await this.scanWithVulnerabilities.click();
    await this.updatePipelineButton.click();
  }



  /**
   * this method is used to configure buildx without dockerfile
   */
  async BuildWithoutDockerFile() {
    //reverting new code editor
    if (await this.codeMirrorEditorTextArea.textContent() != "") {
      await this.page.locator('//*[text()="FROM"]').first().dblclick();
      for (var i = 0; i < 12; i++) {
        if (process.env.OS as string == "Mac") {
          await this.page.keyboard.press('Meta+a');
        }
        else {
          await this.page.keyboard.press('Control+a');
        }
        await this.page.waitForTimeout(1000);
      }
      await this.page.keyboard.press('Backspace');
    }

    await dockerfile();
    //reverting new code editor 
    await this.codeMirrorEditorTextArea.click({ delay: 2000 });
    await this.page.keyboard.press("Shift+Insert");
    try {
      await expect(this.page.locator('//*[text()="CMD"]')).toBeVisible({ timeout: 15000 });
    }
    catch (error) {
      console.error(error);
      throw error;
    }

  }


  /**
   * confivure buildx with builder packs
   * @param data 
   */
  async buildWithBuilderPack(data: AllTypes.BaseCredentils.buildxConfiguration) {
    const keys = Object.keys(data.configuration!);
    for (var i = 0; i < keys.length; i++) {
      const key = keys[i];
      const value = data.configuration![key];
      await this.page.locator(`//*[contains(@class,'build-pack') and contains(@class,'-${key}-dropdown__indicators')]`).click();
      await this.page.locator(`//*[contains(@class,'build-pack') and contains(@class,'-${key}-dropdown__menu-list')]//*[text()="${value}"]`).click();
    }
    var value: string | null = "";
    if (data.configuration?.version != "Auto detect") {
      value = await this.buildPakcEnvVriableInputField.textContent();
    }
    expect(value).toBe(`${data.configuration?.version}`);
  }

  async clickOnAllowOverrideOnCiNode() {
    await this.overrideOptionsText.waitFor({ timeout: 40000 });
    if (await this.ciAllowOverrideButton.isVisible()) {
      await this.ciAllowOverrideButton.click();
    }
  }

  /**
   * this is the parent method that we call to configure buildx 
   * @param data 
   */
  async configureBuildx(data: AllTypes.BaseCredentils.buildxConfiguration) {
    await this.clickOnAllowOverrideOnCiNode();
    await this.page.locator(`//*[contains(text(),"${data.sourceOfImage}")]`).click();

    switch (data.sourceOfImage) {
      case 'Create Dockerfile': {
        if (data.setTargetPlatform != "") {
          await this.setTargetPlatformForBuild([data.setTargetPlatform!]);
        }
        await this.BuildWithoutDockerFile();
        break;
      }
      case 'Build without Dockerfile': {

        await this.buildWithBuilderPack(data);
        break;
      }
      case 'have a Dockerfile': {
        if (data.setTargetPlatform != "") {
          await this.setTargetPlatformForBuild([data.setTargetPlatform!]);
        }
        if (data.envVariables) {
          await this.addEnvVariables(data.envVariables);
        }
        break;
      }
      default: {
        console.log('value was not present in data ');
      }
    }
    await this.updatePipelineButton.click();
  }
  async addEnvVariables(data: string[]) {
    for (var i = 0; i < data.length; i++) {
      await this.dockerBuildArgsAddParameterButton.click();
      await this.dockerBuildArgsKeyInputField.first().fill(data[i].split(':')[0]);
      await this.dockerBuildArgsValueInputField.first().fill(data[i].split(':')[1]);
    }
  }

  async setTargetPlatformForBuild(targetPlatform: string[]) {

    await this.buildxTargetPlatformDropdown.click();
    let alreadyexistedPlatforms = await this.removeTargetPlatformIcon.count();
    console.log('count value is' + alreadyexistedPlatforms);
    for (let i = alreadyexistedPlatforms - 1; i >= 0; i--) {
      await this.removeTargetPlatformIcon.nth(i).click();
    }
    for (let key of targetPlatform) {
      await this.page.locator(`//*[text()="${key}"]`).click();
    }
  }


  /**
   * Creates a External CI workflow with CI
   * @param envNameForCD - The environment name for CD.
   */
  async configureDetailsForExternalCI(envDetails: AllTypes.appConfiguration.addCdModule) {
    try {
      await this.addCdModule(envDetails);
    } catch (error) {
      console.error(`Error during createNewWorkflow: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async closeBuildAndDeployButtonModal() {
    await this.closeButtonBuildAndDeploy.click();
  }

  async createNewWorkflowCiJobs(pipelineName: string) {
    await this.newWorkflowButton.click();
    await this.buildDeployFromSourceCodeButton.waitFor({ state: 'visible' });
    if (!this.page.getByTestId('job-ci-pipeline-button-pipeline-button').isVisible()) {
      console.log("CI-Jobs Pipeline Button is not visible, May be Enable_CI_Job is false");
    } else {
      await this.page.getByTestId('job-ci-pipeline-button-pipeline-button').click();
      await this.ciPipelineNameField.waitFor({ state: 'visible' });
      await this.ciPipelineNameField.fill(pipelineName);
      await this.branchNamePlaceholder.fill('main');
      await this.buildPipelineButton.click();
    }

  }

  /**
   * this method is used to set ci on auto or manually , you have to reach to auto-manual page first 
   * @param stage use Automatically or Manually
   */
  async setCiCdAutoOrManual(autoOrManual: 'Auto' | 'Manual') {
    await this.createBuildPipelineAdvancedOptionsButton.isVisible() ? await this.createBuildPipelineAdvancedOptionsButton.click() : console.log('it is a webhook');
    await this.page.locator(`//span[contains(text(),"${autoOrManual}")]`).click();
  }



  /**
   * Adds CI module to the workflow.
   * @param branchName - The name of the branch.
   * @param pipelineName - optional param for name of workflow to be created.
   */
  async addCiModule(data: { sourceType: 'Branch Fixed' | "Branch Regex", branchName: string[] } | { sourceType: 'Pull Request' | "Tag Creation" }, autoOrManual = 'Auto') {
    try {
      await expect(async () => {
        await this.branchTypeDropdown.waitFor({ state: 'visible' });
        await this.branchTypeDropdown.click();
        await this.page.locator('//*[@role="option"]').locator(`//*[text()="${data.sourceType}"]`).click();
        if ('branchName' in data) {
          for (let i = 0; i < data.branchName.length; i++) {
            await this.branchNamePlaceholder.nth(i).fill(data.branchName[i]);
          }
        }
        await this.setCiCdAutoOrManual("Auto");
        await this.buildPipelineButton.click();
        await expect(this.buildPipelineButton).toBeHidden({ timeout: 30000 });
      }).toPass({ timeout: 3 * 1000 * 60 });

    } catch (error) {
      console.error(`Error during addCiModule: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }


  /**
   * this method is used to fill ci branch name 
   * @param branchName 
   */
  async fillCiBranchName(branchName: string) {
    await this.branchNamePlaceholder.click();
    await this.branchNamePlaceholder.pressSequentially(branchName);
    await this.buildPipelineButton.click();
  }

  async clickOnAnyNodeAndSetAutoOrManual(autoOrManual: 'Auto' | 'Manual', nodeName: string, workflowNumber: number = 0) {
    await this.appConfigurationTab.click();
    await this.clickOnSpecificCiCdNode(workflowNumber, nodeName);
    await this.setCiCdAutoOrManual(autoOrManual);
    await this.updatePipelineButton.click();
    await this.closeBuildAndDeployButtonModal();
  }



  /**
   * Adds CD module to the workflow.
   * @param envNameForCD - The environment name for CD.
   */
  async addCdModule(data: { envNameForCd: string, helmOrGitops: string, clusterName: string, deploymentStrat?: 'ROLLING' | 'RECREATE', autoOrManual?: 'Auto' | "Manual" } | { envNameForCd: string, clusterName: string, virtualEnvConfig: { pushOrNot: 'Do not push' } | { pushOrNot: 'Push to registry', regName: string, repoName: string }, deploymentStrat?: 'ROLLING' | 'RECREATE', autoOrManual?: 'Auto' | "Manual" }) {
    try {
      await expect(async () => {
        await this.selectEnvironmentFromDropDown(data.envNameForCd, data.clusterName);
        if ('helmOrGitops' in data) {
          await this.gitopsDeploymentSpan.locator('span').first().click();
          await this.page.locator(`//*[@data-testid="${data.helmOrGitops}-deployment-span"]`).click();
        }
        if (data.deploymentStrat) {
          await this.deploymentStrategyDropdown.click();
          await this.page.locator('//*[@role="option"]').locator(`//*[text()="${data.deploymentStrat}"]`).click();
        }
        if ('virtualEnvConfig' in data) {
          await this.configureDetailsForVirtualCd(data.virtualEnvConfig);
        }
        if (data.autoOrManual)
          await this.setCiCdAutoOrManual(data.autoOrManual);
        await this.buildPipelineButton.click();
        await expect(this.page.locator(`//*[@data-testid="workflow-editor-page"]//*[text()="${data.envNameForCd}"]`)).toBeVisible({ timeout: 25000 });
        await this.closeUpdationModal();
      }).toPass({ timeout: 2 * 1000 * 60 });

    } catch (error) {
      console.error(`Error during addCdModule: ${error}`);
      throw error;
    }

  }


  /**
   * Updates the deployment strategy for a CD pipeline
   * @param environment The environment name to update
   * @param deploymentStrategyEnum The deployment strategy to set
   */
  async updateCdStrategy(environment: string, deploymentStrategyEnum: DeploymentStrategyEnum, workflowNumber: number = 0) {
    // Navigate to the app configuration tab
    await this.appConfigurationTab.click();
    // Click on the workflow node for the specified environment
    await this.clickOnSpecificCiCdNode(workflowNumber, `${environment}`);
    // Click on the strategy dropdown to show available options
    await this.addStrategyDropdown.click();
    // Select the specified deployment strategy from the dropdown
    await this.page.locator(`//*[contains(@class,'deployment-strategy-dropdown')]//*[text()="${deploymentStrategyEnum}"]`).click();
    // Click on the "set as default" option for the selected strategy with a delay
    await this.page.locator(`//*[text()="${deploymentStrategyEnum}"]/following-sibling::*[contains(@class,'set-as-default')]`).click({ delay: 2000 });
    // Click the update pipeline button to save changes
    await this.updatePipelineButton.click();
    // Close the modal after updating
    await this.closeModealButton.click();
  }



  /**
   * Updates deployment strategy to a custom YAML configuration
   * @param {string} environment - The environment name
   * @param {string} strategy - Which strategy to use ('canary' or 'blueGreen')
   * @param {number} workflowNumber - Optional workflow number (defaults to 0)
   * @returns {Promise<void>}
   */
  async updateDeploymentStrategyToCustomYaml(environment: string, strategy: 'canary' | 'blueGreen', workflowNumber: number = 0): Promise<void> {

    await this.appConfigurationTab.click();
    // Click on the workflow node for the specified environment
    await this.clickOnSpecificCiCdNode(workflowNumber, `${environment}`);

    // Click the config button
    await this.configButton.nth(1).click();
    await this.clearAnyTextArea(this.codeMirrorEditorTextArea);

    // Apply the appropriate strategy YAML based on parameter
    if (strategy === 'canary') {
      await this.enterConfigurationInTextArea(this.codeMirrorEditorTextArea, getCanaryStrategyYaml());
    } else {
      await this.enterConfigurationInTextArea(this.codeMirrorEditorTextArea, getBlueGreenStrategyYaml());
    }

    await this.updatePipelineButton.click({ delay: 300 });
    await this.closeModealButton.click();
  }

  async configureDetailsForVirtualCd(data: { pushOrNot: 'Do not push' } | { pushOrNot: 'Push to registry', regName: string, repoName: string }) {
    await expect(this.pushToRegistryRadioButton).toBeVisible();
    await this.page.locator(`//*[text()="${data.pushOrNot}"]`).click();
    if ('regName' in data) {
      await this.selectRegistryInput.click();
      await this.selectRegistryInput.fill(data.regName);
      await this.page.keyboard.press('Enter');
      console.log('repo name we got is' + data.repoName);
      await this.enterRepository.fill(data.repoName);
      console.log('filled');
    }
  }


  /**
   * Selects environment from CD module dropdown.
   * @param envName - The environment name.
   */

  public async selectEnvironmentFromDropDown(envName: string, clusterName: string = 'default_cluster') {
    try {
      await this.page.locator('.cd-pipeline-environment__control').click({ delay: 300 });

      const input = this.page.locator('input.cd-pipeline-environment__input');
      await input.fill(envName);

      // Use exact text match to avoid partial matches like 'env11', 'env12'
      const optionLocator = this.page.getByRole('option', { name: envName }).nth(0);

      await expect(optionLocator).toBeVisible({ timeout: 10000 });
      await optionLocator.click();
    } catch (error) {
      console.error(`Error during selectEnvironmentFromDropDown: ${error}`);
      throw error;
    }
  }


  /**
   * verify that toglle button is checked
   */
  async verifyImagePullToggleIsLocked() {
    await expect(this.imagePullDigestToggleButton).toBeChecked({ timeout: 25000 });
  }




  /**
   * this is the function if you want to verify the visibility/invisibility of nodes and text inside the nodes 
   * @param data -> we will be using 2 loops one to traverse nodes and one to traverse the text inside the nodes 
   */
  async verifyCdNodes(data: { workflowNumber: number, cdName: string, textToVerify: { text: string, isVisible: boolean }[] }) {
    await this.locatorForPageStabilityVerification.waitFor({ timeout: 12000 });
    for (const text of data.textToVerify) {
      await this.workflowDiv.first().waitFor();
      let isVisible = await this.page.locator(`//*[contains(@class,"workflow__body")]//*[contains(@data-testid,"-${data.cdName}")]//*[contains(text(),"${text.text}")]`).isVisible();
      expect(isVisible).toBe(true);
      expect(isVisible).toBe(text.isVisible);
    }
  }

  async deleteParticularCd(environmentName: string) {
    await this.appConfigurationTab.waitFor({ state: 'visible' });
    await this.appConfigurationTab.click();
    await this.page.locator(`//*[@data-testid="workflow-editor-cd-node-${environmentName}"]`).waitFor({ state: 'visible' });
    await this.page.locator(`//*[@data-testid="workflow-editor-cd-node-${environmentName}"]`).click();
    await this.progressing.waitFor({ state: 'hidden' });
    await this.ciDeletePipelineButton.waitFor({ state: 'visible' });
    await this.ciDeletePipelineButton.click({ delay: 2000 });
    await this.enterEnvironmentToDelete.waitFor({ state: 'visible' });
    await this.enterEnvironmentToDelete.click();
    await this.enterEnvironmentToDelete.fill(environmentName);
    await this.deleteModal.waitFor({ state: 'visible' });
    await BaseTest.checkToast(this.page, this.deleteModal, 'Pipeline Deletion Initiated');
  }

  async turnOnImageScanToggleButton() {
    await this.scanWithVulnerabilities.waitFor({ state: 'visible' });
    await this.scanWithVulnerabilities.click();
    await BaseTest.checkToast(this.page, this.updatePipelineButton, "Pipeline Updated");
  }

  async createWorkflows(
    data:
      | {
        workflowType: 'build-from';
        config:
        | { sourceType: 'Branch Fixed' | 'Branch Regex'; branchName: string[] }
        | { sourceType: 'Pull Request' | 'Tag Creation' };
        cdConfig?: AllTypes.appConfiguration.addCdModule;
      }
      | {
        workflowType: 'linked-build';
        config: {
          linkedPipelineName: string;
          sourcePipelineName: string;
          appName: string;
          checkValidation: boolean;
        };
        cdConfig?: AllTypes.appConfiguration.addCdModule;
      }
      | {
        workflowType: 'deploy-image';
        cdConfig: AllTypes.appConfiguration.addCdModule;
      }
      | {
        workflowType: 'linked-cd';
        config: {
          sourceEnv: string;
          destEnv: string;
        };
      }
      | {
        workflowType: 'job-ci-pipeline-button';
        config: {
          pipelineName: string;
          branchName: string;
          script: string[];
        };
        cdConfig?: AllTypes.appConfiguration.addCdModule;
      },
    areWeChangingImageSource: boolean = false
  ): Promise<void> {
    let existingWorkflows: any;
    if (!areWeChangingImageSource) {
      await this.appConfigurationTab.click();
      existingWorkflows = await this.fetchExistingWorkflowNames();
      await this.newWorkflowButton.first().click();
    }


    if (data.workflowType === 'job-ci-pipeline-button') {
      await this.page.locator(`//*[contains(@data-testid,'${data.workflowType}')]`).first().click();
    } else {
      await this.page.locator(`//*[contains(@data-testid,'${data.workflowType}')]`).click();
    }

    switch (data.workflowType) {
      case 'build-from': {
        await this.addCiModule(data.config);
        if (data.cdConfig && !areWeChangingImageSource) {
          await this.clickOnAddIconToAddCd(existingWorkflows.length, 'ci', 0);
          await this.addCdModule(data.cdConfig);
        }
        break;
      }

      case 'linked-build': {
        await this.createNewLinkedPipeline(data.config);
        if (data.cdConfig && !areWeChangingImageSource) {
          await this.clickOnAddIconToAddCd(existingWorkflows.length, 'ci', 0);
          await this.addCdModule(data.cdConfig);
        }
        break;
      }

      case 'deploy-image': {
        if (!areWeChangingImageSource) {
          await this.configureDetailsForExternalCI(data.cdConfig);
        }
        break;
      }

      case 'linked-cd': {
        if (!areWeChangingImageSource) {
          const { sourceEnv, destEnv } = data.config;
          await this.configureLinkedCdWorkflow(sourceEnv, destEnv);
        }
        break;
      }

      case 'job-ci-pipeline-button': {
        const { pipelineName, branchName, script } = data.config;
        await this.jobPage.fillJobPipelineDetails(pipelineName, branchName);
        await this.prepostCiCdPage.addPrePostTask('pre', 'execute');
        await this.jobPage.executeCustomScript(
          'task-name-' + BaseTest.generateRandomStringWithCharsOnly(3),
          script,
          true
        );

        if (data.cdConfig && !areWeChangingImageSource) {
          await this.clickOnAddIconToAddCd(existingWorkflows.length, 'ci', 0);
          await this.addCdModule(data.cdConfig);
        }
        break;
      }

      default:
        throw new Error(`Unhandled workflowType: ${(data as any).workflowType}`);
    }
  }


  async fetchExistingWorkflowNames(): Promise<string[]> {
    await this.jobsAndDevtronAppsNewWorkflowButton.first().waitFor({ timeout: 25000 });
    let existingWorkflows: string[] = [];
    let workflowHeaderNames = await this.workflowHeader.all();
    console.log('workflows found by us' + workflowHeaderNames.length);
    for (let key of workflowHeaderNames) {
      let name = await key.textContent();
      existingWorkflows.push(name!);
    }
    return existingWorkflows;
  }

  async findIndexOfWorkflow(textToSearchInsideTheWholeWorkflow: string) {
    await this.appConfigurationTab.click();
    await this.workFlowEditorTab.click();
    await this.workflowHeader.first().waitFor({ timeout: 25000 });
    let workflowHeaderNames = await this.workflowHeader.all();
    for (let i = 0; i < workflowHeaderNames.length; i++) {
      // console.log('workflow name is ' + await workflowHeaderNames[i].locator('xpath=following-sibling::div').textContent());
      let name = await workflowHeaderNames[i].locator('xpath=following-sibling::div').textContent();
      if (name?.includes(textToSearchInsideTheWholeWorkflow)) {
        console.log('workflow found at index ' + i);
        return i;
      }
    }
    return -1;
  }

  async verifyDetailsOfWorkflow(data: { workflowNumber: number, nodeNumber: number, textToVerify: string[] }) {
    await this.appConfigurationTab.click();
    for (let i = 0; i < data.textToVerify.length; i++) {
      await expect(this.workflowDiv.nth(data.workflowNumber).locator(`//*[contains(@class,'workflow-node ') or contains(@data-testid,'workflow-editor-link-cd')]`).nth(data.nodeNumber)).toContainText(data.textToVerify[i]);
    }

  }

  async configureBranchForMultiGitWithExistingWorkflows(branchName: string[]) {
    await this.workFlowEditorTab.click();
    await this.page.reload();
    await this.jobsAndDevtronAppsNewWorkflowButton.first().waitFor({ state: 'visible' });
    let allUncofiguredPipelines = await this.page.locator(`//*[text()="Not Configured"]`).all();
    console.log('total length we got is ' + allUncofiguredPipelines.length);
    for (let j = allUncofiguredPipelines.length - 1; j >= 0; j--) {
      await allUncofiguredPipelines[j].click();
      for (let i = 1; i > 0; i--) {
        try {
          await expect(async () => {
            await this.verifyLimitationsOfMultigit();
            await this.branchNamePlaceholder.nth(i).fill(branchName[i - 1], { timeout: 2000 });
          }).toPass({ timeout: 20000 });
        }
        catch (error) {
          console.log('no more branch to fill');
        }
      }
      await this.updatePipelineButton.click();
    }
  }

  async checkAppliedTargetPlatform(targetPlatform: string[]) {
    let completeTextContent = await this.page.locator(`//*[contains(@class,'target-platform__select__value-container')]`).first().textContent();
    for (let key of targetPlatform) {
      expect(completeTextContent).toContain(key);
    }
  }

  async verifyHelmOrGitopsSelectedInCd(isGitopsSelected: boolean) {
    if (isGitopsSelected) {
      await expect(this.gitopsDeploymentSpan.locator('xpath=preceding-sibling::input')).toBeChecked();
    }
  }
  async verifydeployStratConfiguredOnCd(strat: string) {
    await expect(this.page.locator(`//*[@class="deployment-strategy__info"]`)).toContainText(strat);
  }

  async migrateExternalAppsToDevtron(
    typeOfApplication: "helm" | "argocd" | "fluxcd",
    cluster: string,
    application: string
  ) {
    await this.migrateToDevtronTab.click();

    if (typeOfApplication == "argocd") {
      await this.argoCdApplicationRadioButton.click();
    } else if (typeOfApplication == "helm") {
      await this.helmReleaseRadioButton.click();
    } else {
      await this.fluxCdRadioButton.click();
    }
    await this.clusterContainingHelmArgoDropdown.click();
    await this.clusterContainingHelmArgoInput.fill(cluster);
    await expect(
      this.clusterContainingHelmArgoMenuList.getByText(cluster)
    ).toBeVisible({ timeout: 25000 });
    await this.clusterContainingHelmArgoMenuList.getByText(cluster).click();

    await this.externalHelmArgoApplicatioDropdown.click();
    await this.externalHelmArgoApplicatioInput.fill(application);
    await expect(
      this.externalHelmArgoApplicatioMenuList.getByText(application)
    ).toBeVisible({ timeout: 25000 });
    await this.externalHelmArgoApplicatioMenuList
      .getByText(application)
      .click();

    try {
      await expect(async () => {

        await expect(this.buildPipelineButton).toBeVisible({ timeout: 90000 });
        await this.buildPipelineButton.click();
        await expect(this.buildPipelineButton).toBeHidden({ timeout: 90000 });
        await this.closeUpdationModal();
      }).toPass({ timeout: 3 * 1000 * 60 });
    } catch (error) {
      console.error(`Error during linking external CD: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }
  async VarifyBuildAndDeployFromSourceCodeModal() {
    // Validate modal heading
    await expect(this.heading).toBeVisible()
    await expect(this.heading).toHaveText('Build and deploy from source code')

    // Validate close button
    await expect(this.closeButton).toBeVisible()

    // Validate branch name input
    await expect(this.branchNameInput).toBeVisible()
    await expect(this.branchNameInput).toHaveAttribute('placeholder', 'Eg. main')
  }

  async clickBuildAndDeployFromSourceCodeOption() {
    await this.buildAndDeployButton.click();
  }

  async selectSpecificSourceType(sourceType: SourceTypeEnum) {
    try {
      const dropdown = this.page.locator('.ci-pipeline-sourceType__control');
      await dropdown.click({ delay: 100 });

      // Wait for the dropdown menu container to appear
      const menu = this.page.locator('.ci-pipeline-sourceType__menu'); // Adjust class if needed
      await expect(menu).toBeVisible({ timeout: 10000 });

      // Now locate the option inside the menu
      const option = menu.locator('.ci-pipeline-sourceType__option', { hasText: sourceType });
      await expect(option).toBeVisible({ timeout: 10000 });

      await option.click();

      // Validate the selected value
      const selectedValue = this.page.locator('.ci-pipeline-sourceType__single-value');
      await expect(selectedValue).toHaveText(sourceType);

    } catch (error) {
      console.error(`Error in selectSpecificSourceType: ${error}`);
      await this.page.screenshot({ path: 'selectSourceType-error.png' });
      throw error;
    }
  }

  async EnterUserSpecificBranchName(branchName: string) {
    await this.branchNamePlaceholder.click();
    await this.branchNamePlaceholder.pressSequentially(branchName);
  }

  async selectHelmOrGitops(deploymentType: DeploymentTypeEnum) {
    const dataTestId = deploymentType === DeploymentTypeEnum.Helm
      ? 'helm-deployment'
      : 'gitops-deployment';

    const radioInput = this.page.getByTestId(dataTestId);
    const visibleRadio = this.page.getByTestId(`${dataTestId}-span`);

    // Wait for the visible span, not the hidden input
    await expect(visibleRadio).toBeVisible({ timeout: 10000 });

    const isChecked = await radioInput.isChecked();
    if (!isChecked) {
      await visibleRadio.click();
      const testId = deploymentType === DeploymentTypeEnum.Helm
        ? 'helm-deployment-span'
        : 'gitops-deployment-span';

      const visibleLabel = this.page.getByTestId(testId);

      await expect(visibleLabel).toBeVisible();

      // Click only if not already selected
      const isAlreadySelected = await this.page
        .getByTestId(deploymentType === DeploymentTypeEnum.Helm ? 'helm-deployment' : 'gitops-deployment')
        .isChecked();

      if (!isAlreadySelected) {
        await visibleLabel.click();
      }
    }
  }

  async ClickCreateWorkflowButton() {
    await this.createWorkFlowButton.click();
  }

  async ClickOnNewWorkflowButton() {
    await this.newWorkflowButton.click();
  }

  async navigateToAppOverviewTab() {
    await this.appOverviewTabButton.click();
  }
  async verifyLimitationsOfMultigit() {
    try {
      const allowedOptions = [SourceTypeEnum.BranchFixed, SourceTypeEnum.BranchRegex];
      const disallowedOptions = [SourceTypeEnum.TagCreation, SourceTypeEnum.PullRequest];

      const dropdown = this.page.locator('.ci-pipeline-sourceType__control').nth(1);
      await dropdown.click({ delay: 100 });

      // Instead of waiting for menu, wait for actual options to appear
      const options = this.page.locator('.ci-pipeline-sourceType__option');
      await expect(options).toHaveCount(2, { timeout: 2000 });

      const optionTexts = await options.allTextContents();

      // Check all options are allowed
      for (const text of optionTexts) {
        expect(allowedOptions).toContain(text);
      }

      // Ensure disallowed options are not visible
      for (const disallowed of disallowedOptions) {
        expect(optionTexts).not.toContain(disallowed);
      }
    } catch (error) {
      console.log('Error in verifyLimitationsOfMultigit:', error);
    }
  }
  async verifyBlockedWorkflowTypeWhileCreatingWorkflows(data: { workflowType: string, isHidden: boolean }[]) {
    for (let visibilityConfiguration of data) {
      if (visibilityConfiguration.isHidden) {
        await expect(this.page.locator(`//*[contains(@data-testid,'${visibilityConfiguration.workflowType}')]`)).toBeHidden()
      } else {
        await expect(this.page.locator(`//*[contains(@data-testid,'${visibilityConfiguration.workflowType}')]`)).toBeVisible()
      }
    }
  }




}
