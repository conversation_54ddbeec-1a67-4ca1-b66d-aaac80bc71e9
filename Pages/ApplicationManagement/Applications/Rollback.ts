import { expect, Page, Locator } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';

export class Rollback {

  readonly buildAndDeployTab: Locator
  readonly rollbackButton: Locator
  readonly materialHistoryCards: Locator
  readonly deployConfigDropdown: Locator
  readonly configDiffMark: Locator
  readonly configDiffReviewButton: Locator
  readonly rollbackWindowClose: Locator
  readonly lasSavedAndDeployedCloseConfigPopup: Locator;

  constructor(private page: Page) {
    this.buildAndDeployTab = page.getByTestId('build-deploy-click')
    this.rollbackButton = page.getByTestId('cd-trigger-deploy-roll-back-0');
    this.materialHistoryCards = page.locator('//*[contains(@class,"material-history material-history--cd image-tag-parent-card ")]')
    this.deployConfigDropdown = page.locator(`(//*[contains(@class,'pipeline-config-diff-tile')]//div[1])`).first();
    this.configDiffMark = page.getByText('Config Diff');
    this.configDiffReviewButton = this.page.locator(`//*[text()="REVIEW"]/ancestor::button`);
    this.rollbackWindowClose = page.locator('//*[@data-testid="header-close-button" or @class="dc__transparent"]');
    this.lasSavedAndDeployedCloseConfigPopup = this.page.getByTestId('close-config-popup');
  }

  async selectOptionFromDeployConfigDropdown(option: string) {
    await expect(async () => {
      await this.deployConfigDropdown.click();
      await this.page.getByText(option).last().click();
    }).toPass({ timeout: 3 * 1000 * 60 });
  }
  async checkConfigDiffAndReview(isThereADiff: boolean = true) {
    // await this.selectOptionFromDeployConfigDropdown("Last saved config");

    if (isThereADiff) {
      await expect(this.configDiffMark).toBeVisible({ timeout: 4000 });
    }
    // await this.lasSavedAndDeployedCloseConfigPopup.click({force:true});
    const popupClose = this.page.locator('[data-testid="close-config-popup"]');
    if (await popupClose.isVisible()) {
      await popupClose.click();
    }
    await this.configDiffReviewButton.click();
  }
  async applyRollback() {
    await this.buildAndDeployTab.click({ delay: 4000 });
    await Promise.all([this.page.waitForResponse(response => response.url().includes("material/rollback?offset=") && response.status() === 200), await this.rollbackButton.click()]);
  }

  async checkEmptyRollbackImages() {
    await expect(await this.materialHistoryCards.count()).toBe(0);

  }
  async checkNoOfRollbackImages(expectedNoOfImages: number) {
    console.log(await this.materialHistoryCards.count());
    await this.page.locator('//h2[contains(text(),"Rollback for")]')
  .waitFor({ state: "visible" });
    expect(await this.materialHistoryCards.count()).toBe(expectedNoOfImages);
  }

  async closeRollbackModal() {
    await this.rollbackWindowClose.click();
  }
}