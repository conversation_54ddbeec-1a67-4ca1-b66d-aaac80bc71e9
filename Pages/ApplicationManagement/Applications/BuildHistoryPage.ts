import { Page, Locator, expect } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';


const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class BuildHistoryPage {
  baseTest: BaseTest;

  // Locators
  readonly deploymentStatusText: Locator;
  readonly timeOutValueForImageScanningModals: number
  readonly commitDetailsLink: Locator;
  readonly deploymentHistorySourceCodeRepo: Locator;
  readonly gitCommitCredential: Locator;
  readonly artifactsLink: Locator;
  readonly artifactImageText: Locator;
  readonly dcVerticalAlignMiddle: Locator;
  readonly historyPipelineDropdownValueContainer: Locator;
  readonly historyPipelineDropdownIndicators: Locator;
  readonly abortCiBuildButton: Locator;
  readonly abortCiBuildConfirmationButton: Locator;
  readonly ciBuildStatus: Locator;
  readonly viewWorkerPod: Locator;
  readonly logsTab: Locator;
  readonly collapseAllStagesButton: Locator;
  readonly expandOrCollapseAllStagesButton: Locator;
  readonly viewSourcePipelineButton: Locator;
  readonly workerStatusDiv: Locator;
  readonly securityLink: Locator;



  constructor(private page: Page) {
    this.baseTest = new BaseTest();

    // Initialize locators
    this.commitDetailsLink = this.page.getByTestId('commit-details-link');
    this.deploymentHistorySourceCodeRepo = this.page.getByTestId('deployment-history-source-code-repo0');
    this.gitCommitCredential = this.page.getByTestId('git-commit-credential0');
    this.artifactsLink = this.page.locator("//*[contains(@data-testid, 'artifacts-link')]");
    this.artifactImageText = this.page.getByTestId('artifact-image-text');
    this.dcVerticalAlignMiddle = this.page.locator('.dc__vertical-align-middle');
    this.historyPipelineDropdownValueContainer = this.page.locator('.history-pipeline-dropdown__value-container');
    this.historyPipelineDropdownIndicators = this.page.locator('.history-pipeline-dropdown__indicators');
    this.abortCiBuildConfirmationButton = this.page.getByTestId(`confirmation-modal-primary-button`);
    this.abortCiBuildButton = page.locator('//*[text()="Abort"]');
    this.ciBuildStatus = this.page.getByTestId("deployment-status-text");
    this.viewWorkerPod = this.page.locator('//*[text()="View worker pod"]');
    this.logsTab = this.page.locator('//*[text()="Logs"]');
    this.expandOrCollapseAllStagesButton = this.page.locator('//*[@aria-label="Expand all stages" or @aria-label="Collapse all stages"]');
    this.collapseAllStagesButton = this.page.locator('//*[@aria-label="Collapse all stages"]');
    this.viewSourcePipelineButton = this.page.locator('//*[text()="View Source Pipeline"]');
    this.workerStatusDiv = this.page.locator('//*[text()="Worker" or @class="anchor"]/following-sibling::span').first();
    this.securityLink = this.page.getByTestId('security_link');
    this.timeOutValueForImageScanningModals = 45000;
  }

  async ViewCiWorkerPod() {
    let newPage: Page
    await expect(async () => {
      [newPage] = await Promise.all([this.page.waitForEvent('popup'), await this.viewWorkerPod.click({ delay: 1000 })]);
    }).toPass({ timeout: 3 * 1000 * 60 });
    return newPage!;
  }

  /**
   * Waits for build logs and status elements to be visible.
   */
  async verifyBuildLogsAndStatus(verificationText: string[] | string, status: string = "Succeeded") {
    try {
      await this.logsTab.click();
      await expect(this.workerStatusDiv).toContainText(/Running|Succeeded|succeeded|Failed/, { timeout: 12 * 1000 * 60 });
      await this.expandOrCollapseAllStagesInLogs(true);
      for (let i = 0; i < verificationText.length; i++) {
        await this.baseTest.waitForElementWithText(this.page, "//*[@data-testid='check-logs-detail']", verificationText[i], 12 * 60 * 1000);
      }
      let possibleStatus = [status, status.toLowerCase()];
      let regExpForStatus = new RegExp(possibleStatus.join("|"));
      await expect(this.workerStatusDiv).toContainText(status, { timeout: 15 * 1000 * 60 });

      this.verifyBuildStatus(status);
    } catch (error) {
      console.error(`An error occurred while verifying build logs and status: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async verifyBuildStatus(status: string = "Succeeded") {
    let possibleStatus = [status, status.toLowerCase()];
    let regExpForStatus = new RegExp(possibleStatus.join("|"));
    await expect(this.workerStatusDiv).toContainText(status, { timeout: 15 * 1000 * 60 });
  }

  /**
   * Verifies source repository and commit details.
   */
  async verifySourceRepoAndCommitDetails(repoName: string = "sample-html") {
    try {
      await this.commitDetailsLink.click();
      await expect(this.gitCommitCredential).toBeVisible();
      await expect(this.deploymentHistorySourceCodeRepo).toContainText(repoName, { timeout: 30000 });
      // Todo: need to be made dynamic
    } catch (error) {
      console.error(`An error occurred while verifying source repository and commit details: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * Verifies artifacts.
   */
  async verifyArtifacts(verificationText: string): Promise<string> {
    try {
      await this.artifactsLink.click();
      await expect(this.artifactImageText).toBeVisible();
      await expect(this.artifactImageText).toContainText(verificationText, { timeout: 1 * 1000 * 60 });
      var ImageBuilt = await this.artifactImageText.textContent();
      return ImageBuilt!;
    } catch (error) {
      console.error(`An error occurred while verifying artifacts: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * Verifies the overall build history page.
   */
  async verifyBuildHistoryPage() {
    try {
      await expect(this.dcVerticalAlignMiddle).toBeVisible({ timeout: 3 * 60 * 1000 });
      await expect(this.page.locator("//a[@data-testid='deployment-history-0']//div").first()).toBeVisible();
      await expect(this.historyPipelineDropdownValueContainer).toBeVisible();
      await expect(this.historyPipelineDropdownIndicators).toBeVisible();
    } catch (error) {
      console.error(`An error occurred while verifying the build history page: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async verifyNumberOfBuildTriggerByUser(userCountMapping: { users: string, userCounts: number }[]) {
    await this.artifactsLink.waitFor({timeout:35000});
    for (let i = 0; i < userCountMapping.length; i++) {
      const triggerCount = await this.page.locator(`//*[contains(@class,"deployment-history-card-container")]//*[text()='${userCountMapping[i].users}']`).count();
      expect(triggerCount).toBe(userCountMapping[i].userCounts);
    }
  }

  async clickOnTriggerBuildBytTriggerNumber(triggerNumber: number) {
    await this.page.getByTestId(`deployment-history-${triggerNumber}`).waitFor({ state: 'visible' });
    await this.page.getByTestId(`deployment-history-${triggerNumber}`).click();
  }
  async validateBuildTriggerInfoAndUserCountMapping(data: { buildTriggerInfo: { triggerBy: string, triggerNumber: number, status: string }, UserCountMapping: { users: string, userCounts: number }[] }) {
    await this.clickOnTriggerBuildBytTriggerNumber(data.buildTriggerInfo.triggerNumber)
    await this.page.getByTestId(`deployment-history-${data.buildTriggerInfo.triggerNumber}`).getByText(data.buildTriggerInfo.triggerBy).waitFor({ state: 'visible' });
    await this.verifyBuildStatus(data.buildTriggerInfo.status);

    await this.verifyNumberOfBuildTriggerByUser(data.UserCountMapping)

  }


  /**
   * Navigates to the App Configurations page.
   */
  async gotoAppConfigurationsPage() {
    try {
      console.log("Going to the App Configurations page");
      await this.page.getByTestId('app-config-link').click();
      await this.page.waitForLoadState('load');
    } catch (error) {
      console.error(`An error occurred while navigating to the App Configurations page: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * Abort the ci build and verify status should turned into aborted.
   */

  async abortCiBuild() {
    await this.baseTest.waitForElementWithText(this.page, '//*[text()="Worker" or @class="anchor"]/following-sibling::span', 'Running', 10 * 60 * 1000);
    await this.abortCiBuildButton.click();
    await this.abortCiBuildConfirmationButton.click();
    await expect(this.page.locator('//*[text()="aborted"]')).toBeVisible({ timeout: 3 * 1000 * 60 });
  }
  async expandOrCollapseAllStagesInLogs(expandAllStages: boolean) {
    await this.expandOrCollapseAllStagesButton.waitFor({ timeout: 2 * 1000 * 60 });
    console.log('printing the visibility' + await this.collapseAllStagesButton.isVisible());
    let isStageExpanded: boolean = await this.collapseAllStagesButton.isVisible() ? true : false;
    if (isStageExpanded != expandAllStages) {
      await this.expandOrCollapseAllStagesButton.click();
    }
  }

  async verifyNavigationToViewSourcePipeline(sourceAppId: string, workerStatus?: string) {
    await this.viewSourcePipelineButton.click();
    expect(this.page.url()).toContain(sourceAppId);
    if (workerStatus) {
      await expect(this.workerStatusDiv).toContainText(workerStatus, { timeout: 3 * 1000 * 60 });
    }
  }

  async goToSecurityTab() {
    await this.securityLink.waitFor({ state: 'visible' });
    await this.securityLink.click();
  }

  async getCiCdTriggerIdFromPageUrl(pageUrl: string) {
    const ciBuildId = pageUrl.split('/')[pageUrl.split('/').length - 2];
    return ciBuildId;

  }





  async verifySecurityScanSummaryCard() {
    //await expect(this.page.getByText('Security scan summary')).toBeVisible();
    //await expect(this.page.getByTestId('scanned-by-tool')).toContainText('Trivy');
    for (let key in credentials.vulnerabilitiesLabel) {
      await expect(this.page.getByTestId(`segmented-bar-chart-${key}-label`)).toBeVisible();
    }
  }

  async verifySecurityScanSummaryCardPage(isImageScan: boolean = false, isCodeScanEnabled: boolean = false) {
    if (isImageScan) {
      await expect(this.page.locator(`//*[contains(text(),'Image Scan')]/following-sibling::div//*[contains(text(),'vulnerabilities')]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
      process.env.clusterType == "enterprise" ? await expect(this.page.locator(`//*[contains(text(),'Image Scan')]/following-sibling::div//*[contains(text(),'license risks')]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals }) : null
    }
    if (isCodeScanEnabled) {
      await expect(this.page.locator(`//*[contains(text(),'Code Scan')]/following-sibling::div//*[contains(text(),'vulnerabilities')]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
      await expect(this.page.locator(`//*[contains(text(),'Code Scan')]/following-sibling::div//*[contains(text(),'license risks')]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
      await expect(this.page.locator(`//*[contains(text(),'Code Scan')]/following-sibling::div//*[contains(text(),'misconfigurations')]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
      await expect(this.page.locator(`//*[contains(text(),'Code Scan')]/following-sibling::div//*[contains(text(),'exposed secrets')]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
    }
    if (!isImageScan && !isCodeScanEnabled) {
      await expect(this.page.locator(`//*[@data-testid="generic-empty-state"]`)).toBeVisible();
      await expect(this.page.locator(`//*[contains(text(),'Image not scanned')]`)).toBeVisible();
    }
  }

  async verifySecurityScanSummaryCardOnSelectImageModal(image: string) {
    await expect(this.page.locator(`//*[text()="${image}"]/ancestor::*[contains(@class, "material-history")]//*[@data-testid="collapse-hide-info"]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
    await this.page.locator(`//*[text()="${image}"]/ancestor::*[contains(@class, "material-history")]//*[@data-testid="collapse-hide-info"]`).click({ timeout: this.timeOutValueForImageScanningModals });
    await expect(this.page.locator(`//*[contains(@class,'tab-list tab-list--vulnerability')]//*[contains(text(),"Security")]`)).toBeVisible({ timeout: this.timeOutValueForImageScanningModals });
    await this.page.locator(`//*[contains(@class,'tab-list tab-list--vulnerability')]//*[contains(text(),"Security")]`).click({ timeout: this.timeOutValueForImageScanningModals });
    await this.verifySecurityScanSummaryCard();
    await this.page.locator(`//*[@class='dc__transparent']//*[@alt="close"]`).click();
  }

  async verifyWorkerStatusAndMessage(expectedStatus: string, expectedMessage: string): Promise<void> {
    const statusContainer = this.page.locator('div.flexbox.cn-9.fs-13', { hasText: 'Worker' });
    const messageContainer = this.page.locator('div.min-w-385 span.cn-7');

    const normalize = (text: string | null) =>
      text?.replace(/\s+/g, ' ').trim() || '';

    // Wait until status contains the expected substring
    await expect(statusContainer).toContainText(expectedStatus, { timeout: 10000 });
    await expect(messageContainer).toBeVisible({ timeout: 10000 });

    const actualStatus = normalize(await statusContainer.textContent());
    const actualMessage = normalize(await messageContainer.textContent());
    const normalizedExpectedStatus = normalize(expectedStatus);
    const normalizedExpectedMessage = normalize(expectedMessage);

    expect(actualStatus).toContain(normalizedExpectedStatus);
    expect(actualMessage).toContain(normalizedExpectedMessage);
  }

}
