import { Page, Locator } from '@playwright/test'; // Importing necessary modules
import { BaseTest } from '../../../utilities/BaseTest'; // Importing BaseTest utility
export class CaCertificate {
  readonly globalConfiguration: Locator
  readonly ociRegistry: Locator
  readonly addRegistry: Locator

  readonly clickOnRegistryProvider: Locator
  readonly addRegistryName: Locator
  readonly addRegistryUrl: Locator
  readonly addRegistryUsername: Locator
  readonly addRegistryPassword: Locator

  readonly clickOnAdvancedRegistryUrl: Locator
  readonly clickOnAllowOnlySecure: Locator

  readonly clickOnSecureWithCaCertificate: Locator
  readonly clickOnPasteCaCertificateTab: Locator
  readonly clickOnSaveButton: Locator                // this will be used in all the three cases (secure, CA, Insecure)

  readonly clickOnAllowWithInsecure: Locator         // 






  constructor(private page: Page) {

    this.globalConfiguration = page.getByTestId('click-on-global-configuration')
    this.ociRegistry = page.getByTestId('Container/ OCI Registry-page')
    this.addRegistry = page.getByTestId('add-registry-button')
    this.clickOnRegistryProvider = page.locator('//*[@class="select-container-registry-type__input"]');

    this.clickOnAdvancedRegistryUrl = page.getByText('Advanced Registry URL Connection Options')
    this.clickOnSecureWithCaCertificate = page.getByText('Allow secure connection with CA certificate')
    this.clickOnPasteCaCertificateTab = page.locator('//*[@name="certInput"]')
    this.clickOnSaveButton = page.getByTestId('container-registry-save-button')



  }

  async addCaCertificate(CaCert: string) {

    const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
    await this.clickOnAdvancedRegistryUrl.click()
    await this.clickOnSecureWithCaCertificate.click()
    await this.clickOnPasteCaCertificateTab.click()
    await this.clickOnPasteCaCertificateTab.fill(credentials.registryData.HarborRegistry.caCertificate)
    await this.clickOnSaveButton.click()


  }


}

