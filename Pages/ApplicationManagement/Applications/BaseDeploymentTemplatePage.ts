import { Page, Locator, expect } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { BasePage } from '../../BasePage';
import { CompareValueManifestCategorySection } from '../../../enums/ApplicationManagement/Applications/CompareValueManifestCategorySectionEnum';
import { allowOrDeleteOverrideDTO,expressEditDTCMCS } from '../../../DTOs/ApplicationManagement/Applications/BaseDeploymentPageDTO';
import { JobsPage } from '../../AutomationandEnablement/Jobs';
import { editDTCMCSDTO } from '../../../DTOs/ApplicationManagement/Applications/BaseDeploymentPageDTO';

// Class representing a base deployment template page
export class BaseDeploymentTemplatePage extends BasePage {
  // Locators
  readonly saveAndNextButton: Locator;
  readonly baseDeploymentTemplateButton: Locator;
  readonly resourceMemoryInputField: Locator;
  readonly resourceMemoryCpuField: Locator;
  readonly saveBaseDTButton: Locator;
  readonly updateDialogConfirmButton: Locator;
  readonly approveChangesButton: Locator;
  readonly approveChangesConfirmButton: Locator;
  readonly discardDraftButton: Locator;
  readonly discardDraftConfirmButton: Locator;
  readonly baseDeploymentTemplateTab: Locator;
  readonly advancedYamlButton: Locator;
  readonly hideLockedKeys: Locator;
  readonly saveEligibleChangesCheckBox: Locator;
  readonly updateDeploymentTemplateButton: Locator;
  readonly saveDeploymentTemplateButton: Locator;
  readonly lockConfigModalSaveButton: Locator;
  readonly cancelButton: Locator;
  readonly protectConfigurationTab: Locator;
  readonly baseConfigurationTextInsideProtectConfigTab: Locator;
  readonly protectConfigInputCheckbox: Locator;
  readonly protectConfigOnOffToggleButtons: Locator;
  readonly allowOrDeleteOverrideButton: Locator;
  readonly confirmChangesButtonOnModal: Locator;
  readonly openDropdownOfEmailApproverList: Locator;
  readonly emailListContainer: Locator;
  readonly compareWithButton: Locator;
  readonly compareWithEnvAndVersionSelector: Locator;
  readonly compareWithPreviousDeploymentSelector: Locator;
  readonly currentEntityPreviousDeploymentSelector: Locator;
  readonly listOfDropdownEntities: Locator;
  readonly noDiffText: Locator;
  readonly mergeStrategyDropdown: Locator;
  readonly expressEditButton: Locator;
  readonly expressEditCloseButton: Locator;
  readonly appMetricsCheckboxChkSpan: Locator;
  readonly expressEditCompareBtn: Locator;
  readonly baseDeploymentTemplateExpressEditPublishButton: Locator;
  readonly baseConfigurationsLink: Locator;
  readonly deploymentTemplateDiv: Locator;
  readonly deploymentTemplateButton: Locator;
  //scope variable
  readonly scopeVariableValueDescriptorComponent: Locator;
  readonly scopeVariableValueDescriptorComponentCloseButton: Locator;
  readonly scopeVariableSearchInputField: Locator;
  readonly scopeVariableListDivLocator: Locator;
  readonly clearSearchButton: Locator;
  readonly scopeVaribleToggleButton: Locator;
  readonly scopeVariableToggleInputField: Locator;

  //customcharts
  readonly chartTypeDropdown: Locator;
  readonly customChartsButton: Locator;
  readonly customChartsMenuList: Locator;

  // Constructor that takes a Playwright Page instance
  constructor(public page: Page) {
    super(page);
    this.saveAndNextButton = page.getByTestId('base-deployment-template-save-and-next-button');
    this.baseDeploymentTemplateButton = page.getByTestId("base-deployment-template-link");
    this.resourceMemoryInputField = page.locator("//input[@name='resources_memory']");
    this.resourceMemoryCpuField = page.locator("//input[@name='resources_cpu']");
    this.saveBaseDTButton = page.getByTestId("base-deployment-template-save-changes-button");
    this.updateDialogConfirmButton = page.getByTestId("base_deployment_template_update_button");
    this.approveChangesButton = this.page.locator('//*[text()="Approve changes" or text()="Approve Changes"]');
    this.approveChangesConfirmButton = this.page.locator('//*[@data-testid="approve-changes-confirm-button"]');
    this.discardDraftButton = this.page.getByTestId("discard-draft-popup-button");
    this.discardDraftConfirmButton = this.page.getByTestId('discard-draft-config-button');
    this.advancedYamlButton = page.getByTestId("base-deployment-template-advanced-button");
    this.hideLockedKeys = page.getByTestId("hide-locked-keys-chk-span");
    this.saveEligibleChangesCheckBox = page.getByTestId("save-eligible-changes-chk-span");
    this.updateDeploymentTemplateButton = page.getByTestId("base_deployment_template_update_button");
    this.saveDeploymentTemplateButton = page.getByTestId("base-deployment-template-save-changes-button");
    this.lockConfigModalSaveButton = this.page.getByTestId('save-modal-diff');
    this.cancelButton = this.page.locator('//*[text()="Cancel"]');
    this.protectConfigurationTab = this.page.getByTestId("protect-configuration-link");
    this.baseConfigurationTextInsideProtectConfigTab = this.page.locator('//*[@class="app-compose__main"]//*[text()="Base configuration"]');
    this.protectConfigInputCheckbox = this.page.locator('//input[@type="checkbox"]');
    this.protectConfigOnOffToggleButtons = this.page.getByTestId('handle-toggle-button');
    this.allowOrDeleteOverrideButton = this.page.locator("//*[contains(@data-testid,'action-override-')]");
    this.confirmChangesButtonOnModal = this.page.getByTestId('onfirm-changes-button');
    this.openDropdownOfEmailApproverList = this.page.locator('//*[contains(@class,"select-approval__control")]');
    this.emailListContainer = this.page.locator('//*[contains(@class,"select-approval__menu-list")]');
    this.compareWithButton = this.page.getByTestId('compare-with-button');
    this.compareWithEnvAndVersionSelector = this.page.locator(`//*[contains(@class,'compare-with-environment-selector__control')]`);
    this.compareWithPreviousDeploymentSelector = this.page.locator(`//*[contains(@class,'environment-config-type-selector__control')]`);
    this.currentEntityPreviousDeploymentSelector = this.compareWithPreviousDeploymentSelector.nth(1);
    this.listOfDropdownEntities = this.page.locator('//*[@role="listbox"]');
    this.noDiffText = this.page.locator(`//*[contains(text(),'unchanged lines')]`).nth(1);
    this.mergeStrategyDropdown = this.page.locator(`//*[contains(@class,'config-toolbar-select-strategy__control')]`);
    this.deploymentTemplateDiv = this.page.getByTestId('env-deployment-template');
    this.expressEditButton = this.page.getByTestId('express-edit-button');
    this.expressEditCloseButton = this.page.getByTestId('express-edit-close-btn');
    this.appMetricsCheckboxChkSpan = this.page.getByTestId('app-metrics-checkbox-chk-span');
    this.expressEditCompareBtn = this.page.getByTestId('express-edit-compare-btn');
    this.baseDeploymentTemplateExpressEditPublishButton = this.page.getByTestId('base-deployment-template-express-edit-publish-button');
    this.baseConfigurationsLink = this.page.getByTestId('base-configurations-link');
    this.deploymentTemplateButton = this.page.getByText('Deployment Template');
    //scope variable
    this.scopeVariableValueDescriptorComponent = this.page.getByTestId('activate-suggestions');
    this.scopeVariableValueDescriptorComponentCloseButton = this.page.getByTestId('deactivate-suggestions');
    this.scopeVariableSearchInputField = this.page.getByTestId('debounced-search');
    this.scopeVariableListDivLocator = this.page.getByTestId('suggestion-item');
    this.clearSearchButton = this.page.locator(`//*[text()="Clear Search"]`);
    this.scopeVaribleToggleButton = this.page.getByTestId("resolve-scoped-variables");
    this.scopeVariableToggleInputField = this.scopeVaribleToggleButton.locator('xpath=preceding-sibling::input');
    //old code editor

      //customcharts
  this.chartTypeDropdown = page.getByTestId('select-chart-type-dropdown');
  this.customChartsButton = page.locator('span.radio__item-label', { hasText: 'Custom charts' });
  this.customChartsMenuList = page.locator('[data-testid="select-chart-type-menu-list"]');
  }



  async saveDeploymentTemplateWithCustomChart(customchart: string) {
    await this.chartTypeDropdown.click();
    await this.customChartsButton.click();
    await this.customChartsMenuList.getByText(customchart).click();
  }
  /**
   * Saves the deployment template while creating the new app only 
   */
  async saveDeploymentTemplateWhileCreatingNewApp() {

    await expect(async () => {
      await this.page.waitForLoadState('load');
      await this.saveAndNextButton.click({ timeout: 15000 });
      if (await this.updateDeploymentTemplateButton.isVisible({ timeout: 5000 })) {
        await this.updateDeploymentTemplateButton.click();
      }
    }).toPass({ timeout: 3 * 1000 * 60 });


  }

  /**
   * 
   * @param toastMessage after clicking on approve changes button what message you are expecting 
   */
  async approveChanges(toastMessage: string) {
    await expect(async () => {
      await this.approveChangesButton.click();
      await BaseTest.checkToast(this.page, this.approveChangesConfirmButton, toastMessage);
    }).toPass({ timeout: 2 * 1000 * 60 });
  }
  async setMergeStrategy(isReplace: boolean) {
    await this.mergeStrategyDropdown.click();
    let strategyToSelect: string = isReplace ? "Replace" : "Patch";
    await this.page.locator('//*[@role="option"]').locator(`//*[text()="${strategyToSelect}"]`).first().click();
  }


  /**
   * 
   * @param toastMessage after clicking on discard changes button what message you are expecting 
   */
  async discardDraft(toastMessage: string = "Draft discarded successfully") {
    await this.page.getByTestId('config-more-options-popup').first().click();
    await this.discardDraftButton.click();
    await BaseTest.checkToast(this.page, this.discardDraftConfirmButton, toastMessage);
  }

  /**
   * method to click on advanced yaml
   */
  async clickOnAdvancedYamlOrGui(isGui: boolean = false) {
    let textToUseInLocatorForGuiOrYamlButton: string = isGui ? "GUI" : "YAML";
    await this.page.locator(`//*[text()="${textToUseInLocatorForGuiOrYamlButton}"]/ancestor::div[1]`).click({ force: true });
    // await this.setLocatorForGuiAndYamlButton(this.page, isGui).click();
  }
  setLocatorForGuiAndYamlButton(page: Page, isGui: boolean): Locator {
    let finalLocator: Locator;
    let finalString: string = isGui ? 'GUI' : 'YAML'
    finalLocator = page.locator(`//*[@for="dt-yaml-gui-segmented-control-${finalString}"]`);
    return finalLocator;
  }

  /**
   * use thid method to edit any field in deployment template
   * @param field give field name whose value that you want to edit 
   * @param value give values that you want to put w.r.t field name 
   */
  async editAnyField(field: string[], value: string[]) {

    await expect(async () => {
      await this.deploymentTemplateDiv.click();
      var i = 0;
      await this.clickOnAdvancedYamlOrGui();
      while (i < field.length) {
        await this.scrollToParticularField(field[i]);
        // reverting new code editor 
        await this.codeMirrorEditorTextArea.locator(`//*[text()="${field[i]}"]`).click({ clickCount: 4 });
        await this.page.keyboard.press("Backspace");
        await this.page.keyboard.type(`${field[i]}: ${value[i]}`)
        i++;
        await this.page.keyboard.press("Enter");
      }
      await this.verfiyFieldValues(field, value);
    }).toPass({ timeout: 4 * 1000 * 60 });
  }

/**
 * Adds new fields to the deployment template YAML configuration
 * 
 * This function supports two modes:
 * 1. Adding a complete YAML configuration as a string
 * 2. Adding individual key-value pairs through arrays
 * 
 * @param keyOrYaml - Either a complete YAML string or an array of keys
 * @param value - Array of values corresponding to keys (required when using key array)
 * @returns {Promise<void>}
 */
async addNewField(keyOrYaml: string[] | string, value?: string[]): Promise<void> {
  // Navigate to the editor
  await this.deploymentTemplateDiv.click();
  await this.clickOnAdvancedYamlOrGui();
  await this.codeMirrorEditorTextArea.click();
  
  // Handle YAML string input
  if (typeof keyOrYaml === 'string') {
    await this.enterConfigurationInTextArea(this.codeMirrorEditorTextArea, keyOrYaml);
    return;
  }
  
  // Handle key-value pair arrays
  if (!value || value.length === 0) {
    throw new Error("Value array is required when providing key array");
  }
  
  if (keyOrYaml.length !== value.length) {
    throw new Error("Key and value arrays must have the same length");
  }
  
  // Add each key-value pair on a new line
  for (let i = 0; i < keyOrYaml.length; i++) {
    await this.page.keyboard.type(`${keyOrYaml[i]}: ${value[i]}`);
    await this.page.keyboard.press('Enter');
  }
}


  /**
   * this method is to make the field visible in viewport
   * @param fieldName field name that you want to scroll upto so that it is visible
   */
  async scrollToParticularField(fieldName: string) {
    let scrollCount = 0;
    //revert new code editor
    while (!await this.codeMirrorEditorTextArea.locator(`//*[text()="${fieldName}"]`).first().isVisible() && scrollCount < 400) {
      // Click on the manifest scrollable area
      await this.codeMirrorEditorTextArea.click();
      // Simulate mouse scrolling to move through the manifest
      await this.page.mouse.wheel(0, 40);
      // Increment the scroll count
      scrollCount++;
      if (await this.codeMirrorEditorTextArea.locator(`//*[text()="${fieldName}"]`).first().isVisible()) {
        break;
      }
    }
  }


  /**
   * this method is very useful when there are high chances of reload , and you want to verify that fields are edited properly
   * @param fieldName field name whose value that you want to verify 
   * @param values value that you want to verify 
   */
  async verfiyFieldValues(fieldName: string[], values: string[]) {
    for (let i = 0; i < fieldName.length; i++) {
      await this.scrollToParticularField(fieldName[i]);
      // revert new code editor
      await expect(this.codeMirrorEditorTextArea.locator(`//*[text()="${fieldName[i]}"]/parent::div`).first()).toContainText(values[i]);
    }
  }


  /**
   * this method will work for upper fields only as of now , we are not scrolling till down
   * @param verificationFields 
   */
  async verifyHideLockedKeys(verificationFields: string[]) {
    await this.page.getByTestId("config-more-options-popup").first().click();
    await this.page.getByTestId("hide-locked-keys-popup-button").click();
    for (let i = 0; i < verificationFields.length; i++) {
      await expect(this.page.locator(`//*[text()="${verificationFields[i]}"]`)).not.toBeVisible({ timeout: 30000 });
    }
    await this.page.getByTestId("config-more-options-popup").first().click();
    await this.page.getByTestId("show-locked-keys-popup-button").click();
  }


  /**
   * this method is for lock configuration when some lock keys are changed and some normal keys are changed 
   * @param fieldsToVerify 
   * @param deploymentTemplate 
   */
  async verifyChangesForUpdateAndSaveLockConfig(fieldsToVerify: { fieldName: string, isEligible: boolean }[], deploymentTemplate: string = "base-configurations") {
    try {
      await this.saveDeploymentTemplateButton.click();
      for (const key of fieldsToVerify) {
        var tabNumber = key.isEligible == true ? 2 : 1;
        //revert new code editor 
        expect(await this.codeMirrorEditorTextArea.nth(tabNumber).textContent()).toContain(key.fieldName);
      }
      await this.saveEligibleChangesCheckBox.click();
      await this.lockConfigModalSaveButton.click();
    } catch (error) {
      await this.cancelButton.click();
      throw error;
    }

  }


  /**
   * use this method , if app was alredy built 
   * @param deploymentTemplate pass the deployment as base-configurations for base deployment template and exact env name for env override
   */
  async SaveAndUpdateDeploymentTemplate(messageToVerify?: string) {
    if (!messageToVerify) {
      await this.saveDeploymentTemplateButton.click();
      await this.page.waitForTimeout(5000);
    }
    else {
      await BaseTest.checkToast(this.page, this.saveDeploymentTemplateButton, messageToVerify!);
    }
  }


  /**
   *  use this method to verify the diff or just in normal deployment , in case of normal deployment pass left as variable
   * @param data is editbale means that field's value should be visible or not 
   */

  async verifyConfigDifference(data: { field: string, value: string, isEditable: boolean, sideToVerifyDiff?: string }[]) {
    var index: number;
    for (const key of data) {
      var scrollCount = 0;
      index = !key.sideToVerifyDiff || key.sideToVerifyDiff == "left" ? 0 : 1;
      // revert new code editor
      await this.codeMirrorEditorTextArea.first().waitFor();
      while (!await this.codeMirrorEditorTextArea.nth(index).locator(`//*[text()="${key.field}"]`).first().isVisible() && scrollCount < 100) {
        // Click on the manifest scrollable area
        await this.codeMirrorEditorTextArea.first().click();
        // Simulate mouse scrolling to move through the manifest
        await this.page.mouse.wheel(0, 40);
        // Increment the scroll count
        scrollCount++;
        if (await this.codeMirrorEditorTextArea.nth(index).locator(`//*[text()="${key.field}"]`).first().isVisible()) {
          break;
        }
      }
      if (key.isEditable)
        expect(await this.codeMirrorEditorTextArea.nth(index).locator(`//*[text()="${key.field}"]/parent::div`).first().textContent()).toContain(key.value);

      else
        expect(await this.codeMirrorEditorTextArea.nth(index).locator(`//*[text()="${key.field}"]/parent::div`).first().textContent()).not.toContain(key.value);
      await this.page.mouse.wheel(0, -90000);

    }

  }
  async checkDiffInCompareAndApproveSection(data?: { field: string, value: string, isEditable: boolean, sideToVerifyDiff: string }[], isthereADiff: boolean = true, categoryToCheck: CompareValueManifestCategorySection = CompareValueManifestCategorySection.DeploymentTemplate) {
    let exapndableLocator: Locator = await this.returnLocatorforDeploymentTemplateAndPipelineConfigurationExpandButton(categoryToCheck);
    if (!isthereADiff) {
      await expect(exapndableLocator.locator(`xpath=parent::div`).locator(this.noDiffText)).toBeVisible();
    }
    if (data) {
      for (let key of data) {
        let indexNumber: number = key.sideToVerifyDiff == "right" ? 1 : 0;
        let textContent = await exapndableLocator.locator(this.codeMirrorEditorTextArea).nth(indexNumber).locator(this.page.locator(`//*[contains(text(),"${key.field}")]/ancestor::div[1]`)).textContent();
        let result = textContent!.includes(key.value);
        expect(result == key.isEditable);
      }
    }
  }


  /**
   * use this method to turn on or off protect config 
   * @param data stage -> for base deployment template , just pass base , for env just pass env name 
   * is on means if you want to turn protect config on or not 
   */

  async turnOnOffProtectConfigurations(data: { stageName: string, isOn: boolean }[]) {
    await this.protectConfigurationTab.click();
    await this.baseConfigurationTextInsideProtectConfigTab.waitFor({ timeout: 25000 });
    for (const key of data) {
      var stage = key.stageName.toLowerCase() == "base" ? "Base configuration" : key.stageName;
      console.log(await this.page.locator(`//*[text()="${stage}"]/parent::li`).locator(this.protectConfigInputCheckbox).isChecked() + 'value is');
      if (await this.page.locator(`//*[text()="${stage}"]/parent::li`).locator(this.protectConfigInputCheckbox).isChecked() != key.isOn) {
        await this.page.locator(`//*[text()="${stage}"]/parent::li`).locator(this.protectConfigOnOffToggleButtons).click();
      }
    }
  }



  /**
   * this method is used to do override or delete override 
   * @param isAllowOverride 
   * @param isProtectConfigurationTurnedOn 
   */

  async clickOnAllowOverrideOrDeleteOverride(data: allowOrDeleteOverrideDTO, isProtectConfigurationTurnedOn: boolean = false) {
    let valueToCheck: boolean = data.configuration.allowOverrideOrDelete == "allow" ? false : true
    await this.page.locator(`//*[@data-testid="config-head-tab-values"]`).click();
    if (await this.page.locator('//*[@data-testid="create-override-button"]').isVisible() != valueToCheck && data.configuration.allowOverrideOrDelete == 'allow' && 'replaceMergerStrat' in data.configuration) {
      await this.page.locator(`//*[@data-testid="create-override-button"]`).click();
      await this.setMergeStrategy(data.configuration.replaceMergerStrat);
    }
    else if (await this.page.locator('//*[@data-testid="create-override-button"]').isVisible() != valueToCheck && data.configuration.allowOverrideOrDelete == "delete") {
      await this.page.getByTestId("config-more-options-popup").first().click();
      await this.page.locator(`//*[@data-testid="delete-override"]`).click();
      isProtectConfigurationTurnedOn ? console.log('protect config is enabled so skipping modal click') : await this.dialogDeleteConfirmationButton.click();
    }
  }




  /**
   * this function is used to raise draft or propose changes 
   * @param data  approver list is optional , if given then it will look for that specific approver 
   */

  async raiseDraftOrProposeChanges(data: { stage: "save as draft" | "Propose changes" | "delete-override", approverList?: string[] }) {

    const radioTestId = data.stage === "save as draft" ? "Draft-type-span" :
      data.stage === "Propose changes" ? "Propose-type-span" :
        "delete-override";

    // Select radio if not delete override
    if (radioTestId != "delete-override") {
      await this.page.getByTestId(radioTestId).click();
    }

    // Select approvers if provided
    if (data.approverList?.length) {
      await this.openDropdownOfEmailApproverList.click();
      for (const approver of data.approverList) {
        await this.emailListContainer.locator(`//*[text()="${approver}"]`).click();
      }
      await this.openDropdownOfEmailApproverList.click();
    }
    //if propose changes button is not visible, so first close the dropdown.
    //Clicking on Save as draft/ Propose Changes/ Delete override button.
    const proposeChangesButton: Locator = data.stage === "delete-override" ? this.dialogDeleteConfirmationButton : this.page.getByTestId(`save-changes`);
    //await this.openDropdownOfEmailApproverList.click();
    try {
      await proposeChangesButton.click({ delay: 2000 });
    } catch (error) {
      await this.openDropdownOfEmailApproverList.click();
      await proposeChangesButton.click({ delay: 2000 });
    }

  }





  /**
   * this function is for switching between tabs like edit draft/published values / compare values 
   * @param tabName 
   */
  async swithBetweenTabsInDeploymentTemplate(tabName: string) {
    let tabvalue: string | null = tabName.toLowerCase() == "edit draft" ? "visit-editDraft" : tabName.toLowerCase() == "compare and approve values" ? "visit-compare" : tabName.toLowerCase() == "published values" ? "visit-published" : null;
    await this.page.locator(`//*[@data-testid="${tabvalue}"]`).click();
  }




  /**
   * this function is for opening the config diff of values and manifest and you can compare with any entity based on value that u pass
   * 
   * @param compareValues -> if you want to compare values then set it to true , we are picking values on the index basis 
   */
  async openCompareValuesOrManifestWindow(compareValues: boolean) {
    await this.page.locator('//*[@data-testid=""]').click();
    var stage = compareValues == true ? 0 : 1;
    await this.page.locator('//*[@data-testid="dropdown-item"]').nth(stage).click();
  }
  async clickOnCompareWithConfigOrManifestAndSelectComparisonDropdowns(comparisonRelatedData: { compareWithEntity: string, compareWithEntityPrevDeployments?: number, currentEntity?: number, isManifestOutput: boolean }) {
    await this.compareWithButton.click();
    comparisonRelatedData.isManifestOutput ? await this.page.locator(`//*[text()="Manifest Output"]`).click() : await this.page.locator('//*[text()="Configuration"]').click();
    await this.selectValuesFromCompareWithDropdowns('compareWithEntity', comparisonRelatedData.compareWithEntity);
    comparisonRelatedData.compareWithEntityPrevDeployments ? await this.selectValuesFromCompareWithDropdowns('compareWithEntityPrevDeployments', comparisonRelatedData.compareWithEntityPrevDeployments) : console.log('skipping this');
    comparisonRelatedData.currentEntity ? await this.selectValuesFromCompareWithDropdowns('currentEntity', comparisonRelatedData.currentEntity) : console.log('skipping this');
  }

  async verifyTheResultOfCompareWithConfigOrManifest(data: { verificationRelatedData: { field: string, value: string, sideToVerifyDiff: string, isEditable: boolean }[], hasDifference: boolean, categoryToCheck: CompareValueManifestCategorySection }) {
    await this.checkDiffTagOnDtAndpipelineConfiguration(data.categoryToCheck, data.hasDifference);
    await this.checkDiffInCompareAndApproveSection(data.verificationRelatedData, data.hasDifference, data.categoryToCheck);

  }
  async closeCompareConfigOrManifestSection() {
    await expect(async () => {
      await this.page.locator(`//a[contains(@href,'deployment-template')]`).first().click();
      await expect(this.compareWithButton).toBeVisible({ timeout: 10000 });
    }).toPass({ timeout: 1 * 1000 * 60 });
  }

  async returnLocatorforDeploymentTemplateAndPipelineConfigurationExpandButton(categoryToCheck: CompareValueManifestCategorySection): Promise<Locator> {
    return this.page.locator(`//*[@aria-label="expand-collapse-btn"]//*[contains(text(),'${categoryToCheck}')]/ancestor::div[1]`)
  }

  async checkDiffTagOnDtAndpipelineConfiguration(categoryToCheck: CompareValueManifestCategorySection, hasDiffernce: boolean) {
    let locatorToClick = await this.returnLocatorforDeploymentTemplateAndPipelineConfigurationExpandButton(categoryToCheck);
    await locatorToClick.waitFor();
    if (await locatorToClick.locator(`//div`).first().isHidden()) {
      await locatorToClick.click();
    }
    hasDiffernce ? await expect(locatorToClick!.locator('//*[text()="Has difference"]')).toBeVisible() : await expect(locatorToClick!.locator('//*[text()="No difference"]')).toBeVisible()
  }

  async selectValuesFromCompareWithDropdowns(dropdownType: string, valueToSelect: string | number) {
    let dropdownTypeObject = {
      compareWithEntity: this.compareWithEnvAndVersionSelector,
      compareWithEntityPrevDeployments: this.compareWithPreviousDeploymentSelector.first(),
      currentEntity: this.currentEntityPreviousDeploymentSelector
    }
    await dropdownTypeObject[dropdownType].click();
    let typeOfValue = typeof valueToSelect;
    if (typeOfValue == 'number') {
      await this.listOfDropdownEntities.locator(`//*[@role="option"]`).nth(valueToSelect as number - 1).click();
    }
    else {
      await this.listOfDropdownEntities.locator(`//*[text()="${valueToSelect}"]`).click();
    }
  }

  async verifyTheMergeStrategyApplied(mergeStrategy: string) {
    await expect(this.mergeStrategyDropdown).toContainText(mergeStrategy);
  }

  async publishChangesForExceptionUserDeploymentTemplate(config: any, stage: string) {
    await this.deploymentTemplateButton.waitFor();
    await this.deploymentTemplateButton.click();
    if (stage != "base-configurations") {
      await this.clickOnAllowOverrideOrDeleteOverride({ configuration: { allowOverrideOrDelete: 'allow', replaceMergerStrat: false } });
    }
    await this.expressEditButton.waitFor();
    await this.expressEditButton.click();
    await this.expressEditCloseButton.waitFor();
    await this.expressEditCloseButton.click()
    await this.expressEditButton.waitFor();
    await this.expressEditButton.click();
    if (stage != "base-configurations") await this.setMergeStrategy(true);
    await this.editAnyField([config.key], [config.value]);

    await this.appMetricsCheckboxChkSpan.click();

    await BaseTest.checkToast(this.page, this.baseDeploymentTemplateExpressEditPublishButton, "Changes will be reflected after next deployment.")
  }

  async searchAScopeVariableInScopeVariableComponent(scopeVariableToSearch: string) {
    await this.scopeVariableSearchInputField.fill(scopeVariableToSearch);
  }

  async verifyTheResolvedValueOfScopeVariableInTippy(scopeVarObjectData: { scopeVariableName: string, valueToVerify: string[] }[]) {
    for (let scopeVarObject of scopeVarObjectData) {
      await expect(async () => {
        await this.scopeVariableListDivLocator.locator(`//*[text()="${scopeVarObject.scopeVariableName}"]`).hover();
        await this.verifyTheValuesInsideTooltip(scopeVarObject.valueToVerify);
      }).toPass({ timeout: 2 * 1000 * 60 });
    }
  }

  async verifyScopeVariableShouldBeVisibleOrNot(scopeVariableCollections: { scopeVariableName: string, isVisible: boolean }[]) {
    for (let scopeVariable of scopeVariableCollections) {
      await this.searchAScopeVariableInScopeVariableComponent(scopeVariable.scopeVariableName);
      scopeVariable.isVisible ? await expect(this.scopeVariableListDivLocator.locator(`//*[text()="${scopeVariable.scopeVariableName}"]`)).toBeVisible() : await this.page.locator(`//*[text()="Clear Search"]`).click();
    }
  }


  async verifyScopeVariablesValueAndVisibilityInScopeVarComponent(scopeVarData: { scopeVariableName: string, isVisible: boolean, valueToVerify?: string[] }[]) {
    await this.scopeVariableValueDescriptorComponent.click();
    for (let scopeVarObject of scopeVarData) {
      await this.verifyScopeVariableShouldBeVisibleOrNot([{ scopeVariableName: scopeVarObject.scopeVariableName, isVisible: scopeVarObject.isVisible }]);
      if (scopeVarObject.valueToVerify && scopeVarObject.isVisible) {
        await this.verifyTheResolvedValueOfScopeVariableInTippy([{ scopeVariableName: scopeVarObject.scopeVariableName, valueToVerify: scopeVarObject.valueToVerify }]);
      }
    }
    await this.scopeVariableValueDescriptorComponentCloseButton.click();
  }

  async turnOnOffScopeVariableToggle(turnOn: boolean) {
    await this.scopeVaribleToggleButton.waitFor();
    if (turnOn != await this.scopeVariableToggleInputField.isChecked()) {
      await this.scopeVaribleToggleButton.click();
    }
  }

  async editAndSaveFieldsForDtCmCs(object: editDTCMCSDTO, editThroughExpressEdit: boolean = false) {
    await object.jobsPage.operBaseOrEnvOverRideResources(object.stage);
    if (object.resType == "dt") {
      await this.deploymentTemplateButton.click();
      if (object.stage != 'base-configurations') {
        await this.clickOnAllowOverrideOrDeleteOverride(object.mergeStrat!);
        editThroughExpressEdit ? await this.expressEditButton.click() : null;
        if ('replaceMergerStrat' in object.mergeStrat?.configuration! && object.mergeStrat?.configuration.replaceMergerStrat == false && object.stage != "base-configurations") {
          await this.addNewField(object.fieldsToEdit, object.valuesToEdit);
        }
        else {
          await this.editAnyField(object.fieldsToEdit, object.valuesToEdit);
        }
      }
      else {
        editThroughExpressEdit ? await this.expressEditButton.click({ delay: 1000 }) : null;
        console.log('yes clicked on express edit ')
        await this.editAnyField(object.fieldsToEdit, object.valuesToEdit);
      }
      editThroughExpressEdit ? await this.baseDeploymentTemplateExpressEditPublishButton.click() : await this.SaveAndUpdateDeploymentTemplate();
    }
    else {
      await object.jobsPage.clickOnAnyCmCSAndReturnExistence(object.resType, object.resName);
      object.stage != "base-configurations" ? await this.clickOnAllowOverrideOrDeleteOverride(object.mergeStrat!) : null;
      editThroughExpressEdit ? await this.expressEditButton.click() : null;
      for (let i = 0; i < object.fieldsToEdit.length; i++) {
        await object.jobsPage.editCmCs(object.resType, object.resName, object.stage, false, object.fieldsToEdit[i], object.valuesToEdit[i], false, editThroughExpressEdit);
      }
    }
  }

  async expressEditForDtCmCs(object: expressEditDTCMCS) {
    if (object.configuration.isEligible == true) {
      await this.editAndSaveFieldsForDtCmCs(object.configuration.editRelatedData, true);
    } else {
      await this.expressEditButton.waitFor({ state: 'hidden' });
    }
  }




  async verifyFieldsValuesInDtCmCS(fields: string[], valuesToVerify: string[], resType: string, jobsPage: JobsPage) {
    if (resType == "dt") {
      await this.verfiyFieldValues(fields, valuesToVerify);
    }
    else {
      await jobsPage.verifyCmSecrets({ keyName: fields, valueName: valuesToVerify })
    }
  }
}






