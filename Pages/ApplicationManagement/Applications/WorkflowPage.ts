import { Page, Locator, expect } from '@playwright/test';
import { Constants } from '../../../utilities/Constants';
import { BaseTest } from '../../../utilities/BaseTest';
import exp from 'constants';
import { BasePage } from '../../BasePage';

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class WorkflowPage extends BasePage {
  // Locators
  readonly newWorkflowButton: Locator;
  readonly buildDeployFromSourceCodeButton: Locator;
  readonly deployImageFromExternalCILink: Locator;
  readonly branchNamePlaceholder: Locator;
  readonly buildPipelineButton: Locator;
  readonly addCdIcon: Locator;
  readonly cdEnvironmentInputContainer: Locator;
  readonly cdEnvironmentInput: Locator;
  readonly cdEnvironmentMenuList: Locator;
  readonly gitopsDeploymentSpan: Locator;
  readonly triggerCiModuleButton: Locator;
  readonly workflowBuildSelectMaterialButton: Locator;
  readonly ciTriggerStartBuildButton: Locator;
  readonly workflowNodeDetailsLink: Locator;
  readonly buildAndDeployTab: Locator;
  readonly ciModalCloseIcon: Locator;
  readonly ciStatusOfWorkflowPage: Locator;
  readonly createBuildPipelineAdvancedOptionsButton: Locator;
  readonly ciBuildPipelineNameField: Locator;
  readonly ciLinkedStatus: Locator;
  readonly closeButtonBuildAndDeploy: Locator;
  readonly goToBuildAndDeploy: Locator;
  readonly workflowDiv: Locator;
  readonly cdTriggerDeploymentButton: Locator;
  readonly imageModals: Locator;
  readonly imageModalDeploymentTime: Locator;
  readonly latestTag: Locator;
  readonly activeTag: Locator;
  readonly addTagButton: Locator;
  readonly tagInputField: Locator;
  readonly saveTagsButton: Locator;
  readonly imageExcludedTag: Locator;
  readonly eligibleImagesTabButton: Locator;
  readonly noImagesFoundSection: Locator;
  readonly latestImagesTabButton: Locator;
  readonly showOrHideSourceInfo: Locator;
  readonly imageNotEligibleText: Locator;
  readonly configuredFilterLink: Locator;
  readonly passConditionTabButton: Locator;
  readonly failConditionTabButton: Locator;
  readonly ConfigurePluginsToUnblockBuild: Locator;
  readonly startBuildPluginPolicy: Locator;
  readonly configurePluginForWarningButton: Locator;
  readonly pipelineTriggeredPopup: Locator;
  readonly workflowHeader: Locator;
  readonly workflowEditorTab: Locator;
  readonly detailsButton: Locator;
  readonly cmAndSecretButtonInCD: Locator;
  readonly selectImage: Locator;
  readonly configureGitopsRepo: Locator;
  readonly GitOpsRepositoryIsNotConfigured: Locator;
  readonly workflowEditorPage: Locator;
  readonly branchTypeDropdown: Locator;
  readonly branchTypePullRequest: Locator;
  readonly branchTypeTagCreation: Locator;
  readonly closeCdModal: Locator
  readonly parameterTabButton: Locator;
  readonly addParameterButton: Locator;
  readonly paramKeyInputField: Locator;
  readonly paramValueField: Locator;
  readonly ciCommitSearchInputField: Locator;
  readonly genericCiCommitCard: Locator;
  readonly configurePluginWithWarning: Locator;
  readonly triggerWithWarning: Locator;
  readonly commonLocatorForTriggerAndDeploy: Locator;
  readonly commonLocatorForSelectImageAndMaterial: Locator;
  readonly prePostCdCloseIconButton: Locator;
  readonly viewImagesForApproval: Locator;
  readonly requestApprovalButton: Locator;
  readonly notifyUsersInputField: Locator;
  readonly submitRequestButton: Locator;
  readonly approvalPendingTab: Locator;
  readonly cancelRequestButton: Locator;
  readonly cancelRequestButtonInTippy: Locator;
  readonly eligibleApproversTippyOpener: Locator;
  readonly tooltipModal: Locator;
  readonly eligibleUsersRowInToolTipModal: Locator;
  readonly imageApproveButton: Locator;
  readonly imageApproveModalButton: Locator;
  readonly expireApprovalButton: Locator;
  readonly expireApprovalModalButton: Locator;
  readonly promoteImageButton: Locator;
  readonly changeImageSourceForImagePromotionDropdown: Locator;
  readonly promoteToEnvButton: Locator;
  readonly promoteImageButtonInsideTargetEnvModal: Locator;
  readonly closeRequestPromotionModal: Locator;
  readonly imagePromotionApprovalPendingTab: Locator;
  readonly approveForButton: Locator;
  readonly approvePromotionRequestButton: Locator;
  readonly imagePromotionApprovalCloseModal: Locator;
  readonly deploymentWindowConfirmationDeployButton: Locator;
  readonly deploymentWindowConfirmationModal: Locator;
  readonly executeJobInEnvironment: Locator;
  readonly executeJobInEnvironmentDropdownMenu: Locator;
  private backButtonArtifactListingModal: Locator;
  private closeConfigPopup: Locator;
  private lastSaveAndDeployedConfigModal: Locator
  private LastSaveAndDeployedConfigModalButton: Locator;
  private configDiffReviewButton: Locator;
  readonly showIconFilterApplied: Locator;
  readonly showIconFilter: Locator;
  readonly showExcludedCommits: Locator;
  readonly excludedGitCommitCard: Locator;
  readonly refreshCiMaterialButton: Locator;
  readonly hideExcludedCommits: Locator;

  constructor(public page: Page) {
    super(page);
    this.triggerCiModuleButton = page.getByTestId('ci-trigger-start-build-button');
    this.workflowBuildSelectMaterialButton = page.getByTestId('workflow-build-select-material-button-0');
    this.ciTriggerStartBuildButton = page.getByTestId('ci-trigger-start-build-button');
    this.workflowNodeDetailsLink = page.locator('//*[contains(@data-testid,"ci-trigger-select-details-button-")]');
    this.buildAndDeployTab = page.locator('//*[@data-testid="trigger-job-link" or (@data-testid="build-deploy-click") or (@data-testid="trigger-job-link")]')
    this.ciModalCloseIcon = page.locator('//button[@class="dc__transparent" or @data-testid="edit-branch-name-close-button" or @data-testid="header-close-button"]');
    this.ciStatusOfWorkflowPage = page.getByTestId("ci-trigger-status-0");
    this.deployImageFromExternalCILink = page.getByTestId("deploy-image-external-service-link");
    this.ciLinkedStatus = page.getByTestId("cd-trigger-status")
    this.createBuildPipelineAdvancedOptionsButton = page.getByTestId("create-build-pipeline-advanced-options-button");
    this.ciBuildPipelineNameField = page.getByPlaceholder('e.g. my-first-pipeline');
    this.closeButtonBuildAndDeploy = page.getByTestId("close-build-deploy-button");
    this.goToBuildAndDeploy = page.getByTestId("build-deploy-click");
    this.cdTriggerDeploymentButton = this.page.getByTestId("cd-trigger-deploy-button");
    this.imageModals = this.page.locator('//*[contains(@class,"material-history material-history--cd image-tag-parent-card ")]');
    this.imageModalDeploymentTime = this.page.locator('//*[contains(@class,"material-history__info ")]');
    this.latestTag = this.page.locator('//*[text()="Latest"]');
    this.activeTag = this.page.locator('//*[contains(text(),"Active ")]');
    this.addTagButton = this.page.getByTestId('add-tags-button');
    this.pipelineTriggeredPopup = this.page.getByText("Pipeline Triggered", { exact: true });
    this.tagInputField = this.page.getByTestId('add-tag-text-area');
    this.saveTagsButton = this.page.getByTestId('save-tags-button');
    this.imageExcludedTag = this.page.locator('//*[text()="Excluded"]');
    this.eligibleImagesTabButton = this.page.locator('//*[contains(text(),"Eligible images")]');
    this.noImagesFoundSection = this.page.getByTestId("generic-empty-state");
    this.latestImagesTabButton = this.page.locator('//*[contains(text(),"Latest") and contains(text(),"images")]');
    this.showOrHideSourceInfo = this.page.getByTestId("collapse-hide-info");
    this.imageNotEligibleText = this.page.locator('//*[text()="Image was not eligible"]');
    this.configuredFilterLink = this.page.locator('//button[text()="configured filter"]');
    this.failConditionTabButton = this.page.locator('//*[text()="Fail conditions"]');
    this.passConditionTabButton = this.page.locator('//*[text()="Pass conditions"]')
    this.ConfigurePluginsToUnblockBuild = page.getByText('Configure plugins to unblock build', { exact: true });
    this.startBuildPluginPolicy = page.getByTestId('start-build-with-warning');
    this.configurePluginForWarningButton = page.getByTestId('configure-plugin-with-warning');
    this.workflowHeader = page.getByTestId("workflow header");
    this.workflowEditorTab = page.getByTestId("workflow-editor-link");
    this.detailsButton = page.locator(`//*[@class="workflow-node__details-link"]`)
    this.cmAndSecretButtonInCD = page.locator('//*[@class="fcb-5 icon-dim-20 cursor"]')//TODO :data test id
    this.selectImage = page.getByTestId("CD-trigger-select-image-0");
    this.configureGitopsRepo = this.dialogDeleteConfirmationButton;
    this.GitOpsRepositoryIsNotConfigured = page.getByText('GitOps repository is not configured');
    this.workflowEditorPage = page.getByTestId('workflow-editor-page');
    this.closeCdModal = page.getByTestId('close-cd-modal');
    this.parameterTabButton = this.page.locator('//*[text()="Parameters"]');
    this.addParameterButton = this.page.locator('//*[text()="Add parameter"]');
    this.paramKeyInputField = this.page.locator('//*[@placeholder="Enter key"]');
    this.paramValueField = this.page.locator('//*[@placeholder="Enter value or variable"]');
    this.ciCommitSearchInputField = this.page.getByTestId('ci-trigger-search-by-commit-hash');
    this.genericCiCommitCard = this.page.locator("//div[contains(@data-testid,'material-history')]");
    this.configurePluginWithWarning = this.page.getByTestId(`configure-plugin-with-warning`);
    this.triggerWithWarning = this.page.getByTestId(`trigger-with-warning`);
    this.commonLocatorForTriggerAndDeploy = this.page.locator(`//*[@data-testid="cd-trigger-deploy-button" or @data-testid="ci-trigger-start-build-button"]`);
    this.commonLocatorForSelectImageAndMaterial = this.page.locator(`//*[contains(@data-testid,"-select-") and (contains(@data-testid,"image") or contains(@data-testid,'material'))]`);
    this.prePostCdCloseIconButton = this.page.getByTestId('header-close-button');
    this.viewImagesForApproval = this.page.locator(`//*[text()="View images for approval"]`);
    this.requestApprovalButton = this.page.getByTestId(`request-approval`);
    this.notifyUsersInputField = this.page.locator(`//*[@placeholder="Search users to notify"]`);
    this.submitRequestButton = this.page.getByTestId('submit-request-approval');
    this.approvalPendingTab = this.page.locator(`//*[@data-testid="approval-requested-tab"]`);
    this.cancelRequestButton = this.page.getByTestId('cancel-approval');
    this.cancelRequestButtonInTippy = this.page.getByTestId('submit-cancel-request');
    this.eligibleApproversTippyOpener = this.page.getByTestId('show-eligible-approver-tippy');
    this.tooltipModal = this.page.locator(`//*[@class="tippy-content"]`);
    this.eligibleUsersRowInToolTipModal = this.page.locator(`//*[text()="Any below users"]/parent::div/following-sibling::div`);
    this.imageApproveButton = this.page.getByTestId('approve-approval');
    this.imageApproveModalButton = this.page.getByTestId('submit-approve-request');
    this.expireApprovalButton = this.page.locator(`//*[text()="Expire approval"]`);
    this.expireApprovalModalButton = this.page.locator(`//*[contains(@data-testid,'expire-approval-')]`);
    this.workflowDiv = this.page.locator(`//*[contains(@class,'workflow__body')]`)
    this.promoteImageButton = this.page.locator(`//span[text()="Promote"]`);
    this.changeImageSourceForImagePromotionDropdown = this.page.locator(`//*[contains(@class,'image-list-environment-selector__control')]`);
    this.promoteToEnvButton = this.page.getByTestId(`open-request-promotion-overlay`);
    this.promoteImageButtonInsideTargetEnvModal = this.page.getByTestId('request-promotion-cta');
    this.closeRequestPromotionModal = this.page.getByTestId(`close-request-promotion-modal`);
    this.imagePromotionApprovalPendingTab = this.page.getByTestId('image-promotion-tab-pending');
    this.approveForButton = this.page.getByTestId(`approve-artifact-button`);
    this.approvePromotionRequestButton = this.page.getByTestId(`approve-promotion-request`);
    this.imagePromotionApprovalCloseModal = this.page.getByTestId(`close-image-promotion-modal`);
    this.deploymentWindowConfirmationDeployButton = this.page.getByTestId('deployment-window-confirmation-dialog-action-button');
    this.deploymentWindowConfirmationModal = this.page.locator(`//*[contains(@class,'deployment-window__confirmation')]`);
    this.executeJobInEnvironment = this.page.locator('div[class*="job-pipeline-environment-dropdown__control"]')
    this.executeJobInEnvironmentDropdownMenu = this.page.locator('div[class*="job-pipeline-environment-dropdown__menu"]');
    this.backButtonArtifactListingModal = this.page.getByTestId('cd-trigger-back-button')
    this.closeConfigPopup = this.page.getByTestId('close-config-popup');
    this.lastSaveAndDeployedConfigModal = this.page.getByTestId('deployment-config');
    this.LastSaveAndDeployedConfigModalButton = this.page.locator(`//*[contains(text(),'Last saved config with')]`);
    this.configDiffReviewButton = this.page.locator(`(//*[text()='Config diff']/ancestor::*[@type="button"]//*[text()='REVIEW'])`)
    this.showIconFilterApplied = this.page.getByTestId("show-icon-filter-applied");
    this.showIconFilter = this.page.getByTestId('show-icon-filter');
    this.showExcludedCommits = this.page.getByTestId("action-menu-item-show-excluded-commits");
    this.hideExcludedCommits = this.page.getByTestId("action-menu-item-hide-excluded-commits");
    this.excludedGitCommitCard = this.page.getByTestId('excluded-git-commit');
    this.refreshCiMaterialButton = this.page.locator('//*[contains(@class,"material-refresh")]');

  }


  async waitUntilWorkflowEditorPageIsVisible() {
    await expect(this.workflowEditorPage).toBeVisible({ timeout: 30000 });
  }

  async VerifyBuildIsBlocked() {
    await this.buildAndDeployTab.click();
    await this.workflowBuildSelectMaterialButton.click();
    await expect(this.ConfigurePluginsToUnblockBuild).toBeVisible();
    await this.ConfigurePluginsToUnblockBuild.click();
  }

  async selectCIMaterialAndShowExcludedCommit() {
    await this.buildAndDeployTab.click();
    await this.workflowBuildSelectMaterialButton.click();
    await this.refreshCiMaterialButton.click();
    await this.showIconFilterApplied.click();
    await this.showExcludedCommits.click();

  }



  async triggerBuildWithWarning() {
    await this.buildAndDeployTab.click();
    await this.workflowBuildSelectMaterialButton.click();
    await this.ciTriggerStartBuildButton.click();
    await this.startBuildPluginPolicy.click();
    await this.workflowBuildSelectMaterialButton.click();
    await this.ciTriggerStartBuildButton.click();
    await this.configurePluginForWarningButton.click();
    await this.page.waitForLoadState('load');
  }

  /**
   * Triggers CI module.
   */
  async triggerCiModule(popupVerificationText: string = "Pipeline Triggered", workflowNumber: number = 0, params?: string[], runInEnv?: string) {
    try {
      await this.clickOnBuildAndDeployTab();
      // Execute environment selection if runInEnv parameter is provided
      if (runInEnv) {
        // 1. First, click the dropdown to open it
        await this.executeJobInEnvironment.click();

        // 2. Then, limit the text lookup *within* the dropdown menu container
        await this.executeJobInEnvironmentDropdownMenu.getByText(runInEnv, { exact: true }).click();
      }

      await this.page.locator(`//*[@data-testid="workflow-build-select-material-button-${workflowNumber}"]`).click();

      // Only execute this block if params is provided and is a non-empty array
      if (params && Array.isArray(params) && params.length > 0) {
        await this.addRuntimeParameter(params);
      }
      await BaseTest.checkToast(this.page, this.ciTriggerStartBuildButton, popupVerificationText);
    } catch (error) {
      console.error(`Error during triggerCiModule: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async checkTriggerWithMandatoryPlugins(isBlocked: boolean, checkCiTrigger: boolean, nodeNumber: number) {
    await this.buildAndDeployTab.click();
    await this.commonLocatorForSelectImageAndMaterial.nth(nodeNumber).click();
    if (isBlocked) {
      await expect(this.noImagesFoundSection).toBeVisible({ timeout: 7000 });
    }
    else {
      await this.commonLocatorForTriggerAndDeploy.click();
      await expect(this.configurePluginWithWarning).toBeVisible({ timeout: 6000 });
      if (checkCiTrigger) {
        await BaseTest.checkToast(this.page, this.triggerWithWarning, 'Success');
      }
    }
  }

  /**
   * Navigates to the Build History page by clicking on the workflow node details link.
   */
  async goToBuildHistoryPageViaClickOnDetailsLink(workflowNumber: number = 0, nodeNumber: number = 0) {
    try {
      await this.page.locator(`//*[contains(@class,'workflow__body')]`).nth(workflowNumber).locator(`//*[contains(@class,'node__details')]`).nth(nodeNumber).click();
    } catch (error) {
      console.error(`Error during goToBuildHistoryPageViaClickOnDetailsLink: ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  /**
   * Using Promise.ALL to verify the duplicacy warning .
   */
  async verifyDuplicateImageWarning(textToVerify: string, triggerFunction) {
    try {
      await this.buildAndDeployTab.click();
      await Promise.all([
        expect(this.page.locator(`//*[text()="${textToVerify}"]`)).toBeVisible({ timeout: 1 * 60 * 1000 }),
        triggerFunction()
      ]);
      await this.ciModalCloseIcon.click();




    }
    catch (error) {
      console.error(`Error during verifying the warning : ${error}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async clickOnCiDetailsButton(workflowNumber: number = 0) {
    await this.buildAndDeployTab.click();
    await this.page.getByTestId(`ci-trigger-status-${workflowNumber}`).click();
  }

  async CheckStatusforLinkedPipeline(status: string = Constants.TRIGGER_SUCCESS_STATUS) {
    await expect(this.ciStatusOfWorkflowPage).toContainText(status, { timeout: 5 * 1000 * 60 });
    await expect(this.ciLinkedStatus).toContainText(status, { timeout: 5 * 1000 * 60 });
  }

  /**
   * verifying images visibility on select image modal and deploying the image
   * @param cdNodeNumber - cd number on which we want to run this function
   * @param values - images that we want to verify on select image modal
   * @param workflowNumber - workflow number in which we want to run this function
   */
  async verifyImageAndTriggerDeployment(cdNodeNumber: number, values: string[] = [], numberOfOccurences: number[] = [], workflowNumber: number = 0, isAppDetails: boolean = false, rbacRelatedData: { isTriggerPageVisible: boolean, isEligibleForTrigger: boolean } = { isEligibleForTrigger: true, isTriggerPageVisible: true }, isExceptionUser: boolean = false) {
    if (values.length != 0) {
      await this.verifyImageOnSelectImageModal(cdNodeNumber, values, workflowNumber, numberOfOccurences, isAppDetails);
    }
    else {
      await this.clickOnSelectImageButtonOnBuildAndDeployPage(workflowNumber, cdNodeNumber);
    }
    // if(!rbacRelatedData?.isEligibleForTrigger){
    //    await expect(this.noImagesFoundSection).toBeVisible({ timeout: 3 * 1000 * 60 });
    // }
    // else{
    //   await this.triggerDeploymentAndVerifyToast('Deployment Initiated', isExceptionUser);
    // }
    if (rbacRelatedData?.isTriggerPageVisible) {
      let messageToVerify: string = rbacRelatedData.isEligibleForTrigger ? 'Deployment Initiated' : 'Error';
      await this.triggerDeploymentAndVerifyToast(messageToVerify, isExceptionUser);
    }
    else if (!rbacRelatedData?.isTriggerPageVisible) {
      await expect(this.noImagesFoundSection).toBeVisible({ timeout: 3 * 1000 * 60 });
    }
  }

  async clickOnSelectImageButtonOnBuildAndDeployPage(workflowNumber: number, cdNodeNumber: number) {
    await expect(async () => {
      await this.buildAndDeployTab.click();
      await this.page.locator(`//*[contains(@data-testid,"CD-trigger-select-image-${workflowNumber}")]`).nth(cdNodeNumber).click({ delay: 1000, force: true });
      await this.imageModals.first().waitFor({ state: 'visible' });
    }).toPass({ timeout: 1 * 1000 * 60 });
  }

  async verifyBlackoutWindowAndCheckTriggerEligibility(workflowNumber: number, cdNodeNumber: number, isEligible: boolean) {
    await this.buildAndDeployTab.click();
    await this.page.locator(`//*[contains(@data-testid,"CD-trigger-select-image-${workflowNumber}")]`).nth(cdNodeNumber).click();
    await this.cdTriggerDeploymentButton.click();
    if (isEligible) {
      await this.deleteModalInputField.fill('Deploy');
      await BaseTest.checkToast(this.page, this.deploymentWindowConfirmationDeployButton, 'Success');
    } else {
      await this.deploymentWindowConfirmationModal.waitFor();
      await expect(this.deleteModalInputField).toBeHidden();
      await this.page.reload();
      await this.ciModalCloseIcon.click();
    }


  }

  async triggerDeploymentAndVerifyToast(messageToVerify: string = "Deployment Initiated", isExceptionUser: boolean = false) {
    if (isExceptionUser === true) await expect(this.cdTriggerDeploymentButton).toHaveText('Deploy without approval');
    await BaseTest.checkToast(this.page, this.cdTriggerDeploymentButton, messageToVerify);
  }

  /**
   * verifying images visibility on select image modal
   * @param cdNodeNumber - cd number on which we want to run this function(pre-post-cd works for all )
   * @param values - images that we want to verify on select image modal []
   * @param numberOfOccurences - for each image how many occurences we want to test
   * @param workflowNumber - workflow number in which we want to run this function
   */
  async verifyImageOnSelectImageModal(cdNodeNumber: number, values: string[], workflowNumber: number, numberOfOccurences: number[], isAppDetails: boolean = false) {
    if (!isAppDetails) {
      await this.buildAndDeployTab.click();
      await this.page.locator(`//*[contains(@data-testid,"CD-trigger-select-image-${workflowNumber}")]`).nth(cdNodeNumber).click();
    }
    for (var i = 0; i < values.length; i++) {
      //await expect(this.page.locator(`//*[contains(text(),"${values[i]}")]`).first()).toBeVisible({timeout:2*1000*60});
      await expect(
  this.page.locator('//div[contains(@class,"visible-modal__body")]')
    .locator(`//*[contains(text(),"${values[i]}")]`)
).toHaveCount(numberOfOccurences[i], { timeout: 2 * 1000 * 60 });
    }
  }

  async verifySecurityScanSummaryCardOnSelectImageModal(images: string[]) {
    for (let i = 0; i < images.length; i++) {
      await expect(this.page.locator(`//*[text()="${images}"]/ancestor::*[contains(@class, "material-history")]//*[@data-testid="collapse-hide-info"]`)).toBeVisible();
      await this.page.locator(`//*[text()="${images}"]/ancestor::*[contains(@class, "material-history")]//*[@data-testid="collapse-hide-info"]`).click();
      await expect(this.page.locator(`//*[contains(@class,'tab-list tab-list--vulnerability')]//*[contains(text(),"Security")]`)).toBeVisible();
      await this.page.locator(`//*[contains(@class,'tab-list tab-list--vulnerability')]//*[contains(text(),"Security")]`).click()
      await this.verifySecurityScanSummaryCard();
    }

  }
  async closeSelectImageModal() {
    await this.ciModalCloseIcon.first().click();

  }
  async verifySecurityScanSummaryCard() {
    await expect(this.page.getByText('Image Scan')).toBeVisible({ timeout: 1 * 1000 * 60 });
    for (let key in credentials.vulnerabilitiesLabel) {
      await expect(this.page.getByTestId(`segmented-bar-chart-${key}-label`)).toBeVisible();
    }
  }
  /**
   * verifying cd status on workflow page
   * @param verificationText - status that we have to verify
   * @param cdNodeNumber - 
   * @param workflowNumber - workflow is 0 Based Indexing.
   */
  async verifyImageScanForCd(cdNodeNumber: number, values: string[] = [], numberOfOccurences: number[] = [], workflowNumber: number = 0, isAppDetails: boolean = false) {
    await this.verifyImageOnSelectImageModal(cdNodeNumber, values, workflowNumber, numberOfOccurences, isAppDetails);
    await this.verifySecurityScanSummaryCardOnSelectImageModal(values);
  }



  /**
   * verifying tags on images
   * @param textToverify - pass the text as paramter that you want to verify in an image modal
   * @param isVisible - that particular text should be visible or not
   * @param ImageNumber - Image Number on which we have to verify tags , by default we have set to 0
   * @param cdNodeNumber - cd number in which we have to run this function
   * @param workflowNumber - workflow number in which we want to run this function
   */
  async verifyTagsOnImage(textToVerify: string, isVisible: boolean, cdNodeNumber: number, imageNumber: number = 0, workflowNumber: number = 0) {
    await this.page.getByTestId(`CD-trigger-select-image-${workflowNumber}`).nth(cdNodeNumber).click();
    switch (textToVerify) {
      case 'deploymentTime': {
        var deploymentTime = await this.imageModalDeploymentTime.textContent();
        if (isVisible) {
          expect(deploymentTime != null).toBe(true);
        }
        else {
          expect(deploymentTime != null).toBe(false);
        }
        await this.ciModalCloseIcon.click();
        break;
      }
      case 'Excluded': {
        if (isVisible) {
          await this.latestImagesTabButton.click();
          await expect(this.imageModals.nth(imageNumber).locator(this.imageExcludedTag)).toBeVisible({ timeout: 1 * 1000 * 60 });
          await this.ciModalCloseIcon.click();
        }
        else {
          await expect(this.imageModals.nth(imageNumber).locator(this.imageExcludedTag)).toBeHidden({ timeout: 1 * 1000 * 60 });
          await this.ciModalCloseIcon.click();
        }
        break;
      }
      default: {
        if (isVisible) {
          await expect(this.imageModals.nth(imageNumber).locator(`//*[text()="${textToVerify}"]`)).toBeVisible({ timeout: 1 * 1000 * 60 });
        }
        else {
          await expect(this.imageModals.nth(imageNumber).locator(`//*[text()="${textToVerify}"]`)).toBeHidden({ timeout: 1 * 1000 * 60 });
        }
        await this.ciModalCloseIcon.click();
      }

    }
  }

  /**
   * adding tags on images
   * @param tagName - tag name that you want to set
   * @param ImageNumber - Image Number on which we have to verify tags , by default we have set to 0
   * @param cdNodeNumber - cd number in which we have to run this function
   * @param workflowNumber - workflow number in which we want to run this function
   */
  async addReleaseTagsOnImage(cdNodeNumber: number, tagName: string, imageNumber: number = 0, workflowNumber: number = 0, alreadyOnImage: boolean = false) {
    if (!alreadyOnImage) {
      await this.clickOnBuildAndDeployTab();
      await this.clickOnSelectImageButton(cdNodeNumber, workflowNumber);
    }
    await expect(async () => {
      await this.imageModals.nth(imageNumber).locator(this.addTagButton).click();
      await this.tagInputField.click();
      await this.page.keyboard.insertText(tagName);
      await this.saveTagsButton.click();
      alreadyOnImage == false ? await expect(this.imageModals.nth(imageNumber).locator(`//*[text()="${tagName}"]`)).toBeVisible({ timeout: 30000 }) : null
      alreadyOnImage == false ? await this.ciModalCloseIcon.click() : null;
    }).toPass({ timeout: 2 * 1000 * 60 });
  }

  async clickOnSelectImageButton(cdNodeNumber: number, workflowNumber: number = 0) {
    await this.page.getByTestId(`CD-trigger-select-image-${workflowNumber}`).nth(cdNodeNumber).click();
  }

  /**
  * verifying number of eligble images under eligble images section
  * @param count - number of ci image modals
  * @param cdNodeNumber - cd number in which we have to run this function
  * @param workflowNumber - workflow number in which we want to run this function
  */
  async verifyNumberOfEligibleImages(count: number, cdNodeNumber: number, isCdFilter: boolean = false, workflowNumber: number = 0) {
    await this.clickOnSelectImageButton(cdNodeNumber, workflowNumber);
    await this.page.waitForLoadState('load');
    if (isCdFilter) {
      await this.eligibleImagesTabButton.click();
      //await this.eligibleImagesTabButton.dblclick({delay:2000});
      if (count <= 0) {
        await expect(this.noImagesFoundSection).toBeVisible({ timeout: 11000 });
      }
      else {
        await expect(this.imageModals).toHaveCount(count);
      }
    }
    else {
      await expect(this.imageModals).toHaveCount(count);
      await this.ciModalCloseIcon.click();
    }
  }

  async VerifyGitopsRepoNotConfigureModal() {
    await this.buildAndDeployTab.click();
    await this.selectImage.nth(1).click();
    await this.page.waitForTimeout(3000);
    if (await this.page.getByText('No Image Available').isVisible()) {
      await this.page.locator(`//*[@class="dc__transparent"]`).click();
      await this.selectImage.nth(0).click();
    }
    await expect(this.page.getByText('GitOps repository is not configured')).toBeVisible({ timeout: 1 * 1000 * 60 });
    await this.configureGitopsRepo.click();
    await this.page.waitForLoadState('load');
  }

  /**
  * verifying the auto trigger in case of fail condition and also the reason modal regarding auto blocked
  * @param isPassCondition - based on this we will click on pass and fail tab
  * @param conditionToVerify - text that we want to verify , whatever we  have set in filter condition
  * @param cdNodeNumber - cd number in which we have to run this function
  * @param workflowNumber - workflow number in which we want to run this function
  */
  async verifyBlockedAutoTriggerAndAppliedFilter(isPassCondition: boolean | number, conditionToVerify: string, cdNodeNumber: number, workflowNumber: number = 0, imageNUmber: number = 0) {
    await this.page.getByTestId(`CD-trigger-select-image-${workflowNumber}`).nth(cdNodeNumber).click();
    await this.latestImagesTabButton.click();
    await this.showOrHideSourceInfo.nth(imageNUmber).click();
    await expect(this.imageNotEligibleText).toBeVisible({ timeout: 11000 });
    await this.configuredFilterLink.click();
    var booleanValue = isPassCondition!!;
    if (booleanValue) {
      await this.passConditionTabButton.click();
    }
    else {
      await this.failConditionTabButton.click();
    }
    await expect(this.page.locator(`//*[text()="${conditionToVerify}"]`)).toBeVisible({ timeout: 15000 });
  }
  async verifyDuplicateImageWarning1(trigger) {

    await this.buildAndDeployTab.click();
    await Promise.all([
      expect(this.page.locator('//*[text()="Desired image tag already exists"]')).toBeVisible({ timeout: 1 * 60 * 1000 }),
      trigger()
    ]);
    await this.ciModalCloseIcon.click();

  }
  async getCountOfWorkFlow() {
    return await this.workflowHeader.count();
  }

  async clickOnDetailsOfAnyNode(NodeNumber: number, workflowNumber: number = 0) {
    await expect(async () => {
      await this.workflowDiv.nth(workflowNumber).locator(this.detailsButton.nth(NodeNumber)).click();
      await expect(this.workflowDiv.first()).toBeHidden();
    }).toPass({ timeout: 1 * 1000 * 60 });
  }
  async addCmAndSecretInPreAndPostDeployment(resourceName: string[]) {
    await this.cmAndSecretButtonInCD.click();
    for (let key of resourceName) {
      await this.page.getByText(key).click();
    }
  }

  async clickOnBuildAndDeployTab() {
    await expect(async () => {
      await expect(this.buildAndDeployTab).toBeVisible();
      await this.buildAndDeployTab.click({ delay: 3000 });
      await expect(this.commonLocatorForSelectImageAndMaterial.first()).toBeVisible({ timeout: 10000 });
    }).toPass({ timeout: 1 * 1000 * 60 });
  }

  async closeCdModalPoint() {
    await this.closeCdModal.click();
  }

  async goToBuildAndDeployTab() {
    await this.buildAndDeployTab.click();
  }

  async addRuntimeParameter(keyAndValues: string[]) {
    await this.parameterTabButton.click();
    for (let i = 0; i < keyAndValues.length; i++) {
      if (i != 0) {
        await this.page.locator('//*[text()="Add"]').click();
      }
      const object = await BaseTest.splitKeyValuePair(keyAndValues[i]);
      await this.paramKeyInputField.nth(0).fill(object.key);
      await this.paramValueField.nth(0).fill(object.value);
    }
  }


  /**
   * use this method to verify the filter functionality 
   * @param commitToVerify -> whether correct commit hash is coming or not 
   * @param numberOfCardsTobeVisible -> after applying filter how many cards should be visible 
   */

  async checkCommitFilters(commitToVerify: string, numberOfCardsTobeVisible: number = 1) {
    try {
      await expect(async () => {
        await this.ciCommitSearchInputField.waitFor({ timeout: 12000 });
        await this.ciCommitSearchInputField.fill(commitToVerify, { timeout: 300 });
        await this.page.keyboard.press('Enter');
        await this.verifyDetailsOfCiCommitCard(commitToVerify);
        await expect(this.genericCiCommitCard).toHaveCount(numberOfCardsTobeVisible, { timeout: 15000 });
      }).toPass({ timeout: 2 * 1000 * 60 });
    } catch (error) {
      console.error(`❌ checkCommitFilters failed for commit: ${commitToVerify}`, error);
      throw error;
    }
  }

  /**
   * this method is used to verify the text on commit indfo card of ci 
   * @param textToVerify 
   * @param cardNumber 
   */

  async verifyDetailsOfCiCommitCard(textToVerify: string, cardNumber: number = 0) {
    await expect(this.page.locator(`//div[@data-testid="material-history-${cardNumber}"]`).locator(`//*[contains(text(),"${textToVerify}")]`)).toBeVisible({ timeout: 12000 });
  }

  /**
   * this method is a generic entry point to click on select material or image button 
   * Remmeber-> both ci and cd are separate entities are here -> if there are ci and cd in a workflow , then for both ci and cd 
   * we will pass 0 as cd node number 
   * @param selectMaterial -> set this as true if u want to click on select material
   * @param workflowNumber -> as there can be multiple worklfows 
   * @param cicdNodeNumber -> numer of node on which u want to click 
   */

  async clickOnSelectMaterialOrSelectImage(selectMaterial: boolean, workflowNumber: number = 0, cicdNodeNumber: number = 0) {
    await this.buildAndDeployTab.click();
    let buttonLocator: Locator = selectMaterial ? this.page.locator(`//*[@data-testid="workflow-build-select-material-button-${workflowNumber}"]`).nth(cicdNodeNumber) : this.page.locator(`//*[@data-testid="CD-trigger-select-image-${workflowNumber}"]`).nth(cicdNodeNumber);
    await buttonLocator.click();
  }

  async raiseOrApproveDeclineRequest(data: { requestData?: { imageNumber: number, notifyUsers?: string[], cancelRequest: boolean }, approvalData?: { isApprove: boolean } }) {
    await this.clickOnSelectMaterialOrSelectImage(false);
    await this.viewImagesForApproval.click();
    if (data.requestData) {
      await this.sendApprovalRequestOrCancelRequest({ cancelRequest: data.requestData.cancelRequest, imageNumber: data.requestData.imageNumber, notifyUsers: data.requestData.notifyUsers })
    }
    else if (data.approvalData) {
      await this.approvalPendingTab.click({ delay: 1000 });

    }

  }
  async sendApprovalRequestOrCancelRequest(data: { cancelRequest: boolean, imageNumber: number, notifyUsers?: string[] | undefined }) {
    await this.clickOnSelectMaterialOrSelectImage(false);
    await this.viewImagesForApproval.click();
    let pageUrl: string = this.page.url();
    if (!data.cancelRequest) {
      await this.imageModals.nth(data.imageNumber).locator(this.requestApprovalButton).click();
      if (data.notifyUsers != undefined) {
        for (let key of data.notifyUsers) {
          await this.notifyUsersInputField.fill(key);
          await this.page.keyboard.press('Enter');
          await this.page.locator(`//*[@class="form__checkbox-container"]`).first().click();
        }
      }
      await this.submitRequestButton.click();
    }
    else {
      await this.approvalPendingTab.click({ delay: 1000 });
      await this.imageModals.nth(data.imageNumber).locator(this.cancelRequestButton).click();
      await this.cancelRequestButtonInTippy.click();
    }
    return pageUrl;
  }
  async verifyEligibleApproversListAndSucessfullApprovals(eligibleApprovals?: string[], approvalCountVerification?: { totalApprovalsRequired: number, approvalsDone: number }) {
    await this.approvalPendingTab.click({ delay: 1000 });
    if (eligibleApprovals) {
      await this.eligibleApproversTippyOpener.click();
      for (let key of eligibleApprovals) {
        await expect(this.tooltipModal.locator(this.page.locator(`//*[text()="${key}"]`))).toBeVisible({ timeout: 2000 });
      }
    }
    if (approvalCountVerification) {
      await expect(this.imageModals.nth(0).locator(`//*[text()="${approvalCountVerification.approvalsDone}/${approvalCountVerification.totalApprovalsRequired} Approvals"]`)).toBeVisible({ timeout: 2000 });
    }
  }
  async approveRequest() {
    await this.approvalPendingTab.click({ delay: 1000 });
    await this.imageApproveButton.click();
    await this.imageApproveModalButton.click();
  }
  async verifyCountOfApprovedImagesAndPerformAction(imageCount: number, triggerOrExpire?: "trigger" | "expire", imageCountAfterTriggerOrExpire?: number) {
    await this.clickOnSelectMaterialOrSelectImage(false);
    await expect(this.page.locator(`//*[text()="Approved images"]/parent::div/following-sibling::div[contains(@class,'image-tag-parent-card')]`)).toHaveCount(imageCount, { timeout: 5000 });
    if (triggerOrExpire) {
      if (triggerOrExpire == "expire") {
        await this.expireApprovalButton.click();
        await this.expireApprovalModalButton.click();
      }
      else {
        await this.cdTriggerDeploymentButton.click();
      }
    }
    if (imageCountAfterTriggerOrExpire != undefined && triggerOrExpire) {
      if (triggerOrExpire == "trigger") {
        await this.clickOnSelectMaterialOrSelectImage(false);
      }
      await expect(this.page.locator(`//*[text()="Approved images"]/parent::div/following-sibling::div[contains(@class,'image-tag-parent-card')]`)).toHaveCount(imageCountAfterTriggerOrExpire, { timeout: 5000 });
    }

  }
  async checkWhetherImageApprovalIsAppliedOrNot(isApplied: boolean, envName: string) {
    await this.clickOnBuildAndDeployTab();
    await this.selectImage.first().waitFor({ timeout: 10000 });
    expect(await this.page.locator(`//*[@data-testid="approval-node-button"]/ancestor::div[contains(@class,'workflow__body')]//*[text()="${envName}"]`).isVisible()).toBe(isApplied);
  }
  async verifyCiCdStatus(workflowNumber: number, nodeNumber: number, status: string, isDevtronApp: boolean = true, timeoutValue: number = 9 * 1000 * 60) {
    isDevtronApp ? await this.clickOnBuildAndDeployTab() : null;
    await expect(this.page.locator(`//*[contains(@class,'workflow__body')]`).nth(workflowNumber).locator(`//*[contains(@class,'-trigger-status') and not(contains(@class,'__icon'))]`).nth(nodeNumber)).toContainText(status, { timeout: timeoutValue });
  }
  async promoteAnImage(imageSource: string, envConfiguration: { envName: string, isEligible: boolean, messageAfterRequestRaised?: string }[]) {
    await this.clickOnBuildAndDeployTab();
    await this.promoteImageButton.click();
    await this.selectImageSourceForImagePromotion(imageSource);
    await this.promoteToEnvButton.click();
    for (let key of envConfiguration) {
      if (key.isEligible) {
        await this.page.locator(`//h4[text()='${key.envName}']`).click();
      } else {
        await expect(this.page.locator(`//h4[text()='${key.envName}']/ancestor::label//input`)).toBeDisabled();
      }
    }
    await this.promoteImageButtonInsideTargetEnvModal.click();
    for (let key of envConfiguration) {
      if (key.isEligible && key.messageAfterRequestRaised) {
        await expect(this.page.locator(`//*[contains(@class,'box-shadow')]//span[text()='${key.envName}']/following-sibling::div`)).toContainText(key.messageAfterRequestRaised);
      }
    }
    await this.closeRequestPromotionModal.click();
    await this.imagePromotionApprovalCloseModal.click();
  }



  async selectImageSourceForImagePromotion(imageSource: string) {
    await this.changeImageSourceForImagePromotionDropdown.click();
    await this.page.locator(`//*[@role='option']`).locator(`//*[text()="${imageSource}"]`).click();
  }
  async checkEligibilityAndApprovePromotedImages(data: { envName: string, isEligible: boolean }[]) {
    await this.promoteImageButton.click();
    await this.imagePromotionApprovalPendingTab.click();
    await this.approveForButton.click();
    let isApproveButtonEnabled: boolean = false;
    for (let i = 0; i < data.length; i++) {
      if (data[i].isEligible) {
        isApproveButtonEnabled = true;
        data.length > 1 ? await this.page.locator(`//h4[text()="${data[i].envName}"]`).click() : '';
      }
      else {
        expect(await this.page.locator(`//h4[text()="${data[i].envName}"]/ancestor::label//input`).isEnabled()).toBe(false);
      }
    }
    isApproveButtonEnabled ? await this.approvePromotionRequestButton.click() : null;
    await this.imagePromotionApprovalCloseModal.click();
  }

  async checkWhetherBlackoutIsAppliedOrNot(cdNodeName: string, isApplied: boolean) {
    await this.buildAndDeployTab.click();
    if (isApplied) {
      await expect(this.page.locator(`//*[contains(@class,'workflow__body')]//*[text()="${cdNodeName}"]/ancestor::div[@class="workflow-node"]//*[text()="DO NOT DEPLOY"]`)).toBeVisible();
    }
    else {
      await expect(this.page.locator(`//*[contains(@class,'workflow__body')]//*[text()="${cdNodeName}"]/ancestor::div[@class="workflow-node"]//*[text()="DO NOT DEPLOY"]`)).toBeHidden();
    }
  }

  async verifyDeploymentisBlockedForExceptionUser(workflowNumber: number = 0, cdNodeNumber: number = 0, url?: string) {
    if (url !== undefined) await this.page.goto(url!);
    await this.buildAndDeployTab.click();
    await this.page.locator(`//*[contains(@data-testid,"CD-trigger-select-image-${workflowNumber}")]`).nth(cdNodeNumber).click();
    await this.cdTriggerDeploymentButton.waitFor({ timeout: 10000 });
    if (await this.page.locator(`//*[text()="Security Issues Found"]`).isVisible() === false) {
      await expect(this.cdTriggerDeploymentButton).toBeDisabled();
    }
  }


  async chooseStrategyAtArtifactListingModall(strategy: string) {
    await this.page.locator(`//*[contains(@class,'select-deployment-strategy__indicators')]`).nth(0).click({ force: true });
    await this.page.keyboard.insertText(strategy);
    await this.page.keyboard.press('Enter');
    await this.strategyDiffInPipelineConfiguration(strategy);
  }

  async updateStrategyUsingLastSavedAndDeployedConfigModal(strategy: string) {
    await this.LastSaveAndDeployedConfigModalButton.waitFor({ state: 'visible' });
    await this.LastSaveAndDeployedConfigModalButton.click({ force: true });
    await this.lastSaveAndDeployedConfigModal.waitFor({ state: 'visible' })
    await this.page.locator(`//*[@data-testid="deployment-config"]//*[@value="${strategy}"]`).click({ force: true });
    await this.strategyDiffInPipelineConfiguration(strategy);
  }

  async strategyDiffInPipelineConfiguration(strategy: string) {
    try {
      await expect(async () => {
        await this.configDiffReviewButton.waitFor({ state: 'visible' });
        await this.configDiffReviewButton.click({ force: true });
        await this.configDiffReviewButton.waitFor({ state: 'hidden' });
      }).toPass({ timeout: 2 * 1000 * 60 })
    } catch (error) {
      console.log(`Review Button is not clickable ${error}`)
      throw error;
    }
    await this.page.locator(`//*[@id="pipeline-strategy"]//*[contains(@class,(code-editor-green-diff))]//*[text()='${strategy}']`).waitFor({ state: 'visible' });
    await this.backButtonArtifactListingModal.click({ force: true });

  }

}


