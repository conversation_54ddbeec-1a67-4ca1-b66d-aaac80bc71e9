import { Locator, Page, expect } from "@playwright/test";
import { LockConfiguration } from "../../GlobalConfigurations/LockConfiguration";
import { FilterByScopeEnum } from "../../../enums/FilterByScopeEnum";
import { ApplyToOptionEnum } from "../../../enums/ApplyToOptionEnum";
import { BaseTest } from "../../../utilities/BaseTest";

export class ApprovalPolicyPage {
  readonly anyApproverRadioBox: Locator;
  readonly specificApproverRadioBox: Locator;
  readonly addCriteriaButton: Locator;
  readonly userGroupDropdown: Locator;
  readonly approverCountWrapper: Locator;
  readonly removeUserGroupIcon: Locator;
  readonly specificUserDropdownSelect: Locator;
  readonly approvalPolicySaveButton: Locator;
  readonly approvalStrategyDropdown: Locator;
  readonly lockConfigPage: LockConfiguration;
  readonly profilesTab: Locator;
  readonly appliedProfilesTab: Locator;
  readonly exceptionsTab: Locator;
  readonly createProfileButton: Locator;
  readonly applyProfileButton: Locator;
  readonly policyNameInputBox: Locator;
  readonly policyDescriptionInputBox: Locator;
  readonly warningMessageForSpecificApprover: Locator;
  readonly profileDropdownIndicator: Locator;
  readonly saveProfileButton: Locator;
  readonly filterByOptionsDiv: Locator;

  constructor(private page: Page) {
    this.anyApproverRadioBox = page.locator(`//*[text()="Any Approver"]`);
    this.addCriteriaButton = page.locator(`//*[text()="Add Criteria"]`);
    this.userGroupDropdown = page.locator(`//*[contains(@class,'user-group-selector-0__control')]`);
    this.approverCountWrapper = page.locator(`//*[contains(@class,'manual-approvals-switch')]`);
    this.removeUserGroupIcon = page.locator(`//*[contains(@aria-label,"Delete user group:")]`);
    this.specificUserDropdownSelect = page.locator(`//*[contains(@class,"specific-user-and-token-selector__control")]`);
    this.approvalPolicySaveButton = page.getByTestId(`approval-policy__save`);
    this.specificApproverRadioBox = page.locator(`//*[text()="Specific Approver"]`);
    this.approvalStrategyDropdown = page.locator(`//*[contains(@class,'applicable-to-type-selector__control')]`);
    this.profilesTab = page.locator("(//span[contains(@class,'dc__truncate')][normalize-space()='Profiles'])[1]");
    this.appliedProfilesTab = page.locator("(//span[contains(@class,'dc__truncate')][normalize-space()='Applied Profiles'])[1]");
    this.exceptionsTab = page.locator("(//span[contains(@class,'dc__truncate')][normalize-space()='Exceptions'])[1]");
    this.createProfileButton = page.getByTestId("create-profile");
    this.applyProfileButton = page.getByTestId("apply-profile");
    this.policyNameInputBox = page.getByTestId("approval-policy-name");
    this.policyDescriptionInputBox = page.getByTestId("approval-policy-description");
    this.warningMessageForSpecificApprover = page.locator("//span[@class='dc__ellipsis-right__2nd-line']");
    this.profileDropdownIndicator = page.locator('//*[contains(@class,"profile-selection__dropdown-indicator")]');
    this.filterByOptionsDiv = page.locator('//*[text()="Filter by" or text()="Add match criteria"]/parent::aside');
    this.saveProfileButton = page.locator('//*[text()="Save Changes"]');
    this.lockConfigPage = new LockConfiguration(page);
  }

  async navigateToApprovalPolicyPage() {
    await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/approval-policy/policy`);
  }

  async verifyApprovalPolicyPage() {
    await expect(this.profilesTab).toBeVisible();
    await expect(this.appliedProfilesTab).toBeVisible();
    await expect(this.exceptionsTab).toBeVisible();
    await expect(this.createProfileButton).toBeVisible();
    await expect(this.applyProfileButton).toBeVisible();
  }

  async createPolicyForAnyApprover(numberOfApproversRequired: number, policyName: string) {
    await this.createProfileButton.click();
    await this.policyNameInputBox.fill(policyName);
    await this.policyDescriptionInputBox.fill("This policy is required for the automation testing purpose only");
    await this.anyApproverRadioBox.click();
    await this.approverCountWrapper.locator(`//*[text()="${numberOfApproversRequired}"]`).click();
    await this.approvalPolicySaveButton.click();
  }

  async createPolicyForSpecificApprovers(
    policyName: string,
    userNamesList: string[],
    userGroupsListWithApproverCountPerGroup?: { name: string; count: number }[]
  ) {
    await this.createProfileButton.click();
    await this.policyNameInputBox.fill(policyName);
    await this.policyDescriptionInputBox.fill("This policy is required for the automation testing purpose only");
    await this.specificApproverRadioBox.click();

    await expect(this.warningMessageForSpecificApprover).toContainText(
      "Please specify user group criteria or specific users"
    );

    if (userGroupsListWithApproverCountPerGroup?.length) {
      for (const group of userGroupsListWithApproverCountPerGroup) {
        await this.addCriteriaButton.click();
        await this.userGroupDropdown.click();
        await this.page.keyboard.type(group.name);
        await this.page.keyboard.press("Enter");

        const countLocator = this.approverCountWrapper.locator(`//*[text()="${group.count}"]`);
        await countLocator.click();
      }
    }

    if (userNamesList.length === 0) {
      throw new Error("userNamesList must contain at least one user");
    }

    await this.specificUserDropdownSelect.click();
    for (const userName of userNamesList) {
      await this.page.keyboard.type(userName);
      await this.page.keyboard.press("Enter");
    }

    await this.approvalPolicySaveButton.click();
  }

  async useSelectedPolicyForDeployments(profilesListToApply: string[]) {
    await this.applyProfileButton.click();
    await this.selectProfilesToApply(profilesListToApply);
    await this.approvalStrategyDropdown.click();
    await this.page.locator('//*[text()="Deployments"]').click();
  }

  async useSelectedPolicyForConfigurations(
    profilesListToApply: string[],
    isCMRequired: boolean,
    isCSRequired: boolean,
    isDTRequired: boolean
  ) {
    await this.applyProfileButton.click();
    await this.selectProfilesToApply(profilesListToApply);
    await this.approvalStrategyDropdown.click();
    await this.page.locator('//*[text()="Configuration change"]').click();

    await this.toggleConfigurationCheckbox('configMaps', isCMRequired);
    await this.toggleConfigurationCheckbox('secrets', isCSRequired);
    await this.toggleConfigurationCheckbox('dt', isDTRequired);
  }

  private async toggleConfigurationCheckbox(fieldId: string, shouldBeChecked: boolean) {
    const checkbox = this.page.locator(`//*[@id="applicable-to-configuration/${fieldId}"]`).first();
    const isChecked = await checkbox.isChecked();

    if (isChecked !== shouldBeChecked) {
      const toggleSpan = this.page.locator(
        `//*[@id="applicable-to-configuration/${fieldId}"]/following-sibling::span`
      ).first();
      await toggleSpan.click();
    }
  }

  private async selectProfilesToApply(profilesListToApply: string[]) {
    await this.page.locator(`//*[contains(@class,'profile-selection__control')]`).click();
    for (const profileName of profilesListToApply) {
      await this.page.keyboard.type(profileName);
      await this.page.keyboard.press("Enter");
      await this.profileDropdownIndicator.click({ delay: 1000 });
    }
  }

  async applyApprovalPolicyWithScope(
    applyToOption: ApplyToOptionEnum,
    scopeSelections: { type: FilterByScopeEnum; ValuesAfterScopeSelection: string[] }[]
  ) {
    await this.page.locator(`//*[text()="${applyToOption}"]`).click();

    if (applyToOption === ApplyToOptionEnum.Global) {
      await this.saveProfileButton.click();
      await BaseTest.checkToast(this.page, this.saveProfileButton, "Success");
    } else {
      for (const scope of scopeSelections) {
        await this.filterByOptionsDiv.locator(`//*[text()="${scope.type}"]`).click();
        await this.page.locator(`text="${scope.type}"`).click();

        for (const value of scope.ValuesAfterScopeSelection) {
          await this.page.keyboard.type(value, { delay: 300 });
          await this.page.keyboard.press("Enter");
          await this.clearWrittenContent();
          await this.page.locator(`text="${value}"`).first().click();
        }
      }

      await this.page.locator('text="Save Changes"').click();
      await BaseTest.checkToast(this.page, this.saveProfileButton, "Success");
    }
  }

  private async clearWrittenContent() {
    const isMac = process.env.OS === "Mac" || process.platform === "darwin";

    await this.page.keyboard.down(isMac ? "Meta" : "Control");
    await this.page.keyboard.press("KeyA");
    await this.page.keyboard.up(isMac ? "Meta" : "Control");

    await this.page.keyboard.press("Delete");
  }
}
