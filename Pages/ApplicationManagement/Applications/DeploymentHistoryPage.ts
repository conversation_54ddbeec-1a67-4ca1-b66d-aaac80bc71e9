
import { Locator, <PERSON> } from "playwright-core";
import { expect } from "playwright/test";

export class DeploymentHistoryPage {
    readonly deploymentHistoryTabButton: Locator;
    readonly environmentDropdown: Locator;
    readonly deploymentStatusText: Locator;
    readonly sourceTab: Locator;
    readonly deploymentHistoryList: Locator;
    readonly sourceInfoDiv: Locator;
    readonly environmentList: Locator;
    readonly deploymentListPopUpCard: Locator;
    readonly successTickIcon: Locator;
    readonly stepsTabButton: Locator;
    readonly configurationTabButton: Locator;

    constructor(private page: Page) {
        this.deploymentHistoryTabButton = this.page.getByTestId("deployment-history-link");
        this.environmentDropdown = this.page.locator('//*[contains(@class,"history-pipeline-dropdown")]');
        this.deploymentStatusText = this.page.getByTestId('deployment-status-text');
        this.sourceTab = this.page.getByTestId("deployment-history-source-code-link");
        this.sourceInfoDiv = page.getByTestId("source-code-git-hash");
        this.environmentList = this.page.locator('//*[contains(@class,"history-pipeline-dropdown__menu ")]');
        this.deploymentListPopUpCard = this.page.locator(`//*[contains(@class,'shadow__overlay')]`);
        this.successTickIcon = this.page.getByTestId('success-green-tick');
        this.stepsTabButton = this.page.getByTestId("deployment-history-steps-link");
        this.configurationTabButton = this.page.getByTestId('deployment-history-configuration-link');


    }

    /**
    *  verify that deployment status
    * @param {string} verificationText - value to be checked -> by default we have set succedded
    */
    async verifyDeploymentStatus(verificationText: string = "Succeeded") {
        await expect(this.deploymentStatusText).toContainText(verificationText, { timeout: 7 * 60 * 1000 });
    }

    /**
    *  selecting environment from dropdown
    * @param {string} envName - value to be checked -> by default we have set succedded
    * 
    */

    
    async selectEnvironment(envName: string, throghAppDetails: boolean = false) {

        if (!throghAppDetails) {
            await this.deploymentHistoryTabButton.click();
        }
        await this.page.waitForLoadState('load');
        await this.environmentDropdown.first().click({ delay: 2000 });
        await this.environmentList.locator(`//*[text()="${envName}"]`).click();
        if (!throghAppDetails) {
            await this.sourceTab.waitFor({ timeout: 2 * 1000 * 60 });
            await this.deploymentHistoryTabButton.hover();
        }
    }

    /**
    *  verifying artifcats on the page
    * @param {string} Image - value to be checked ->
    */
    async verifyArtifactsOnDeploymentHistory(Image: string, deploymentNumber: string = "0") {
        await this.page.getByTestId(`deployment-history-${deploymentNumber}`).click();
        await this.sourceTab.click();
        await expect(this.page.locator(`//*[text()="${Image}"]`)).toBeVisible({ timeout: 3 * 1000 * 60 });
        //we are splitting the image to verify image tag only
        await expect(this.page.locator('//*[@class="ci-details__body"]').locator(`//*[text()="${Image.split(':')[1]}"]`)).toHaveCount(2, { timeout: 3 * 1000 * 60 });
    }

    /**
    *  verify source info card
    * @param {string} repoName - value to be checked ->
    * @param {string} branchName - value to be checked ->
    */
    async verifySourceInfo(repoName: string, branchName: string) {
        await this.sourceTab.click();
        var textToBeVerified = repoName.split('/').pop()?.split('.')[0];
        console.log("string is" + textToBeVerified);
        await expect(this.sourceInfoDiv.locator(`//*[text()="${textToBeVerified}"]`)).toBeVisible({ timeout: 3 * 1000 * 60 });
        await expect(this.sourceInfoDiv.locator(`//*[text()="${branchName}"]`)).toBeVisible({ timeout: 1 * 1000 * 60 });
    }

    /**
    *  verify source info card
    * @param {string} deploymentNumber - value to be checked -> by default we have set it to 0 , user can pass customize value
    * @param {string} branchName - value to be checked -> by default we are passing main , user can again pass customized value
    */
    async verifyDeploymentPopupCard(deploymentNumber: string = "0", branchName: string = "main") {
        await this.page.getByTestId(`deployment-history-${deploymentNumber}`).hover();
        await expect(this.deploymentListPopUpCard.locator(`//*[text()="${branchName}"]`)).
            toBeVisible({ timeout: 4 * 1000 * 60 });
    }

    /**
    *  verify source info card
    * @param {string} deploymentNumber - value to be checked -> by default we have set it to 0 , user can pass customize value
    */
    async verifyGitopsDeployment(deploymentNumber: string = "0") {
        await this.page.getByTestId(`deployment-history-${deploymentNumber}`).click();
        await expect(this.successTickIcon).toHaveCount(5, { timeout: 12 * 1000 * 60 });
        // we are verifying that gitops has successfully completed all 5 steps

    }
    async verifyDeployedByExceptionUser() {
        await expect(this.page.locator(`//*[text()="Deployed by an exception user"]`)).toBeVisible({ timeout: 4 * 1000 * 60 });
    }

    async clickOnConfigurationAndOpenResources(envName: string) {
        await this.deploymentHistoryTabButton.click();
        await this.selectEnvironment(envName);
        await this.configurationTabButton.click();
        await this.page.locator(`//a[contains(@href,'configuration/deployment-template')]`).click();
    }
    async closeComparisonWindow() {
        await this.page.locator(`//a[contains(@href,'configuration')]`).nth(1).click();
    }

   
}

