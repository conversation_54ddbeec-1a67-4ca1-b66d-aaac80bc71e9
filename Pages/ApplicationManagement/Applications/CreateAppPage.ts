import { Page, Locator, expect, LocatorScreenshotOptions } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { AllTypes } from '../../../utilities/Types';
import { BasePage } from '../../BasePage';

export class CreateAppPage extends BasePage {
  readonly createAppButtonOnHeader: Locator;
  readonly customAppLink: Locator;
  readonly appNameTextbox: Locator;
  readonly descriptionTextbox: Locator;
  readonly createAppButtonOnDrawer: Locator;
  readonly selectProjectInputContainer: Locator;
  readonly visibleModalClose: Locator;
  readonly appSearchBar: Locator;
  readonly singleAppSelectionModal: Locator;
  readonly cloneExistingApp: Locator;
  readonly cloneExistingInputField: Locator;
  readonly appConfigurationLink: Locator;
  readonly gitRepoTab: Locator;
  readonly clickOnApplication: Locator;
  readonly cloneAppSourceAppDropdownButton: Locator;
  readonly createJobButton: Locator;
  readonly cloneJobButton: Locator;
  readonly createJobButtonOnDrawer: Locator;
  readonly helmAppListButton: Locator;
  readonly formError: Locator;
  readonly helpButton: Locator;
  readonly pageIsStabled: Locator;
  readonly appNameTextFieldInAppListRows: Locator;
  readonly clearAllFiltersButton: Locator;
  readonly appNameSortingButton: Locator;
  readonly applyFiltersButton: Locator;
  readonly genericEmptyState: Locator;
  readonly syncButton: Locator;
  readonly profileButton: Locator;
  readonly createAppFromTemplateButton: Locator;
  readonly appTemplateWorkflowDivs: Locator;
  readonly addTagsButton: Locator;
  ////devtron licensing
  readonly aboutDevtron: Locator;

  readonly licenseDialogButton: Locator;
  readonly title: Locator;
  readonly version: Locator;
  readonly copyright: Locator;
  readonly termsOfService: Locator;
  readonly privacyPolicy: Locator;
  readonly licenseAgreement: Locator;
  readonly okayButton: Locator;
  //License Card
  readonly devtronText: Locator;
  readonly trialBadge: Locator;
  readonly fingerprint: Locator;
  readonly updateLicenseButton: Locator;
  readonly contactSupportButton: Locator;
  readonly knowMore: Locator;
  readonly stackManagerButton: Locator;
  readonly fingerPrint: Locator;
  readonly recentlyVisitedDevtronAppsDropdown: Locator;
  constructor(public page: Page) {
    super(page);
    this.createAppButtonOnHeader = page.getByTestId('create-app-button-on-header');
    this.addTagsButton = this.page.locator(`//*[contains(text(),'Add tags')]`);
    this.customAppLink = page.getByText('Custom app', { exact: true });
    this.appNameTextbox = page.locator('//*[@name="name"]');
    this.descriptionTextbox = page.getByTestId('description');
    this.createAppButtonOnDrawer = page.getByTestId('create');
    this.selectProjectInputContainer = page.locator(`//*[contains(@class,'project__control')]`).first();
    this.visibleModalClose = page.getByTestId('visible-modal-close');
    this.appSearchBar = this.page.getByTestId('Search-by-app-name');
    this.singleAppSelectionModal = this.page.locator(`//*[contains(@data-testid,'app-list-row') or contains(@data-testid,'app-list__row')]`);
    this.cloneExistingApp = this.page.locator(`//*[@aria-label="Creation method: Clone application"]`)
    this.cloneExistingInputField = page.locator("//*[@class= 'app-name-for-clone__input']");
    this.appConfigurationLink = page.getByTestId("app-config-link");
    //ToDo to use testid in future
    this.gitRepoTab = page.getByTestId("git-repository-link");
    this.clickOnApplication = page.getByTestId("click-on-application");
    this.cloneAppSourceAppDropdownButton = page.locator(`//*[contains(@class,'app-name-for-clone__control')]`);
    this.createJobButton = page.getByTestId("action-menu-item-create-job");
    this.createJobButtonOnDrawer = page.getByTestId("create");
    this.cloneJobButton = page.locator(`//button[@aria-label='Creation method: Clone job']`);
    this.helmAppListButton = this.page.getByTestId("helm-app-list-button");
    this.formError = this.page.locator("//*[contains(@class,'form__input--error')]");
    this.helpButton = page.getByTestId("page-header-help-button");
    this.pageIsStabled = this.page.locator(`//*[@data-testid="app-list-row" or text()="No results"]`).first();
    this.appNameTextFieldInAppListRows = this.page.locator(`//*[@data-testid="app-list-for-sort"]`);
    this.clearAllFiltersButton = this.page.locator(`//*[text()="Clear All Filters"]`);
    this.appNameSortingButton = this.page.locator('//*[text()="APP NAME"]');
    this.applyFiltersButton = this.page.getByTestId("filter-select-picker-apply");
    this.genericEmptyState = this.page.getByTestId('generic-empty-state');
    this.syncButton = this.page.getByTestId(`sync-now-button`);
    this.profileButton = this.page.getByTestId(`profile-button`);
    this.createAppFromTemplateButton = this.page.locator('//*[@aria-label="Creation method: From template"]');
    this.appTemplateWorkflowDivs = this.page.getByTestId('rf__wrapper');
    //devtron licensing
    //this.helpButton = page.getByTestId("go-to-get-started");
    this.aboutDevtron = page.getByTestId('action-menu-item-about-devtron');
    this.licenseDialogButton = page.locator('label:has-text("License")');
    this.title = page.getByText("Devtron");
    this.version = page.getByText("Enterprise Version");
    this.copyright = page.getByText(/Copyright © 2025 Devtron Inc./);
    this.termsOfService = page.getByTestId("terms-of-service");
    this.privacyPolicy = page.getByTestId("privacy-policy");
    this.licenseAgreement = page.getByTestId("license-agreement");
    this.okayButton = page.getByText("Okay");
    //License
    this.devtronText = page.locator('.font-merriweather.cn-9.fs-16');
    this.trialBadge = page.locator('.trial-license-badge');
    this.fingerprint = page.locator('.dc__truncate');
    this.updateLicenseButton = page.getByTestId('update-license');
    this.contactSupportButton = page.getByTestId('contact-support');
    this.knowMore = page.getByTestId('banner-license-know-more-button');
    this.stackManagerButton = this.page.getByTestId('click-on-stack-manager');
    this.fingerPrint = this.page.locator(`//*[contains(@class,'cn-9 fs-13 lh-1-5 fw-4 dc__truncate')]`);
    this.recentlyVisitedDevtronAppsDropdown = this.page.locator(`//*[contains(@class,'app-switcher') and contains(@class,'__control')]`)
  }

  async createCustomAppJob(appOrJob: string, appName: string, projectName: string, baseServerURL: string, rbacRelatedData?: { isSuccessfull: boolean, hiddenProjectName?: string }) {
    try {
      await BaseTest.clickOnDarkMode(this.page);
      await BaseTest.clickOnToolTipOkayButton(this.page);
      await this.page.addLocatorHandler(this.page.locator(`//*[contains(text()," [${appName}] already exists")]`), async () => {
        await this.page.locator('//*[contains(@data-testid,"close-create-custom")]').click();
        await this.searchAndSelectAppsFromList(appName, false, appOrJob);
        await this.page.locator(`//*[@data-testid="${appOrJob}-config-link"]`).click();
      });
      await expect(async () => {
        await this.navigateToDevtronAppListPage(baseServerURL);
        await this.createAppButtonOnHeader.click();
        if (appOrJob === "app")
          await this.customAppLink.click();
        else
          await this.createJobButton.click();
        // Wait for the API call to complete on modal open
        await this.page.waitForLoadState('load');
        await this.appNameTextbox.pressSequentially(appName, { delay: 20 });
        // Click the description textbox and fill it
        await this.descriptionTextbox.click();
        //await this.page.waitForTimeout(3000);
        await this.descriptionTextbox.fill('this is for testing purpose only');
        await this.selectProjectFromDropDown(projectName, rbacRelatedData?.hiddenProjectName);
        // Wait for the API call to complete on modal open
        await this.page.waitForLoadState('load');
        if (!rbacRelatedData) {
          if (appOrJob === "app")
            await this.createAppButtonOnDrawer.click();
          else
            await this.createJobButtonOnDrawer.click()
          await expect(this.page.locator('//*[text()="Git Account"]')).toBeVisible({ timeout: 50000 });
        }
        else {
          let createButton: Locator = appOrJob == "app" ? this.createAppButtonOnDrawer : this.createJobButtonOnDrawer;
          // let toastMessage: string = rbacRelatedData.isSuccessfull ? 'Success' : 'Error'
          let statusCode: number = rbacRelatedData.isSuccessfull ? 200 : 403;
          // await BaseTest.checkToast(this.page, createButton, toastMessage);
          await BaseTest.waitForApiResponse(this.page, '/app', statusCode, async (page) => { await createButton.click() })
        }
      }).toPass({ timeout: 3 * 1000 * 60 });

    } catch (error) {
      console.error(`An error occurred during app creation: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }
  async cloningApp(cloneAppName: string, newAppName: string, projectName: string) {
    await this.page.goto(process.env.BASE_SERVER_URL!);
    await this.createAppButtonOnHeader.click();
    await this.customAppLink.click();
    await this.cloneExistingApp.click();
    await this.page.getByTestId('template-list-search').fill(cloneAppName);
    await this.page.keyboard.press('Enter');
    await this.page.locator(`//h3[text()="${cloneAppName}"]`).click();
    await this.appNameTextbox.fill(newAppName);
    await this.selectProjectFromDropDown(projectName);
    await BaseTest.checkToast(this.page, this.createAppButtonOnDrawer, "Success");
  }

  async cloningJob(cloneJobName: string, newJobName: string, projectName: string) {
    await this.page.goto(process.env.BASE_SERVER_URL!);
    await this.createAppButtonOnHeader.click();
    await this.createJobButton.click();
    await this.cloneJobButton.click();
    await this.page.getByTestId('template-list-search').fill(cloneJobName);
    await this.page.keyboard.press('Enter');
    await this.page.locator(`div[class*="generic-info-card"]`, { hasText: cloneJobName }).click();
    await this.appNameTextbox.fill(newJobName);
    // await this.cloneAppSourceAppDropdownButton.click();
    // await this.cloneExistingInputField.fill(cloneAppName);
    // await expect(this.page.locator(`//*[contains(text(),"${cloneAppName}")]`).nth(1)).toBeVisible();
    await this.page.keyboard.press('Enter');
    await this.selectProjectFromDropDown(projectName);
    await BaseTest.checkToast(this.page, this.createAppButtonOnDrawer, "Success");
  }


  async selectProjectFromDropDown(projectName: string, hiddenProjectName?: string) {
    // Click the project input container and enter project name
    await this.selectProjectInputContainer.click();
    if (hiddenProjectName) {
      let allProjects = await this.page.locator(`//*[@role="listbox"]//h4`).all();
      let wasProjectAvailable = await Promise.all(allProjects.map(async (key) => {
        return await key.textContent() == hiddenProjectName
      })).then(result => result.some(Boolean));
      expect(wasProjectAvailable).toBeFalsy();
    }
    await this.page.locator('//*[@role="listbox"]').locator(`//*[text()="${projectName}"]`).click();
  }

  async navigateToDevtronAppListPage(baseServerURL: string) {
    await this.page.goto(`${baseServerURL}/dashboard/app/list/d`, { waitUntil: 'load' });
  }

  async navigateToHelmAppListPage(baseUrl: String) {
    let urlTogo: string = process.env.clusterType?.includes('ea') ? '/app/list/d?cluster=1' : '/app/list/h';
    await this.page.goto(process.env.BASE_SERVER_URL + urlTogo);
    await this.page.waitForLoadState('load');

  }
  async searchAndSelectAppsFromList(Appname: string, isHelm: boolean = false, appOrJob: string = 'app', doesApplicationExist: boolean = true) {
    await BaseTest.clickOnDarkMode(this.page);
    await BaseTest.clickOnToolTipOkayButton(this.page);
    const isAppOrJob: string = appOrJob == "app" ? "application" : "job"
    if (isHelm) {
      await this.helmAppListButton.first().click();
    }
    else {
      await this.page.locator(`//*[@data-testid="click-on-${isAppOrJob}"]`).click();
    }
    let searchLocator: Locator = appOrJob == 'app' ? this.page.locator(`//*[@data-testid="search-by-app-name"]`) : this.page.locator(`//*[@data-testid="Search-by-job-name"]`);
    await this.profileButton.hover();
    await searchLocator.click();
    await this.page.keyboard.type(Appname);
    await this.page.keyboard.press('Enter');
    await this.page.waitForLoadState('load');
    //await this.singleAppSelectionModal.click();
    if (doesApplicationExist) {
      await this.page.locator(`//*[@data-testid="${appOrJob}-list-row"]`).first().click();
    }
    else {
      await expect(this.genericEmptyState).toBeVisible({ timeout: 1 * 1000 * 60 });
    }

  }
  async checkMandatTagWarning(projectName: string, tagName: string) {
    await this.page.goto(process.env.BASE_SERVER_URL!);
    await this.createAppButtonOnHeader.click();
    await this.customAppLink.click();
    await expect(async () => {
      await this.selectProjectFromDropDown(projectName);
      await BaseTest.checkToast(this.page, this.createAppButtonOnDrawer, "Error");
    }).toPass({ timeout: 3 * 1000 * 60 });
  }
  async applyFilter(data: { filterType: string, filterValue: string, verificationText: string, clearPreviousFilter: boolean }[]) {
    for (const key of data) {
      if (key.clearPreviousFilter) {
        try { await this.clearAllFiltersButton.click(); }
        catch (error) {
          console.log("no filters were there");
        }
      }
      await this.page.locator(`//*[contains(@class,'app-list-${key.filterType}-')]`).first().click();
      await this.page.locator('//*[@role="listbox"]').locator(`//*[text()="${key.filterValue}"]`).click();
      await this.applyFiltersButton.click();
      await expect(this.pageIsStabled).toBeVisible({ timeout: 12000 });
      const appListCount = await this.singleAppSelectionModal.count();
      console.log(appListCount);
      key.filterType = key.filterType == "app-status" ? "status" : key.filterType;
      const verificationTextCount = await this.singleAppSelectionModal.locator(`//*[@data-testid="${key.filterValue}-${key.filterType}" or @data-testid="${key.filterValue}"]`).count();
      console.log('filter value is' + key.filterValue);
      console.log('filterType is' + key.filterType);

      console.log(verificationTextCount);
      expect(appListCount).toEqual(verificationTextCount);
    }
    await this.clearAllFiltersButton.click();
  }
  async verifySorting() {
    await this.logicForAppNameSorting(true);
    await this.appNameSortingButton.click();
    await this.logicForAppNameSorting(false);
  }

  async logicForAppNameSorting(isAscendingOrder: boolean) {
    await this.pageIsStabled.waitFor({ timeout: 13000 });
    const appNameTextField = await this.appNameTextFieldInAppListRows.all();
    if (appNameTextField.length > 1) {
      for (let i = 0; i < appNameTextField.length - 1; i++) {
        let firstAppName = await this.appNameTextFieldInAppListRows.nth(i).textContent();
        let secondAppName = await this.appNameTextFieldInAppListRows.nth(i + 1).textContent();
        let result = firstAppName! < secondAppName!;
        if (isAscendingOrder) {
          expect(result).toBeTruthy();
        }
        else {
          expect(result).toBeFalsy();
        }
      }
    }
  }

  async verifyEnvInWorkflow(data: { workflowNumber: number, envNames: string[] }[]) {
    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < data[i].envNames.length; j++) {
        await expect(this.appTemplateWorkflowDivs.nth(data[i].workflowNumber).locator(`//*[text()="${data[i].envNames[j]}"]`)).toBeVisible();
      }
    }
  }
  async changeEnvOfWorkflow(workflowNumber: number, existingEnvName: string, envNameToChange: string) {
    await this.appTemplateWorkflowDivs.nth(workflowNumber).locator(`//*[text()="${existingEnvName}"]`).click();
    await this.page.waitForLoadState('load');
    await this.page.locator('//*[@role="option"]').locator(`//*[text()="${envNameToChange}"]`).first().click();
  }


  async createAppFromTemplate(data: { appName: string, projectName: string, templateName: string, tags?: { key: string, value: string }[], verificationOfPreDefinedData?: { codeSource?: { gitAccountName: string, gitRepoUrl: string }[], buildConfiguration?: { registryName: string, repoName: string }, workflow?: { workflowNumber: number, envNames: string[] }[] }, configurationChange?: { workflowNumber: number, existingEnvName: string, envNameToChange: string } }, allObjects: AllTypes.fixtures.allPageObjects) {
    await this.navigateToDevtronAppListPage(process.env.BASE_SERVER_URL!);
    await this.createAppButtonOnHeader.click();
    await this.customAppLink.click();
    await this.createAppFromTemplateButton.click();
    await allObjects.applicationTemplatePage.searchAndClickOnAnyTemplate(data.templateName);
    await this.appNameTextbox.fill(data.appName);
    await this.selectProjectFromDropDown(data.projectName);
    if (data.tags) {
      await this.addTagsButton.click();
      for (let key of data.tags) {
        await allObjects.tagPropagatonPage.addTagDetails(key.key, key.value);
      }
    }
    if (data.verificationOfPreDefinedData) {
      if (data.verificationOfPreDefinedData.codeSource) {
        await allObjects.gitRepositoryPage.verifyGitAccountAndRepoConfigured(data.verificationOfPreDefinedData.codeSource);
      }
      if (data.verificationOfPreDefinedData.buildConfiguration) {
        await allObjects.buildConfigurationPage.verifySavedRegistry(data.verificationOfPreDefinedData.buildConfiguration.registryName, data.verificationOfPreDefinedData.buildConfiguration.repoName);
      }
      if (data.verificationOfPreDefinedData.workflow) {
        await this.verifyEnvInWorkflow(data.verificationOfPreDefinedData.workflow);
      }
    }
    if (data.configurationChange) {
      await this.changeEnvOfWorkflow(data.configurationChange.workflowNumber, data.configurationChange.existingEnvName, data.configurationChange.envNameToChange);
    }
    await this.createAppButtonOnDrawer.click();
  }

  async verifyApplicationPage() {
    await this.helpButton.waitFor();
  }
  async gotoDialog() {

  }
  //devtron licensing
  async verifyAboutAndLicenseDialog() {
    await this.helpButton.click();
    await this.aboutDevtron.click()
    await this.verifyAboutSection();
    await this.licenseDialogButton.click();
    await this.verifyLicenseSection();
    await this.okayButton.click();
  }
  async verifyLimitationsOfFreemiumLicense() {
    await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/cluster-env`);
    await this.page.getByTestId('add-cluster-button').click();
    await expect(this.page.getByText('Upgrade to Enterprise Plan')).toBeVisible();
    await expect(
      this.page.getByText('Your freemium plan allows only 1 cluster. Unlock more to scale without limits.')
    ).toBeVisible();
  }

  async verifyAboutSection() {
    //await expect(this.version).toBeVisible();
    await expect(this.copyright).toBeVisible();
    await expect(this.termsOfService).toBeVisible();
    await expect(this.privacyPolicy).toBeVisible();
    await expect(this.licenseAgreement).toBeVisible();
    await expect(this.okayButton).toBeVisible();
  }

  async verifyLicenseSection() {
    await this.trialBadge.waitFor();
    await expect(this.trialBadge).toHaveText('FREEMIUM LICENSE');
    await expect(this.page.getByText('Unlimited single cluster usage')).toBeVisible();
    await expect(this.page.locator('[data-testid="mail-support"]'))
      .toHaveAttribute('href', 'mailto:<EMAIL>');
    await this.updateLicenseButton.waitFor();
    await this.contactSupportButton.nth(0).waitFor();
  }

  async verifyUpdateLicense() {

  }

  async verifyKnowMoreSection(days: number = 14) {
    await this.knowMore.click();
    await this.licenseDialogButton.click();
    await this.verifyLicenseSection();
    let fingerPrint = await this.fingerPrint.textContent();
    await this.page.locator(`${days} days remaining`)
    await this.okayButton.click();
    return fingerPrint;

  }

  async devtronStackManager() {
    await this.stackManagerButton.waitFor();
    await this.stackManagerButton.click();
    expect(await this.page.getByTestId('main-header')).toHaveText('Devtron Stack Manager')

  }

  async clickOnAnyAppInRecentlyVisitedAppsDropdown(appNameToClick: string) {
    if (await this.dropdownOptionsDivContainer.isHidden())
      await this.recentlyVisitedDevtronAppsDropdown.click();
    await this.singleEntityInsideDropdownOptions.locator(`//*[text()="${appNameToClick}"]`).click();
  }
  async verifyAppVisibilityInRecentlyVisitedAppsDropdown(appNameToVerify: string, isVisible: boolean) {
    if (await this.dropdownOptionsDivContainer.isHidden())
      await this.recentlyVisitedDevtronAppsDropdown.click();
    if (!isVisible) {
      await expect(this.singleEntityInsideDropdownOptions.locator(`//*[text()="${appNameToVerify}"]`)).toBeHidden();
      return;
    }
    await expect(this.singleEntityInsideDropdownOptions.locator(`//*[text()="${appNameToVerify}"]`)).toBeVisible();
  }
}