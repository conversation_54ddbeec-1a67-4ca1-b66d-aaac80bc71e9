import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../BasePage';
import { BaseTest } from '../../utilities/BaseTest';

export class ProjectPage extends BasePage {
  readonly addProjectButton: Locator;
  readonly projectNameInput: Locator;
  readonly projectSaveButton: Locator;
  readonly deleteProjectButton: Locator;

  constructor(public page: Page) {
    super(page)
    this.addProjectButton = page.getByText('Add Project');
    this.projectNameInput = page.getByPlaceholder('e.g. My Project');
    this.projectSaveButton = page.getByTestId('project-save-button');
  }

  async addProject(projectName: string, baseServerURL: string) {
    await expect(async () => {
      await this.navigateToProjectsPage(baseServerURL);
      await this.clickAddProject();
      await this.fillProjectName(projectName);
      await BaseTest.checkToast(this.page, this.projectSaveButton, "Success");
    }).toPass({ timeout: 4 * 1000 * 60 });

  }
  async deleteProject(projectName: string, baseServerURL: string) {
    await this.navigateToProjectsPage(baseServerURL);
    await this.page.locator(`//*[text()="${projectName}"]`).hover();
    await this.clickDeleteProjectButton(projectName);
    await this.confirmDeleteProject();
  }

  async navigateToProjectsPage(baseServerURL: string) {
    await this.page.goto(`${baseServerURL}/global-config/projects`, { waitUntil: 'load' });
  }

  private async clickAddProject() {
    await this.addProjectButton.click();
  }

  private async fillProjectName(projectName: string) {
    await this.projectNameInput.fill(projectName);
  }

  private async clickProjectSaveButton() {
    await this.projectSaveButton.click();
  }

  private async clickDeleteProjectButton(projectName: string) {
    const deleteButton = this.page.getByTestId(`delete-project-button-${projectName}`);
    await deleteButton.click();
  }

  private async confirmDeleteProject() {
    await this.dialogDeleteConfirmationButton.click();
  }
}
