import { Locator, Page, expect } from '@playwright/test';
import { WorkflowPage } from '../Applications/WorkflowPage';

export class ApplicationGroupPage {
    readonly applicationGroupHeader: Locator;
    readonly environmentSearchBox: Locator;
    readonly devtroncdClickOnEnv: Locator;
    readonly appGroupClusterButton: Locator;
    readonly appGroupSelecterText: Locator;
    readonly saveSelectionAsfilter: Locator;
    readonly enterFilterName: Locator;
    readonly enterDescriptionOfFilter: Locator;
    readonly saveFilterButton: Locator;
    readonly groupBuildDeploy: Locator;
    readonly selectAllAppsCheckBox: Locator;
    readonly changeBranchButton: Locator;
    readonly enterBranchName: Locator;
    readonly updateBranchButton: Locator;
    readonly closePopup: Locator;
    readonly bulkBuildImageButton: Locator;
    readonly startBuild: Locator;
    readonly bulkDeployButton: Locator;
    readonly deployButton: Locator;
    readonly appFilter: Locator;
    private selectPipelineUsingApp: Locator;
    private selectApplicationSearchFilter: Locator;
    private app: string;
    readonly savedFiltersButton: Locator;
    readonly workflowPage: WorkflowPage;
    readonly selectImageByReleaseTagDropdown: Locator;

    constructor(private page: Page) {
        this.applicationGroupHeader = page.getByTestId('main-header');
        this.environmentSearchBox = page.getByTestId('environment-search-box');
        this.devtroncdClickOnEnv = page.getByTestId('devtroncd-click-on-env');
        this.appGroupClusterButton = this.page.locator("//*[contains(@class,'app-group-cluster-filter__control')]");
        this.appGroupSelecterText = page.getByTestId('app-group-selector-text');
        this.saveSelectionAsfilter = page.getByText('Save selection as filter');
        this.enterFilterName = page.getByPlaceholder('Enter filter name');
        this.enterDescriptionOfFilter = page.getByPlaceholder('Write a description for this filter');
        this.saveFilterButton = page.locator('//*[@class="cta flex h-36" or text()="Save"]');//need to provide data test id for this element in dashboard.
        this.groupBuildDeploy = page.getByTestId('group-build-deploy');
        this.selectAllAppsCheckBox = page.getByTestId('select-all-apps-chk-span');
        this.changeBranchButton = page.getByText('Change branch');
        this.enterBranchName = page.getByPlaceholder('Enter branch name');
        this.updateBranchButton = page.locator('//*[text()="Update branch"]')
        this.closePopup = page.getByTestId('close-popup');
        this.bulkBuildImageButton = page.getByTestId('bulk-build-image-button')
        this.startBuild = page.getByTestId('start-build');
        this.bulkDeployButton = page.getByTestId('bulk-deploy-button');
        this.deployButton = page.getByTestId('cd-trigger-deploy-button');
        this.appFilter = page.locator('//*[@data-selected-tab="appFilter"]');
        this.savedFiltersButton = this.page.locator('//*[@data-selected-tab="groupFilter"]');
        this.workflowPage = new WorkflowPage(this.page);
        this.selectImageByReleaseTagDropdown = this.page.locator(`//*[contains(@class,'build-config__select-repository-containing-code__control')]`);
    }
    // Setter method
    async setAppName(app: string) {
        this.app = app;
        this.selectApplicationSearchFilter = this.page.locator(`//*[text()="${this.app}"]`);
    }
    async setSelectPipeline(appName: string | undefined) {
        this.selectPipelineUsingApp = this.page.getByTestId(`app-group-checkbox-${appName}-chk-span`);
    }
    // Setter method
    async VerifyApplicationGroupPage(applicationGroup: string) {
        await this.appGroupClusterButton.hover();
    }


    async searchEnvironment(envName: string) {
        await this.environmentSearchBox.fill(envName);
        await this.page.keyboard.press('Enter');
        await this.page.getByTestId(`${envName}-click-on-env`).click();
        // await this.devtroncdClickOnEnv.click();
    }

    async selectApplcations(app1: string, app2?: string) {
        await this.appGroupSelecterText.click();
        await this.appFilter.click();
        await this.setAppName(app1);
        const app1Element = await this.selectApplicationSearchFilter.first();
        // Click on app1 element
        await app1Element.click();
        if (app2) {
            await this.setAppName(app2);
            await this.selectApplicationSearchFilter.first().click();
        }
    }

    async createOrApplyFilter(filterName: string, isFilterWithSameNameAlreadyExists: boolean = false) {
        if (isFilterWithSameNameAlreadyExists) {
            await this.appGroupSelecterText.click();
            await this.savedFiltersButton.click();
            await this.page.locator(`//*[text()="${filterName}"]`).click();
        }
        else {
            await this.saveSelectionAsfilter.click();
            await this.enterFilterName.fill(filterName);
            await this.enterDescriptionOfFilter.fill("Description for this filter");
            await this.saveFilterButton.click();
            await this.page.waitForLoadState("load");
            await this.groupBuildDeploy.click();
        }
    }

    async changeBranch(branchName) {
        await this.changeBranchButton.click();
        await this.enterBranchName.fill(branchName);
        await this.updateBranchButton.click();
        await this.closePopup.click();
        await this.page.waitForLoadState('load');
    }

    async bulkBuildImage() {
        await this.bulkBuildImageButton.click();
        await this.startBuild.click();
        await this.closePopup.click();
    }

    async bulkDeploy(isException: Boolean = false, filterByReleaseTag?: string, addReleaseTagName?: string) {
        await this.bulkDeployButton.click();
        if (isException) {
            expect(this.deployButton).toContainText('Deployement without Approval');
        }
        if (addReleaseTagName) {
            await this.workflowPage.addReleaseTagsOnImage(0, addReleaseTagName, 0, 0, true);
        }
        if (filterByReleaseTag) {
            await this.selectImageByReleaseTagDropdown.click();
            await this.page.locator(`//*[@role="option"]//*[text()='${filterByReleaseTag}']`).click();
        }
        await this.deployButton.click();
        await this.closePopup.click();
    }

    async setPipeline() {

    }
    async selectPipelines(branchName: string, appName?: string) {
        if (appName === "") {
            await this.selectAllAppsCheckBox.click();
            await this.changeBranch(branchName);
            await this.bulkBuildImage();
            await this.workflowPage.verifyCiCdStatus(0, 1, "Succeeded", false);
            await this.workflowPage.verifyCiCdStatus(1, 1, "Succeeded", false);
            await this.bulkDeploy(false, undefined, 'test');
            await this.workflowPage.verifyCiCdStatus(0, 0, "Succeeded", false);
            await this.workflowPage.verifyCiCdStatus(1, 0, "Succeeded", false);

        } else {
            await this.selectAllAppsCheckBox.click();
            await this.setSelectPipeline(appName)
            await this.selectPipelineUsingApp.click();
            await this.changeBranch(branchName);
            await this.bulkBuildImage();
            await this.workflowPage.verifyCiCdStatus(0, 1, "Succeeded", false);
            await this.bulkDeploy();
            await this.workflowPage.verifyCiCdStatus(0, 0, "Succeeded", false);
        }
    }

    async deleteGroupFilter() {
        await this.appGroupSelecterText.click();
    }
};
