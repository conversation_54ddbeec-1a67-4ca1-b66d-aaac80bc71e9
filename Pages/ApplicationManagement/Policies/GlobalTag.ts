// TAG PROPAGATION  continued in Mandatory Tags

import { Page, Locator, expect } from '@playwright/test'; // Importing necessary modules
import { BaseTest } from '../../../utilities/BaseTest'; // Importing BaseTest utility
import { text } from 'stream/consumers';
import { BasePage } from '../../BasePage';

export class GlobalTag extends BasePage {
  readonly hoverOnSideIcon: Locator;
  readonly globalConfiguraton: Locator;
  readonly projects: Locator;
  readonly addProjects: Locator;
  readonly addProjectName: Locator;
  readonly saveProjectName: Locator;

  readonly tags: Locator;
  readonly addTags: Locator;
  readonly suggestedTags: Locator;
  readonly keySuggested: Locator;
  readonly descriptionSuggested: Locator;
  readonly selectIconTag: Locator;
  readonly saveSuggested: Locator;

  readonly mandatoryTags: Locator;
  readonly selectProject: Locator;
  readonly keyMandatory: Locator;
  // readonly selectIconTag : Locator;  -- this for mandatory tag, but data test id will be same ig
  readonly saveMandatory: Locator;

  readonly createAppButtonOnHeader: Locator;
  readonly customAppLink: Locator;
  readonly appNameTextbox: Locator;
  readonly selectProjectInput: Locator;
  readonly selectAppIcon: Locator;
  readonly tagsSearchbar: Locator;
  readonly deleteIcon: Locator;
  readonly mandatoryProjectDropdown: Locator;

  constructor(public page: Page) {
    super(page);
    this.globalConfiguraton = page.getByTestId('click-on-global-configuration')
    this.projects = page.getByTestId('Projects-page')
    this.addProjects = page.getByTestId('project-add-project-button')
    this.addProjectName = page.locator('//*[@class="form__input fs-13 lh-20 fw-4 "]')  // will change this later
    this.saveProjectName = page.getByTestId('project-save-button');
    this.mandatoryProjectDropdown = this.page.locator(`//*[contains(@class,'mandatory-project-selector__control')]`);


    this.tags = page.locator('//*[@href="/dashboard/global-config/tags"]');
    this.addTags = page.getByText('Add tag').last()
    this.suggestedTags = page.getByText('Suggested tags').first()
    this.keySuggested = page.getByTestId('tag-key-0')
    this.descriptionSuggested = page.getByPlaceholder('Enter description');
    this.selectIconTag = page.locator('//*[@data-index="0"]').first();
    this.saveSuggested = page.getByTestId('Save-button-tag')

    this.mandatoryTags = page.getByTestId('undefined-span').last()
    this.selectProject = page.locator('//*[text()="Type to select projects"]/parent::div');
    this.keyMandatory = page.getByPlaceholder("Eg. owner-name");
    this.saveMandatory = page.getByTestId('tag-save-button');

    this.createAppButtonOnHeader = page.getByTestId('create-app-button-on-header');
    this.customAppLink = page.getByText('Custom app', { exact: true });
    this.appNameTextbox = page.getByTestId('app-name-textbox');
    this.selectProjectInput = page.getByTestId('select-project');
    this.selectAppIcon = page.getByTestId('click-on-application');
    this.tagsSearchbar = page.getByTestId('tag-search');


  }
  async addingProject(projName: string) {

    await this.addProjects.click()
    await this.addProjectName.click()
    await this.addProjectName.fill(projName)
    await this.saveProjectName.click()


  }


  async createTag(tagType: string, key: string, projectName: string) {

    if (tagType === "Suggested") {


      await this.tags.click()
      await this.addTags.click()
      await this.suggestedTags.click()
      await this.keySuggested.click()
      await this.keySuggested.fill(key)
      await this.descriptionSuggested.click()
      await this.descriptionSuggested.fill("We are taking this for testing")
      await this.selectIconTag.click()
      await this.saveSuggested.click()

    }
    if (tagType === "Mandatory") {
      await this.tags.click()
      await this.addTags.click()
      await expect(async () => {
        await this.mandatoryTags.click()
        await this.selectProject.click()
        await this.selectProject.pressSequentially(projectName);
        await this.page.keyboard.press("Enter");
        await this.mandatoryProjectDropdown.click();
        await this.keyMandatory.click()
        await this.page.keyboard.insertText(key)
        await this.descriptionSuggested.click()
        await this.page.keyboard.insertText("We are taking this for testing")
        //await this.selectIconTag.click()
        await this.saveMandatory.click()
        await expect(this.page.locator(`//*[text()="${key}"]`)).toBeVisible({ timeout: 20000 });
      }).toPass({ timeout: 3 * 1000 * 60 });
    }
  }
  async deleteTags(tagName: string) {
    await expect(async () => {
      await this.tags.click();
      await this.tagsSearchbar.fill(tagName);
      await this.page.keyboard.press("Enter");
      await this.page.locator(`//*[text()="${tagName}"]`).hover();
      await this.page.locator(`//*[@data-tag-key="${tagName}"]`).click();
      await this.dialogDeleteConfirmationButton.click();
      await this.addTags.waitFor({ timeout: 15000 });
      await expect(this.page.locator(`//*[text()="${tagName}"]`)).toBeHidden({ timeout: 25000 });
    }).toPass({ timeout: 3 * 1000 * 60 });
  }

}
