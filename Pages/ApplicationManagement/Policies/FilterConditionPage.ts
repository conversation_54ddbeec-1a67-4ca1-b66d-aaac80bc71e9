import { Locator, <PERSON> } from "playwright-core";
import { BasePage } from "../../BasePage";

export class FilterConditionPage extends BasePage {

    readonly addFilterConditionButton: Locator;
    readonly profileNameInputField: Locator;
    readonly passConditionButton: Locator;
    readonly failConditionButton: Locator;
    readonly textAreaToDefineCondition: Locator;
    readonly applyToButton: Locator;
    readonly applicationDropdown: Locator;
    readonly environmentDropdown: Locator;
    readonly nextButton: Locator;
    readonly checkboxes: Locator;
    readonly saveButton: Locator;
    readonly updateButton: Locator;
    readonly deleteButtonOnModal: Locator;
    readonly checkboxClickableArea: Locator;
    readonly editOrDeleteFilterIcon: Locator;

    constructor(public page: Page) {
        super(page);
        this.textAreaToDefineCondition = this.page.locator("//*[contains(@placeholder,'Example')]");
        this.passConditionButton = this.page.locator("//*[contains(text(),'Pass ')]");
        this.failConditionButton = this.page.locator("//*[contains(text(),'Fail ')]");
        this.applyToButton = this.page.locator("//*[contains(text(),'Apply ')]");
        this.addFilterConditionButton = this.page.locator('//*[contains(text(),"Add")]');
        this.profileNameInputField = this.page.locator('//*[@name="filterName"]');
        this.applicationDropdown = this.page.locator('//*[@id="filter-conditions-application-select"]');
        this.environmentDropdown = this.page.locator('//*[@id="filter-conditions-env-select"]');
        this.nextButton = this.page.locator('//*[text()="Next"]');
        this.checkboxes = this.page.locator('//*[@type="checkbox"]');
        this.saveButton = this.page.locator('//*[text()="Save"]');
        this.updateButton = this.page.locator('//*[text()="Update"]');
        this.deleteButtonOnModal = this.page.locator('//*[text()="Delete"]');
        this.checkboxClickableArea = this.page.getByTestId('undefined-chk-span');
        this.editOrDeleteFilterIcon = this.page.locator('//*[@class="dc__outline-none-imp dc__no-border p-0 h-20 dc__no-background"]');
    }
    /**
   * entering filter condition based on the 0 or 1 or boolean 
   * @param {string} conditionToEnter - enter the filter condition
   * @param {string} isPassCondition - based on the boolean or 0 or 1 we are entering the values in respective area
   */
    async enterFilterCondition(conditionToEnter: string, isPassCondition: boolean | number) {
        await this.page.waitForLoadState('load');
        await this.clearExistingConditions();
        var booleanValue = isPassCondition!!;
        if (booleanValue) {
            await this.passConditionButton.click();
        }
        else {
            await this.failConditionButton.click();
        }
        await this.textAreaToDefineCondition.click({ force: true });
        await this.page.keyboard.type(conditionToEnter);
        console.log(conditionToEnter);
    }

    /**
    * clearing the data in pass fail condition text box
    */
    async clearExistingConditions() {
        await this.passConditionButton.click();
        await this.clearConditionTextArea();
        await this.failConditionButton.click();
        await this.clearConditionTextArea();
    }
    async clearConditionTextArea() {
        var text = await this.textAreaToDefineCondition.textContent();
        await this.textAreaToDefineCondition.fill('');
    }

    /**
   * we are using this to create and update the filter condition 
   * @param {string} conditionToEnter - enter the filter condition
   * @param {string} applicationName - enter the application name
   * @param {string} environmentName - enter the enironment name
   * the updation part is set up to update env only for now , we are assuming if new profile is false , app is already set
   * as we are just testing on 1 app only   -----------<<<<<<<<<<
   */
    async createNewFilterCondition(isNewProfile: boolean, name: string, conditionToEnter: string, isPassCondition: boolean | number, applicationName: string, environmentName: string) {
        await this.page.goto(process.env.BASE_SERVER_URL! + '/global-config/filter-condition');
        if (isNewProfile) {
            console.log('not found ');
            await this.addFilterConditionButton.click();
            await this.profileNameInputField.fill(name);
        }
        else {
            await this.page.locator(`//a[text()="${name}"]`).click();
            //  await this.page.locator(`//div[div[a[text()="${name}"]]]`).locator(this.editOrDeleteFilterIcon).first().click();

        }
        await this.enterFilterCondition(conditionToEnter, isPassCondition);
        if (isNewProfile) {
            await this.nextButton.click();
            await this.selectApplication(applicationName);
            await this.selectEnvironment(environmentName);
            await this.saveButton.click();
        }
        else {
            await this.applyToButton.click();
            await this.selectEnvironment(environmentName);
            await this.updateButton.click();
        }
    }

    /**
    * selecting application from dropdown 
    */
    async selectApplication(applicationName: string) {
        await this.applicationDropdown.click();
        await this.page.locator(`//*[text()="${applicationName}"]`).click();
        await this.applicationDropdown.click();
    }

    /**
    * selecting environment from dropdown 
    * we are also clearing if any previous checkbox is already selected 
    */
    async selectEnvironment(environmentName: string) {
        await this.environmentDropdown.click();
        await this.page.waitForLoadState('load');
        // var totalCheckBoxes = await this.checkboxes.all();
        // for (var i = 1; i < totalCheckBoxes.length; i++) {
        //     if (await this.checkboxes.nth(i).isChecked()) {
        //         await this.checkboxClickableArea.nth(i).click();
        //     }
        // }
        //unchecking the previous selected envs
        for (let i = 0; i < 2; i++) {
            if (await this.page.locator('//*[text()="All Environments"]/parent::div//*[@type="checkbox"]').nth(i).isChecked()) {
                await this.page.locator('//*[text()="All Environments"]/parent::div//label').nth(i).click();
            }
        }
        await this.page.locator(`//*[text()="${environmentName}"]`).click();
    }

    /**
    * Deleting the filter 
    */
    async deleteFilterCondition(filterName: string) {
        await this.page.goto(process.env.BASE_SERVER_URL! + '/global-config/filter-condition');
        await this.page.locator(`//*[text()="${filterName}"]`).hover();
        await this.page.waitForTimeout(2000);
        await this.editOrDeleteFilterIcon.nth(1).waitFor({ state: 'visible' });
        await this.editOrDeleteFilterIcon.nth(1).click({force:true});
        await this.page.waitForTimeout(2000);
        await this.dialogDeleteConfirmationButton.waitFor({ state: 'visible' });
        await this.dialogDeleteConfirmationButton.click({force:true});

    }

}