import { Page, Locator, expect } from '@playwright/test';
import { PrePostCiCd } from '../Applications/PrePostCiCd';
import { LockConfiguration } from './LockConfiguration';
import { BasePage } from '../../BasePage';
export class MandatoryPluginPage extends BasePage {
    readonly pluginsAppliedRow: Locator;
    readonly addPluginButton: Locator;
    readonly prePostCiCdPage: PrePostCiCd;
    readonly doneButton: Locator;
    readonly saveProfileButton: Locator;
    readonly lockConfigPage: LockConfiguration;
    readonly threeButtonsOnAppliedProfilesPage: Locator;
    readonly deleteButton: Locator;
    readonly noOffendingPipelonesOkButton: Locator;



    constructor(public page: Page) {
        super(page)
        this.pluginsAppliedRow = this.page.locator(`//*[contains(@class,'mandatory-plugin-list__table-row')and contains(@class,'last-child')]`);
        this.addPluginButton = this.page.locator(`//*[text()="Add plugin" or text()="Add Plugin"]`);
        this.prePostCiCdPage = new PrePostCiCd(this.page);
        this.doneButton = this.page.getByTestId("done-button");
        this.saveProfileButton = this.page.getByTestId(`plugin-profile__save`);
        this.lockConfigPage = new LockConfiguration(page);
        this.threeButtonsOnAppliedProfilesPage = this.page.locator(`//div[contains(@class,"applied-profiles-list__table-row") and contains(@class,"visible")]//button`);
        this.deleteButton = this.page.locator(`//*[text()="Delete"]`);
        this.noOffendingPipelonesOkButton = this.page.getByTestId(`acknowledge-no-offending-pipelines`);
    }


    /**
     * checks whether a plugin with provided configuration exist or not , if does not exist will add new one , if exists but with 
     * wrong detils , will update the details
     * 
     * @param data ->> isCi( whether we are setting on ci or on cd)
     */
    async editConfigurationForPluginPolicy(data: { isCi: boolean, plugins: { pluginName: string, stage: string }[], consequences: string }) {
        let checkboxValueForCiCd: string = data.isCi ? 'CI_PLUGIN' : 'CD_PLUGIN';
        let valueForConsequences: string = data.consequences == "Allow respective triggers with warning" ? 'ALLOW_FOREVER' : data.consequences == "Block respective triggers immediately" ? 'BLOCK' : 'ALLOW_UNTIL_TIME';
        await this.page.locator(`//*[@value="${checkboxValueForCiCd}"]/parent::label`).click();
        await this.page.waitForTimeout(4000);
        if (data.plugins) {
            let pluginsAppliedRow: Locator[] = await this.pluginsAppliedRow.all();
            let existingPlugins: string[] = [];
            for (let key of pluginsAppliedRow) {
                let value = await key.textContent();
                if (value) {
                    const key = /Pre|Post|Select/;
                    let indexToCut = value.search(key);
                    let valueToAdd = indexToCut != -1 ? value.substring(0, indexToCut) : value;
                    existingPlugins.push(valueToAdd);
                }
            }
            for (let key of existingPlugins) {
                console.log('printing pushed value');
                console.log(key);
            }
            for (let key of data.plugins) {
                if (existingPlugins.length != 0 && existingPlugins.includes(key.pluginName)) {
                    if (await this.page.locator(`//*[text()="${key.pluginName}"]/parent::div//input[contains(@name,'enforce')]`).getAttribute('value') != key.stage) {
                        await expect(async () => {
                            await this.page.locator(`//*[text()="${key.pluginName}"]/parent::div//*[contains(@class,'stage__control')]`).click();
                            await this.page.locator(`//*[@role="listbox"]//*[text()="${key.stage}"]`).click();
                        }).toPass({ timeout: 4 * 1000 * 60 });
                    }
                }
                else {
                    await this.addPluginButton.click();
                    await this.prePostCiCdPage.searchAndSelectPluginorTaskFromList(key.pluginName);
                    await this.doneButton.click();
                    await this.page.locator(`//*[text()="${key.pluginName}"]/parent::div//*[contains(@class,'stage__control')]`).click();
                    await this.page.locator(`//*[@role="listbox"]//*[text()="${key.stage}"]`).click();
                }
            }
        }
        await this.page.locator(`//*[@value="${valueForConsequences}"]/parent::label`).click();
        await this.saveProfileButton.click();
    }



    /**
     * this function will simply let u navigate to this page 
     */
    async navigateToMandatoryPluginPage() {
        await this.page.goto(process.env.BASE_SERVER_URL + '/global-config/plugin-policy/profiles');
    }



    /**
     * call this function to set configuration while applying a profile to global or match criteria level
     * @param profileName 
     * @param data  -> (applying type=Global or Criteria)-> values to be used
     * 
     */

    async settingConfigurationToApplyAProfile(profileName: string, data: { applyingType: string, matchCriteriaData?: { Project: string, Application: string, Cluster: string, Environment: string } }) {
        await this.lockConfigPage.selectProfileFromDropdown(profileName);
        await this.page.locator(`//*[@value="${data.applyingType}"]/parent::label`).click();
        if (data.matchCriteriaData) {
            let filterKeys = Object.keys(data.matchCriteriaData!);
            for (let key of filterKeys) {
                await this.lockConfigPage.filterPipelineDiv.locator(`//*[text()="${key}"]`).click();
                await this.lockConfigPage.selectValuesFromDropdown(data.matchCriteriaData[key]);
            }
        }
        await this.lockConfigPage.profileSaveButton.click();
    }



    /**
     * this function is for basic use , it applied a filter on a profile , and picks the first occurence and delete that 
     * @param data 
     */
    async removeAppliedProfile(data: { filterType: string, filterValue: string[], clearPreviousFilter: boolean }) {
        await this.lockConfigPage.appliedProfilesTab.click();
        await this.lockConfigPage.clickOnFilterAndSelectValue(data);
        await this.threeButtonsOnAppliedProfilesPage.click();
        await this.deleteButton.click();
        await this.deleteModalInputField.fill('delete');
        await this.dialogDeleteConfirmationButton.click();
    }



    /**
     * use this function to check pipelines are non comliance or not , basic flow is written as of now , assuming single app is getting impacted
     * @param profileName 
     * @param willThereBeAnyPipeline  (will there be any non compliance pipeline )
     * @returns 
     */
    async checkNonCompliancePipelinesAndNavigateToPipelines(profileName: string, willThereBeAnyPipeline: boolean): Promise<Page | null> {
        await this.lockConfigPage.profilesTab.click();
        await this.page.locator(`//*[contains(@class,'plugin-profiles-list__table-row')]//*[text()="${profileName}"]`).hover();
        await this.page.locator(`//*[@data-testid="show-non-compliant-pipeline-for-${profileName}"]`).click();
        if (willThereBeAnyPipeline) {
            let [newPage] = await Promise.all([this.page.waitForEvent('popup'), this.page.locator(`//*[contains(@class,'warning-icon')]`).click()]);
            return newPage;
        }
        else {
            await this.noOffendingPipelonesOkButton.click();
        }
        return null;
    }



    /**
     * use this function to delete a profile
     * @param profileName 
     */
    async delteProfileCreated(profileName: string) {
        await this.page.locator(`//a[text()="${profileName}"]`).hover();
        await this.page.locator(`//*[@data-testid="delete-${profileName}"]`).click();
        await this.deleteModalInputField.fill(profileName);
        await this.dialogDeleteConfirmationButton.click();


    }

}

