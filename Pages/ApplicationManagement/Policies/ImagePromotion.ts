import { Locator, <PERSON> } from "playwright";
import { BasePage } from "../../BasePage";

export class ImagePromotion extends BasePage {
    readonly createImagePromotionProfile: Locator
    readonly profileNameInputField: Locator;
    readonly profileDescriptionInputField: Locator;
    readonly turnOnApprovalsRequiredToggle: Locator;
    readonly turnOnApprovalRequiredToggleInput: Locator;
    readonly genericLocatorForNumberOfApprovers: Locator;
    readonly approverCanDeployCheckboxSpan: Locator;
    readonly requesterCanApproveCheckboxSpan: Locator;
    readonly imageBuilderCanApproveCheckboxSpan: Locator;
    readonly saveProfileButton: Locator;
    readonly passFailConditionValueTextbox: Locator;
    readonly profileSearchBar: Locator;
    readonly profileListDiv: Locator;

    constructor(public page: Page) {
        super(page);
        this.createImagePromotionProfile = this.page.getByTestId('create-profile');
        this.profileNameInputField = this.page.getByTestId('policyName');
        this.profileDescriptionInputField = this.page.getByTestId('description');
        this.turnOnApprovalsRequiredToggle = this.page.getByTestId('toggle-manual-approval');
        this.turnOnApprovalRequiredToggleInput = this.turnOnApprovalsRequiredToggle.locator('xpath=preceding-sibling::input');
        this.genericLocatorForNumberOfApprovers = this.page.getByTestId(`//*[class="manual-approvals-switch flex left radio-group"]`);
        this.approverCanDeployCheckboxSpan = this.page.getByTestId(`allow-approver-from-deploy-chk-span`);
        this.requesterCanApproveCheckboxSpan = this.page.getByTestId('allow-requester-from-approve-chk-span');
        this.imageBuilderCanApproveCheckboxSpan = this.page.getByTestId('allow-image-builder-from-approve-chk-span');
        this.saveProfileButton = this.page.locator(`//*[text()='Save Changes']`);
        this.passFailConditionValueTextbox = this.page.getByTestId('cel-expression');
        this.profileSearchBar = this.page.locator(`//*[@placeholder="Search Policy"]`);
        this.profileListDiv = this.page.locator(`//*[contains(@class,'image-promotion-list__table')]//*[contains(@class,'image-promotion__profile-list__row ')]`);

    }



    async createProfile(data: { profileName: string, description?: string, passFailConditions: { conditionType: 'Pass' | "Fail", conditionValue: string }, approvalConfig?: { requiredApproval: number, approverCanDeploy?: boolean, requesterCanApprove?: boolean, builderCanApprove?: boolean } }) {
        await this.createImagePromotionProfile.click();
        await this.profileNameInputField.fill(data.profileName);
        data.description ? await this.profileDescriptionInputField.fill(data.description) : null;
        await this.setUpPassFailCondition(data.passFailConditions);
        await this.configureApprovalsForAProfile(data.approvalConfig);
        await this.saveProfileButton.click();

    }
    async setUpPassFailCondition(data: { conditionType: 'Pass' | "Fail", conditionValue: string }) {
        await this.page.locator(`//*[contains(text(),'${data.conditionType}')]`).click();
        await this.passFailConditionValueTextbox.click();
        await this.page.keyboard.type(data.conditionValue);
    }
    async configureApprovalsForAProfile(data?: { requiredApproval: number, approverCanDeploy?: boolean, requesterCanApprove?: boolean, builderCanApprove?: boolean }) {
        let turnOnApproval: boolean = data || data!.requiredApproval != 0 ? true : false;
        if (await this.turnOnApprovalRequiredToggleInput.isChecked() != turnOnApproval) {
            await this.turnOnApprovalsRequiredToggle.click();
        }
        if (data && data.requiredApproval != 0) {
            await this.page.locator(`//*[text()="${data.requiredApproval}"]`).locator(`xpath=parent::label`).click();
            data.approverCanDeploy ? await this.approverCanDeployCheckboxSpan.click() : null;
            data.builderCanApprove ? await this.imageBuilderCanApproveCheckboxSpan.click() : null;
            data.requesterCanApprove ? await this.requesterCanApproveCheckboxSpan.click() : null;
        }

    }
    async searchAnImagePromotionProfile(profileName: string) {
        await this.profileSearchBar.fill(profileName);
        await this.page.keyboard.press('Enter');

    }
    async deleteAProfile(profileName: string) {
        await this.searchAnImagePromotionProfile(profileName);
        await this.profileListDiv.locator(`//*[text()='${profileName}']`).hover();
        await this.page.getByTestId(`profile-delete-${profileName}`).click();
        await this.dialogDeleteConfirmationButton.click();
    }
}