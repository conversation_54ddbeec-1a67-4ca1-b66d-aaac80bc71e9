import { Page, Locator, expect, Keyboard } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { BasePage } from '../../BasePage';
import { ApplyProfileScopeCategoryEnum } from '../../../enums/ApplicationManagement/Policies/LockConfigurationPageEnums';
import { applyProfileDTO } from '../../../DTOs/ApplicationManagement/Policies/LockConfigurationPageDTO';

export class LockConfiguration extends BasePage {
    readonly createProfileButton: Locator;
    readonly profileNameInputField: Locator;
    readonly searchProfileNameInputField: Locator;
    readonly clearSearchIcon: Locator;
    readonly profileSaveButton: Locator;
    readonly applyProfileButton: Locator;
    readonly profileSelectIonDropdownInputField: Locator;
    readonly filterPipelineDiv: Locator;
    readonly dropdownCloseIcon: Locator;
    readonly appliedProfilesTab: Locator;
    readonly manageProfilesButton: Locator;
    readonly profilesTab: Locator;
    readonly clearFilterButton: Locator;
    readonly genericEmptyState: Locator;
    readonly profileDropdownIndicator: Locator;
    readonly multiSelectOptionOuterDiv: Locator;
    readonly checkBoxInsideMutltiselect: Locator;
    readonly profileRemovalToggleButton: Locator;
    readonly removedProfilesSign: Locator;
    readonly appliedProfilesDiv: Locator;
    readonly clearAllFilterButton: Locator;
    readonly bulkSelectAllOnThisPageButton: Locator;
    // old code editor
    readonly monacoEditorTextArea: Locator;


    constructor(public page: Page) {
        super(page);
        this.createProfileButton = this.page.locator('//*[text()="Create Profile"]');
        this.profileNameInputField = this.page.locator(`//input[contains(@data-testid,'-name')]`);
        this.searchProfileNameInputField = this.page.getByTestId("search-bar");
        this.clearSearchIcon = this.page.locator('//*[@aria-label="Clear search"]');
        this.profileSaveButton = this.page.locator('//*[text()="Save Changes"]');
        this.applyProfileButton = this.page.locator('//*[text()="Apply Profile"]');
        this.profileSelectIonDropdownInputField = this.page.locator('//*[@class="profile-selection"]');
        this.filterPipelineDiv = this.page.locator('//*[text()="Filter by" or text()="Add match criteria"]/parent::aside');
        this.dropdownCloseIcon = this.page.locator('//*[contains(@class,"profile-criterion-selector__indicator ")]').nth(1);
        this.appliedProfilesTab = this.page.locator(`//*[@role="tablist"]`).locator('//span[text()="Applied Profiles"]');
        this.manageProfilesButton = this.page.locator('//*[text()="Manage Profiles"]');
        this.profilesTab = this.page.locator('//*[text()="Profiles"]');
        this.clearFilterButton = this.page.locator('//*[text()="Clear Filters"]');
        this.genericEmptyState = this.page.getByTestId('generic-empty-state');
        this.profileDropdownIndicator = this.page.locator('//*[contains(@class,"profile-selection__dropdown-indicator")]');
        this.multiSelectOptionOuterDiv = this.page.locator('//*[@aria-multiselectable="true"]');
        this.checkBoxInsideMutltiselect = this.page.locator(`//*[contains(@class,'profile-criterion-selector__option') or contains(@class,'applied-profiles__specific-table-row')]//*[@class="form__checkbox-container"]`);
        this.removedProfilesSign = this.page.locator("//*[contains(@class,'dc__strike-through')]");
        this.appliedProfilesDiv = this.page.locator("//*[contains(@class,'applied-profiles-list__table-row')]");
        this.clearAllFilterButton = this.page.locator('//*[text()="Clear All Filters"]');
        this.profileRemovalToggleButton = this.page.locator('//*[contains(@aria-label,"Toggle ")]');
        this.monacoEditorTextArea = this.page.locator('//*[@class="inputarea monaco-mouse-cursor-text"]');
        this.bulkSelectAllOnThisPageButton = this.page.getByTestId('bulk-selection-pop-up-menu');
    }

  


    /**
     * use this method to create or edit profile 
     * @param profileName  name that you want to make profile of 
     * @param lockValue value that you want to enter in profile to lock 
     */
    async createOrEditProfile(profileName: string, lockValue: string) {
        await this.clickOnProfileOrCreateNew(profileName);
        //revert new code editor
        await this.codeMirrorEditorTextArea.clear();
        await this.codeMirrorEditorTextArea.fill(lockValue);
        await this.profileSaveButton.click();
    }

    async clickOnProfileOrCreateNew(profileName: string) {
        await this.profilesTab.click();
        try {
            await this.searchProfileNameInputField.fill(profileName, { timeout: 5000 });
            await this.page.keyboard.press("Enter");
            try {
                await this.page.locator('//*[text()="No results"]').waitFor({ timeout: 2000 });
                await this.createProfileButton.first().click();
                await this.profileNameInputField.fill(profileName);
            }
            catch (e) {
                await this.page.locator(`//span[text()="${profileName}"]`).click();
            }
        }
        catch (error) {
            await this.createProfileButton.first().click();
            await this.profileNameInputField.fill(profileName);
        }
    }


    /**
     * this method is used to apply the profile to a specific scope 
     * @param data 
     */
    async applyProfile(data: applyProfileDTO) {
        await this.selectProfileFromDropdown(data.profileName);
        await this.page.locator(`//*[text()="${data.scope}"]`).click();
        if (data.scope == "Specific deployment templates" || data.scope == "By match criteria" || data.scope == "Specific criteria") {
            for (let i = 0; i < data.filterType.length; i++) {
                console.log(data.filterType[i].filterName);
                console.log(data.filterType[i].filterValue);
                await this.filterPipelineDiv.locator(`//*[text()="${data.filterType[i].filterName}"]`).click();
                if (data.filterType[i].filterName != "Base deployment template" && data.filterType[i].filterName != "Base configuration") {
                    await this.selectValuesFromDropdown(data.filterType[i].filterValue);
                }
                if ((data.scope == "Specific deployment templates" || data.scope == "Specific criteria") && data.filterType[i].filterName != "Base deployment template" && i == data.filterType.length - 1) {
                    await this.checkBoxInsideMutltiselect.first().click();
                }
            }
        }
        await BaseTest.checkToast(this.page, this.profileSaveButton, "Success");
    }

    async selectProfileFromDropdown(profileName: string) {
        await this.applyProfileButton.click();
        await this.page.locator(`//*[contains(@class,'profile-selection__control')]`).click();
        await this.page.keyboard.type(profileName);
        await this.page.keyboard.press('Enter');
        await this.profileDropdownIndicator.click({ delay: 1000 });
    }

    async selectValuesFromDropdown(value: string) {
        try {
            await this.multiSelectOptionOuterDiv.locator(this.checkBoxInsideMutltiselect).first().waitFor({ timeout: 6000 });
        }
        catch (error) {
            console.log('it was for branch type ');
        }
        await this.page.keyboard.type(value, { delay: 300 });
        await this.page.keyboard.press('Enter');
        await this.dropdownCloseIcon.click({ delay: 400 });
    }


    /**
     * use this function to bulk add or remove profiles , for add use text Add , for remove use Remove , for bulk operation use all
     * @param data 
     */
    async bulkAdditionOrRemovalOFprofiles(data: any[]) {
        await this.appliedProfilesTab.click();
        await this.bulkSelectionApplyProfileCheckbox.click();
        await this.bulkSelectAllOnThisPageButton.click();
        await this.manageProfilesButton.click();
        for (const key of data) {
            await this.page.locator(`//*[text()="${key.stage}"]`).click();
            if (key.stage == "Add") {
                await this.profileSelectIonDropdownInputField.fill(key.profileName);
                await this.page.keyboard.press('Enter');
            }
            else {
                if (key.profileName == "all") {
                    var allProfiles = await this.profileRemovalToggleButton.all();
                    for (const element of allProfiles) {
                        await element.click();
                    }
                    await expect(this.removedProfilesSign).toHaveCount(allProfiles.length);
                }
                else {
                    await this.page.locator(`//*[@aria-label="Toggle ${key.profileName} profile"]`).click();
                }
            }
        }
        await this.profileSaveButton.click();
    }


    /**
     * this function is used for bulk deletion of profiles 
     * @param profileNames 
     */
    async deleteProfile(profileNames: string[]) {
        for (const key of profileNames) {
            try {
                await this.clearFilterButton.click({ timeout: 5000 });
            } catch (error) {
                console.log('clearFilterDidNotAppear');
            }
            await this.searchProfileNameInputField.fill(key);
            await this.page.keyboard.press('Enter');
            await this.page.locator(`//span[text()="${key}"]`).hover();
            await this.page.locator(`//*[@aria-label="Delete ${key}"]`).click();
            await this.deleteModalInputField.fill(key);
            await this.dialogDeleteConfirmationButton.click();
        }

    }



    /**
     * 
     * @param data filter type means which level of filter we want tp apply , null state if after applying the filter we get a null state , clear previous filters to clear previous applied filters
     */
    async applyFilterAndVerifyOutput(data: [{ filterType: string, filterValue: string[], isNullState?: boolean, ClearPreviousFilter: boolean, verificationOfFields?: [{ scope: string, profileValue: string }] }]) {
        await this.appliedProfilesTab.click();
        for (const key of data) {
            await this.clickOnFilterAndSelectValue({ filterType: key.filterType, filterValue: key.filterValue, clearPreviousFilter: key.ClearPreviousFilter })
            if (key.verificationOfFields) {
                for (const verificationData of key.verificationOfFields) {
                    await this.appliedProfilesDiv.nth(1).waitFor({ timeout: 12000 });
                    let allElements = await this.appliedProfilesDiv.all();
                    let final = await Promise.all(
                        allElements.filter(async (element) => {
                            let result = await element.textContent();
                            console.log(result?.includes(verificationData.scope) && result.includes(verificationData.profileValue));
                            return (result?.includes(verificationData.scope) && result.includes('Global'))
                        })
                    )
                    expect(final.length).toBeGreaterThan(0);
                }
            }
            if (key.isNullState) {
                await expect(this.genericEmptyState).toBeVisible({ timeout: 6000 });
            }
        }

    }

    async clickOnFilterAndSelectValue(data: { filterType: string, filterValue: string[], clearPreviousFilter: boolean }) {
        await expect(async () => {
            if (data.clearPreviousFilter) {
                await this.RemoveAllAppliedFilters();
            }
            await this.clickOnFiltersDropDowns(data.filterType);
            for (const value of data.filterValue) {
                await this.page.keyboard.type(value, { delay: 200 });
                await this.page.keyboard.press("Enter", { delay: 400 });
            }
            await this.page.locator('//*[text()="Apply"]').click()
        }).toPass({ timeout: 5 * 1000 * 60 });

    }


    /**
     * function to remove previously applied filters if any 
     */
    async RemoveAllAppliedFilters() {
        try {
            await this.clearAllFilterButton.waitFor({ timeout: 6000 });
            await this.clearAllFilterButton.click();
        }
        catch (error) {
            console.log('clear filter button not visible');
        }

    }
    async clickOnFiltersDropDowns(filterName: string) {
        await this.page.locator(`//*[contains(@class,'${filterName}-filter__control')]`).click();
        await this.page.locator(`//*[contains(@class,'${filterName}-filter__menu-list')]//*[@data-testid='undefined-chk-span']`).first().waitFor();
    }


}