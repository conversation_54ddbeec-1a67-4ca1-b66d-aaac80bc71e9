import { Locator, <PERSON> } from "@playwright/test";
import { LockConfiguration } from "./LockConfiguration";
import { exceptionCategoryEnum } from "../../../enums/Application Management/Policies/ApprovalPolicyPageEnums";
import { BasePage } from "../../BasePage";
import { applyApprovalPolicyScopeCategory, setNumberOfApprovers } from "../../../DTOs/Application Management/Policies/ApprovalPolicyPageDTO";
import { applyProfileDTO } from "../../../DTOs/Application Management/Policies/LockConfigurationPageDTO";

export class ApprovalPolicyPage extends BasePage {
    readonly anyApproverRadioBox: Locator;
    readonly specificApproverRadioBox: Locator;
    readonly addCriteriaButton: Locator;
    readonly userGroupDropdown: Locator;
    readonly approverCountWrapper: Locator;
    readonly removeUserGroupIcon: Locator;
    readonly specificUserDropdownSelect: Locator;
    readonly approvalPolicySaveButton: Locator;
    readonly applyingStrategyDropdown: Locator;
    readonly lockConfigPage: LockConfiguration;
    //Expection page locator
    readonly configurationChangeButton: Locator;
    readonly SuperAdminExceptionToggleButton: Locator;
    readonly superAdminExceptionToggleInputField: Locator;
    readonly approvalPolicyAddExceptionUsersButton: Locator;
    readonly exceptionUsersDropdownLocator: Locator;
    readonly alreadyAddedExceptionUsersDiv: Locator;
    readonly exceptionsTab: Locator;
    readonly addUserInExceptionListSaveButton: Locator;
    //*[text()="Configuration change approval"]
    readonly ConfigurationChangeApprovalButton: Locator;
    readonly deleteExceptionUserIcon: Locator;
    readonly deploymentApprovalButton: Locator;



    constructor(public page: Page) {
        super(page);
        this.anyApproverRadioBox = this.page.locator(`//*[text()="Any Approver"]`);
        this.addCriteriaButton = this.page.locator(`//*[text()="Add Criteria"]`);
        this.userGroupDropdown = this.page.locator(`//*[contains(@class,'user-group-selector-0__control')]`);
        this.approverCountWrapper = this.page.locator(`//*[contains(@class,'manual-approvals-switch')]`);
        this.removeUserGroupIcon = this.page.locator(`//*[contains(@aria-label,"Delete user group:")]`);
        this.specificUserDropdownSelect = this.page.locator(`//*[contains(@class,"specific-user-and-token-selector__control")]`);
        this.approvalPolicySaveButton = this.page.getByTestId(`approval-policy__save`);
        this.specificApproverRadioBox = this.page.locator(`//*[text()="Specific Approver"]`);
        this.applyingStrategyDropdown = this.page.locator(`//*[contains(@class,'applicable-to-type-selector__control')]`);
        this.configurationChangeButton = this.page.getByText(`Configuration Change`);
        this.SuperAdminExceptionToggleButton = this.page.getByTestId(`approval-policy-exception-super-admin-toggle`);
        this.superAdminExceptionToggleInputField = this.SuperAdminExceptionToggleButton.locator('xpath=preceding-sibling::input');
        this.approvalPolicyAddExceptionUsersButton = this.page.getByTestId('approval-policy-exception-add-user-user-groups');
        this.lockConfigPage = new LockConfiguration(page)
        this.ConfigurationChangeApprovalButton = this.page.locator(`//*[text()="Configuration change approval"]`);
        this.deploymentApprovalButton = this.page.locator(`//*[text()="Deployments approval"]`);
        this.exceptionUsersDropdownLocator = this.page.locator(`//*[contains(@class,'exception-users-dropdown__control')]`);
        this.alreadyAddedExceptionUsersDiv = this.page.locator(`//*[contains(@class,'policy-exception__user-list-table ')]`);
        this.deleteExceptionUserIcon = this.page.getByTestId('approval-policy-exception-delete-user-user-groups');
        this.exceptionsTab = this.page.locator(`//*[text()="Exceptions"]`);
        this.addUserInExceptionListSaveButton = this.page.getByTestId(`add-user-groups-modal-save`);
    }
    async navigateToApprovalPolicyPage() {
        await this.page.goto(process.env.BASE_SERVER_URL! + `/global-config/approval-policy/policy`);
    }


    async setNumberOfApproversAndSaveProfile(data: setNumberOfApprovers) {
        if (data.normalApproverCount) {
            await this.anyApproverRadioBox.click();
            await this.approverCountWrapper.locator(`//*[text()="${data.normalApproverCount}"]`).click();
        }
        if (data.userGroupDetails) {
            await this.specificApproverRadioBox.click();
            if (!await this.page.locator(`//*[text()="${data.userGroupDetails.name}"]`).isVisible()) {
                await this.addCriteriaButton.click();
                await this.userGroupDropdown.click();
                await this.page.keyboard.type(data.userGroupDetails.name);
                await this.page.keyboard.press("Enter");
                await this.approverCountWrapper.locator(`//*[text()="${data.userGroupDetails.count}"]`).click();
            }
        }
        if (data.specificUsers) {
            if (data.specificUsers.clearUserGroup && await this.removeUserGroupIcon.first().isVisible()) {
                let allUserGroups = await this.removeUserGroupIcon.all();
                for (let key of allUserGroups) {
                    await key.click();
                }
            }
            await this.specificApproverRadioBox.click();
            await this.specificUserDropdownSelect.click();
            for (let key of data.specificUsers.names) {
                await this.page.keyboard.type(key);
                await this.page.keyboard.press('Enter');
            }
        }
        if (await this.approvalPolicySaveButton.isDisabled()) await this.page.keyboard.press('Enter');
        await this.approvalPolicySaveButton.click();
    }

    async selectScopeWhileApplyingPolicy(data: applyApprovalPolicyScopeCategory) {
        await this.lockConfigPage.applyProfileButton.click();
        await this.applyingStrategyDropdown.click();
        if (data.configuration) {
            await this.ConfigurationChangeApprovalButton.click();
            let childConfigTypes = {
                configMaps: 'config-map',
                secrets: 'config-secret',
                dt: 'deployment-template'
            }
            let iterableKeys = Object.keys(childConfigTypes);
            for (let key of iterableKeys) {
                if (await this.page.locator(`//*[@id="applicable-to-configuration/${childConfigTypes[key]}"]`).first().isChecked() != data.configuration[key]) {
                    await this.page.locator(`//*[@id="applicable-to-configuration/${childConfigTypes[key]}"]/following-sibling::span`).first().click();
                }
            }
        }
        else {
            await this.deploymentApprovalButton.click();
        }

    }

    async navigateToExceptionPage() {
        await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/approval-policy/exceptions`);
    }



    async setOrRemoveExceptionUsersForProtectConfig(configurationData: { allowSuperAdmins?: boolean, specificUsersData?: { userName: string, addUser: boolean }[], exceptionFor: exceptionCategoryEnum }) {
        await this.navigateToExceptionPage();
        await this.exceptionsTab.click();
        await this.page.locator(`//*[text()="${configurationData.exceptionFor}"]`).click();
        if (configurationData.allowSuperAdmins != undefined) {
            await this.enableOrDisableSuperAdminsForExceptions(configurationData.allowSuperAdmins);
        }
        if (configurationData.specificUsersData) {
            await this.addOrRemoveSpecificUsersAndGroupsInExceptionList(configurationData.specificUsersData);
        }
    }


    async enableOrDisableSuperAdminsForExceptions(allowSuperAdmins: boolean) {
        if (await this.superAdminExceptionToggleInputField.isChecked() != allowSuperAdmins)
            await this.SuperAdminExceptionToggleButton.click();
    }

    async addOrRemoveSpecificUsersAndGroupsInExceptionList(userAndGroupData: { userName: string, addUser: boolean }[]) {
        for (let key of userAndGroupData) {
            if (key.addUser) {
                await this.approvalPolicyAddExceptionUsersButton.click();
                await this.exceptionUsersDropdownLocator.click();
                if (await this.exceptionUsersDropdownLocator.textContent().then(text => text && text.includes(key.userName))) {
                    continue;
                }
                await this.page.locator(`//*[@role="option"]`).locator(`//*[text()="${key.userName}"]`).click();
                await this.exceptionUsersDropdownLocator.click();
                await this.addUserInExceptionListSaveButton.click();
            }
            else {
                if (await this.alreadyAddedExceptionUsersDiv.locator(`//*[text()="${key.userName}"]`).isVisible()) {
                    await this.returnRowLocatorOfAlreadyAddedExceptionUsers(key.userName).locator(this.deleteExceptionUserIcon).click();
                    await this.dialogDeleteConfirmationButton.click();
                }
            }
        }
    }


    returnRowLocatorOfAlreadyAddedExceptionUsers(userName: string): Locator {
        return this.alreadyAddedExceptionUsersDiv.locator(`//*[text()="${userName}"]/ancestor::div[contains(@class,'policy-exception__user-list-table-row')]`);
    }

    async createApprovalPolicyProfile(data: { profileName: string, setUserConfigurationForApprovalPolicy: setNumberOfApprovers, lockConfigPage: LockConfiguration }) {
        await this.navigateToApprovalPolicyPage();
        await data.lockConfigPage.clickOnProfileOrCreateNew(data.profileName);
        await this.setNumberOfApproversAndSaveProfile(data.setUserConfigurationForApprovalPolicy);
    }
    async applyApprovalPolicyProfile(data: { applyPolicyData: applyProfileDTO, scope: applyApprovalPolicyScopeCategory, lockConfigPage: LockConfiguration }) {
        await this.selectScopeWhileApplyingPolicy(data.scope);
        await data.lockConfigPage.applyProfile(data.applyPolicyData);

    }
}
