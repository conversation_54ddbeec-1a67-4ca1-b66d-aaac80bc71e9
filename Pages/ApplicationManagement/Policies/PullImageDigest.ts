import { Page, Locator, expect } from '@playwright/test'; // Importing necessary modules
export class ImagePullDigest {
  readonly globalConfiguration: Locator
  readonly pullImageDigestPage: Locator
  readonly searchPullImageDigest: Locator
  readonly toggleButtonPullImage: Locator
  readonly environmentCheckBox: Locator
  readonly saveEnvironmentCheckBox: Locator
  readonly infoTippyButton: Locator;
  readonly globalImagePullDigestToggleButton: Locator;
  readonly globalImagePullDigestInputToggle: Locator;

  constructor(private page: Page) {

    this.globalConfiguration = page.getByTestId('click-on-global-configuration')
    this.pullImageDigestPage = page.getByText('Pull Image Digest')
    this.searchPullImageDigest = page.getByTestId('search-bar')
    this.toggleButtonPullImage = page.getByTestId('create-build-pipeline-image-pull-digest-toggle')
    this.environmentCheckBox = page.getByTestId('enforce-policy-chk-span')
    this.saveEnvironmentCheckBox = page.getByTestId('save-changes')
    this.infoTippyButton = this.page.getByTestId('info-tippy-button');
    this.globalImagePullDigestToggleButton = this.page.getByTestId('toggle-pull-image-digest-global');
    this.globalImagePullDigestInputToggle = this.globalImagePullDigestToggleButton.locator('xpath=preceding-sibling::input');
  }

  async openPullImageDigest() {


    await this.pullImageDigestPage.click()

  }

  async selectEnvironment(allEnv: boolean, envName: string) {

    {
      await this.infoTippyButton.waitFor({ timeout: 11000 });
      if (allEnv && (!await this.checkWhetherGlobalImagePullDigestIsOn()))  // for checking whether all the environments are ticked or not
      {
        await this.globalImagePullDigestToggleButton.first().click();
      }
      else if (envName != '') {
        if (await this.checkWhetherGlobalImagePullDigestIsOn()) {
          await this.globalImagePullDigestToggleButton.first().click();
        }
        await this.searchPullImageDigest.click()
        await this.searchPullImageDigest.fill(envName)
        await this.page.keyboard.press('Enter')
        if (!await this.environmentCheckBox.last().locator(`xpath=preceding-sibling::input`).isChecked()) {
          await this.environmentCheckBox.last().click()
        }
      }

      await this.saveEnvironmentCheckBox.click()
    }
  }

  async checkWhetherGlobalImagePullDigestIsOn() {
    await this.infoTippyButton.waitFor({ timeout: 11000 });
    if (await this.globalImagePullDigestInputToggle.first().isChecked()) {
      return true;
    }
    else {
      return false;
    }
  }
}


