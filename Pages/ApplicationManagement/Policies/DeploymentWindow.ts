import { Locator, <PERSON> } from "playwright";
import { AllTypes } from "../../../utilities/Types";
import { BasePage } from "../../BasePage";
import { BaseTest } from "../../../utilities/BaseTest";
import { expect } from "playwright/test";

export class DeploymentWindow extends BasePage {
    readonly addWindowButton: Locator;
    readonly windowNameInputField: Locator;
    readonly timeZoneDropdown: Locator;
    readonly superAdminCheckboxSpan: Locator;
    readonly specificUserCheckboxSpan: Locator;
    readonly specificUsersDropdown: Locator;
    readonly saveChangesButton: Locator;
    readonly applyToButton: Locator;
    readonly appFilterDropdown: Locator;
    readonly envFilterDropdown: Locator;
    readonly dwDropdown: Locator;
    readonly filterApplyButton: Locator;
    readonly setDateFromDropdown: Locator;
    readonly setDateToDropdown: Locator;
    readonly setTimeFromDropdown: Locator;
    readonly setTimeToDropdown: Locator;
    readonly setStartDateCheckbox: Locator;
    readonly setEndDateCheckbox: Locator;
    readonly windowConfigurationDiv: Locator;
    readonly weekDayFromSelectDropdown: Locator;
    readonly weekDayToSelectDropdown: Locator;
    readonly doneButton: Locator;
    readonly searchBar: Locator;
    readonly addDurationButton: Locator;
    readonly removeAlreadyAddedConfiguration: Locator;
    readonly bulkSelectCheckbox: Locator;
    readonly bulkSelectAllOnThisPage: Locator;
    readonly manageWindowsButton: Locator;
    readonly deploymentWindowProfileDrodpdownBulkSelectAddModal: Locator
    readonly saveButton: Locator;
    readonly deleteButton: Locator;





    constructor(public page: Page) {
        super(page);
        this.addWindowButton = this.page.locator(`//*[text()="Add Window"]`);
        this.windowNameInputField = this.page.getByTestId(`windowName`);
        this.timeZoneDropdown = this.page.locator(`//*[contains(@class,'Select time zone__value-container ')]`);
        this.superAdminCheckboxSpan = this.page.getByTestId('super-admin-access-checkbox-chk-span');
        this.specificUserCheckboxSpan = this.page.getByTestId('specific-users-access-checkbox-chk-span');
        this.specificUsersDropdown = this.page.locator(`//*[contains(@class,'Select users__control')]`);
        this.saveChangesButton = this.page.locator(`//*[text()='Save Changes']`);
        this.applyToButton = this.page.locator(`//*[text()="Apply To"]`);
        this.appFilterDropdown = this.page.locator(`//*[contains(@class,'application-filter__control')]`);
        this.envFilterDropdown = this.page.locator(`//*[contains(@class,'environment-filter__control')]`);
        this.dwDropdown = this.page.locator(`//*[contains(@class,'deployment-window-filter__control')]`);
        this.filterApplyButton = this.page.getByTestId('filter-select-picker-apply');
        this.setDateFromDropdown = this.page.locator(`//*[contains(@id,'time-from')]/parent::div`).first();
        this.setDateToDropdown = this.page.locator(`//*[contains(@id,'time-to')]/parent::div`).first();
        this.setTimeFromDropdown = this.page.locator(`//*[contains(@class,'time_picker__control')]`).first();
        this.setTimeToDropdown = this.page.locator(`//*[contains(@class,'time_picker__control')]`).nth(1);
        this.setStartDateCheckbox = this.page.getByTestId(`start-date-chk-span`);
        this.setEndDateCheckbox = this.page.getByTestId(`end-date-chk-span`);
        this.windowConfigurationDiv = this.page.locator(`//*[contains(@class,'window-configuration')]`);
        this.weekDayFromSelectDropdown = this.page.locator(`//*[contains(@class,'week-day-to-select__control')]`);
        this.weekDayToSelectDropdown = this.page.locator(`//*[contains(@class,'week-day-from-select__control')]`);
        this.doneButton = this.page.locator(`//*[text()='Done']`);
        this.searchBar = this.page.getByTestId(`search-bar`);
        this.addDurationButton = this.page.getByTestId('add-window-duration')
        this.removeAlreadyAddedConfiguration = this.page.locator(`//*[@class="icon-dim-16 cursor"]`).nth(1);
        this.bulkSelectCheckbox = this.page.locator(`//*[@aria-label="Bulk selection dropdown"]`);
        this.bulkSelectAllOnThisPage = this.page.getByTestId('action-menu-item-SELECT_ALL_ON_PAGE');
        this.manageWindowsButton = this.page.locator(`//*[text()="Manage Windows"]`);
        this.deploymentWindowProfileDrodpdownBulkSelectAddModal = this.page.locator(`//*[contains(@class,'deployment-window-profile-select__control')]`);
        this.saveButton = this.page.locator(`//*[text()='Save']`);
        this.deleteButton = this.page.locator(`//*[text()='Delete']`);
    }

    async navigateToDeploymentWindow() {
        await this.page.goto(process.env.BASE_SERVER_URL! + '/global-config/deployment-window/list');
    }

    async selectMaintainanceAndBlackoutWindow(isMaintenance: boolean) {
        let valueToSelect = isMaintenance ? 'MAINTENANCE' : 'BLACKOUT'
        await this.page.locator(`//input[@value='${valueToSelect}']/parent::label`).click();
    }
    async selectValueFromTimeZoneDropdown(value: string) {
        await this.timeZoneDropdown.click();
        await this.page.locator(`//h4[contains(text(),'${value}']`).click();
    }
    async addDurationToWindow(data: AllTypes.deploymentWindow.dwConfigForOnceAndDaily | AllTypes.deploymentWindow.dwConfigForWeekly | AllTypes.deploymentWindow.dwConfigForWeeklyAndMonthlyRange) {
        await this.addDurationButton.click();
        await this.page.getByTestId(`window-frequency-${data.type}-span`).click();
        if (data.type == 'weekly') {
            //    await this.windowConfigurationDiv.locator(`//*[text()="${data.dayOfWeek}"]`).click();
        }
        if (data.type != 'fixed' && data.fromDate) {
            await this.turOnCheckBoxForStartAndEndDate();
        }
        if (data.type == 'weekly (range)') {
            await this.weekDayFromSelectDropdown.click();
            await this.page.locator(`//*[@role='option']`).locator(`//*[contains(text(),'${data.fromDay}')]`).click();
            await this.weekDayToSelectDropdown.click();
            await this.page.locator(`//*[@role='option']`).locator(`//*[contains(text(),'${data.toDay}')]`).click();
        }
        if (data.fromDate && data.toDate) {
            await this.setDateFromDropdown.click();
            await this.setCustomDate(data.fromDate.date, data.fromDate.month, data.fromDate.year);
            await this.setDateToDropdown.click();
            await this.setCustomDate(data.toDate.date, data.toDate.month, data.toDate.year);
        }
        await this.setFromAndToTime(data.fromTime, data.toTime);
        await this.doneButton.click();

    }

    async createAProfile(data: { profileName: string, durationConfiguration: AllTypes.deploymentWindow.dwConfigForOnceAndDaily | AllTypes.deploymentWindow.dwConfigForWeekly | AllTypes.deploymentWindow.dwConfigForWeeklyAndMonthlyRange, userData?: { superAdmin: boolean, specificUser?: string[] }, isMaintenanceWindow: boolean }) {
        await this.addWindowButton.click();
        await this.windowNameInputField.fill(data.profileName);
        await this.selectMaintainanceAndBlackoutWindow(data.isMaintenanceWindow);
        await this.addDurationToWindow(data.durationConfiguration);
        data.userData ? await this.setSuperAdminOrSpecificUserForDW(data.userData!) : null;
        await this.verifySuccessfullSaveOfWindowProfile();
    }

    async verifySuccessfullSaveOfWindowProfile() {
        await BaseTest.checkToast(this.page, this.saveChangesButton, 'Success');
    }
    async turOnCheckBoxForStartAndEndDate() {
        if (!await this.setStartDateCheckbox.locator(`xpath=preceding-sibling::input`).isChecked()) {
            await this.setStartDateCheckbox.click();
        }
        if (!await this.setEndDateCheckbox.locator(`xpath=preceding-sibling::input`).isChecked()) {
            await this.setEndDateCheckbox.click();
        }
    }
    async setFromAndToTime(fromTime: string, toTime: string) {
        await this.setTimeFromDropdown.click();
        await this.page.locator(`//*[@role="option"]`).locator(`//*[contains(text(),"${fromTime}")]`).first().click();
        await this.setTimeToDropdown.click();
        await this.page.locator(`//*[@role="option"]`).locator(`//*[contains(text(),"${toTime}")]`).first().click();
    }



    async setSuperAdminOrSpecificUserForDW(data: { superAdmin: boolean, specificUser?: string[] }) {

        if (data.superAdmin != await this.superAdminCheckboxSpan.locator(`xpath=preceding-sibling::input`).isChecked()) {
            await this.superAdminCheckboxSpan.click();
        }
        let isSpecificUserAllowed: boolean = data.specificUser ? true : false;
        if (isSpecificUserAllowed != await this.specificUserCheckboxSpan.locator(`xpath=preceding-sibling::input`).isChecked()) {
            await this.specificUserCheckboxSpan.click();
            await this.specificUsersDropdown.click();
            for (let key of data.specificUser!) {
                await this.page.locator(`//*[@role='listbox']`).locator(`//*[text()='${key}']`).click();
            }
            await this.specificUsersDropdown.click();
        }
    }
    async applyDWProfileToAnApp(data: { appName: string, envName: string, windows?: string, profileName: string }) {
        await this.applyToButton.click();
        await this.applyFilterOnAppEnvWindow('app', data.appName);
        await this.applyFilterOnAppEnvWindow('env', data.envName);
        await this.bulkSelectionApplyProfileCheckbox.click();
        try {
            await this.bulkSelectAllOnThisPage.click();
        }
        catch (error) {
            console.log('bulk select all on page is not visible' + error);
        }
        await this.manageWindowsButton.click();
        await this.deploymentWindowProfileDrodpdownBulkSelectAddModal.click();
        await this.page.locator(`//*[@role='option']`).locator(`//*[text()='${data.profileName}']`).click();
        await this.saveButton.click();

    }
    async applyFilterOnAppEnvWindow(filterType: 'window' | 'app' | 'env', value: string) {
        let locatorToClick: Locator = filterType == 'app' ? this.appFilterDropdown : filterType == 'env' ? this.envFilterDropdown : this.dwDropdown;
        await locatorToClick.click();
        await this.page.locator(`//*[@role='option']`).locator(`//*[text()='${value}']`).click();
        await this.filterApplyButton.click();
    }
    async searchAProfile(profileName: string) {
        await this.searchBar.fill(profileName);
        await this.page.keyboard.press('Enter');
    }
    async searchAndClickOnAprofile(profileName: string) {
        await this.searchAProfile(profileName);
        await this.page.getByTestId(`${profileName}`).click();
    }
    async clearPreviousAppliedDurationOnAProfile() {
        await this.addDurationButton.waitFor();
        await this.removeAlreadyAddedConfiguration.click();
    }
    async verifyDeploymentWindowTimeline(locator: Locator, maxTimeInSec: number) {
        await locator.waitFor({ state: 'visible' });
        let time = await locator.textContent();
        let multiplierValue: number = 1;
        let index: number = -1;
        let separtedValues = time?.split(':');
        let numberConversionValue = 0;
        let finalValueInSec: number = 0;
        for (const key of separtedValues!) {
            multiplierValue = key.includes('m') ? 60 : key.includes('h') ? 3600 : 1;
            index = key.search(/[msh]/);
            for (let i = 0; i < index; i++) {
                numberConversionValue = numberConversionValue * 10 + Number(key[i]);
            }
            finalValueInSec = finalValueInSec + (numberConversionValue * multiplierValue);
            console.log('in between value is ' + finalValueInSec);
            numberConversionValue = 0;
        }
        console.log('mine value in sec is ' + finalValueInSec);
        expect(finalValueInSec < maxTimeInSec);
    }

    async deleteDWProfile(profileName: string) {
        await this.searchAndClickOnAprofile(profileName);
        await this.deleteButton.click();
        await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, 'Success');
    }

}