
import { Locator, Page, expect } from "@playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { count, time } from "console";
import { BasePage } from "../../BasePage";
import clipboardy from "clipboardy";
import { ephimeralContainerAdvancedConfigurations } from "../../../utilities/clipboardyYamls.ts/customObjectsOrYaml";
import { AllTypes } from "../../../utilities/Types";
import { ApiTokenPage } from "../../Global Configuration/Authorization/ApiTokenPage";
import { Constants } from "../../../utilities/Constants";
export class ChartStoreAppDetailsPage extends BasePage {
    readonly applicationStatus: Locator;
    readonly appDetailsTab: Locator;
    readonly deploymentStatus: Locator;
    readonly terminalLogsManifestDiv: Locator;
    readonly terminalTab: Locator;
    readonly manifesttabLink: Locator;
    readonly terminalOpeningSign: Locator;
    readonly eventTabLink: Locator;
    readonly scaleWorkloadButton: Locator;
    readonly checkboxScaling: Locator;
    readonly restoreAndScaleDownButton: Locator;
    readonly noActiveWorkloadsText: Locator;
    readonly podCloseIcon: Locator;
    readonly scaleWorkloadModalCloseButton: Locator;
    readonly noAvailablePods: Locator;
    readonly alreadyScaledDownWorkloadsTabButton: Locator;
    readonly terminalClickableArea: Locator;
    readonly manifestScrollableArea: Locator;
    readonly apiVersionText: Locator;
    readonly ApplicationStatusCard: Locator;
    readonly deploymentStatusCard: Locator;
    readonly configApplyStatusCard: Locator;
    readonly clearTerminalButton: Locator;
    readonly noPodState: Locator;
    readonly podReadyState: Locator;
    readonly appDetailsPageDeployButton: Locator;
    readonly podAgeCountDiv: Locator;
    readonly downloadFileFolder: Locator;
    readonly filePathForDownloadLogs: Locator;
    readonly downloadButton: Locator;
    readonly monacoEditor: Locator;
    readonly terminalDiv: Locator;
    readonly logsTabButton: Locator;
    readonly dropdownForCustomizingLogs: Locator;
    readonly logsSetDurationUnitDropdown: Locator;
    readonly logsSetLinesAndDurationInputField: Locator;
    readonly downloadLogsInputFieldsErrorMessages: Locator;
    readonly apiTokenPage: ApiTokenPage
    readonly doneButton: Locator;
    readonly lastDeployedTimeInfoDiv: Locator;
    readonly grepSearchInputField: Locator;
    readonly resourceStatusDiv: Locator;
    readonly navigateToResourceBrowserAllResourcesButton: Locator;
    readonly calendarDropdownOpeningInput: Locator;
    readonly clearGrepSearchButton: Locator;
    readonly k8sResourcesTabButton: Locator;
    readonly filePathErrorMessage: Locator;


    // ephimeral containers
    readonly ephimeralContainerButton: Locator;
    readonly containerNamePrefixInputField: Locator;
    readonly containerImageDropdownSelector: Locator;
    readonly containerImageListLocator: Locator;
    readonly launchEphimeralContainerButton: Locator;
    readonly advancedConfigurationButton: Locator;
    readonly availableContainersListDropdown: Locator;
    readonly ephemeralDeleteButton: Locator;
    readonly targetContainerNameInputField: Locator;
    readonly resumeContainerButton: Locator;
    readonly cancelButton: Locator;
    readonly currentActiveContainerDiv: Locator;
    readonly containerLogsHeader: Locator;
    readonly downloadLogsButton: Locator;
    readonly navigateToResourceBrowserDefaultClusterButton: Locator;

    constructor(public page: Page) {
        // Initializing locators
        super(page);
        this.applicationStatus = this.page.locator('//*[@class="app-details-info-card__top-container__content"]//*[@class="flex fs-12 fw-4"]');
        this.appDetailsTab = this.page.locator('//*[text()="App details" or text()="App Details"]');
        this.deploymentStatus = this.page.locator('//*[@class="app-details-info-card__top-container__content"]//*[contains(@class,"fs-14")]');
        this.terminalLogsManifestDiv = this.page.locator('//*[@data-testid="logs-tab"]').first();
        this.terminalTab = this.page.locator('//*[@data-testid="terminal-nav-link" or @data-testid="terminal-tab"]');
        this.manifesttabLink = this.page.locator('//*[text()="Manifest" or text()="manifest"]');
        this.terminalOpeningSign = this.page.locator(`//*[contains(text(),"# ") or contains(text(),'$ ')]`);
        this.eventTabLink = this.page.getByTestId('events-nav-link');
        this.scaleWorkloadButton = this.page.getByTestId('scale-workload-button-app-details');
        this.restoreAndScaleDownButton = this.page.getByTestId('scale-or-restore-workloads');
        this.noActiveWorkloadsText = this.page.locator('//*[contains(text(),"No")]');
        this.podCloseIcon = this.page.locator('//*[contains(@aria-label,"Close tab ")]');
        this.scaleWorkloadModalCloseButton = this.page.getByTestId('scale-workload-close-button');
        this.checkboxScaling = this.page.getByTestId('undefined-chk-span');
        this.noAvailablePods = this.page.getByTestId('no-pod');
        this.alreadyScaledDownWorkloadsTabButton = this.page.getByTestId('scale-workloads-tab-1');
        this.terminalClickableArea = this.page.locator('//*[@class="xterm-accessibility-tree"]');
        this.manifestScrollableArea = this.page.getByTestId('app-manifest-container');
        this.apiVersionText = this.page.locator('//*[text()="apiVersion"]');
        this.configApplyStatusCard = this.page.getByTestId('helm-config-apply-status-card');
        this.ApplicationStatusCard = this.page.getByTestId("app-status-card");
        this.deploymentStatusCard = this.page.getByTestId("deployment-status-card");
        this.clearTerminalButton = page.getByTestId("clear-terminal-editor");
        this.noPodState = this.page.getByTestId('no-pod');
        this.podReadyState = this.page.getByTestId('pod-ready-count');
        this.appDetailsPageDeployButton = this.page.getByTestId('deploy-button');
        this.podAgeCountDiv = this.page.getByTestId("pod-age-count");
        this.downloadFileFolder = this.page.locator(`//*[text()="Download file/folder"]`);
        this.filePathForDownloadLogs = this.page.locator(`//*[@name="file-path"]`);
        this.downloadButton = this.page.locator(`//*[text()="Download"]`);
        this.monacoEditor = this.page.locator(`//*[@class="view-lines monaco-mouse-cursor-text"]`);
        this.resumeContainerButton = this.page.locator(`//button[text()="Resume"]`);
        this.terminalDiv = this.page.getByTestId('app-terminal-container');
        this.currentActiveContainerDiv = this.page.locator(`//*[contains(@class,'containers-select__single-value')]`);
        this.logsTabButton = this.page.getByTestId('logs-nav-link');
        this.containerLogsHeader = this.page.getByTestId('logs-container-header');
        this.dropdownForCustomizingLogs = this.containerLogsHeader.locator(`//*[contains(@class,'indicatorContainer') and not (contains(@class,'containers-select__indicator'))]`);
        this.downloadLogsButton = this.dropdownForCustomizingLogs.locator(`xpath=/ancestor::div[contains(@class,'container')][1]`).locator('xpath=/following-sibling::span');
        this.logsSetDurationUnitDropdown = this.page.locator(`//*[contains(@class,'time-selector__control')]`);
        this.logsSetLinesAndDurationInputField = this.page.getByTestId('range-input');
        this.downloadLogsInputFieldsErrorMessages = this.page.locator('#range-input-error-msg');
        this.apiTokenPage = new ApiTokenPage(this.page);
        this.doneButton = this.page.locator('//*[text()="Done"]');
        this.lastDeployedTimeInfoDiv = this.deploymentStatusCard.locator(`//*[contains(@class,'app-details-info-card__bottom-container__message')]`);
        this.grepSearchInputField = this.page.locator(`//*[contains(@class,'search-bar__input')]`);
        this.resourceStatusDiv = this.page.getByTestId(`node-resource-status`);
        this.navigateToResourceBrowserAllResourcesButton = this.page.locator('//*[text()="All resources"]');
        this.navigateToResourceBrowserDefaultClusterButton = this.page.locator('//*[text()="Cluster terminal"]');
        this.calendarDropdownOpeningInput = this.page.locator(`//*[contains(@class,'SingleDatePickerInput_calendarIcon')]`);
        this.clearGrepSearchButton = this.page.getByTestId('clear-search');
        this.k8sResourcesTabButton = this.page.locator(`//*[@id="K8s Resources"]`);
        this.filePathErrorMessage = this.page.locator('//*[@id="file-path-error-msg"]');

        // ephimeral containers
        this.ephimeralContainerButton = this.page.locator('//*[text()="Launch Ephemeral Container"]');
        this.containerNamePrefixInputField = this.page.getByTestId('container-name');
        this.containerImageDropdownSelector = this.page.locator(`//*[contains(@class,'select-token-expiry-duration__control')]`);
        this.containerImageListLocator = this.page.locator('//*[@role="option"]');
        this.launchEphimeralContainerButton = this.page.locator('//*[text()="Launch container"]');
        this.advancedConfigurationButton = this.page.locator('//*[text()="Advanced"]');
        this.availableContainersListDropdown = this.page.locator(`//*[contains(@class,'containers-select__control')]`);
        this.ephemeralDeleteButton = this.page.getByTestId('ephemeral-delete-button');
        this.targetContainerNameInputField = this.page.locator('//*[@name="target-container-name"]');
        this.cancelButton = this.page.locator(`//button[text()="Cancel"]`);

    }


    /**
     * Verifies details of the application page.
     * 
     * @param {boolean} isChartStoreHelm - Specifies if the chart store is Helm.
     * @param {string} chartVersion - The version of the chart.
     * @param {string} terminalScript - The script to be executed in the terminal.
     * @param {string} terminalExpectedValue - The expected value from the terminal execution.
     * @param {string} chartName - The name of the chart.
     */
    async verificationAppDetailsPage(isChartStoreHelm: boolean, chartVersion: string, terminalScript: string[], terminalExpectedValue: string, chartName: string, isExternal: boolean = false): Promise<void> {
        // Verifying application status, deployment status, chart version, terminal, events, and manifest
        const baseTest = new BaseTest();
        await this.verifyApplicationStatus();
        if (isChartStoreHelm) {
            await this.verifyConfigApplyStatus();
        } else {
            await this.verifyDeploymentStatus();
        }
        await this.verifyChartVersion(chartVersion);
        if (!isChartStoreHelm) {
            await this.openResourcesAndClickOnSpecificResource('workloads', 'Pod');
        }
        await this.goToTerminal();
        await this.verifyTerminal(terminalScript, terminalExpectedValue);
        await this.manifesttabLink.click();
        await expect(this.page.locator('//*[text()="apiVersion"]').first()).toBeVisible({ timeout: 2 * 1000 * 60 });
        if (!isExternal) {
            // revert new code editor
            await expect(this.page.locator(`//*[contains(@class,'${this.codeMirrorSingleLineClass}') and contains(.,'${chartName}-${chartVersion}')]`)).toBeVisible({ timeout: 2 * 1000 * 60 });
            // await expect(this.page.locator(`//*[text()="${chartName}-${chartVersion}"]`)).toBeVisible({ timeout: 2 * 1000 * 60 });
        }
        await this.eventTabLink.click();
        await expect(this.page.locator('//*[text()="Started"]').first()).toBeVisible({ timeout: 2 * 1000 * 60 })
        await this.podCloseIcon.click();
    }

    /**
     * Verifies application status.
     * 
     * @param {string} appVerificationText - The text to verify the application status. Defaults to 'Healthy'.
     */
    async verifyApplicationStatus(appVerificationText: string = "Healthy") {
        await this.appDetailsTab.click();
        await this.page.waitForTimeout(8000);
        await expect(this.ApplicationStatusCard.locator(`//*[text()="${appVerificationText}"]`)).toBeVisible({ timeout: 8 * 1000 * 60 });
    }

    /**
     * Verifies deployment status.
     * 
     * @param {string} DeploymentVerificationText - The text to verify the deployment status. Defaults to 'Succeeded'.
     */
    async verifyDeploymentStatus(DeploymentVerificationText: string = "Succeeded") {
        await expect(this.deploymentStatusCard.locator(`//*[text()="${DeploymentVerificationText}"]`)).toBeVisible({ timeout: 6 * 1000 * 60 });
    }

    /**
     * Verifies configuration apply status.
     * 
     * @param {string} DeploymentVerificationText - The text to verify the configuration apply status. Defaults to 'deployed'.
     */
    async verifyConfigApplyStatus(DeploymentVerificationText: string = "deployed") {
        await expect(this.configApplyStatusCard.locator(`//*[text()="${DeploymentVerificationText}"]`)).toBeVisible({ timeout: 6 * 1000 * 60 });
    }

    /**
     * Verifies chart version.
     * 
     * @param {string} chartVersion - The version of the chart.
     */
    async verifyChartVersion(chartVersion: string): Promise<void> {
        await expect(this.page.locator(`//*[text()="${chartVersion}"]`)).toBeVisible({ timeout: 1 * 60 * 1000 });
    }

    async goToTerminal(rbacRelatedData: { isEligible: boolean } = { isEligible: true }) {
        await expect(async () => {
            await this.appDetailsTab.click();
            await this.terminalLogsManifestDiv.hover();
            if (rbacRelatedData.isEligible == false) {
                await BaseTest.checkToast(this.page, this.terminalTab, 'Not authorized');
            }
            else {
                await this.terminalTab.click();
                await expect(this.page.locator('//*[@role="list"]').locator(this.terminalOpeningSign).last()).toBeVisible({ timeout: 1 * 60 * 1000 });
            }
        }).toPass({ timeout: 1 * 1000 * 60 });
    }
    async downloadFile(filePath: string, isEligible: boolean = true, fileContent?: string, checkValidations: boolean = false) {
        await this.terminalTab.click();
        await this.downloadFileFolder.click();
        if (checkValidations) {
            expect(this.downloadButton).toBeDisabled();
            await this.filePathForDownloadLogs.fill('reen');
            await expect(this.filePathErrorMessage).toBeVisible();
        }
        await this.filePathForDownloadLogs.fill(filePath);
        if (!isEligible || !fileContent) {
            let messageToVerify: string = isEligible ? 'Success' : 'Not authorized';
            await BaseTest.checkToast(this.page, this.downloadButton, messageToVerify);
        } else {
            await BaseTest.verifyDownloads(this.page, this.downloadButton, fileContent!, true);
        }

    }
    async openResourcesAndClickOnSpecificResource(resourceType: 'workloads' | "networking", resourceName?: string) {
        await this.k8sResourcesTabButton.click();
        let locatorToClick: Locator = resourceName ? this.page.locator(`//*[@data-testid="${resourceType}"]/parent::button/following-sibling::div//*[text()="${resourceName}"]`) : this.page.locator(`//*[@data-testid="${resourceType}"]/parent::button/following-sibling::div//a`).first();
        try {
            await this.page.locator(`//*[@data-testid="${resourceType}"]/parent::button/following-sibling::div`).waitFor({ timeout: 7000 });
            await locatorToClick.click();
        }
        catch (e) {
            await this.page.locator(`//*[@data-testid="${resourceType}"]`).click();
            await locatorToClick.click();
        }

    }


    /**
     * Verifies terminal.
     * 
     * @param {string} terminalScript - The script to be executed in the terminal.
     * @param {string} terminalExpectedValue - The expected value from the terminal execution.
     * @param {string} terminalOpeningSign - The opening sign of the terminal. Defaults to '$'.
     */
    async verifyTerminal(terminalScript: string[] | any, terminalExpectedValue: string | any) {
        for (let i = 0; i < terminalScript.length; i++) {
            await expect(this.page.locator('//*[@role="list"]').locator(this.terminalOpeningSign).last())
                .toBeVisible({ timeout: 4 * 60 * 1000 });
            console.log(terminalScript);
            await this.terminalClickableArea.click();
            await this.terminalClickableArea.click();
            await this.page.keyboard.press(' ');
            await this.page.keyboard.type(terminalScript[i]);
            await this.page.keyboard.press("Enter");
        }
        await expect(this.terminalDiv.locator(`//div[contains(text(),'${terminalExpectedValue}')]`).first()).toBeVisible({ timeout: 3 * 1000 * 60 });
        await this.page.keyboard.type("cd ..");
        await this.page.keyboard.press("Enter");
        await this.clearTerminalButton.click();
        await expect(this.terminalDiv.locator(`//div[contains(text(),'${terminalExpectedValue}')]`).first()).toBeHidden();
    }

    /**
   * Asynchronously verifies if a given text is present in the manifest.
   * 
   * @param verificationText The text to verify in the manifest.
   */
    async verifyManifest(verificationText: string) {
        // Click on the manifest tab link
        var isProperlyVisible: boolean = false;
        await this.manifesttabLink.click();
        // Wait for the first API version text to appear
        await this.apiVersionText.first().waitFor();
        // Initialize a variable to count the number of scroll attempts
        var scrollCount = 0;
        // Loop until the verification text is visible or scroll count exceeds 9
        // revert new code editor
        while (!await this.codeMirrorEditorTextArea.locator(`//*[contains(text(),'${verificationText}')]`).first().isVisible() && scrollCount < 60) {
            // Click on the manifest scrollable area
            await this.codeMirrorEditorTextArea.click();
            // Simulate mouse scrolling to move through the manifest
            await this.page.mouse.wheel(0, 80);
            // Increment the scroll count
            scrollCount++;
            if (await this.page.locator(`//div[contains(@class, '${this.codeMirrorSingleLineClass}') and contains(., '${verificationText}')]`).first().isVisible()) {
                isProperlyVisible = true;
                break;
            }
        }
        // old code editor ends
        console.log('scroll count is' + count);
        if (scrollCount == 0) {
            isProperlyVisible = true;
        }
        // await expect(this.page.locator(`//*[text()="${verificationText}"]`).first()).toBeVisible({ timeout: 3 * 1000 * 60 });
        expect(isProperlyVisible).toBe(true);
        // Verify if the verification text is visible within a timeout period

    }



    /** 
     * @param scaleUp pass value as true if you want to scale up the workloads
     **/

    async scaleUpAndDownWorkloadsChartStoreApps(scaleUp: boolean) {
        await this.scaleWorkloadButton.click();
        scaleUp ? this.alreadyScaledDownWorkloadsTabButton.click() : null;
        await this.checkboxScaling.first().click();
        await this.restoreAndScaleDownButton.click();
        await expect(this.noActiveWorkloadsText.first()).toBeVisible({ timeout: 2 * 1000 * 60 });
        await this.scaleWorkloadModalCloseButton.click();
        await this.verifyScaleUpAndDownWorkloads(scaleUp);
    }
    async verifyScaleUpAndDownWorkloads(isScaledUp: boolean, scaleUpStatus: string = 'Progressing') {
        isScaledUp ? await this.openResourcesAndClickOnSpecificResource('workloads', 'Pod') : null;
        let statusToVerify: string = isScaledUp ? scaleUpStatus : 'Hibernating';
        await this.verifyApplicationStatus(statusToVerify);
        let locatorToWaitFor: Locator = isScaledUp ? this.podReadyState : this.noPodState;
        await expect(locatorToWaitFor).toBeVisible({ timeout: 45000 });
    }
    async clickOnAppDetailsDeployButton() {
        await this.appDetailsPageDeployButton.click();
    }



    /**
     * use this method , if you want to check that new pod has come or not 
     * @param maxTimeInSec 
     */
    async verifyPodAge(maxTimeInSec: number) {
        await expect(this.podReadyState).toHaveCount(1, { timeout: 40000 });
        const time = await this.podAgeCountDiv.textContent();
        let multiplierValue: number = 1;
        let index: number = -1;
        let separtedValues = time?.split(' ');
        let numberConversionValue = 0;
        let finalValueInSec: number = 0;
        for (const key of separtedValues!) {
            multiplierValue = key.includes('m') ? 60 : 1;
            index = key.search(/[ms]/);
            for (let i = 0; i < index; i++) {
                numberConversionValue = numberConversionValue * 10 + Number(key[i]);
            }
            finalValueInSec = finalValueInSec + (numberConversionValue * multiplierValue);
            console.log('in between value is ' + finalValueInSec);
            numberConversionValue = 0;
        }
        console.log('mine value in sec is ' + finalValueInSec);
        expect(finalValueInSec < maxTimeInSec);
    }



    /**
     * use this method to launch ephimeral container
     * @param targetContainerName  specify the name of the container
     * @param containerNamePrefix specify the name of the container prefix
     * @param isBaseConfiguration here you can pass true if you want to use base configuration not advanced configuration
     * @param isSuccessfull set true  if launch of ephemeral container would be successfull
     * @param image set the image name if you want to use any other image to launch the container
     */
    async launchEphimeralContainer(targetContainerName: string, containerNamePrefix?: string, isBaseConfiguration?: boolean, isSuccessfull: boolean = true, image?: AllTypes.ephemeralContainers.ephemeralContainerImageType) {
        await this.ephimeralContainerButton.click();
        if (isBaseConfiguration) {
            containerNamePrefix ? await this.containerNamePrefixInputField.fill(containerNamePrefix) : console.log('container name prefix is not provided');
            if (image) {
                await this.containerImageDropdownSelector.first().click();
                await this.containerImageListLocator.locator(`//*[text()="${image}"]`).click();
            }
            await this.targetContainerNameInputField.getAttribute('value').then((value) => { expect(value).toBe(targetContainerName) });
        } else {
            await this.advancedConfigurationButton.click();
            await this.codeMirrorEditorTextArea.dblclick();
            for (let i = 0; i < 2; i++) {
                if (process.env.OS as string == "Mac") {
                    await this.page.keyboard.press('Meta+a');
                }
                else {
                    await this.page.keyboard.press('Control+a');
                }
                await this.page.waitForTimeout(1000);
            }
            if (!image) {
                image = Constants.defaultImageNameForEphemeralContainers;
            }
            await this.page.keyboard.press('Backspace');
            await clipboardy.write(ephimeralContainerAdvancedConfigurations(targetContainerName, containerNamePrefix, image));
            await this.page.keyboard.press('Shift+Insert');
        }
        let toastToVerify: string = isSuccessfull ? Constants.successToastMessage : Constants.failureToastMessage;
        await BaseTest.checkToast(this.page, this.launchEphimeralContainerButton, toastToVerify);
        toastToVerify == Constants.failureToastMessage ? await this.cancelButton.click() : "";
        let retryCount = 0;
        while (await this.resumeContainerButton.isVisible() && retryCount < 18) {
            await this.resumeContainerButton.click();
            await this.page.waitForTimeout(1000);
            retryCount++;
        }
    }

    async setBasicConfigurationForEphemeralContainers(targetContainerName: string, containerNamePrefix?: string, image?: AllTypes.ephemeralContainers.ephemeralContainerImageType) {

    }
    async selectContainerFromAvailableContainersList(containerName: string, containerType: 'Ephemeral' | 'Main') {
        await this.availableContainersListDropdown.click();
        let containerTypeLocator = this.locatorForContainerSelection(containerName, containerType);
        await containerTypeLocator.click();
    }


    async verifyContainerListing(containerType: 'Ephemeral' | 'Main', isVisible: boolean, containerName: string) {
        await this.availableContainersListDropdown.click();
        let containerTypeLocator = this.locatorForContainerSelection(containerName, containerType);
        if (isVisible) {
            await expect(containerTypeLocator).toBeVisible({ timeout: 1 * 1000 * 60 });
        } else {
            await expect(containerTypeLocator).not.toBeVisible({ timeout: 1 * 1000 * 60 });
        }
    }


    async removeEphimeralContainer(containerName: string, fallbackContainerName?: string) {
        await this.availableContainersListDropdown.click();
        await BaseTest.checkToast(this.page, this.page.locator(`//*[contains(text(),'${containerName}')]/ancestor::div[@role="option"]`).locator(this.ephemeralDeleteButton), "Success");
        if (fallbackContainerName) {
            expect(await this.currentActiveContainerDiv.textContent()).toContain(fallbackContainerName);
            await expect(this.resumeContainerButton).not.toBeVisible();
        }
    }


    locatorForContainerSelection(containerName: string, containerType: string): Locator {
        let containerTypeLocator = containerType == 'Main' ? this.page.locator(`//*[text()="Main containers"]/parent::div//*[contains()text()="${containerName}"]`) : this.page.locator(`//*[text()="Ephemeral containers"]/parent::div//*[text()="${containerName}"]`);
        return containerTypeLocator;
    }



    async downloadLogs(customizeLogs?: AllTypes.downloadLogs.downloadLogsData) {
        await this.logsTabButton.click();
        await this.dropdownForCustomizingLogs.click();
        await this.page.locator('//*[@role="listbox"]').locator(`//*[text()="${customizeLogs?.valueToSelectFromDropdown}"]`).click();
        if (customizeLogs?.customDataSet) {
            await this.setCustomConfigurationForDownloadLogs(customizeLogs);
        } else {
            await BaseTest.verifyDownloads(this.page, this.downloadLogsButton, "Container starting...", true);
        }
    }

    async setCustomConfigurationForDownloadLogs(data: AllTypes.downloadLogs.downloadLogsData) {
        let configurationToSelect = Object.keys(data.customDataSet!);
        console.log(configurationToSelect[0] + "this is the key that we are going to click");
        await this.page.locator(`//*[text()="${configurationToSelect[0]}"]`).click();
        if (data.customDataSet) {
            let isSuccessfull: boolean = true;
            if ('Set duration' in data.customDataSet) {
                if (data.customDataSet['Set duration'].checkValidation) {
                    await this.logsSetLinesAndDurationInputField.fill('0');
                    await expect(this.downloadLogsInputFieldsErrorMessages).toBeVisible();
                }
                await this.logsSetLinesAndDurationInputField.fill(data.customDataSet['Set duration'].value);
                await this.logsSetDurationUnitDropdown.click();
                await this.page.locator('//*[@role="listbox"]').locator(`//*[text()="${data.customDataSet['Set duration'].unit}"]`).click();
                isSuccessfull = data.customDataSet['Set duration'].isSuccessfull
            }
            else if ('Set lines' in data.customDataSet) {
                if (data.customDataSet['Set lines'].checkValidationo) {
                    await this.logsSetLinesAndDurationInputField.fill('0');
                    await expect(this.downloadLogsInputFieldsErrorMessages).toBeVisible();
                }
                await this.logsSetLinesAndDurationInputField.fill(data.customDataSet['Set lines'].value);
                isSuccessfull = data.customDataSet['Set lines'].isSuccessfull
            }
            else if ('Since date & time' in data.customDataSet) {
                await this.calendarDropdownOpeningInput.click();
                await this.apiTokenPage.setCustomDate(data.customDataSet["Since date & time"].date, data.customDataSet["Since date & time"].month, data.customDataSet["Since date & time"].year);
                isSuccessfull = data.customDataSet["Since date & time"].isSuccessfull
            }
            await this.doneButton.click();
            await BaseTest.verifyDownloads(this.page, this.downloadLogsButton, "Container starting...", isSuccessfull!);
        }

    }
    async verifyServiceUrlAndManifest(appName: string, envName: string, serviceType: string = "ClusterIP", targetPort: string = "80") {
        await this.openResourcesAndClickOnSpecificResource('networking', "Service");
        await expect(this.page.locator(`//*[text()="${appName}-${envName}-service.${envName}:${targetPort}"]`)).toBeVisible();
        await this.verifyManifestOfService(serviceType);
    }
    async verifyManifestOfService(textToVerify: string) {
        await this.openResourcesAndClickOnSpecificResource('networking', "Service");
        await this.manifesttabLink.click();
        await this.verifyManifest(`${textToVerify}`);
    }
    async verifyDeploymentTimeOnDeploymentCard(units: 'hours' | "minutes" | "seconds", value?: string, deployedBy?: string) {
        let lastDeployedInfo: string = String(await this.lastDeployedTimeInfoDiv.textContent());
        console.log('last deployment related data' + lastDeployedInfo);
        expect(lastDeployedInfo).toContain(units);
        if (value) {
            value == "few" ? expect(lastDeployedInfo).toContain('few seconds ago') : expect(Number(lastDeployedInfo.split(' ')[0]) <= Number(value));
        }
        if (deployedBy) {
            expect(lastDeployedInfo).toContain(deployedBy);
        }
    }
    async verifyGrepSearchFunctionality(value: string) {
        await this.grepSearchInputField.fill(value);
        await this.page.keyboard.press('Enter');
        const canvas = this.page.locator('//*[@class="xterm-text-layer"]').first();

        // Ensure the canvas is visible
        await expect(canvas).toBeVisible({ timeout: 20000 });

        // Mask the dynamic part of the canvas (e.g., timeline)
        await canvas.evaluate((canvas) => {
            const ctx = (canvas as HTMLCanvasElement).getContext('2d');
            if (!ctx) {
                throw new Error('Could not get 2D context');
            }

            // Mask the timeline area
            ctx.fillStyle = 'black'; // Mask color
            ctx.fillRect(0, 0, 530, 25); // Adjust the rectangle to cover the timeline
        });

        // Capture a screenshot of the canvas
        const screenshot = await canvas.screenshot();
        // Use testInfo.snapshotPath to generate the correct path
        expect(screenshot).toMatchSnapshot('grep-functionality-reference.png');
        await this.clearGrepSearchButton.click();
    }
    async verifyStatusOfParticularResource(data: { resourceName: string, resourceType: 'workloads' | 'networking', status: "Running" | "Missing" | "Healthy" }[]) {
        for (let key of data) {
            await this.openResourcesAndClickOnSpecificResource(key.resourceType, key.resourceName);
            await expect(this.resourceStatusDiv).toContainText(key.status, { timeout: 45000 });
        }
    }
    async verifyNavigationToResourceBrowserFromAppDetailsPage(navgigationTo: 'all resources' | 'default cluster'): Promise<Page> {

        let [navigatedPage] = await Promise.all([this.page.waitForEvent('popup'), navgigationTo == 'all resources' ? await this.navigateToResourceBrowserAllResourcesButton.click() : await this.navigateToResourceBrowserDefaultClusterButton.click()]);
        return navigatedPage;
    }
}
