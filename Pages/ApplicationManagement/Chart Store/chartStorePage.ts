import { expect, Locator, Page } from "@playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { BasePage } from "../../BasePage";
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class ChartStorePage extends BasePage {
    readonly chartSearchField: Locator;
    readonly chartModal: Locator;
    readonly textSectionChartModal: Locator;
    readonly profileButton: Locator;
    readonly configureAndDeployButton: Locator;
    readonly helmChartSourceButton: Locator;
    readonly searchRegistryInSource: Locator;
    readonly refetchCharts: Locator;
    readonly closeModal: Locator;
    readonly createChartGroupButton: Locator;
    readonly chartGroupNameInputField: Locator;
    readonly saveGroupButtonOrCreateGroupModalButton: Locator;
    readonly chartGroupEditButton: Locator;
    readonly viewAllChartGroupsButton: Locator;
    readonly chartGroupDeleteButton: Locator;
    readonly deploymentsTabOfChart: Locator;
    readonly presetValuesAndDeploymentSearchBar: Locator;
    readonly createPresetButton: Locator;
    readonly usePresetValueToDeployChartButton: Locator;
    readonly clearFiltersButton: Locator;
    readonly deploymentTabThreeDotsButton: Locator;
    readonly presetValuesTabButton: Locator;
    readonly deleteDeploymentButton: Locator;
    readonly presetValueNameInputField: Locator;
    readonly presetValueSaveButton: Locator;

    constructor(public page: Page) {
        super(page)
        // defining all web elements on this page 
        this.chartSearchField = this.page.locator('//*[@data-testid="chart-store-search-box"]');
        this.chartModal = this.page.locator('//*[contains(@data-testid,"chart-card-single-")]');
        this.textSectionChartModal = this.page.locator('//*[@class="chart-grid-item__title-repo"]');
        this.profileButton = this.page.locator('//*[@data-testid="profile-button"]');
        this.configureAndDeployButton = this.page.getByTestId("deploy-chart-button")
        this.helmChartSourceButton = this.page.getByTestId('chart-store-source-button')
        this.searchRegistryInSource = this.page.getByPlaceholder('Search by repository or registry');
        this.refetchCharts = this.page.getByTestId('chart-refetch-button')
        this.closeModal = this.page.locator('//*[@class="dc__transparent flex"]');
        this.createChartGroupButton = this.page.locator(`//*[contains(@data-testid,'create-button')]`);
        this.chartGroupNameInputField = this.page.locator('//*[@name="name"]');
        this.saveGroupButtonOrCreateGroupModalButton = this.page.locator("//*[contains(@data-testid,'save-group-button')]");
        this.chartGroupEditButton = this.page.getByTestId("chart-group-edit-button");
        this.viewAllChartGroupsButton = this.page.locator(`//*[text()="View all chart groups"]`);
        this.chartGroupDeleteButton = this.page.getByTestId(`chart-group-delete-button`);
        this.deploymentsTabOfChart = this.page.locator(`//*[text()="Deployments"]`);
        this.presetValuesAndDeploymentSearchBar = this.page.getByTestId('chart-details-search-bar');
        this.createPresetButton = this.page.locator(`//*[@data-testid="create-chart-preset-value" or @data-testid="chart-preset-values-clear-filters"]`);
        this.usePresetValueToDeployChartButton = this.page.getByTestId('chart-deploy-with-preset-value');
        this.clearFiltersButton = this.page.getByTestId(`clear-filters`);
        this.presetValuesTabButton = this.page.locator(`//*[text()="Preset Values"]`);
        this.deploymentTabThreeDotsButton = this.page.locator('//*[contains(@data-testid,"chart-details-deployments-table-action-menu-delete-")]');
        this.deleteDeploymentButton = this.page.getByTestId('action-menu-item-delete');
        this.presetValueNameInputField = this.page.getByTestId('value-name');
        this.presetValueSaveButton = this.page.getByTestId('preset-save-values-button');

    }
    async SelectingChart(chartName: string, chartSource: string, clickOnDeploy: boolean = true) {

        await this.profileButton.hover();
        await this.chartSearchField.click();
        // entering the chart name that we want to deploy 
        await expect(async () => {
            await this.chartSearchField.fill(chartName);
            try { await BaseTest.waitForApiResponse(this.page, 'app-store/discover/', 200, async (page) => { await page.keyboard.press('Enter') }); }
            catch (error) { console.log(error) };
            //waitng that chart card to be visible with specific chart name and source
            await this.page.locator(`//*[@data-testid="${chartSource}-chart-repo"]`).click({ force: true });
            await this.page.locator(`//*[@data-testid="chart-grid-view"]//*[text()="${chartSource}"]/ancestor::div[contains(@data-testid,'chart-card-single')]//*[text()="${chartName}"]`
            ).click();
            if (clickOnDeploy) {
                await this.configureAndDeployButton.click();
            }
        }).toPass({ timeout: 2 * 1000 * 60 });
    }

    async fetchCharts(repositoryName: string, registryName: string) {
        await this.helpButton.hover();
        await expect(this.helmChartSourceButton).toBeVisible({ timeout: 20000 });
        await this.helpButton.hover();
        await this.helmChartSourceButton.click();
        await expect(this.searchRegistryInSource).toBeVisible();
        await this.searchRegistryInSource.pressSequentially(registryName, { delay: 400 });
        await this.page.keyboard.press('Enter');
        await BaseTest.checkToast(this.page, this.refetchCharts, "Success");
        await this.helmChartSourceButton.click({ force: true });
        await this.page.locator(`//*[@data-testid="${registryName}-chart-repo"]`).click({ force: true });
        try {
            let chartFound: boolean = false
            if (await this.isChartsVisible(repositoryName, registryName) === true) {
                chartFound = true;
            }
            else {
                for (let i = 1; i <= 10; i++) {
                    await this.page.waitForTimeout(3000);
                    await this.page.reload();
                    if (await this.isChartsVisible(repositoryName, registryName) === true) {
                        console.log(`Charts have visible!`);
                        chartFound = true
                        break;
                    }
                }
            }
            expect(chartFound).toBeTruthy();
        }
        catch (error) {
            throw error;
        }
    }

    async isChartsVisible(chartName: string, chartSource: string) {
        await this.chartSearchField.first().fill(chartName);
        await this.page.keyboard.press('Enter');
        let chartFound = false;
        try {
            await expect(this.page.locator(`//*[text()="${chartSource}"]/ancestor::div[contains(@data-testid,'chart-card-single')]//*[text()="${chartName}"]`)).toBeVisible({ timeout: 3000 });
            chartFound = true;
        }
        catch (error) {
            console.log(`Chart not found: ${error}`);
        }
        return chartFound;

    }
    async createChartGroup(chartGroupName: string, isEligible: boolean) {
        await BaseTest.clickOnDarkMode(this.page);
        await BaseTest.clickOnToolTipOkayButton(this.page);
        await this.createChartGroupButton.click();
        await this.chartGroupNameInputField.fill(chartGroupName);
        let messageToVerify: string = isEligible ? 'Success' : 'Error'
        await BaseTest.checkToast(this.page, this.saveGroupButtonOrCreateGroupModalButton, messageToVerify);
    }
    async checkWarningOnSavingTheGroup(chartNameToEdit: string, isEligible: boolean) {
        await this.viewAllChartGroupsButton.click();
        await this.page.locator(`p.chart-grid-item__title:has-text("${chartNameToEdit}")`).click();
        await this.chartGroupEditButton.click();
        await (expect(async () => {
            let messageToVerify: string = isEligible ? 'Success' : 'Error'
            await BaseTest.checkToast(this.page, this.saveGroupButtonOrCreateGroupModalButton, messageToVerify);
        })).toPass({ timeout: 4 * 1000 * 60 })
    }
    async deleteChartGroups(chartNameToDelete: string) {
        await this.viewAllChartGroupsButton.click();
        await this.page.locator(`p.chart-grid-item__title:has-text("${chartNameToDelete}")`).click();
        await this.chartGroupDeleteButton.click();
        await this.dialogDeleteConfirmationButton.click();
    }
    async searchPresetValueOrDeployments(presetValueOrDeploymentName: string, doesExist: boolean = true) {
        await this.presetValuesAndDeploymentSearchBar.fill(presetValueOrDeploymentName);
        await this.page.keyboard.press('Enter');
        if (doesExist) {
            await expect(this.rowLocatorForPresetValuesOrDeployments(presetValueOrDeploymentName).first()).toBeVisible();
        }
        else {
            await this.clearFiltersButton.click();
        }
    }
    async clickOnTabOfPresetValuesOrDeployments(presetValues: boolean) {
        await this.presetValuesTabButton.waitFor();
        await (presetValues ? this.presetValuesTabButton : this.deploymentsTabOfChart).click({ delay: 500 });
    }
    async verifyDetailsOfAPresetValueOrDeployment(presetValueOrDeploymentName: string, detailsToVerify: string[]) {
        await this.searchPresetValueOrDeployments(presetValueOrDeploymentName);
        let completeRowLocator = this.rowLocatorForPresetValuesOrDeployments(presetValueOrDeploymentName);
        for (let detail of detailsToVerify) {
            await expect(completeRowLocator.locator(`//*[text()="${detail}"]`)).toBeVisible();
        }

    }
    async clickOneditOrUseOrDeletePresetValueButton(presetValueName: string, actionToPerform: 'edit' | 'use' | 'delete') {
        let rowLocator = this.rowLocatorForPresetValuesOrDeployments(presetValueName);
        await rowLocator.hover();
        let actionButton: Locator = actionToPerform == "use" ? this.page.getByTestId('chart-deploy-with-preset-value') : actionToPerform == "delete" ? this.page.getByTestId('chart-preset-value-delete') : this.page.getByTestId('chart-preset-value-edit');
        await rowLocator.locator(actionButton).click();
        if (actionToPerform == "delete") {
            await this.dialogDeleteConfirmationButton.click();
        }
    }
    rowLocatorForPresetValuesOrDeployments(deploymentOrPresetValueName: string): Locator {
        return this.page.locator(`//*[text()="${deploymentOrPresetValueName}"]/ancestor::div[contains(@class,'generic-table__row')]`);
    }

    async createPresetValue(presetValueName: string) {
        await this.createPresetButton.click();
        await this.presetValueNameInputField.fill(presetValueName);
        await this.presetValueSaveButton.click();
    }
    async deleteDeployment(deploymentName: string) {
        await this.searchPresetValueOrDeployments(deploymentName);
        await this.rowLocatorForPresetValuesOrDeployments(deploymentName).hover();
        await this.deploymentTabThreeDotsButton.click();
        await this.deleteDeploymentButton.click();
        await this.dialogDeleteConfirmationButton.click();
    }

}
