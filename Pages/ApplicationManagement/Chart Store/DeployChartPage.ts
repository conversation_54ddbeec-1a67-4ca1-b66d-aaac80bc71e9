import { Locator, Page, expect } from "@playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { BasePage } from "../../BasePage";
export class DeployChartPage extends BasePage {
    readonly chartNameField: Locator;
    readonly projectDropdown: Locator;
    readonly envDropDown: Locator;
    readonly helmCheckBox: Locator;
    readonly gitopsCheckBox: Locator;
    readonly manifestOutputButton: Locator;
    readonly chartVersionDropdown: Locator;
    readonly deployChartButton: Locator;
    readonly configureTab: Locator;
    readonly deployedChartDeleteButton: Locator;
    readonly successfulChartDeletionConfirmation: Locator;
    readonly selectChartVersion: Locator;
    readonly compareValuesOrManifestButton: Locator;
    readonly addClusterHeader: Locator;
    readonly autoCreateRepositoryRadioButton: Locator;
    readonly saveGitRepo: Locator;
    readonly autoCreateRepository: Locator;
    readonly commitManifestToDesiredRepoRadioButton: Locator;
    readonly enterRepoUrl: Locator;
    readonly successfulySavedPopup: Locator;
    readonly workflowEditorPage: Locator;
    readonly imageRegistryText: Locator;
    readonly connectToChartIcon: Locator;
    readonly editProjectIcon: Locator;
    readonly projectInputFieldForExternalCharts: Locator;
    readonly saveButton: Locator;
    readonly selectChartDropdownForExternalCharts: Locator;
    readonly projectDropdownForExternalCharts: Locator;
    readonly fluxDeploymentRadioButton: Locator;


    constructor(public page: Page) {
        super(page);
        this.chartNameField = page.locator('//*[@name="app-name"]');
        this.projectDropdown = page.locator("//*[contains(@class,'select-chart-project__control')]");
        this.envDropDown = page.locator('//*[contains(@class,"values-environment-select__control")]');
        this.helmCheckBox = page.getByTestId('helm-deployment-span');
        this.gitopsCheckBox = page.getByTestId('gitops-deployment-span');
        this.manifestOutputButton = page.getByTestId('manifest-radio-button');
        this.chartVersionDropdown = page.locator("//*[contains(@class,'chart-values-selector__control')]");
        this.deployChartButton = page.getByTestId('preset-save-values-button');
        this.configureTab = page.locator('//*[text()="Configure" or @data-tetstid="helm-configure-link"]').first();
        this.deployedChartDeleteButton = page.getByTestId('delete-preset-value');
        this.successfulChartDeletionConfirmation = page.locator('//*[contains(text(),"was removed")]');
        this.selectChartVersion = this.page.locator('//*[@role="option"]').nth(0);
        this.compareValuesOrManifestButton = page.getByTestId('compare-values');
        this.addClusterHeader = page.getByTestId('add_cluster_header');
        this.autoCreateRepositoryRadioButton = page.getByTestId('auto-create-repository-span');
        this.saveGitRepo = page.getByTestId('save_cluster_list_button_after_selection');
        this.autoCreateRepository = page.getByText('Auto-create repository');
        this.commitManifestToDesiredRepoRadioButton = page.locator('//*[@data-testid="undefined-span"]');
        this.enterRepoUrl = page.getByPlaceholder('Enter repository URL');
        this.successfulySavedPopup = page.getByText("Successfully saved", { exact: true });
        this.workflowEditorPage = page.getByTestId('workflow-editor-page');
        this.imageRegistryText = this.page.locator('//*[text()="imageRegistry"] | //*[text()="ConfigMaps"]');
        this.connectToChartIcon = this.page.locator(`//*[contains(@class,"connect-to-chart-icon")]`);
        this.editProjectIcon = this.page.getByTestId(`edit-project-icon`);
        this.projectInputFieldForExternalCharts = this.page.locator(`//*[@name="project"]`);
        this.saveButton = this.page.getByTestId(`overview-project-save-button`);
        this.selectChartDropdownForExternalCharts = this.page.locator('//*[contains(@class,"chart-name__control")]');
        this.projectDropdownForExternalCharts = this.page.locator(`//*[contains(@class,'select-project-list__control')]`);
        this.fluxDeploymentRadioButton = this.page.getByTestId('flux-deployment-span');
    }

    async deployingChart(appName: string, projectName: string, envName: string, strategy: string, userDefinedGitRepo?: string, rbacRelatedData?: { isEligible: boolean }): Promise<string | null> {
        var ChartValue: string | null = ""
        //entering chart name 
        await this.imageRegistryText.waitFor({ timeout: 1 * 1000 * 60 });
        await expect(async () => {
            await this.page.reload();
            await this.chartNameField.click();
            await this.chartNameField.fill(appName);
            await this.page.waitForLoadState('load');
            // selecting project, env, gitops-helm option 
            await this.selectProjectAndEnvironment(projectName, envName);
            await this.selectDeploymentType(strategy);
            if (await this.addClusterHeader.isVisible()) {

                await this.setGitRepository(appName, userDefinedGitRepo);
            }
            // capturing chart version to verify on app details page 
            ChartValue = await this.chartVersionDropdown.first().textContent();
            await this.selectChartValues(ChartValue!);
            //verify that manifest ouput is not null
            // await this.manifestOutputButton.click();
            // await expect(this.page.locator('//*[text()="apiVersion"]').nth(0)).toBeVisible({ timeout: 45000 });
            if (rbacRelatedData) {
                let messageToVerify: string = rbacRelatedData.isEligible ? "Success" : "Error";
                let nthNumber: number = messageToVerify == "Success" ? 0 : 1
                console.log('message that we are verifying in toast is' + messageToVerify);
                await this.deployChartAndHandleGitSave(messageToVerify);
                
            }
            else {
                // deploying the chart and waiting for page navigation .
                await this.deployChartAndHandleGitSave("Success");
                await this.page.waitForNavigation({ timeout: 45000 });
                expect(this.page.url()).toContain('/app')
            }

        }).toPass({ timeout: 4 * 1000 * 60 });


        return ChartValue;
    }
    private async selectChartValues(valueVersion: string) {
        await this.chartVersionDropdown.nth(1).click();
        await this.page.locator(`//*[contains(@class,'chart-values-selector__menu')]//*[contains(text(),"Default") and contains(text(),'${valueVersion}')]`).click();
    }
    async verifyTheValueSelectedInChartValueDropdown(textToVerify: string) {
        await expect(this.chartVersionDropdown.nth(1)).toContainText(textToVerify);
    }
    private async deployChartAndHandleGitSave(messageToVerify:string){
       try{
            await BaseTest.checkToast(this.page, this.deployChartButton, messageToVerify);
       }catch(Error){
            await this.saveGitRepo.waitFor();
            await this.saveGitRepo.click();
            await this.deployChartButton.waitFor();
            await BaseTest.checkToast(this.page, this.deployChartButton, messageToVerify);
       }
    }
    private async selectProjectAndEnvironment(projectName: string, envName: string) {
        // selecting project from dropdown based on parameter passed 
        await this.projectDropdown.click();
        await this.page.locator(`//*[@role="listbox"]//*[text()="${projectName}"]`).click();
        // selecting environment based on parameter passed .
        await this.envDropDown.click();
        await this.page.locator(`//*[@role="listbox"]//*[text()="${envName}"]`).click();

    }
    private async selectDeploymentType(deploymentType: string) {
        // select helm or gitops option based on parameter 
        if (!process.env.clusterType?.includes('ea')) {
            if (deploymentType.toLowerCase().trim() == "helm") {
                await this.helmCheckBox.click({ delay: 500 });
            }
            else if(deploymentType==="Flux"){
                await this.fluxDeploymentRadioButton.click({delay:500});
            }
            else {
                await this.gitopsCheckBox.click({ delay: 500 });

            }
        }
    }
    async verifyChoosedCommit() {
        await expect(this.autoCreateRepository).toBeVisible();
    }

    async setGitRepository(appName: string, repo: string | undefined) {

        if (repo === "Auto-create repository") {

            await this.autoCreateRepositoryRadioButton.click();
            await this.saveGitRepo.click();
            await this.verifyChoosedCommit();
        } else {
            await this.commitManifestToDesiredRepoRadioButton.click();
            //await this.enterRepoUrl.fill(`https://gitlab.com/${Constants.GITHUB_ORGANISATION_NAME}/${appName}.git`);
            if (process.env.isStaging == "false") {
                await this.enterRepoUrl.fill(`https://gitlab.com/deep-test/${appName}.git`);
            }
            else {
                await this.enterRepoUrl.fill(`https://github.com/stage-gitops/${appName}.git`);
            }
            await this.saveGitRepo.click();
        }
    }

    async deletingChartStoreApp() {
        // steps to delete the chart store app 
        await this.configureTab.click();
        await this.deployedChartDeleteButton.click();
        await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, 'Success')
    }

    async ComparingManifest() {
        await expect(async () => {
            await this.configureTab.click();
            // await this.page.reload();
            // await this.manifestOutputButton.click();
            // await this.page.locator('//*[text()="apiVersion"]').first().waitFor({ timeout: 40000 });
            // await this.compareValuesOrManifestButton.click();
            // // we are verifying that "api-versiom" is visible 2 times to check that there is no null state 
            // await expect(this.page.locator('//*[text()="apiVersion"]')).toHaveCount(2, { timeout: 30000 });
        }).toPass({ timeout: 2 * 1000 * 60 });
        // await this.compareValuesOrManifestButton.click();

    }
    async updatingChartVersion(rbacRelatedData?: { isEligible: boolean }): Promise<string | null> {
        await this.pickChartVersion();
        // to update the chart we are taking the 2 value always , after that we are returning the same to verify on app detail page
        var updatedVersion: string | null = await this.chartVersionDropdown.first().textContent();
        await this.selectChartValues(updatedVersion!);
        if (rbacRelatedData) {
            let messageToVerify: string = rbacRelatedData.isEligible ? "Success" : "Error";
            await BaseTest.checkToast(this.page, this.deployChartButton, messageToVerify);
        }
        else {
            await this.deployChartButton.click();
            await this.page.waitForNavigation();
        }
        return updatedVersion;
    }
    async pickChartVersion(chartVersion?: string) {
        await this.chartVersionDropdown.first().click();
        if (chartVersion) {
            await this.page.locator(`//h4[text()="${chartVersion}"]`).click();
        }
        else {
            await this.selectChartVersion.click();
        }
    }



    /**
     * use this method to link eexternal charts
     * @param data 
     */
    async linkExternalHelmChartAndTrigger(data?: { projectName?: string, chartVersion?: string, chartValue?: string }, chartSource: string = 'bitnami') {
        await this.configureTab.click();
        await this.connectToChartIcon.click();
        await this.selectChartDropdownForExternalCharts.click();
        await this.page.locator(`//*[@role="option"]//*[text()="${chartSource}"]`).first().click();
        if (data?.projectName) {
            await this.editProjectIcon.click();
            await this.projectDropdownForExternalCharts.click();
            await this.page.locator(`//*[@role="listbox"]`).waitFor();
            await this.page.keyboard.type(data.projectName);
            await this.page.keyboard.press('Enter');
            await this.saveButton.click({ delay: 500 });
        }
        if (data?.chartVersion) {
            await this.pickChartVersion(data.chartVersion!);
        }
        if (data?.chartValue) {
            await this.selectChartValues(data.chartValue);
        }
        await expect(async () => {
            await this.deployChartButton.click();
            await this.page.waitForNavigation({ timeout: 45000 });
            expect(this.page.url()).toContain('/app')
        }).toPass({ timeout: 3 * 1000 * 60 });

    }
}