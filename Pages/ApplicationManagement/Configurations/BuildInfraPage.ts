import { Page, Locator, expect } from '@playwright/test';
import { AllTypes } from '../../../utilities/Types';
import { BaseTest } from '../../../utilities/BaseTest';
import { JobsPage } from '../../AutomationandEnablement/Jobs';
import { BasePage } from '../../BasePage';

/**
 * BuildInfra Page Object Model
 *
 * This class represents the Build Infrastructure page in the application and provides
 * methods to interact with various elements for managing build profiles, configurations,
 * and target platforms.
 *
 * @extends BasePage
 */
export class BuildInfra extends BasePage {

  // Profile Management Elements
  // ---------------------------
  readonly createProfileButton: Locator;           // Button to create a new profile
  readonly editDefaultProfileButton: Locator;      // But<PERSON> to edit the default profile
  readonly deleteProfileDialogButton: Locator;     // Button to confirm profile deletion in dialog
  readonly inputFieldProfileName: Locator;         // Input field for profile name
  readonly description: Locator;                   // Text area for profile description
  readonly profileSaveUpdateButton: Locator;       // Button to save or update profile
  readonly searchProfile: Locator;                 // Search input for finding profiles
  readonly profileTabButton: Locator;              // Tab button to switch to profiles view
  readonly genericEmptyState: Locator;             // Empty state indicator when no profiles exist

  // Navigation Elements
  // -------------------
  readonly goToBuildInfraApplicationsLink: Locator; // Link to navigate to applications tab
  readonly goToBuildInfraProfilesLink: Locator;     // Link to navigate to profiles tab

  // Build Configuration Elements
  // ----------------------------
  readonly cpuCheckBox: Locator;                   // Checkbox to enable CPU configuration
  readonly memoryCheckBox: Locator;                // Checkbox to enable memory configuration
  readonly buildTimeOut: Locator;                  // Checkbox to enable build timeout configuration
  readonly cpuRequestInputField: Locator;          // Input field for CPU request value
  readonly cpuLimitInputField: Locator;            // Input field for CPU limit value
  readonly memoryRequestInputField: Locator;       // Input field for memory request value
  readonly memoryLimitInputField: Locator;         // Input field for memory limit value
  readonly timeoutInputField: Locator;             // Input field for timeout value

  // Unit Selection Elements
  // -----------------------
  readonly selectUnitDropdown: Locator;            // Dropdown control for selecting units
  readonly selectUnitDropdownMenu: Locator;        // Dropdown menu for unit options
  readonly listEntityDiv: Locator;                 // Generic list entity for dropdown options

  // Application Management Elements
  // -------------------------------
  readonly bulkSelectionPopUpMenu: Locator;        // Popup menu for bulk selection actions
  readonly bulkActionSelectAllOnPage: Locator;     // Button to select all items on current page
  readonly clearApplicationSearch: Locator;        // Button to clear application search
  readonly applicationSearchBar: Locator;          // Search input for finding applications
  readonly buildInfraSelectControl: Locator;       // Dropdown control for selecting build infrastructure
  readonly buildInfraSelectMenu: Locator;          // Dropdown menu for build infrastructure options
  readonly buildInfraConfirmationChangeButton: Locator; // Button to confirm bulk changes

  // Target Platform Elements
  // ------------------------
  readonly runnerOrTargetPlatformFilter: Locator;  // Filter dropdown for runner/target platform
  readonly addTargetPlatformButton: Locator;       // Button to add new target platform
  readonly targetPlatformInputField: Locator;      // Input field for target platform selection
  readonly saveTargetPlatformChanges: Locator;     // Button to save target platform changes

  // Toggle and Checkbox Elements
  // ----------------------------
  readonly inheritenceToggleButtonSpan: Locator;   // Toggle button span for inheritance settings
  readonly inheritenceToggleButtonInputField: Locator; // Input field within inheritance toggle
  readonly activeK8sDriverCheckboxSpan: Locator;   // Checkbox span for K8s driver activation
  readonly activeK8sDriverCheckboxInput: Locator;  // Input field for K8s driver checkbox

  // Modal and Action Elements
  // -------------------------
  readonly disableButton: Locator;                 // Button to disable features
  readonly rightModalDrawer: Locator;              // Right-side modal drawer container
  readonly deleteIcon: Locator;                    // Delete icon within modal drawer

  // Composed Page Objects
  // ---------------------
  readonly jobPage: JobsPage;                      // Jobs page object for related functionality

  /**
   * Constructor initializes all page locators with proper organization
   * @param page - Playwright Page object
   */
  constructor(public page: Page) {
    super(page);

    // Initialize Profile Management Elements
    this.createProfileButton = page.getByTestId("create-profile");
    this.editDefaultProfileButton = page.getByTestId("edit-default-profile");
    this.deleteProfileDialogButton = page.getByTestId("dialog-delete");
    this.inputFieldProfileName = page.getByPlaceholder("Enter a name eg. java/node/small/medium/large");
    this.description = page.getByTestId("build-infra-profile-description");
    this.profileSaveUpdateButton = page.getByTestId('save-profile-btn');
    this.searchProfile = page.getByTestId("search-profile");
    this.profileTabButton = page.locator(`//button[text()="Profiles"]`);
    this.genericEmptyState = page.getByTestId("generic-empty-state");

    // Initialize Navigation Elements
    this.goToBuildInfraApplicationsLink = page.locator("//button[contains(text(),'Applications')]");
    // Note: Fixed duplicate - this should point to Profiles tab
    this.goToBuildInfraProfilesLink = page.locator("//button[contains(text(),'Profiles')]");

    // Initialize Build Configuration Elements
    this.cpuCheckBox = page.getByTestId("cpu_marker-chk-span");
    this.memoryCheckBox = page.getByTestId("memory_marker-chk-span");
    this.buildTimeOut = page.getByTestId("timeout_marker-chk-span");
    this.cpuRequestInputField = page.getByTestId("cpu_request-input-field");
    this.cpuLimitInputField = page.getByTestId("cpu_limit-input-field");
    this.memoryRequestInputField = page.getByTestId("memory_request-input-field");
    this.memoryLimitInputField = page.getByTestId("memory_limit-input-field");
    this.timeoutInputField = page.getByTestId("timeout-input-field");

    // Initialize Unit Selection Elements
    this.selectUnitDropdown = page.locator(".unit-dropdown__control");
    this.selectUnitDropdownMenu = page.locator(".unit-dropdown__menu");
    this.listEntityDiv = page.locator('//*[@role="option"]');

    // Initialize Application Management Elements
    this.bulkSelectionPopUpMenu = page.locator("//*[@aria-label='Bulk selection dropdown']");
    this.bulkActionSelectAllOnPage = page.getByTestId("action-menu-item-SELECT_ALL_ON_PAGE");
    this.clearApplicationSearch = page.getByTestId("clear-application-search");
    this.applicationSearchBar = page.getByTestId("application-search-bar");
    this.buildInfraSelectControl = page.locator('.build-infra-select__control');
    this.buildInfraSelectMenu = page.locator('.build-infra-select__menu');
    this.buildInfraConfirmationChangeButton = this.dialogDeleteConfirmationButton;

    // Initialize Target Platform Elements
    this.runnerOrTargetPlatformFilter = page.locator(`//*[contains(@class,'build-infra__target-platform-filter__control')]`);
    this.addTargetPlatformButton = page.getByTestId('add-target-platform-button');
    this.targetPlatformInputField = page.locator('//*[@name="create-target-platform-form__select-platform"]');
    this.saveTargetPlatformChanges = page.locator('//*[@data-testid="save-target-platform-changes" or text()="Save changes"]');

    // Initialize Toggle and Checkbox Elements
    this.inheritenceToggleButtonSpan = page.locator('//*[contains(@data-testid,"-toggle")]');
    this.inheritenceToggleButtonInputField = this.inheritenceToggleButtonSpan.locator('xpath=preceding-sibling::input');
    // Note: 'undefined-chk-span' suggests this test-id needs to be fixed in the application
    this.activeK8sDriverCheckboxSpan = page.getByTestId('undefined-chk-span');
    this.activeK8sDriverCheckboxInput = this.activeK8sDriverCheckboxSpan.locator('xpath=preceding-sibling::input');

    // Initialize Modal and Action Elements
    this.disableButton = page.locator('//*[text()="Disable"]');
    this.rightModalDrawer = page.locator('//*[@class="drawer right show"]');
    this.deleteIcon = this.rightModalDrawer.locator(`//*[contains(@data-testid,'delete')]`);

    // Initialize Composed Page Objects
    this.jobPage = new JobsPage(page);
  }
  /**
   * Searches for a profile by name
   * Note: To edit Default Profile, use name = "default"
   * @param name - The name of the profile to search for
   * @returns Promise<void>
   */
  async searchAProfile(name: string): Promise<void> {
    await this.searchProfile.click({ delay: 300 });
    await this.searchProfile.fill(name);
    await this.page.keyboard.press('Enter');
  }

  /**
   * Deletes a profile by name
   * @param name - The name of the profile to delete
   * @returns Promise<void>
   */
  async deleteGivenProfile(name: string): Promise<void> {
    // Search for the profile first
    await this.searchProfile.click({ delay: 300 });
    await this.searchProfile.fill(name);
    await this.page.keyboard.press('Enter');

    // Click the delete button for the specific profile
    await this.page.locator(`//*[@data-testid="profile-delete-${name}"]`).click({ delay: 300 });

    // Confirm deletion
    await this.dialogDeleteConfirmationButton.click();

    // Wait for deletion to complete
    await this.page.waitForTimeout(3000);
  }

  /**
   * Clicks on the edit icon of a specific profile
   * @param name - The name of the profile to edit
   * @returns Promise<void>
   */
  async clickOnEditIconOfAProfile(name: string): Promise<void> {
    const profileRowLocator = await this.generateLocatorForProfileRowDiv(name);

    // Hover over the profile row to reveal the edit button
    await profileRowLocator.hover();

    // Click the edit button for the specific profile
    await this.page.getByTestId(`profile-edit-${name}`).click();
  }

  /**
   * Creates or edits a build infrastructure profile
   * This is the main method that orchestrates profile creation/editing
   * @param data - Profile configuration data containing profile name and various configurations
   * @returns Promise<void>
   */
  async createOrEditBuildInfraProfile(data: AllTypes.BuildInfra.editOrCreateProfile): Promise<void> {
    // Handle profile creation/editing for enterprise clusters
    if (process.env.clusterType === "enterprise") {
      await this.searchProfile.fill(data.profileName);
      await this.page.keyboard.press('Enter');

      // Create profile if it doesn't exist (empty state is visible)
      if (await this.genericEmptyState.isVisible()) {
        await this.createProfileButton.click();
        await this.inputFieldProfileName.fill(data.profileName);
        await this.description.fill("This is for testing");
        await this.savingOrUpdatingProfile();
      }

      await this.clickOnEditIconOfAProfile(data.profileName);
    }

    // Configure runner build settings if provided
    if (data.runnerConfiguration?.buildConfiguration) {
      if (process.env.clusterType === "enterprise") {
        await this.page.locator(`//*[contains(@data-testid,'-runner-platform')]`).first().click();
      }
      await this.setBuildConfiguration(data.runnerConfiguration.buildConfiguration);
      await BaseTest.checkToast(this.page, this.saveTargetPlatformChanges, 'Success');
    }

    // Configure ConfigMaps/Secrets if provided
    if (data.runnerConfiguration?.cmcsConfiguration) {
      await this.page.locator(`//*[contains(@data-testid,'-runner-platform')]`).first().click();
      await this.configureCmCsToAProfile(data.runnerConfiguration.cmcsConfiguration);
      await BaseTest.checkToast(this.page, this.saveTargetPlatformChanges, 'Success');
    }

    // Configure K8s driver settings if provided
    if (data.k8sDriverConfiguration) {
      await this.addTargetPlatform(data.k8sDriverConfiguration);
    }
  }

  /**
   * Sets build configuration for CPU, memory, timeout, node selector, and tolerance
   * @param data - Build configuration data containing CPU, memory, timeout settings
   * @returns Promise<void>
   */
  async setBuildConfiguration(data: AllTypes.BuildInfra.buildConfiguration): Promise<void> {
    const keys = Object.keys(data) as (keyof typeof data)[];

    for (const key of keys) {
      // Click on target platform for enterprise clusters
      if (process.env.clusterType === "enterprise") {
        await this.page.getByTestId(`target-platform-${key}`).click();
      }

      const configItem = data[key];
      if (!configItem) continue;

      // Toggle inherited/override setting for enterprise clusters
      if (process.env.clusterType === "enterprise") {
        await this.toggleInheritedOrOverride(configItem.isInherited);
      }

      // Skip if inherited
      if (configItem.isInherited) continue;

      // Handle request values (CPU/Memory)
      if ('request' in configItem && configItem.request) {
        await this.page.getByTestId(`${key}_request-input-field`).fill(configItem.request.value);
        await this.page.getByTestId(`${key}_request-input-field`)
          .locator('xpath=parent::div')
          .locator('xpath=following-sibling::div')
          .click();
        await this.listEntityDiv.locator(`//*[text()="${configItem.request.units}"]`).click();
      }

      // Handle limit values (CPU/Memory)
      if ('limit' in configItem && configItem.limit) {
        await this.page.getByTestId(`${key}_limit-input-field`).fill(configItem.limit.value);
        await this.page.getByTestId(`${key}_limit-input-field`)
          .locator('xpath=parent::div')
          .locator('xpath=following-sibling::div')
          .click();
        await this.listEntityDiv.locator(`//*[text()="${configItem.limit.units}"]`).click();
      }

      // Handle timeout configuration
      if (key === "timeout" && data.timeout) {
        await this.page.getByTestId('timeout-input-field').fill(data.timeout.value);
        await this.page.locator(`//*[contains(@class,'build-infra-unit-select__control')]`).click();
        await this.listEntityDiv.locator(`//*[text()="${data.timeout.units}"]`).click();
      }

      // Handle node selector and tolerance key-value pairs
      if ('key' in configItem && configItem.key) {
        await this.page.locator(`//*[@data-testid="data-table-add-row-button" or @data-testid="add-item-button"]`).click();
        await this.page.locator(`//*[@placeholder="Key"]`).first().fill(configItem.key);
        await this.page.locator('//*[@placeholder="Enter value or variable"]').first().fill(configItem.value!);
      }
    }
  }

  /**
   * Adds target platforms for K8s driver configuration
   * @param data - Array of K8s driver configuration objects
   * @returns Promise<void>
   */
  async addTargetPlatform(data: AllTypes.BuildInfra.k8sDriverConfiguration[]): Promise<void> {
    for (const platformConfig of data) {
      // Toggle K8s provider on/off
      await this.turnOnOffK8sProvider(platformConfig.turnOn);

      // Configure target platform if specified
      if (platformConfig.targetPlatform) {
        try {
          // Try to click existing platform edit button
          await this.page.locator(
            `//*[contains(@data-testid,'-${platformConfig.targetPlatform}-platform') and contains(@data-testid,'edit')]`
          ).first().click({ timeout: 6000 });
        } catch (error) {
          // Add new target platform if edit button not found
          await this.addTargetPlatformButton.click();
          await this.targetPlatformInputField.fill(platformConfig.targetPlatform);
          await this.page.keyboard.press('Enter');
        }
      }

      // Apply configuration if provided
      if (platformConfig.configuration) {
        await this.setBuildConfiguration(platformConfig.configuration);
      }

      // Save changes if platform is enabled and configured
      if (platformConfig.turnOn && platformConfig.targetPlatform) {
        await this.saveTargetPlatformChanges.click();
      }
    }
  }

  /**
   * Toggles the K8s provider on or off
   * @param turnOn - Whether to turn on (true) or off (false) the K8s provider
   * @returns Promise<void>
   */
  async turnOnOffK8sProvider(turnOn: boolean): Promise<void> {
    const isCurrentlyChecked = await this.activeK8sDriverCheckboxInput.isChecked();

    if (isCurrentlyChecked !== turnOn) {
      await this.activeK8sDriverCheckboxSpan.click();

      // Handle disable confirmation if turning off
      if (!turnOn) {
        try {
          await this.disableButton.click({ timeout: 5000 });
        } catch (error) {
          console.log('disable button is not visible' + error);
        }
      }
    }
  }

  /**
   * Toggles between inherited and override settings
   * @param turnOn - Whether to turn on (true) inheritance or override (false)
   * @returns Promise<void>
   */
  async toggleInheritedOrOverride(turnOn: boolean): Promise<void> {
    try {
      const isCurrentlyChecked = await this.inheritenceToggleButtonInputField.isChecked({ timeout: 3000 });

      if (turnOn !== isCurrentlyChecked) {
        await this.inheritenceToggleButtonSpan.click({ timeout: 3000 });
      }
    } catch (error) {
      console.log('inheritence or overriden toggle button is not visible' + error);
    }
  }

  async selectConfigurationsToSetOrUpdate(
    whichConfigurations: string,
    RequestUnit: string,
    RequestValue: Number,
    LimitOrTimeoutUnit: string,
    LimitValue: Number,
    Timeoutvalue?: Number | any
  ) {
    let var1 = "Limit";

    switch (whichConfigurations) {
      case "Build":
        var1 = "TimeOut";
        if (await this.buildTimeOut.isChecked() === false) {
          await this.buildTimeOut.click({ delay: 400 });
        }
        await this.timeoutInputField.fill(Timeoutvalue.toString());
        break;
      case "CPU":
        if (await this.cpuCheckBox.isChecked() === false) {
          await this.cpuCheckBox.click({ delay: 400 });
        }
        await this.cpuRequestInputField.fill(RequestValue.toString());
        await this.cpuLimitInputField.fill(LimitValue.toString());
        break;
      case "Memory":
        if (await this.memoryCheckBox.isChecked() === false) {
          await this.memoryCheckBox.click({ delay: 400 });
        }
        await this.memoryRequestInputField.fill(RequestValue.toString());
        await this.memoryLimitInputField.fill(LimitValue.toString());
        break;
    }
    if (whichConfigurations != "Build") {
      await this.page
        .locator(
          `//div[h3[contains(text(),"${whichConfigurations}")]]//div[label[text()="Request"]]`
        )
        .locator(".unit-dropdown__control")
        .click();
      await this.page
        .locator(".unit-dropdown__menu")
        .getByText(RequestUnit, { exact: true })
        .click({ delay: 500 });
    }
    await this.page.waitForTimeout(1000);
    await this.page
      .locator(
        `//div[h3[contains(text(),"${whichConfigurations}")]]//div[label[text()="${var1}"]]`
      )
      .locator(".unit-dropdown__control")
      .click()
    await this.page
      .locator(".unit-dropdown__menu")
      .getByText(LimitOrTimeoutUnit, { exact: true })
      .click({ delay: 300 });
  }

  /**
   * Saves or updates the current profile
   * @returns Promise<void>
   */
  async savingOrUpdatingProfile(): Promise<void> {
    await this.profileSaveUpdateButton.click();
  }

  /**
   * Applies given profiles to given applications
   * @param profiles - Array of profile names to apply
   * @param applications - Array of application names to apply profiles to
   * @returns Promise<void>
   */
  async applyGivenProfilesToGivenApplications(
    profiles: string[],
    applications: string[]
  ): Promise<void> {
    // Navigate to applications tab
    await this.goToBuildInfraApplicationsLink.click({ delay: 300 });

    for (let i = 0; i < applications.length; i++) {
      const currentApplication = applications[i];
      const currentProfile = profiles[i];

      // Search for the application
      await this.applicationSearchBar.click({ delay: 300 });
      await this.applicationSearchBar.fill(currentApplication);
      await this.page.keyboard.press('Enter');

      // Select the application
      await this.bulkSelectionApplyProfileCheckbox.click();
      try {
        await this.bulkActionSelectAllOnPage.click({ delay: 300 });
      }
      catch (error) {
        console.log('bulk select all on page is not visible' + error);
      }
      // Apply the profile
      await this.buildInfraSelectControl.nth(1).click({ delay: 300 });
      await this.buildInfraSelectMenu.getByText(currentProfile).click({ delay: 300 });
      await this.buildInfraConfirmationChangeButton.click({ delay: 300 });

      // Wait for the operation to complete
      await this.page.waitForTimeout(2000);
    }
  }

  /**
   * Applies filter on runner configuration or target platform
   * @param filterValue - The filter value to apply
   * @returns Promise<void>
   */
  async applyFilterOnRunnerConfigurationOrTargetPlatform(
    filterValue: 'Runner configuration' | 'linux/arm64' | 'linux/amd64' | 'linux/arm/v7'
  ): Promise<void> {
    await this.runnerOrTargetPlatformFilter.click();
    await this.listEntityDiv.locator(`//*[text()="${filterValue}"]`).click();
  }



  async fetchConfigurationOfAProfileAndVerify(profileName: string, filterType: AllTypes.BuildInfra.RunnerOrTargetPlatformFilterType, configToCompare?: AllTypes.BuildInfra.configurationAppliedOnAProfile | string): Promise<AllTypes.BuildInfra.configurationAppliedOnAProfile | string> {
    let configurationSectionKeys: (keyof AllTypes.BuildInfra.configurationAppliedOnAProfile)[] = ['cpu', 'memory', 'timeout', 'node selector', 'tolerance', 'cm', 'cs'];
    let result = {} as AllTypes.BuildInfra.configurationAppliedOnAProfile;
    let finalResult: AllTypes.BuildInfra.configurationAppliedOnAProfile | string;
    let profileRowLocator = await this.generateLocatorForProfileRowDiv(profileName);
    await this.profileTabButton.click();
    await this.applyFilterOnRunnerConfigurationOrTargetPlatform(filterType);
    if (await profileRowLocator.locator('//*[@data-testid="cpu-heading"]').isVisible()) {
      for (let key of configurationSectionKeys) {
        let inhOrOverLocator: Locator = key == 'cpu' || key == "memory" || key == "timeout" ? profileRowLocator.getByTestId(`${key}-heading`).locator('xpath=following-sibling::div') : profileRowLocator.getByTestId(`${key}-heading`).locator('xpath=parent::div').locator('xpath=following-sibling::div');
        result[key] ??= { value: "", overOrInh: "" };
        result[key].value = String(await profileRowLocator.getByTestId(`${key}-heading`).textContent());
        if (profileName != 'global') {
          result[key].overOrInh = String(await inhOrOverLocator.textContent());
        }
      }
      finalResult = result;
    }
    else {
      finalResult = String(await profileRowLocator.locator('//span').first().textContent());
    }
    if (configToCompare && typeof configToCompare === 'object') {
      let keys = Object.keys(configToCompare);
      for (let key of keys) {
        let expected = JSON.stringify(finalResult[key]);
        let received = JSON.stringify(configToCompare[key]);
        let expectedValue = BaseTest.normalizeString(expected);
        let receivedValue = BaseTest.normalizeString(received);
        expect(receivedValue).toEqual(expectedValue);
      }
    }
    else if (configToCompare && typeof configToCompare === 'string') {
      expect(finalResult).toEqual(configToCompare)
    }
    return finalResult;
  }
  /**
   * Configures ConfigMaps and Secrets for a profile
   * @param data - Array of ConfigMap/Secret configuration objects
   * @returns Promise<void>
   */
  async configureCmCsToAProfile(data: AllTypes.BuildInfra.cmcsConfiguration[]): Promise<void> {
    for (const configItem of data) {
      console.log('printing type: ' + configItem.type);

      const isPresent = await this.jobPage.clickOnAnyCmCSAndReturnExistence(
        configItem.cmOrSecret,
        configItem.name
      );

      if (isPresent && configItem.isInherited === false) {
        // Edit existing ConfigMap/Secret
        await this.toggleInheritedOrOverride(configItem.isInherited);
        await this.jobPage.editCmCs(
          configItem.cmOrSecret,
          configItem.name,
          'base',
          false,
          configItem.configuration?.key,
          configItem.configuration?.value,
          true
        );
      } else {
        // Add new ConfigMap/Secret
        await this.page.locator(
          `//*[@class="drawer right show"]//*[contains(text(),"${configItem.cmOrSecret}")]/ancestor::div[1]//button`
        ).nth(1).click();

        await this.jobPage.addConfigMapOrSecret({
          resource: configItem.cmOrSecret,
          resourceType: configItem.type,
          resourceName: configItem.name,
          Data: [`${configItem.configuration?.key}:${configItem.configuration?.value}`],
          mode: 'gui'
        }, false, true);
      }
    }
  }

  /**
   * Verifies that applications are assigned to a specific profile
   * @param profileName - Name of the profile to check
   * @param appName - Name of the application to verify
   * @returns Promise<void>
   */
  async verifyApplicationsAssignedToAProfile(profileName: string, appName: string): Promise<void> {
    // Navigate to profiles tab
    await this.profileTabButton.click();

    // Search for the specific profile
    await this.searchAProfile(profileName);

    // Click on profile impact to see assigned applications
    await this.page.getByTestId(`profile-impact-${profileName}`).click();

    // Verify the application is visible in the list
    await expect(this.page.locator(`//*[text()="${appName}"]`)).toBeVisible();
  }

  /**
   * Generates a locator for a profile row div based on profile name
   * @param profileName - Name of the profile
   * @returns Promise<Locator> - The locator for the profile row
   */
  async generateLocatorForProfileRowDiv(profileName: string): Promise<Locator> {
    return this.page.locator(
      `//*[@data-testid="edit-profile-${profileName}"]/ancestor::div[contains(@class,'infra-list-container')]`
    );
  }

  /**
   * Removes ConfigMap or Secret from a profile
   * @param resourceName - Name of the resource to remove
   * @param resourceType - Type of resource ("ConfigMaps" or "Secret")
   * @returns Promise<void>
   */
  async removeCmCsOfAProfile(
    resourceName: string,
    resourceType: "ConfigMaps" | "Secret"
  ): Promise<void> {
    // Click on runner platform
    await this.page.locator(`//*[contains(@data-testid,'-runner-platform')]`).first().click();

    // Find and click on the ConfigMap/Secret
    await this.jobPage.clickOnAnyCmCSAndReturnExistence(resourceType, resourceName);

    // Delete the resource
    await this.deleteIcon.click();

    // Save the changes
    await this.saveTargetPlatformChanges.click();
  }

}
















