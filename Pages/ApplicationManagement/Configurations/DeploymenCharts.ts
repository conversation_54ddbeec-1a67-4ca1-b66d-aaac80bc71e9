
/**
 * DeploymentCharts Page Object Model
 *
 * This class represents the Deployment Charts page in the application and provides
 * methods to interact with various elements on the page, particularly for managing
 * chart configurations and hibernation patches.
 */
import { Locator, Page } from "playwright";
import { expect } from "playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { BasePage } from "../../BasePage";
import { BaseDeploymentTemplatePage } from "../Applications/BaseDeploymentTemplatePage";
import { ApiUtils } from "../../../utilities/ApiUtils";

// Constants for timeouts to improve maintainability
const TIMEOUTS = {
    ELEMENT_VISIBILITY: 10000,
    NAVIGATION: 30000,
    FILE_UPLOAD: 60000
};

export class DeploymentCharts extends BasePage {
    // UI Element Locators
    // ------------------
    // Chart management elements
    readonly uploadChartButton: Locator;  // Button to upload a new chart
    readonly searchBar: Locator;          // Search input field for finding charts
    readonly selectTgzFile: Locator;      // Button to select a .tgz file for upload
    readonly saveButton: Locator;         // Button to save chart configuration

    // Chart editing elements
    readonly editChartButton: Locator;           // Button to open chart editing options
    readonly editGuiSchemaButton: Locator;       // Button to edit GUI schema
    readonly editHibernationPatchButton: Locator; // Button to edit hibernation patch
    readonly resetToDefaultButton: Locator;
    readonly preViewGuiSchemaButton: Locator;     // Button to preview GUI schema

    /**
     * Constructor initializes all page locators
     * @param page - Playwright Page object
     */
    constructor(public page: Page) {
        super(page);

        // Initialize locators with appropriate selectors
        // Using data-testid attributes where available for better reliability
        this.uploadChartButton = this.page.getByTestId('upload-custom-chart-button');
        this.searchBar = this.page.getByTestId('search-bar');
        this.selectTgzFile = this.page.getByTestId('select-tgz-file-button');
        this.saveButton = this.page.getByTestId('save-chart');

        // Note: These would ideally use data-testid selectors for better reliability
        this.editGuiSchemaButton = this.page.locator(`//*[contains(@aria-label,'Edit GUI Schema')]`);
        this.editHibernationPatchButton = this.page.locator(`//*[text()="Hibernation Patch"]`);
        this.preViewGuiSchemaButton = this.page.getByTestId('preview-gui-schema');
        this.editChartButton = this.page.getByTestId('chart-actions');
        this.resetToDefaultButton = this.page.getByTestId('reset-to-default-schema');
    }


    /**
     * Navigates to the Deployment Charts page and verifies it loaded successfully
     * @returns Promise<void>
     */
    async navigateToDeploymentChartsPage(): Promise<void> {
        // Navigate to the deployment charts page using environment variable for base URL
        await this.page.goto(process.env.BASE_SERVER_URL + '/global-config/deployment-charts');

        // Verify the page loaded by checking for the upload button
        await expect(this.uploadChartButton).toBeVisible({ timeout: TIMEOUTS.ELEMENT_VISIBILITY });
    }

    /**
     * Searches for a chart and checks if it exists
     *
     * @param chartName - The name of the chart to search for
     * @returns Promise<boolean> - True if the chart exists, false otherwise
     */
    async isChartVisible(chartName: string): Promise<boolean> {
        // Clear existing search text and enter new search term
        await this.searchBar.clear();
        await this.searchBar.fill(chartName);
        await this.page.keyboard.press('Enter');
        // Get the chart row and check if it's visible
        const chartRow = this.getRowLocatorForChartOnChartListingPage(chartName);
        return await chartRow.isVisible({ timeout: TIMEOUTS.ELEMENT_VISIBILITY });
    }

    /**
     * Uploads a custom chart file
     * @param filePath - Path to the chart file (.tgz)
     * @returns Promise<void>
     */
    async uploadCustomChart(filePath: string): Promise<void> {
        // Click upload button
        await this.uploadChartButton.click();

        // Wait for file chooser dialog and click the select file button
        const [fileChooserObject] = await Promise.all([
            this.page.waitForEvent('filechooser'),
            this.selectTgzFile.click({ delay: 2000 })
        ]);

        // Set the file to upload
        await fileChooserObject.setFiles(filePath);

        // Verify success toast message appears
        await BaseTest.checkToast(this.page, this.saveButton, 'Success');
    }

    /**
     * Clicks on Edit GUI Schema or Edit Hibernation Patch button for a specific chart
     * @param chartName - The name of the chart to edit
     * @param editHibernationPatch - If true, clicks Hibernation Patch button; otherwise, clicks Edit GUI Schema
     * @returns Promise<void>
     */
    async clickOnEditGuiSchemaOrEditHibernationPatchButton(
        chartName: string,
        editHibernationPatch: boolean
    ): Promise<void> {
        // Get the row for the specified chart
        const chartRowLocator = this.getRowLocatorForChartOnChartListingPage(chartName);

        // Determine which button to click based on the editHibernationPatch parameter
        const guiSchemaOrHibernationPatchEditButton = editHibernationPatch
            ? this.editHibernationPatchButton
            : this.editGuiSchemaButton;

        // Click the edit button for the chart, then click the specific edit option
        await chartRowLocator.locator(this.editChartButton).click();
        await guiSchemaOrHibernationPatchEditButton.click();
    }

    /**
     * Clears the hibernation patch text area
     * @returns Promise<void>
     */
    async clearHibernationPatchTextArea(): Promise<void> {
        // Use the inherited method to clear the text area
        await this.clearAnyTextArea(this.codeMirrorEditorTextArea);
    }

    /**
     * Enters hibernation patch configuration in the text area
     * @param configuration - YAML configuration string to enter
     * @returns Promise<void>
     */
    async enterHibernationPatchConfiguration(configuration: string): Promise<void> {
        // Use the inherited method to enter configuration in the text area
        await this.enterConfigurationInTextArea(this.codeMirrorEditorTextArea, configuration);
    }

    /**
     * Verifies hibernation patch configuration values
     * @param baseDeploymentTemplateObject - The base deployment template object
     * @param keyToSearch - Array of keys to verify
     * @param valueToSearch - Array of expected values corresponding to the keys
     * @returns Promise<void>
     */
    async verifyHibernationPatchConfiguration(
        baseDeploymentTemplateObject: BaseDeploymentTemplatePage,
        keyToSearch: string[],
        valueToSearch: string[]
    ): Promise<void> {
        // Use the base deployment template object to verify field values
        await baseDeploymentTemplateObject.verfiyFieldValues(keyToSearch, valueToSearch);
    }

    /**
     * Gets the locator for a chart row by chart name
     * @param chartName - The name of the chart
     * @returns Locator - The locator for the chart row
     */
    getRowLocatorForChartOnChartListingPage(chartName: string): Locator {
        // Use XPath to find the chart row by chart name
        return this.page.locator(`//*[text()="${chartName}"]/ancestor::div[contains(@class,'chart-list-row')]`);
    }

    /**
     * Saves the chart configuration and verifies the toast message
     * @param isSuccessfullySaved - If true, expects success message; otherwise, expects error
     * @returns Promise<void>
     */
    async saveTheChartConfigurationAndVerifyToastMessage(isSuccessfullySaved: boolean): Promise<void> {
        // Determine expected toast message based on success parameter
        const toastMessage = isSuccessfullySaved ? 'Success' : 'Error';

        // Click save button and verify toast message
        await BaseTest.checkToast(this.page, this.saveButton, toastMessage);
    }

    /**
     * Configures hibernation patch for a specific chart
     *
     * This method provides a comprehensive way to configure hibernation patch settings
     * for a chart. It can either reset to default configuration or apply a custom
     * configuration, and optionally verify the configuration after applying it.
     *
     * @param config - Configuration object for hibernation patch
     * @param config.chartName - The name of the chart to configure
     * @param config.resetToDefault - Whether to reset to default configuration
     * @param config.configuration - The YAML configuration to enter (required if resetToDefault is false)
     * @param config.keyToVerify - Array of keys to verify after configuration (optional)
     * @param config.valueToVerify - Array of expected values corresponding to the keys (optional)
     * @param config.baseDeploymentTemplateObject - Object needed for verification (optional)
     * @param config.expectSuccess - Whether to expect success (default: true)
     * @returns Promise<void>
     */
    async configureHibernationPatch(config: {
        chartName: string;
        resetToDefault: boolean;
        configuration?: string;
        keyToVerify?: string[];
        valueToVerify?: string[];
        baseDeploymentTemplateObject?: BaseDeploymentTemplatePage;
        expectSuccess?: boolean;
    }): Promise<void> {
        // Set default value for expectSuccess if not provided
        const expectSuccess = config.expectSuccess !== undefined ? config.expectSuccess : true;

        try {
            // Navigate to the chart's hibernation patch editor
            await this.clickOnEditGuiSchemaOrEditHibernationPatchButton(config.chartName, true);

            if (config.resetToDefault) {
                // Reset to default configuration
                await this.resetToDefaultButton.click();

                // Verify configuration if verification parameters are provided
                if (config.baseDeploymentTemplateObject && config.keyToVerify && config.valueToVerify) {
                    await this.verifyHibernationPatchConfiguration(
                        config.baseDeploymentTemplateObject,
                        config.keyToVerify,
                        config.valueToVerify
                    );
                }
            } else {
                // Clear existing configuration
                await this.clearHibernationPatchTextArea();

                // Enter new configuration if provided
                if (config.configuration) {
                    await this.enterHibernationPatchConfiguration(config.configuration);
                }
            }

            // Save the configuration and verify the expected result
            await this.saveTheChartConfigurationAndVerifyToastMessage(expectSuccess);

            console.log(`Successfully configured hibernation patch for chart: ${config.chartName}`);
        } catch (error) {
            console.error(`Failed to configure hibernation patch for chart: ${config.chartName}`, error);
            throw error; // Re-throw to allow caller to handle the error
        }
    }


    /**
     * Checks if a custom chart is already uploaded and uploads it if not
     *
     * This helper method is useful for test setup to ensure a required chart
     * is available before running tests.
     *
     * @param chartName - The name of the chart to check
     * @param chartVersion - The version of the chart
     * @param filePath - Path to the chart file (.tgz) for upload if needed
     * @param apiUtils - ApiUtils instance for API operations
     * @param token - Authentication token for API operations
     * @returns Promise<boolean> - True if chart was already uploaded, false if it needed to be uploaded
     */
    async ensureCustomChartIsUploaded(
        chartName: string,
        chartVersion: string,
        filePath: string,
        apiUtils: ApiUtils,
        token: string
    ): Promise<boolean> {
        try {
            // Check if chart is already uploaded using API
            const isChartAlreadyUploaded = await apiUtils.getChartRefid(token, chartName, chartVersion);

            if (!isChartAlreadyUploaded) {
                // Navigate to deployment charts page
                await this.navigateToDeploymentChartsPage();

                // Upload the custom chart
                await this.uploadCustomChart(filePath);

                console.log(`Successfully uploaded custom chart: ${chartName} version ${chartVersion}`);
                return false; // Chart was not already uploaded
            }

            console.log(`Chart ${chartName} version ${chartVersion} is already uploaded`);
            return true; // Chart was already uploaded
        } catch (error) {
            console.error(`Failed to ensure custom chart is uploaded: ${chartName}`, error);
            throw error;
        }
    }

    /**
     * use this function to check that gui schema is empty or not 
     * @param isEmpty 
     */
    async verifyThatCustomGuiSchemaTextAreaIsEmpty(isEmpty: boolean) {
        await BasePage.verifyTextAreaIsEmptyOrNot(this.codeMirrorEditorTextArea, isEmpty);
    }

    /**
     * use this function to define the locator for gui schema screenshot section div 
     * @param isGuiSchemaPerfectlyConfigured 
     * @param checkingThroughPreviewGui 
     * @returns 
     */
    setGuiSchemaLocatorToVerifyScreenshot(isGuiSchemaPerfectlyConfigured: boolean, checkingThroughPreviewGui: boolean): Locator {
        let locator: Locator = isGuiSchemaPerfectlyConfigured || checkingThroughPreviewGui ? this.page.locator(`//*[contains(@class,'rjsf-form-template__container')]/parent::div`) : this.page.getByTestId('generic-empty-state');
        return locator;
    }
}
