import { Page, Locator, expect, Keyboard, APIRequestContext } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest'
import { Constants } from '../../../utilities/Constants';
import { ApiUtils } from '../../../utilities/ApiUtils';
const webhookPayload = '../../utilities/webhookPayload.txt';
//Use Switch Functions and Verify Slack and Webhook

export class NotificationsConfig {
    readonly notificationsLinkButton: Locator;
    readonly sesAddButton: Locator;
    readonly sesTabTitle: Locator;
    readonly addSesConfigurationName: Locator;
    readonly addSesAccessKey: Locator;
    readonly addSesSecretAccessKey: Locator;
    readonly addSesAwsRegionDropdown: Locator;
    readonly addSesAwsRegionInput: Locator;
    readonly addSesAwsRegionMenuList: Locator;
    readonly addSesSendEmailFrom: Locator;
    readonly addSesSaveButton: Locator;
    readonly sesConfigEditButton: Locator;
    readonly sesConfigDeleteButton: Locator;
    readonly sesContainer: Locator;
    readonly smtpAddButton: Locator;
    readonly smtpTabTitle: Locator;
    readonly addSmtpConfigurationName: Locator;
    readonly addSmtpHost: Locator;
    readonly addSmtpPort: Locator;
    readonly addSmtpUsername: Locator;
    readonly addSmtpPassword: Locator;
    readonly addSmtpSendEmailFrom: Locator;
    readonly addSmtpSaveButton: Locator;
    readonly smtpConfigEditButton: Locator;
    readonly smtpConfigDeleteButton: Locator;
    readonly smtpConfigName: Locator;
    readonly smtpConfigHost: Locator;
    readonly smtpContainer: Locator;
    readonly slackAddButton: Locator;
    readonly slackTabTitle: Locator;
    readonly addSlackChannel: Locator;
    readonly addWebhookUrl: Locator;
    readonly slackSelectProjectInput: Locator;
    readonly addSlackProjectDropdown: Locator;
    readonly slackSelectProjectMenu: Locator;
    readonly addSlackSaveButton: Locator;
    readonly slackConfigEditButton: Locator;
    readonly slackConfigDeleteButton: Locator;
    readonly visibleModalClose: Locator;
    readonly webhookAddButton: Locator;
    readonly webhookTabTitle: Locator;
    readonly webhookConfigName: Locator;
    readonly webhookUrl: Locator;
    readonly addWebhookHeaderButton: Locator;
    readonly webhookAvailableData: Locator;
    readonly webhookEditButton: Locator;
    readonly webhookDeleteButton: Locator;
    readonly webhookSaveButton: Locator;
    readonly webhookConfigNameFieldError: Locator;
    readonly webhookUrlFieldError: Locator;
    readonly dialogDeleteButton: Locator;
    readonly genericEmptyState: Locator;
    readonly addNotification: Locator;
    readonly addNotificationHeadingTitle: Locator;
    readonly sendToText: Locator;
    readonly selectPipelinesText: Locator;
    readonly addNotificationSendToInput: Locator;
    readonly addNotificationSelectpipeline: Locator;
    readonly addNotificationSelectPipelineFilterApplication: Locator;
    readonly CheckBox: Locator;
    readonly addNotificationSaveButton: Locator;
    readonly addNotificationSesCheckboxSpan: Locator;
    readonly addNotificationSelectAgentdropdown: Locator;
    readonly addNotificationSmtpCheckboxSpan: Locator;
    readonly addNotificationSelectPipelineInput: Locator;
    readonly notificationFeaturTitle: Locator;
    readonly addSesNotifiersAsDefaultCheckbox: Locator;


    constructor(private page: Page) {
        //SES Configurations
        this.notificationsLinkButton = page.getByTestId("notifications-link-button");
        this.sesAddButton = page.getByTestId("ses-add-button");
        this.sesTabTitle = page.getByTestId("ses-tab-title");
        this.addSesConfigurationName = page.getByPlaceholder("Enter a name");
        this.addSesAccessKey = page.getByPlaceholder("Enter access key ID");
        this.addSesSecretAccessKey = page.getByPlaceholder("Enter Secret access Key");
        this.addSesAwsRegionDropdown = page.locator(".add-ses-aws-region__control");
        this.addSesAwsRegionInput = page.locator(".add-ses-aws-region__input");
        this.addSesAwsRegionMenuList = page.locator(".add-ses-aws-region__menu-list");
        this.addSesSendEmailFrom = page.getByPlaceholder("Enter sender's email");
        this.addSesSaveButton = page.getByTestId("add-ses-save-button");
        this.sesConfigEditButton = page.getByTestId("ses-config-edit-button");
        this.sesConfigDeleteButton = page.getByTestId("ses-config-delete-button");

        //SMTP Configurations
        this.smtpAddButton = page.getByTestId("smtp-add-button");
        this.smtpTabTitle = page.getByTestId("smtp-tab-title");
        this.addSmtpConfigurationName = page.getByPlaceholder("Enter a name");
        this.addSmtpPort = page.getByPlaceholder("Enter SMTP port");
        this.addSmtpHost = page.getByPlaceholder("Eg. smtp.gmail.com");
        this.addSmtpUsername = page.getByPlaceholder("Enter SMTP username");
        this.addSmtpPassword = page.getByPlaceholder("Enter SMTP password");
        //Page is not able to locate the placeholder for this field so using data-testid
        // this.addSmtpSendEmailFrom = page.getByPlaceholder(`Enter sender's email`);

        this.addSmtpSendEmailFrom = page.getByTestId("fromEmail");
        this.addSmtpSaveButton = page.getByTestId("add-smtp-save-button");
        this.smtpConfigEditButton = page.getByTestId("smtp-config-edit-button");
        this.smtpConfigDeleteButton = page.getByTestId("smtp-config-delete-button");
        //Slack Configurations
        this.slackAddButton = page.getByTestId("slack-add-button");
        this.slackTabTitle = page.getByTestId("slack-tab-title");
        this.addSlackChannel = page.getByPlaceholder("Enter channel name");
        this.addWebhookUrl = page.getByPlaceholder("Enter incoming webhook URL");
        this.addSlackProjectDropdown = page.locator(".slack-project__control");
        this.slackSelectProjectInput = page.locator(".slack-project__input");
        this.slackSelectProjectMenu = page.getByTestId("slack-project__menu-list");
        this.addSlackSaveButton = page.getByTestId("add-slack-save-button");
        this.slackConfigEditButton = page.getByTestId("slack-configure-edit-button");
        this.slackConfigDeleteButton = page.getByTestId("slack-configure-delete-button");
        this.visibleModalClose = page.getByTestId('visible-modal-close');

        //Webhook Configurations
        this.webhookAddButton = page.getByTestId("webhook-add-button");
        this.webhookTabTitle = page.getByTestId("webhook-tab-title");
        this.webhookConfigName = page.getByTestId("add-webhook-config-name");
        this.addWebhookUrl = page.getByPlaceholder('Enter incoming webhook URL');
        this.webhookUrl = page.getByTestId("add-webhook-url");
        this.addWebhookHeaderButton = page.getByTestId("add-new-header-button");
        this.webhookAvailableData = page.getByTestId("available-webhook-data");
        this.webhookEditButton = page.getByTestId("webhook-configure-edit-button");
        this.webhookDeleteButton = page.getByTestId("webhook-configure-delete-button");
        this.webhookSaveButton = page.getByTestId("add-webhook-save-button");
        this.webhookConfigNameFieldError = page.getByTestId("webhook-config-name-error");
        this.webhookUrlFieldError = page.getByTestId("webhook-url-error");
        this.dialogDeleteButton = page.getByTestId("confirmation-modal-primary-button");

        this.genericEmptyState = page.getByTestId("generic-empty-state");
        this.addNotification = page.locator('//*[@data-testid="add-notification-button" or @data-testid="add-new-notification-button" or @data-testid="delete-notification-button"]');
        this.addNotificationHeadingTitle = page.getByTestId('add-notifications-heading-title');
        this.sendToText = page.getByText('Send to');
        this.selectPipelinesText = page.getByText('Select pipelines');
        this.addNotificationSendToInput = page.locator('.add-notification-send-to__input');
        this.addNotificationSelectpipeline = page.getByTestId('add-notification-select-pipeline');
        this.addNotificationSelectPipelineFilterApplication = page.getByTestId('add-notification-select-pipeline-filter-application');
        this.CheckBox = page.getByTestId('undefined-chk-span');
        this.addNotificationSaveButton = page.getByTestId('add-notification-save-button');
        this.addNotificationSesCheckboxSpan = page.getByTestId('add-notification-ses-checkbox-span');
        this.addNotificationSelectAgentdropdown = page.getByTestId('add-notification-select-agent-dropdown');
        this.addNotificationSmtpCheckboxSpan = page.getByTestId('add-notification-smtp-checkbox-span');
        this.addNotificationSelectPipelineInput = page.getByTestId('add-notification-select-pipeline-input');
        this.notificationFeaturTitle = page.getByTestId('notifications-feature-title');
        this.addSesNotifiersAsDefaultCheckbox = this.page.getByTestId('add-ses-default-checkbox-chk-span');
    }
    async goToNotificationsConfigPage() {
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/notifier/configurations");
    }

    async goToNotificationsConfigSESPage() {
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/notifier/configurations?modal=ses");
    }

    async goToNotificationsConfigSMTPPage() {
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/notifier/configurations?modal=smtp");
    }

    async goToNotificationsConfigSlackPage() {
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/notifier/configurations?modal=slack");
    }

    async goToNotificationsConfigWebhookPage() {
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/notifier/configurations?modal=webhook");
    }

    async goToNotificationsPage() {
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/notifier/channels");
        await this.page.waitForTimeout(2000);
    }

    //It can be verified by checking if notifier pod is running or just visit stack manager
    async verifyIfNotificationIntegrationIsInstalled() {
        try {
            await this.page.locator('//*[@href="/dashboard/stack-manager"]').click();
            // await this.page.getByTestId("click-on-stack-manager").click({ delay: 500 });
            await this.page.waitForLoadState('domcontentloaded');
            await this.page.mouse.move(50, 50);
            await this.page.waitForSelector("[data-testid='status-module-card-3']", { state: 'visible' });

            const isInstalledElement = await this.page.getByTestId("status-module-card-3");
            const textContent = await isInstalledElement.textContent();
            if (textContent !== null && textContent.trim() === "Installed") {
                return true;
            } else { return false; }

        } catch (error) {
            console.error("Error verifying notification integration:", error);
            return false;
        }
    }

    async isNotificationsConfigPageValid() {
        await expect(this.sesTabTitle).toBeVisible();
        await expect(this.smtpTabTitle).toBeVisible();
        await expect(this.slackTabTitle).toBeVisible();
        await expect(this.webhookTabTitle).toBeVisible();
    }

    async addNewSESConfig(sesConfigName: string, markThisConfigAsDefault: boolean = false) {
        await this.goToNotificationsConfigSESPage();
        if (await this.genericEmptyState.nth(0).isVisible()) {
            console.log("No SesConfig=> AddingNew");
        }
        await expect(this.sesAddButton).toBeVisible();
        await this.sesAddButton.click();
        await this.addSesConfigurationName.fill(sesConfigName);
        await this.addSesAccessKey.fill(process.env.SES_CONFIG_ACCESS_KEY as string);
        await this.addSesSecretAccessKey.fill(process.env.SES_CONFIG_SECRET_ACCESS as string);
        await this.addSesAwsRegionDropdown.click();
        await expect(this.addSesAwsRegionMenuList.getByText(process.env.SES_CONFIG_AWS_REGION as string)).toBeVisible();
        await this.addSesAwsRegionMenuList.getByText(process.env.SES_CONFIG_AWS_REGION as string).click();
        await this.addSesSendEmailFrom.fill(Constants.NOTIFICATION_EMAIL);
        markThisConfigAsDefault && await this.addSesNotifiersAsDefaultCheckbox.locator(`xpath=preceding-sibling::input`).isEnabled() ? await this.addSesNotifiersAsDefaultCheckbox.click() : '';
        await this.addSesSaveButton.click({ delay: 300 });
        await this.page.waitForTimeout(2000);
    }

    async addNewSMTPConfig(smtpConfigName: string) {
        if (await this.genericEmptyState.nth(1).isVisible()) {
            console.log("No SMTPConfig=> AddingNew");
        }
        await this.goToNotificationsConfigSMTPPage();
        await expect(this.smtpAddButton).toBeVisible();
        await this.smtpAddButton.click();
        await this.addSmtpConfigurationName.fill(smtpConfigName);
        await this.addSmtpPort.fill(process.env.SMTP_CONFIG_PORT as string);
        await this.addSmtpHost.fill(Constants.SMTP_CONFIG_HOST);
        await this.addSmtpUsername.fill(process.env.SMTP_USERNAME as string);
        await this.addSmtpPassword.fill(process.env.SMTP_PASSWORD as string);
        // await this.page.waitForTimeout(5000);
        await this.addSmtpSendEmailFrom.fill(Constants.NOTIFICATION_EMAIL);
        await this.addSmtpSaveButton.click({ delay: 300 });
        await this.page.waitForTimeout(2000);
        await this.isValidSMTPConfigPresent(smtpConfigName, Constants.SMTP_CONFIG_HOST);
    }


    async addNewSlackConfig(slackChannelName: string, webhookUrl: string) {
        if (await this.genericEmptyState.nth(2).isVisible()) {
            console.log("No SlackConfig=> AddingNew");
        }
        await this.goToNotificationsConfigSlackPage();
        await expect(this.slackAddButton).toBeVisible();
        await this.slackAddButton.click();
        await this.addSlackChannel.fill(slackChannelName);
        await this.addWebhookUrl.click({ delay: 1000 })
        await this.addWebhookUrl.fill(webhookUrl);
        await this.slackSelectProjectInput.click();
        await this.page.keyboard.insertText('devtron-demo');
        await this.page.keyboard.press('Enter');
        await BaseTest.checkToast(this.page, this.addSlackSaveButton, "Saved Successfully");
        await this.isSlackConfigValid();
    }

    async addNewWebhookConfig(webhookConfigName: string, webhookUrl: string, editWebhookName: string, headerKey: string, headerValue: string) {

        await expect(this.page.getByText(webhookConfigName, { exact: true })).not.toBeVisible();
        await this.goToNotificationsConfigWebhookPage();
        await this.webhookAddButton.click();
        await this.checkFieldValidations();
        await this.webhookConfigName.type(webhookConfigName, { delay: 200 });
        await this.webhookUrl.type(webhookUrl, { delay: 200 });
        await expect(this.addWebhookHeaderButton).toBeVisible();
        await this.page.getByTestId(`header-key-0`).type(headerKey, { delay: 200 });
        await this.page.getByTestId(`header-value-0`).type(headerValue, { delay: 200 });
        await this.fillwebhookPayload();
        await this.webhookSaveButton.click();
        await this.isWebhookConfigValid(webhookConfigName, webhookUrl);
        await this.editWebhookConfig(webhookConfigName, webhookUrl, editWebhookName);
    }

    async checkFieldValidations() {
        await this.page.waitForLoadState('domcontentloaded');
        await this.webhookSaveButton.click();
        await expect(this.webhookConfigNameFieldError).toBeVisible();
        await expect(this.webhookUrlFieldError).toBeVisible();
    }

    async fillwebhookPayload() {
        const fs = require('fs');
        const Data = fs.readFileSync(webhookPayload, 'utf8');
        const clipboardy = await import('clipboardy');
        const clipboardWrite = clipboardy.write;
        await clipboardWrite(Data);
        const monacoEditor = await this.page.locator(".monaco-editor").nth(0);
        await monacoEditor.click();
        await monacoEditor.press("Shift+Insert");
    }

    async isValidSESConfigPresent(sesConfigName: string, sesConfigAccessKey: string): Promise<boolean> {
        await this.goToNotificationsConfigSESPage();
        try {
            await this.sesAddButton.waitFor();
            await expect(this.page.locator(`//*[text()="${sesConfigName}"]`)).toBeVisible();
            console.log(`SES Config with name ${sesConfigName} is present`);
            // If all expectations pass, return true
            return true;
        } catch (error) {
            // If any expectation fails, log the error and return false
            console.error("ValidSESConfig already Exists");
            return false;
        }
    }

    async isValidSMTPConfigPresent(smtpConfigName: string, smtpConfigHost: string): Promise<boolean> {
        try {
            //await this.page.getByTestId(`smtp-container-${smtpConfigName}`).hover();
            await expect(this.page.getByTestId(`smtp-container-${smtpConfigName}`).getByText(smtpConfigName).first()).toBeVisible();
            await expect(this.page.getByTestId(`smtp-container-${smtpConfigName}`).getByText(smtpConfigHost).first()).toBeVisible();

            // If all expectations pass, return true
            return true;
        } catch (error) {
            // If any expectation fails, log the error and return false
            console.error("ValidSESConfig already Exists");
            return false;
        }
    }


    async isWebhookConfigValid(webhookConfigName: string, webhookUrl: string) {
        await expect(this.page.getByTestId(`webhook-config-name-${webhookConfigName}`).getByText(webhookConfigName)).toBeVisible();
        await expect(this.page.getByTestId(`webhook-url-${webhookUrl}`).getByText(webhookUrl)).toBeVisible();
        await this.page.getByTestId(`webhook-container-${webhookConfigName}`).hover()
    }

    async editWebhookConfig(webhookConfigName: string, webhookUrl: string, editWebhookName: string) {
        await this.page.getByTestId(`webhook-container-${webhookConfigName}`).hover();
        await expect(this.page.getByTestId(`webhook-configure-edit-button-${webhookConfigName}`)).toBeVisible();
        await this.page.getByTestId(`webhook-configure-edit-button-${webhookConfigName}`).click();
        await expect(this.page.getByText(webhookConfigName, { exact: true })).toBeVisible();
        await expect(this.page.getByText(webhookUrl, { exact: true })).toBeVisible();
        await this.webhookConfigName.clear();
        await this.webhookConfigName.type(editWebhookName, { delay: 200 });
        await this.webhookSaveButton.click();
        await this.isWebhookConfigValid(editWebhookName, webhookUrl);
    }

    async isSlackConfigValid() {
        //todo
    }

    async deleteSESConfig(sesConfigName: string) {
        await this.goToNotificationsConfigSESPage();
        let sesNotifierRowLocator: Locator = this.page.locator(`//*[text()="${sesConfigName}"]/ancestor::div[2]`);
        await sesNotifierRowLocator.waitFor();
        if (!await sesNotifierRowLocator.textContent().then(text => text!.includes('default'))) {
            await sesNotifierRowLocator.hover();
            await sesNotifierRowLocator.locator(this.page.getByTestId("ses-config-delete-button")).click();
            await BaseTest.checkToast(this.page, this.dialogDeleteButton, "Successfully deleted");
        }
    }


    async deleteSMTPConfig(smtpConfigName: string) {
        await this.goToNotificationsConfigSMTPPage();
        await this.notificationFeaturTitle.waitFor();
        await this.smtpTabTitle.click();
        if (!await this.page.getByTestId(`smtp-container-${smtpConfigName}`).isVisible()) {
            console.log("Nothing to delete");
        } else {
            const deleteSES = this.page.getByTestId(`smtp-container-${smtpConfigName}`);
            await this.page.getByTestId(`smtp-container-${smtpConfigName}`).hover()
            //await this.smtpConfigDeleteButton.click({force:true});
            await this.page.locator(`//*[@data-testid="smtp-container-${smtpConfigName}"]//*[@data-testid="smtp-config-delete-button"]`).click();
            //await this.page.getByTestId("confirmation-modal-primary-button").click();
            //await this.dialogDeleteButton.click();
            //await expect(this.page.getByText("Successfully deleted", { exact: true })).toBeVisible();
            await BaseTest.checkToast(this.page, this.dialogDeleteButton, "Successfully deleted");
        }
    }

    async deleteSlackConfig(slackConfigName: string) {
        await this.goToNotificationsConfigSlackPage();
        await this.notificationFeaturTitle.waitFor();
        await this.slackTabTitle.click();
        if (!await this.page.getByText(slackConfigName).isVisible()) {
            console.log("Nothing to delete");
        } else {
            await this.page.getByText(slackConfigName).hover();
            //await this.page.locator(`//*[text()="${slackConfigName}"]/parent::td//*[@data-testid="slack-configure-delete-button"]`).click({force:true})
            //*[text()="delete"]/ancestor::*[contains(@class,'slack-config-grid')]//*[@data-testid="slack-config-delete-button"]
            await this.page.locator(`//*[text()="${slackConfigName}"]/ancestor::*[contains(@class,'slack-config-grid')]//*[@data-testid="slack-config-delete-button"]`).click({ force: true })
            //await this.dialogDeleteButton.click();
            //await expect(this.page.getByText("Successfully deleted", { exact: true })).toBeVisible();
            await BaseTest.checkToast(this.page, this.dialogDeleteButton, "Successfully deleted");
        }
    }

    async deleteWebhookConfig(webhookConfigName: string) {
        await this.page.getByTestId(`webhook-container-${webhookConfigName}`).hover()
        await expect(this.page.getByTestId(`webhook-configure-delete-button-${webhookConfigName}`)).toBeVisible();
        await this.page.getByTestId(`webhook-configure-delete-button-${webhookConfigName}`).click();
        await this.dialogDeleteButton.click();
        await expect(this.page.getByText("Successfully deleted", { exact: true })).toBeVisible();
        await expect(this.page.getByText(webhookConfigName, { exact: true })).not.toBeVisible();
    }
    async verifyExistanceOfNotifier(request: APIRequestContext, notifierType: string, fieldsToVerify?: { [key: string]: string | boolean }): Promise<boolean> {
        const apiUtils = new ApiUtils(request);
        let token: string;
        if (process.env.isStaging != 'true') {
            token = await apiUtils.login(process.env.PASSWORD!);
        }
        else {
            token = process.env.stagingSuperAdminToken!;
        }
        const response = await apiUtils.getNotifiersList({ token: token });
        const notifierData = await response.result[notifierType];
        let keysToCheck = Object.keys(fieldsToVerify!);
        let resultant = notifierData.some(key => {
            return keysToCheck.every(item => {
                return fieldsToVerify![item] == key[item]
            })
        })
        return resultant;

    }
    async verifyAddNotificationsModal() {
        await this.page.waitForLoadState('load');
        await expect(this.addNotificationHeadingTitle).toBeVisible();
        await expect(this.sendToText).toBeVisible();
        await expect(this.selectPipelinesText).toBeVisible();
    }
    async ChooseSesAndSmtpOption(sesOrSmtp: string, sesOrSmtpName: string) {
        if (sesOrSmtp === "SES") {
            await this.addNotificationSesCheckboxSpan.click();
        } else {
            await this.addNotificationSmtpCheckboxSpan.click();
        }
        await this.addNotificationSelectAgentdropdown.click();
        await this.page.getByTestId(`list-add-notification-select-agent-menu-${sesOrSmtpName}`).click({ delay: 500 });
        await expect(this.addNotificationSelectAgentdropdown).toContainText(sesOrSmtpName);
    }
    async configureNotification(filterDetails: { filterName: string, filterValue: string }[], emailProviderName: string, sesOrSmtp: string, emailAddress: string) {
        await this.goToNotificationsConfigPage();
        await this.notificationsLinkButton.click();
        await this.addNotification.click({ delay: 300 });
        await this.verifyAddNotificationsModal();
        await expect(async () => {
            await this.addNotificationSendToInput.fill(emailAddress);
            await this.page.keyboard.press('Enter');
            if (sesOrSmtp == "SES" || sesOrSmtp == "SMTP") {
                await this.ChooseSesAndSmtpOption(sesOrSmtp, emailProviderName);
            }
            for (let filterObject of filterDetails) {
                await this.addNotificationSelectpipeline.click();
                await this.page.getByTestId(`add-notification-select-pipeline-filter-${filterObject.filterName}`).click({ force: true });
                await this.addNotificationSelectPipelineInput.fill(filterObject.filterValue);
                await this.page.getByTestId(`add-notification-select-pipeline-filter-${filterObject.filterValue}`).click({ timeout: 3000 });
                await expect(this.CheckBox.nth(0)).toBeVisible();
            }
            for (let i = 0; i < 2; i++) {
                !await this.CheckBox.nth(i).locator(`xpath=preceding-sibling::input`).isChecked() ? await this.CheckBox.nth(i).click() : '';
            }
            await this.addNotificationSaveButton.click();
        }).toPass({ timeout: 4 * 1000 * 60 })
    }

    async deleteNotification(emailAddress: string) {
        await this.page.goto(process.env.BASE_SERVER_URL + "/global-config/notifier/channels");
        await this.notificationFeaturTitle.waitFor({ timeout: 10000 });
        await this.page.locator(`//*[text()="${emailAddress}"]`).nth(0).hover();
        let count = await this.page.locator(`//*[text()="${emailAddress}"]/ancestor::td[@class="pipeline-list__recipients"]/following-sibling::td//button`).count();
        console.log(count);
        for (let i = 0; i < count; i++) {
            await this.page.locator(`//*[text()="${emailAddress}"]`).nth(0).hover();
            await this.page.locator(`//*[text()="${emailAddress}"]/ancestor::td[@class="pipeline-list__recipients"]/following-sibling::td//button`).nth(0).click();
            await this.page.getByTestId('confirmation-modal-primary-button').waitFor();
            await BaseTest.checkToast(this.page, this.page.getByTestId('confirmation-modal-primary-button'), "Deleted Successfully");
        }
    }


    async verifySlackNotificationCount(frequencyMap: Map<string, number>, statusArrray: string[], globalErrors: string[]) {
        statusArrray.forEach((item) => {
            var count = (frequencyMap.get(item) || 0);
            console.log(`${item}:${count}`)
            try {
                expect(count).toBeGreaterThanOrEqual(1); // Assertion
            } catch (error) {
                globalErrors.push(`Assertion failed for '${item}': Expected ${count} to be greater than or equal to 1`);
            }

        });
    }

    async storeSlackNotificationCount(responseBody: any, frequencyMap: Map<string, number>, appName: string) {
        for (let key in responseBody.messages) {
            const message = responseBody.messages[key];
            const text = message.text;
            const status = text.split(':')[2].split('|')[0].trim().toLowerCase();
            const appMatch = text.match(/Application >\s*(\S+)/);
            const application: string = appMatch ? appMatch[1] : "Unknown";
            if (appName == application) {
                if (frequencyMap.has(status)) {
                    frequencyMap.set(status, (frequencyMap.get(status)!) + 1);
                } else {
                    frequencyMap.set(status, 1);
                }
            }
        }
    }
}
