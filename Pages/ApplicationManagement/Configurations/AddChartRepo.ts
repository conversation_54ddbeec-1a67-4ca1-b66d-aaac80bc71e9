import { Page, Locator, expect } from '@playwright/test'; // Importing necessary modules
import { BaseTest } from '../../../utilities/BaseTest'; // Importing BaseTest utility
import { BasePage } from '../../BasePage';

export class AddChartRepo extends BasePage {
    // Readonly properties representing various locators and credentials
    readonly chartRepoLinkButton: Locator;
    readonly helpButton: Locator;
    readonly addRepositorybutton: Locator;
    readonly publicRepository: Locator;
    readonly publicRepositoryName: Locator;
    readonly publicRepositoryUrl: Locator;
    readonly publicRepositorySave: Locator;
    readonly publicRepositoryCancel: Locator;
    readonly privateRepositoryUsername: Locator;
    readonly privateRepositoryPassword: Locator;
    readonly chartRepositoryWrapper: Locator;
    readonly validateButton: Locator;
    readonly repositoryUpdate: Locator;
    readonly repositoryDelete: Locator;
    readonly repositoryDeleteCancel: Locator;
    readonly repositoryDeleteConfirm: Locator;
    readonly chartsDeployedThroughThisRepoText: Locator;
    readonly privateRepositoryCheckboxSpan: Locator;

    // Constructor to initialize the class with page and chart name
    constructor(public page: Page) {
        super(page);
        // Initializing locators
        this.chartRepoLinkButton = page.getByTestId('Chart Repositories-page');
        this.helpButton = page.getByTestId("go-to-get-started");
        this.addRepositorybutton = page.getByText('Add repository');

        // Locating elements for adding public repository
        this.publicRepository = page.getByTestId('Public repository');
        this.publicRepositoryName = page.getByTestId('name')
        this.publicRepositoryUrl = page.getByTestId('url')
        this.publicRepositorySave = page.getByTestId('chart-repo-save-button')
        this.publicRepositoryCancel = page.getByTestId('chart-repo-cancel-button');

        // Locating elements for adding private repository
        this.privateRepositoryUsername = page.getByTestId('username')
        this.privateRepositoryPassword = page.getByTestId('password')
        // Additional locators
        this.chartRepositoryWrapper = page.getByTestId('chart-repository-wrapper')
        this.validateButton = page.getByText('VALIDATE', { exact: true })   // validate
        this.repositoryUpdate = page.getByTestId('chart-repo-save-button')
        this.repositoryDelete = page.getByTestId('chart-repo-delete-button')
        this.repositoryDeleteCancel = page.getByTestId('dialog-cancel');
        this.chartsDeployedThroughThisRepoText = this.page.locator('//*[text()="Some deployed helm apps are using this repository."]');
        this.privateRepositoryCheckboxSpan = this.page.getByTestId('Private repository-span');


    }

    async addRepo(data: { repoName: string, repoType: 'public' | 'private', repoUrl: string, userName?: string, password?: string }) {
        await this.addRepositorybutton.click();
        if (data.repoType === "public") {

            if (await this.publicRepository.isChecked() === false) {
                await this.publicRepository.click();
            }
            await this.publicRepositoryName.click();
            await this.publicRepositoryName.fill(data.repoName);
            await this.publicRepositoryUrl.click();
            await this.publicRepositoryUrl.fill(data.repoUrl);
        }
        if (data.repoType === "private") {

            await this.privateRepositoryCheckboxSpan.click();
            await this.publicRepositoryName.fill(data.repoName);
            await this.publicRepositoryUrl.click();
            await this.publicRepositoryUrl.fill(data.repoUrl);
            await this.privateRepositoryUsername.click();
            await this.privateRepositoryUsername.fill(data.userName!);
            await this.privateRepositoryPassword.click();
            await this.privateRepositoryPassword.fill(data.password!);

        }
        await BaseTest.checkToast(this.page, this.publicRepositorySave, 'Chart repo saved');
    }


    async repoDelete(chartName: string, isDeletionSuccessfull: boolean) {
        await this.chartRepositoryWrapper.getByTestId(chartName).getByTestId("select-existing-repository-button").click();
        await this.repositoryDelete.click();
        if (isDeletionSuccessfull) {
            await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, 'Success');
        } else {
            await this.dialogDeleteConfirmationButton.click();
            await expect(this.chartsDeployedThroughThisRepoText).toBeVisible();
            await this.dialogDeleteConfirmationButton.click();
        }
    }



}


