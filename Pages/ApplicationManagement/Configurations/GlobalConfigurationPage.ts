import { Locator, Page } from '@playwright/test';
import { BasePage } from '../../BasePage';

export class GlobalConfigurationPage extends BasePage {
    readonly globalConfigurationButton: Locator;
    readonly applicationTemplateButton: Locator;
    readonly imagePromotionButton: Locator;
    readonly deploymentWindowButton: Locator;
    readonly lockConfigurationButton: Locator;
    readonly mandatoryPluginButton: Locator;
    readonly approvalPolicyButton: Locator;
    readonly globalTagButton: Locator;
    readonly imagePullDigestButton: Locator;
    readonly filterConditionButton: Locator;
    readonly externalLinksButton: Locator;
    readonly notificationsButton: Locator;
    readonly buildInfraButton: Locator;
    readonly scopedVariableButton: Locator;
    readonly catalogFrameworkButton: Locator;
    readonly deploymentChartsButton: Locator;
    readonly gitopsButton: Locator;
    readonly addChartRepoButton: Locator;

    constructor(public page: Page) {
        super(page);
        this.globalConfigurationButton = page.getByTestId('click-on-global-configuration');
        this.applicationTemplateButton = page.getByTestId('Application Template-page');
        this.imagePromotionButton = page.getByTestId('Image Promotion-page');
        this.deploymentWindowButton = page.getByTestId('Deployment Window-page');
        this.lockConfigurationButton = page.getByTestId('Lock Configuration-page');
        this.mandatoryPluginButton = page.getByTestId('Mandatory Plugin-page');
        this.approvalPolicyButton = page.getByTestId('Approval Policy-page');
        this.globalTagButton = page.getByTestId('Global Tag-page');
        this.imagePullDigestButton = page.getByTestId('Image Pull Digest-page');
        this.filterConditionButton = page.getByTestId('Filter Condition-page');
        this.externalLinksButton = page.getByTestId('External Links-page');
        this.notificationsButton = page.getByTestId('Notifications-page');
        this.buildInfraButton = page.getByTestId('Build Infra-page');
        this.scopedVariableButton = page.getByTestId('Scoped Variable-page');
        this.catalogFrameworkButton = page.getByTestId('Catalog Framework-page');
        this.deploymentChartsButton = page.getByTestId('Deployment Charts-page');
        this.gitopsButton = page.getByTestId('GitOps-page');
        this.addChartRepoButton = page.getByTestId('Add Chart Repo-page');
    }

    async goToGlobalConfigurations(url: string) {
        await this.page.goto(url);
        await this.globalConfigurationButton.click();
    }

    async navigateToApplicationTemplate() {
        await this.applicationTemplateButton.click();
    }

    async navigateToImagePromotionPage() {
        await this.imagePromotionButton.click();
    }

    async navigateToDeploymentWindow() {
        await this.deploymentWindowButton.click();
    }

    async navigateToLockConfiguration() {
        await this.lockConfigurationButton.click();
    }

    async navigateToMandatoryPlugin() {
        await this.mandatoryPluginButton.click();
    }

    async navigateToApprovalPolicy() {
        await this.approvalPolicyButton.click();
    }

    async navigateToGlobalTag() {
        await this.globalTagButton.click();
    }

    async navigateToImagePullDigest() {
        await this.imagePullDigestButton.click();
    }

    async navigateToFilterCondition() {
        await this.filterConditionButton.click();
    }

    async navigateToExternalLinks() {
        await this.externalLinksButton.click();
    }

    async navigateToNotifications() {
        await this.notificationsButton.click();
    }

    async navigateToBuildInfra() {
        await this.buildInfraButton.click();
    }

    async navigateToScopedVariable() {
        await this.scopedVariableButton.click();
    }

    async navigateToCatalogFramework() {
        await this.catalogFrameworkButton.click();
    }

    async navigateToDeploymentCharts() {
        await this.deploymentChartsButton.click();
    }

    async navigateToGitops() {
        await this.gitopsButton.click();
    }

    async navigateToAddChartRepo() {
        await this.addChartRepoButton.click();
    }
}
