import { Page, Locator } from '@playwright/test';
import { expect } from 'playwright/test';
import { Constants } from '../../../utilities/Constants';
import { BaseTest } from '../../../utilities/BaseTest';
import exp from 'constants';

//Todo To combine functions into 2


export class GitopsConfigurationPage {
    baseTest: BaseTest;
    readonly gitopsConfigurationHeading: Locator;
    readonly autoCreateRepositoryCheckbox: Locator;
    readonly autoCreateRepositoryHeading: Locator;
    readonly gitopsConfigSaveButton: Locator;
    readonly gitopsConfigLeftNav: Locator;
    readonly useDefaultGitRadioButton: Locator;
    readonly gitopsSaveButton: Locator;
    readonly configurationSavedSuccesfullyToast: Locator;
    readonly allowChangingGitRadioButton: Locator;
    readonly gitopsHeading: Locator;
    readonly gitopsSubheading: Locator;
    readonly gitopsGithubButton: Locator;
    readonly gitopsGitlabButton: Locator;
    readonly gitopsAzureButton: Locator;
    readonly gitopsBitbucketButton: Locator;
    readonly gitopsCreateOrganizationLink: Locator;
    readonly gitopsGithubHostUrlTextbox: Locator;
    readonly gitopsGithubOrganisationNameTextbox: Locator;
    readonly gitopsGithubUsernameTextbox: Locator;
    readonly gitopsGithubPersonalAccessTokenTextbox: Locator;
    readonly gitopsGithubUpdateButton: Locator;
    readonly gitopsGitlabHostUrlTextbox: Locator;
    readonly gitopsGitlabGroupIdTextbox: Locator;
    readonly gitopsGitlabUsernameTextbox: Locator;
    readonly gitopsGitlabPersonalAccessTokenTextbox: Locator;
    readonly gitopsAzureOrganisationUrlTextbox: Locator;
    readonly gitopsAzureProjectNameTextbox: Locator;
    readonly gitopsAzureUsernameTextbox: Locator;
    readonly gitopsAzurePersonalAccessTokenTextbox: Locator;
    readonly gitopsBitbucketHostUrlTextbox: Locator;
    readonly gitopsBitbucketWorkspaceIdTextbox: Locator;
    readonly gitopsBitbucketUsernameTextbox: Locator;
    readonly gitopsBitbucketPersonalAccessTokenTextbox: Locator;
    readonly gitopsGitAccessCredentialsHeading: Locator;
    readonly gitopsProviderUsernameHeadingGitlab: Locator;
    readonly gitopsProviderUsernameHeadingAzure: Locator;
    readonly gitopsBitbucketProjectTextbox: Locator;
    readonly directoryManagmentInGit: Locator;
    readonly saveButton: Locator;
    readonly gitopsConfigurationTabOnWorkflowPage: Locator;

    constructor(private page: Page) {
        // Locators initialization
        this.gitopsConfigurationHeading =
            page.getByTestId('gitops-config-heading')
                .getByText('GitOps Configuration', { exact: true });
        this.autoCreateRepositoryCheckbox = page.locator("//label[@class='form__radio-item']").nth(1);
        this.autoCreateRepositoryHeading = page.getByTestId("auto-create-repository-span")
            .getByText('Auto-create repository', { exact: true });
        this.gitopsConfigSaveButton = page.getByTestId("save_cluster_list_button_after_selection");
        this.gitopsConfigLeftNav = page.locator('//span[@class="dc__ellipsis-right nav-text"]').getByText("GitOps Configuration", { exact: true });
        this.useDefaultGitRadioButton = page.locator('.form__radio-item-content').first();
        this.allowChangingGitRadioButton = page.getByText('Ask git repository for each application');
        this.gitopsSaveButton = page.getByTestId("gitops-save-button");
        this.configurationSavedSuccesfullyToast = page.getByRole('alert');
        this.gitopsHeading = page.getByTestId("gitops-heading");
        this.gitopsSubheading = page.getByTestId("gitops-subheading");
        this.gitopsGithubButton = page.getByTestId("gitops-github-button");
        this.gitopsGitlabButton = page.getByTestId("gitops-gitlab-button");
        this.gitopsAzureButton = page.getByTestId("gitops-azure-button");
        this.gitopsBitbucketButton = page.getByTestId("gitops-bitbucket-button");
        this.gitopsCreateOrganizationLink = page.getByTestId("gitops-create-organization-link");
        this.gitopsGithubHostUrlTextbox = page.getByTestId("gitops-github-gitlab-host-url-textbox");
        this.gitopsGithubOrganisationNameTextbox = page.getByTestId("gitops-github-organisation-name-textbox");
        this.gitopsGithubUsernameTextbox = page.getByTestId("gitops-github-username-textbox");
        this.gitopsGithubPersonalAccessTokenTextbox = page.getByTestId("gitops-github-pat-textbox");
        this.gitopsGithubUpdateButton = page.getByTestId("gitops-github-update-button");
        this.gitopsGitlabHostUrlTextbox = page.getByTestId("gitops-github-gitlab-host-url-textbox");
        this.gitopsGitlabGroupIdTextbox = page.getByTestId("gitops-gitlab-group-id-textbox");
        this.gitopsGitlabUsernameTextbox = page.getByTestId("gitops-gitlab-username-textbox");
        this.gitopsGitlabPersonalAccessTokenTextbox = page.getByTestId("gitops-gitlab-pat-textbox");
        this.gitopsAzureOrganisationUrlTextbox = page.getByTestId("gitops-azure-organisation-url-textbox");
        this.gitopsAzureProjectNameTextbox = page.getByTestId("gitops-azure-project-name-textbox");
        this.gitopsAzureUsernameTextbox = page.getByTestId("gitops-azure-username-textbox");
        this.gitopsAzurePersonalAccessTokenTextbox = page.getByTestId("gitops-azure-pat-textbox");
        this.gitopsBitbucketHostUrlTextbox = page.getByTestId("gitops-bitbucket-host-url-textbox");
        this.gitopsBitbucketWorkspaceIdTextbox = page.getByTestId("gitops-bitbucket-workspace-id-textbox");
        this.gitopsBitbucketUsernameTextbox = page.getByTestId("gitops-bitbucket-username-textbox");
        this.gitopsBitbucketPersonalAccessTokenTextbox = page.getByTestId("gitops-bitbucket-pat-textbox");
        this.gitopsSaveButton = page.getByTestId("gitops-save-button");
        this.gitopsGitAccessCredentialsHeading = page.getByTestId("gitops-gitaccess-credentials-heading");
        this.gitopsProviderUsernameHeadingAzure = page.getByTestId('label-gitops-azure-username-textbox');
        this.gitopsProviderUsernameHeadingGitlab = page.getByTestId('label-gitops-gitlab-username-textbox');
        this.directoryManagmentInGit = page.getByText('Directory Managment in Git');
        this.saveButton = this.page.locator('//*[text()="Save"]');
        this.gitopsConfigurationTabOnWorkflowPage = this.page.getByTestId('gitops-configuration-link')

    }

    /**
   * function to click on save button in gitops config page
   */
    async ClickOnSaveButton() {
        await this.page.locator('//*[@data-testid="workflow-editor-link"]').waitFor({ timeout: 1 * 1000 * 60 });
        if (await this.page.getByTestId("gitops-configuration-link").isVisible()) {
            await this.gitopsConfigurationTabOnWorkflowPage.click();
            await expect(this.gitopsConfigurationHeading).toBeVisible({ timeout: 1 * 1000 * 60 });
            await expect(this.autoCreateRepositoryHeading).toBeVisible({ timeout: 1 * 1000 * 60 });
            await this.autoCreateRepositoryHeading.click();
            await expect(async () => {
                await this.gitopsConfigSaveButton.click();
                await expect(this.page.getByTestId("new-workflow-button")).toBeVisible({ timeout: 30000 });
            }).toPass({ timeout: 3 * 1000 * 60 });
        } else {
            console.log("GitOps Configuration Tab not turned on")
        }
    }

    async switchRadioButtonBetweenDefaultAndAllowChangingGit(radioButton: string) {
        await expect(async () => {
            if (radioButton === "useDefaultGit") {
                await this.useDefaultGitRadioButton.click()
                //wait until Configured validated.
            } else {
                await this.allowChangingGitRadioButton.click();
            }
            await this.gitopsSaveButton.click();
            var result = await BaseTest.checkToast(this.page, this.saveButton, 'Configuration saved successfully');
            expect(result).toBe(true);
        }).toPass({ timeout: 4 * 1000 * 60 });
    }

    async VerifyUserDefinedGitRepoIsVisible() {
        if (await this.directoryManagmentInGit.isVisible()) {
            console.log("Gitops Flag is Enabled");
        } else {
            console.log("Gitops Flag is not Enabled, Please enable it.");
        }
    }

    async gotoGlobalConfigGitopsPage() {
        const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json")
        await this.page.goto(process.env.BASE_SERVER_URL as string + "/global-config/gitops");
    }

    async isGitopsPageVisible() {
        await expect(this.gitopsHeading).toHaveText('GitOps');
        await expect(this.gitopsSubheading).toBeVisible();
        await expect(this.gitopsSaveButton).toBeVisible();
        await expect(this.gitopsSubheading).toContainText('Devtron uses GitOps configuration to store kubernetes configuration files of applications');
    }
    async isThereNeedToAddGitops(userName: string, providerName: string = 'gitlab') {
        try {
            switch (providerName.toLowerCase()) {
                case 'github': {
                    await this.gitopsGithubButton.click({ delay: 300 });
                    if (await this.gitopsGithubUsernameTextbox.getAttribute('value') != userName) {
                        await this.ValidateAndAddGitopsGithub();
                    }
                    break;
                }
                case "gitlab": {
                    await this.gitopsGitlabButton.click({ delay: 300 });
                    if (await this.gitopsGitlabUsernameTextbox.getAttribute('value') != userName) {
                        await this.ValidateAndAddGitopsGitlab();
                    }
                    else {
                        await this.switchRadioButtonBetweenDefaultAndAllowChangingGit('dee');
                    }
                    break;
                }
                case "azure": {
                    await this.gitopsAzureButton.click({ delay: 300 });
                    if (await this.gitopsAzureButton.getAttribute('value') != userName) {
                        await this.ValidateAndAddGitopsAzure();
                    }
                    break;
                }
                case "bitbucket": {
                    await this.gitopsBitbucketButton.click({ delay: 300 });
                    if (await this.gitopsBitbucketUsernameTextbox.getAttribute('value') != userName) {
                        await this.ValidateAndAddGitopsBitBucket();
                    }
                    break;
                }
                default: {
                    console.log('please enter a valid gitops provider ');
                }
            }

            // If all textboxes are empty, return true
        } catch (error) {
            // If any textbox is not empty, return false
            console.error('something went wrong ' + error);
        }
    }


    async ValidateAndAddGitopsGithub() {
        await this.isGitopsPageVisible();
        await this.gitopsGithubButton.click();
        await expect(this.gitopsGitAccessCredentialsHeading).toBeVisible();
        await expect(this.gitopsCreateOrganizationLink).toHaveText('How to create organization in GitHub ?');
        await expect(this.gitopsGithubHostUrlTextbox).toHaveValue('https://github.com/');

        await this.gitopsGithubOrganisationNameTextbox.fill(Constants.GITHUB_ORGANISATION_NAME)
        await this.gitopsGithubUsernameTextbox.fill(process.env.GITHUB_USERNAME as string)
        await this.gitopsGithubPersonalAccessTokenTextbox.fill(process.env.GITHUB_PAT as string)
        await this.gitopsSaveButton.click({ delay: 500 });
        await this.page.waitForTimeout(1000)
        await this.page.waitForLoadState('networkidle');
        await expect(this.gitopsGithubOrganisationNameTextbox).toHaveValue(Constants.GITHUB_ORGANISATION_NAME)
        await expect(this.gitopsGithubUsernameTextbox).toHaveValue(process.env.GITHUB_USERNAME as string);

    }

    async ValidateAndAddGitopsGitlab() {
        await this.gitopsGitlabButton.click();
        await expect(this.gitopsCreateOrganizationLink).toHaveText('How to create group in GitLab ?');
        await expect(this.gitopsProviderUsernameHeadingGitlab).toHaveText('GitLab Username');
        await expect(this.gitopsGitlabHostUrlTextbox).toHaveValue('https://gitlab.com/')
        await this.gitopsGitlabGroupIdTextbox.clear();
        await this.gitopsGitlabGroupIdTextbox.fill(process.env.GITLAB_GROUPID as string);
        await this.gitopsGitlabUsernameTextbox.clear();
        await this.gitopsGitlabUsernameTextbox.fill(process.env.GITLAB_USERNAME as string);
        await this.gitopsGitlabPersonalAccessTokenTextbox.clear();
        await this.gitopsGitlabPersonalAccessTokenTextbox.fill(process.env.GITLAB_PAT as string)
        await this.switchRadioButtonBetweenDefaultAndAllowChangingGit('dee');
        await this.page.waitForTimeout(1000)
        await expect(this.gitopsGitlabGroupIdTextbox).toHaveValue(process.env.GITLAB_GROUPID as string)
        await expect(this.gitopsGitlabUsernameTextbox).toHaveValue(process.env.GITLAB_USERNAME as string);
    }

    async ValidateAndAddGitopsAzure() {
        await this.gitopsAzureButton.click();
        await expect(this.gitopsCreateOrganizationLink).toHaveText('How to create project in Azure ?');
        await expect(this.gitopsProviderUsernameHeadingAzure).toHaveText('Azure DevOps Username*');

        await this.gitopsAzureOrganisationUrlTextbox.fill("https://dev.azure.com/");
        await this.gitopsAzureProjectNameTextbox.fill(Constants.AZURE_PROJECT_NAME)
        await this.gitopsAzureUsernameTextbox.fill(Constants.AZURE_USERNAME)
        await this.gitopsAzurePersonalAccessTokenTextbox.fill(Constants.AZURE_PAT)
        await this.gitopsSaveButton.click();
        await expect(this.gitopsAzureProjectNameTextbox).toHaveValue(Constants.AZURE_PROJECT_NAME)
        await expect(this.gitopsAzureUsernameTextbox).toHaveValue(Constants.AZURE_USERNAME);

    }

    async ValidateAndAddGitopsBitBucket() {
        await this.gitopsBitbucketButton.click();
        await expect(this.gitopsBitbucketHostUrlTextbox).toHaveValue('https://bitbucket.org/')
        await this.gitopsBitbucketWorkspaceIdTextbox.fill(Constants.BITBUCKET_WORKSPACEID)
        await this.gitopsBitbucketProjectTextbox.fill(Constants.BITBUCKET_PROJECT)
        await this.gitopsBitbucketUsernameTextbox.fill(Constants.BITBUCKET_USERNAME)
        await this.gitopsBitbucketPersonalAccessTokenTextbox.fill(process.env.BITBUCKET_PAT as string)
        await this.gitopsSaveButton.click({ delay: 500 });

        await expect(this.gitopsBitbucketWorkspaceIdTextbox).toHaveValue(Constants.BITBUCKET_WORKSPACEID)
        await expect(this.gitopsBitbucketUsernameTextbox).toHaveValue(Constants.BITBUCKET_USERNAME);
    }

    //todo: add more gitops configuration cases.

}