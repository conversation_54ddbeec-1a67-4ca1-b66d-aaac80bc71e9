import { expect, Locator, <PERSON> } from "@playwright/test";
import clipboardy from 'clipboardy';
import { BasePage } from "../../BasePage";

export class CatalogFrameworkPage extends BasePage {
    readonly catalogResourceNameDiv: Locator;
    readonly sampleSchemaTab: Locator;
    readonly copySampleSchemaButton: Locator;
    readonly schemaTab: Locator;
    readonly reviewChangesButton: Locator;
    readonly kindInputField: Locator;
    readonly saveButton: Locator;
    readonly typeText: Locator;
    readonly monacoEditor: Locator;
    constructor(public page: Page) {
        super(page)
        this.catalogResourceNameDiv = this.page.locator("//*[contains(@class,'catalog-framework__table-row')]");
        this.sampleSchemaTab = this.page.locator('//span[text()="Sample Schema"]');
        this.copySampleSchemaButton = this.page.locator("//*[contains(@class,'gui-yaml-switch')]/following-sibling::button");
        this.schemaTab = this.page.locator('//*[text()="Schema"]');
        this.reviewChangesButton = this.page.locator("//*[text()='Review Changes']");
        this.kindInputField = this.page.locator('//*[@placeholder="Kind for the schema"]');
        this.saveButton = this.page.locator('//*[text()="Save"]');
        this.typeText = this.page.locator('//*[contains(text(),"type")]');
        this.monacoEditor = this.page.locator('//*[@class="view-lines monaco-mouse-cursor-text"]');

    }

    /**
     * directly navigate to catalog page 
     */
    async navigateToCatalogFrameworkPage() {
        await this.page.goto(process.env.BASE_SERVER_URL + '/global-config/catalog-framework');
    }



    /**
     * this method is used to resource catalog
     * we are copying the default cataog and pasting it , if u want to set custom then pass custom yaml
     * @param data 
     */
    async editAnyResourceCatalog(data: { resourceKind: string, isSampleSchema: boolean, customizedObjects?: any }) {

        await this.catalogResourceNameDiv.locator(`//*[text()="${data.resourceKind}"]`).click();
        await expect(this.kindInputField).toBeDisabled();
        if (data.isSampleSchema) {
            await this.sampleSchemaTab.click();
            await this.copySampleSchemaButton.click();
        }
        await this.schemaTab.click();
        // revert new code editor
        await this.codeMirrorEditorTextArea.locator(this.typeText).first().click();
        for (let i = 0; i < 10; i++) {
            if (process.env.OS as string == "Mac") {
                await this.page.keyboard.press('Meta+a');
                await this.page.waitForTimeout(300);
            }
            else {
                await this.page.keyboard.press('Control+a');
                await this.page.waitForTimeout(300);
            }
        }
        await this.page.keyboard.press('Backspace');
        if (data.isSampleSchema) {
            if (process.env.OS as string == "Mac") {
                await this.page.keyboard.press('Meta+v');
            }
            else {
                await this.page.keyboard.press('Control+v');
            }
        }
        else {
            await clipboardy.write(data.customizedObjects);
            await this.page.keyboard.press('Meta+v');
        }
    }



    /**
     * this method will verify the diff and will save the changes 
     * the last key will tell that we have scrolled the yaml till end 
     * @param data 
     */
    async reviewAndSaveChanges(data: { fieldName: string, sideToVerify: string, isVisible: boolean, theLastKey: string }[]) {
        await this.reviewChangesButton.click();
        await this.verifyDifference(data);
        await this.saveButton.click();
        try {
            await this.page.locator('//*[text()="Save Changes"]').click({ timeout: 3000 });
        }
        catch (error) {

        }
    }



    /**
     * this method will actually verify the diff of catalog before saving 
     * @param data  
     */
    async verifyDifference(data: { fieldName: string, sideToVerify: string, isVisible: boolean, theLastKey: string }[]) {
        for (const key of data) {
            let diffTabNumber = key.sideToVerify == 'left' ? 0 : 1;
            console.log('value is' + diffTabNumber);
            console.log('fildName is ' + key.fieldName);
            let propertyVisible: boolean = false;
            let scrolledCount = 0;
            //revert new code editor
            await this.codeMirrorEditorTextArea.first().locator('//*[contains(text(),"type")]').first().click();
            // revert new code editor
            while (!await this.page.locator(`//*[contains(text(),"${key.theLastKey}")]`).first().isVisible() && (!await this.codeMirrorEditorTextArea.nth(diffTabNumber).locator(`//*[contains(text(),"${key.fieldName}")]`).isVisible())) {
                await this.page.mouse.wheel(0, 200);

                if (await this.codeMirrorEditorTextArea.nth(diffTabNumber).locator(`//*[contains(text(),"${key.fieldName}")]`).isVisible()) {
                    propertyVisible = true;
                }
                scrolledCount++;
            }
            console.log(propertyVisible);
            if (key.isVisible) {
                expect(propertyVisible == true || scrolledCount == 0).toBeTruthy();
            }
            else {
                expect(await this.page.locator(`//*[contains(text(),"${key.theLastKey}")]`).first().isVisible() && propertyVisible == false);
            }
        }
    }


    /**
     * this method is used to check the last updated time on catalog framework page 
     * @param resourceKind like Clusters , applications ->> use text for the same 
     */
    async checklastUpdatedDateOfCatalog(resourceKind: string) {
        const day = new Date();
        await this.catalogResourceNameDiv.locator(`//*[text()=${resourceKind}]`).waitFor({ timeout: 12000 });
        var timeValue = await this.page.locator(`//*[text()="${resourceKind}"]/following-sibling::span[contains(text(),'AM') or contains(text(),'PM')]`).textContent();
        expect(timeValue?.split(',')[1].split(' ')[0] == day.getDate().toString());
    }

    async checkAvailableCatalogs(resourceToCheck: string) {
        await this.catalogResourceNameDiv.first().waitFor();
        if (await this.page.locator('//*[contains(@class,"global-configuration__component-wrapper")]').locator(`//*[text()="${resourceToCheck}"]`).isVisible()) {
            return true;
        }
        return false;
    }
}