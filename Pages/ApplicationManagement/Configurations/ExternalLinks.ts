import { expect, Locator, <PERSON> } from "@playwright/test";
import { link } from "fs";
import { BasePage } from "../../BasePage";

export class ExternalLinks extends BasePage {
    readonly addExternalLinksButton: Locator;
    readonly linkNameInputField: Locator;
    readonly descriptionInputField: Locator;
    readonly applicationDropdownSelector: Locator;
    readonly clusterDropdownSelector: Locator;
    readonly urlTemplateInputField: Locator;
    readonly openInNewTabSpan: Locator;
    readonly appAdminsCanEditSpan: Locator;
    readonly saveLinkButton: Locator;
    readonly applicationFilterDropdownSelector: Locator;
    readonly clusterFilterDropdownSelector: Locator;
    readonly filterApplyButton: Locator;
    readonly externalLinkDetailsList: Locator;

    constructor(public page: Page) {
        super(page);
        this.addExternalLinksButton = this.page.getByTestId('external-links-add-link');
        this.linkNameInputField = this.page.locator('//*[@placeholder="Link name"]');
        this.descriptionInputField = this.page.getByTestId(`external-link-description-input`);
        this.applicationDropdownSelector = this.page.locator(`//*[contains(@class,'link-applications__select__control')]`);
        this.clusterDropdownSelector = this.page.locator(`//*[contains(@class,'link-clusters__select__control')]`);
        this.urlTemplateInputField = this.page.locator('//*[@name="urlTemplate"]');
        this.openInNewTabSpan = this.page.locator(`//*[text()="Always open in new tab"]`);
        this.appAdminsCanEditSpan = this.page.locator(`//*[text()="App admins can edit"]`);
        this.saveLinkButton = this.page.getByTestId(`save-link-button`);
        this.applicationFilterDropdownSelector = this.page.locator(`//*[contains(@class,'app-list-app-status-select__control')]`).first();
        this.clusterFilterDropdownSelector = this.page.locator(`//*[contains(@class,'app-list-app-status-select__control')]`).nth(1);
        this.filterApplyButton = this.page.getByTestId(`filter-select-picker-apply`);
        this.externalLinkDetailsList = this.page.locator(`//*[contains(@class,'external-link-list__row') and not(contains(@class,'bg__primary'))]`);


    }

    async configureExternalLinks(data: { linkName: string, description?: string, applicationSpecific: boolean, clusterOrAppValue: string[], urlTemplate: string, openInNewTab: boolean, appAdminsCanEdit?: boolean }, isFromGlobalConfig: boolean = true) {
        await this.addExternalLinksButton.click();
        await this.linkNameInputField.fill(data.linkName);
        data.description ? await this.descriptionInputField.fill(data.description) : console.log('desc was not provided');
        if (isFromGlobalConfig) {
            data.applicationSpecific ? await this.page.getByTestId(`specific-applications-select`).click() : await this.page.getByTestId('specific-clusters-select').click();
            let dropdownLocator: Locator = data.applicationSpecific ? this.applicationDropdownSelector : this.clusterDropdownSelector;
            for (let entity of data.clusterOrAppValue) {
                await dropdownLocator.click();
                await this.page.keyboard.type(entity);
                if (await this.page.locator(`//*[@role="listbox"]//*[text()="${entity}"]`).isVisible()) {
                    await this.page.keyboard.press('Enter');
                }
                else {
                    await this.page.locator(`//*[@role="listbox"]`).click();
                }
            }
        }
        await this.urlTemplateInputField.fill(data.urlTemplate);
        data.openInNewTab ? await this.openInNewTabSpan.click() : console.log('skipping the open in new tab');
        data.appAdminsCanEdit ? await this.appAdminsCanEditSpan.click() : console.log('skipping app admins editing');
        await this.saveLinkButton.click();
    }
    async applyFilterAndVerifyResult(filterType: ("Application" | "cluster")[], filterValue: string[], resultValues: string[]) {
        for (let element of filterType) {
            for (let key of filterValue) {
                element == "Application" ? await this.applicationFilterDropdownSelector.click() : await this.clusterFilterDropdownSelector.click();
                await this.page.keyboard.type(key);
                await this.page.keyboard.press('Enter');
                await this.filterApplyButton.click();
            }
        }
        let allExternalLinks = await this.externalLinkDetailsList.all();
        let elementFound: boolean = false;
        for (let key of allExternalLinks) {
            let elementContent = await key.textContent();
            let i: number;
            for (i = 0; i < resultValues.length; i++) {
                console.log(key.textContent());
                console.log("current value" + resultValues[i]);
                console.log(elementContent?.includes(resultValues[i]));
                if (!elementContent?.includes(resultValues[i])) {
                    break;
                }
            }
            if (i == resultValues.length) {
                elementFound = true;
                break;
            }
        }
        expect(elementFound).toBeTruthy();


    }
    /**
     * 
     * @param linkName 
     */
    async deleteExternalLink(linkName: string) {
        await this.externalLinkDetailsList.locator(`//*[text()="${linkName}"]`).hover({ timeout: 10000 });
        await this.page.locator(`//*[@data-testid="external-link-delete-button-${linkName}"]`).click();
        await this.dialogDeleteConfirmationButton.click();

    }
}   