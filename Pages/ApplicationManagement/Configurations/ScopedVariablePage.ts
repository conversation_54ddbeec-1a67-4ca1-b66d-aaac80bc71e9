/**
 * fs import has been used in verifyDefaultTemplateDownload method
 * Constants import has been used in reviewAndSaveChangesOfScopedVariableConfiguration method
 * BaseTest import has been used in reviewAndSaveChangesOfScopedVariableConfiguration method
 * BaseDeploymentTemplatePage import has been used in reviewAndSaveChangesOfScopedVariableConfiguration method
 */
import { Page, Locator, expect } from "@playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { BasePage } from "../../BasePage";
import { BaseDeploymentTemplatePage } from "../Applications/BaseDeploymentTemplatePage";
import { Constants } from "../../../utilities/Constants";
const fs = require('fs');

/**
 * This class represents the Scoped Variables page and provides
 * methods to interact with various elements on the page, particularly for managing
 * scoped variables and their configurations.
 */
export class ScopedVariablePage extends BasePage {
  readonly downloadTemplateButton: Locator;
  readonly uploadYamlConfigurationButton: Locator;
  readonly reviewChangesButton: Locator;
  readonly saveChangesButton: Locator;
  readonly baseDeploymentPage: BaseDeploymentTemplatePage;
  readonly editScopeVariableConfigurationButton: Locator;
  readonly yamlTabButton: Locator;
  readonly variablesTabButton: Locator;
  readonly searchVariableInputField: Locator;
  readonly sensitiveVariableIcon: Locator;
  readonly nonSensitiveVariableIcon: Locator;
  readonly environmentTabButton: Locator;
  readonly clusterFilterDropdown: Locator;
  readonly listItemDivInsideDropdown: Locator;
  readonly clusterFilterApplyButton: Locator;
  readonly searchEnvironmentInputField: Locator;
  readonly downloadSavedFileButton: Locator;
  readonly downloadDefaultTemplateButton: Locator;

  /**
   * constructor will initialize the page and other locators
   * @param page 
   */
  constructor(public page: Page) {
    super(page);
    this.page = page;
    this.downloadTemplateButton = this.page.getByTestId('dropdown-btn');
    this.uploadYamlConfigurationButton = this.page.locator('//*[@for="upload-scoped-variables-input" or @for="descriptor-variables-input"]');
    this.reviewChangesButton = this.page.getByTestId('scope-variables-editor-save-btn');
    this.saveChangesButton = this.reviewChangesButton;
    this.baseDeploymentPage = new BaseDeploymentTemplatePage(this.page);
    this.editScopeVariableConfigurationButton = this.page.getByTestId(`edit-variables-btn`);
    this.yamlTabButton = this.page.getByText('YAML');
    this.variablesTabButton = this.page.locator(`//*[text()="Variables"]`);
    this.searchVariableInputField = this.page.getByTestId('search-by-variable-name');
    this.sensitiveVariableIcon = this.page.getByTestId('visibility-off');
    this.nonSensitiveVariableIcon = this.page.getByTestId('visibility-on');
    this.environmentTabButton = this.page.getByText(`Environment`);
    this.clusterFilterDropdown = this.page.locator(`//*[contains(@class,'cluster-variable-select__control')]`);
    this.listItemDivInsideDropdown = this.page.locator('//*[@role="option"]');
    this.searchEnvironmentInputField = this.page.locator(`//*[@placeholder="Search environment"]`);
    this.downloadSavedFileButton = this.page.locator(`//*[text()="Download saved file"]`);
    this.downloadDefaultTemplateButton = this.page.locator(`//*[text()="Download template"]`);
  }

  /**
   * This method will verify the functionality of download template button
   * @param fileContentToVerify content you want to verify in downloaded file
   */
  async verifyTemplateDownload(downloadDefaultTemplate: boolean = true, fileContentToVerify: string[] = ['category: Global']): Promise<void> {
    await this.yamlTabButton.click();
    await this.downloadTemplateButton.click();
    let [downloadedFileObject] = await Promise.all([this.page.waitForEvent('download'), downloadDefaultTemplate ? this.downloadDefaultTemplateButton.click() : this.downloadSavedFileButton.click()]);
    let downloadedFilePath = await downloadedFileObject.path();
    let downloadedFileContent = await fs.readFileSync(downloadedFilePath, 'utf8');
    for (let content of fileContentToVerify) {
      expect(downloadedFileContent).toContain(content);
    }
  }

  /**
   * this method will upload the yaml configuration for scoped variables
   * @param filePathOfYamlConfiguration 
   */
  async uploadScopeVariableYamlConfiguration(filePathOfYamlConfiguration: string): Promise<void> {
    let [fileChooserObject] = await Promise.all([
      this.page.waitForEvent('filechooser'),
      this.uploadYamlConfigurationButton.click()
    ]);
    await fileChooserObject.setFiles(filePathOfYamlConfiguration);
  }

  /**
   * use this method to verify the last saved config and save the changes
   * @param configDiffToVerify optional param if you want to verify the config diff
   */
  async reviewAndSaveChangesOfScopedVariableConfiguration(configDiffToVerify?: { field: string, sideToVerifyDiff: string, isEditable: boolean, value: string }[], isSuccessfull = true): Promise<void> {
    await this.reviewChangesButton.click();
    if (configDiffToVerify) {
      await this.baseDeploymentPage.verifyConfigDifference(configDiffToVerify);
    }
    let toastMessageToVerify: string = isSuccessfull ? Constants.successToastMessage : Constants.failureToastMessage;
    await BaseTest.checkToast(this.page, this.saveChangesButton, toastMessageToVerify);
  }


  /**
   * This method will clear the existing yaml and replace it with updated yaml that u will provide
   * @param updatedConfiguration 
   */
  async editScopeVariableConfigurationAndReplaceItWithAnotherYaml(updatedConfiguration: string): Promise<void> {
    await this.yamlTabButton.click();
    await this.editScopeVariableConfigurationButton.click();
    await this.clearAnyTextArea(this.codeMirrorEditorTextArea);
    await this.enterConfigurationInTextArea(this.codeMirrorEditorTextArea, updatedConfiguration)
  }


  /**
   * Search a variable 
   * @param variableName 
   */
  async searchAVariable(variableName: string): Promise<void> {
    await this.variablesTabButton.click();
    await this.searchVariableInputField.fill(variableName);
    await this.page.keyboard.press('Enter');
  }

  /**
   * This method will verify whether the variable is sensitive or not
   * @param verificationObjectArray  varName and isSensitive are the 2 properties that you need to pass
   */
  async verifyValueOfVariableIsSensitiveOrNot(verificationObjectArray: { varName: string, isSensitive: boolean }[]): Promise<void> {
    await this.variablesTabButton.click();
    for (let verificationObject of verificationObjectArray) {
      await this.searchAVariable(verificationObject.varName);
      let rowLocatorOfVariable = this.returnCompleteRowLocatorOfVariableOnVariablesPage(verificationObject.varName);
      verificationObject.isSensitive ? await expect((rowLocatorOfVariable).locator(this.sensitiveVariableIcon)).toBeVisible() :
        await expect((rowLocatorOfVariable).locator(this.nonSensitiveVariableIcon)).toBeVisible();
    }
  }


  /**
   * This method will navigate to scoped variable page
   */
  async navigateToScopedVariablePage() {
    await this.page.goto(process.env.BASE_SERVER_URL as string + '/global-config/scoped-variables');
  }

  /**
   * This method will return the complete row div locator of a variable , and we can further use this locator for multi purpose
   * @param varName 
   * @returns Locator
   */
  returnCompleteRowLocatorOfVariableOnVariablesPage(varName: string): Locator {
    return this.page.locator(`//*[text()="${varName}"]/ancestor::div[3]`);
  }

  /**
   * This method will return the complete row div locator of a environment , and we can further use this locator for multi purpose
   * @param envName 
   * @returns 
   */
  returnCompleteRowLocatorOfEnvironmentsOnEnvPage(envName: string) {
    return this.page.locator(`//*[text()="${envName}"]`).locator(`xpath=parent::div`);
  }

  /**
   * This method will apply the cluster filter
   * @param clusterNames 
   */
  async applyClusterFilter(clusterNames: string[]) {
    await this.clusterFilterDropdown.click();
    for (let clusterName of clusterNames) {
      await this.listItemDivInsideDropdown.locator(`//*[text()="${clusterName}"]`).click();
    }
    await this.clusterFilterApplyButton.click();
  }


  /**
   * This method will search for an environment
   * @param environmentName 
   */
  async searchEnvironment(environmentName: string) {
    await this.environmentTabButton.click();
    await this.searchEnvironmentInputField.fill(environmentName);
    await this.page.keyboard.press('Enter');
  }

  /**
   * 
   * @param environmentName 
   * @param count 
   */
  async verifyCountOfVariablesOfAnEnvironment(environmentName: string, count: number) {
    await this.searchEnvironment(environmentName);
    let rowLocatorOfEnvironment = this.returnCompleteRowLocatorOfEnvironmentsOnEnvPage(environmentName);
    await expect(rowLocatorOfEnvironment.locator(`//span`)).toContainText(count.toString());
  }


}
