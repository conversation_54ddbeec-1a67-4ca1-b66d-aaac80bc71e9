import { Page, Locator, expect, APIRequestContext, request } from '@playwright/test';
import { dataForSecret, dataForDeployment, dataForConfigMap } from '../../utilities/clipboardyYamls.ts/YamlForResourceBrowser';
import { BaseTest } from '../../utilities/BaseTest'
import { BasePage } from '../BasePage';
import { BaseDeploymentTemplatePage } from '../ApplicationManagement/Applications/BaseDeploymentTemplatePage';
import { ApiUtils } from '../../utilities/ApiUtils';
import { UpdateK8sResourceYamlDTO } from '../../DTOs/Infrastructure Management/Resource Browser/UpdateK8sResourceYamlDTO'
import YAML from 'yaml';
import { Json } from 'mailslurp-client';
import clipboardy from 'clipboardy';
import { group } from 'console';


export class ResourceBrowserPage extends BasePage {
    // Buttons and controls related to resource browser.
    readonly resourceBrowserButton: Locator;
    readonly refreshButtonOnResourceBrowser: Locator;
    readonly searchButtonOnResourceBrowser: Locator;
    readonly nodeAndPodButton: Locator;
    readonly clusterSearch: Locator;
    readonly clusterListContainer: Locator;
    readonly resourceBrowserLinkOnHeader: Locator;
    readonly textContentForCreatedResource: Locator;
    readonly filterByLabelInputField: Locator;
    readonly fieldSelectorKeyInputField: Locator;
    readonly fieldSelectorValueInputField: Locator;
    readonly celExpressionInputField: Locator;
    readonly celFilterApplyButton: Locator;
    readonly defaultClusterTerminalNamespaceDropdown: Locator;
    readonly addTaintButton: Locator;


    // Locators related to resource filtering and searching.
    readonly resourceFilterButton: Locator;
    readonly resourceFilterList: Locator;
    readonly resourceSearchBar: Locator;
    readonly genericEmptyState: Locator;


    // Locators specific to pods.
    readonly podsList: Locator;
    readonly resourceListDivSection: Locator;
    readonly podsRunningStatus: Locator;
    readonly podManifestNameVerification: Locator;
    readonly resourcePopupMenuButton: Locator;
    readonly resourcePopupManifestOption: Locator;
    readonly podDeleteButton: Locator;
    readonly podEventsButton: Locator;
    readonly podManifestsButton: Locator;
    readonly podLogsButton: Locator;
    readonly podTerminalButton: Locator;
    readonly podTerminalEditor: Locator;
    readonly podTerminalVerfication: Locator;
    readonly clearFiltersButton: Locator;
    readonly editLiveManifestButton: Locator;

    // Locators related to creating and applying resources.
    readonly createResourceButton: Locator;
    readonly yamlEditor: Locator;
    readonly resourceApplyButton: Locator;
    readonly resourceCreateMessage: Locator;
    readonly createResourceCloseButton: Locator;
    readonly resourceCreated: Locator;
    readonly createdResourceList: Locator;
    readonly appplyChangesButton: Locator;

    // Locators related  secrets.
    readonly secretButton: Locator;
    readonly secretSearchBar: Locator;
    readonly resourceList: Locator;
    readonly showDecodedValueCheckBox: Locator;

    // Locators related to resource deletion.
    readonly resourceDeleteButton: Locator;
    readonly editYAMLButton: Locator;
    readonly resourceDeletionToast: Locator;

    // Locators related to terminal and cluster operations.
    readonly terminalButton: Locator;
    readonly clusterTerminalButton: Locator;
    readonly terminalConnectionVerification: Locator;
    readonly terminalDisconnectButton: Locator;
    readonly terminalDisconnectionStripMessage: Locator;

    // Locators related to replicaSets
    readonly replicaSetCreated: Locator;
    readonly numberOfReadyReplicaSets: Locator;

    //Locators related to Node Columns filter
    readonly pageSyncButton: Locator;
    readonly columnsFilter: Locator;
    readonly columnsFilterApplyButton: Locator;
    readonly columnsListCheckBox: Locator;
    readonly columnsList: Locator;
    readonly clusterNodesLink: Locator;
    readonly cordonButton: Locator;
    readonly editTaintsButton: Locator;
    readonly checkVulnerabilityButton: Locator;
    readonly apiVersionFieldInManifest: Locator;

    //Locators related to resource recommender
    readonly resourceRecommenderButton: Locator;
    readonly openResourceRecommenderActionMenu: Locator;
    readonly rescanForRecommendations: Locator;
    readonly resourceRecommFilterMenuList: Locator;
    readonly resourceRecommFilterDrowpdown: Locator;
    readonly resourceRecommFilterInput: Locator;

    constructor(public page: Page) {
        super(page);
        // Assign locators to corresponding UI elements on the resource Browser.
        this.resourceBrowserButton = page.getByTestId("click-on-resource-browser");
        this.resourceBrowserLinkOnHeader = page.locator(`//*[contains(@class,'resource-browser')]//a[@href="/dashboard/resource-browser"]`);
        this.refreshButtonOnResourceBrowser = page.getByTestId("cluster-list-refresh-button");
        this.searchButtonOnResourceBrowser = page.locator('//*[@class="kind-search-select__input"]');
        this.nodeAndPodButton = page.getByTestId("K8s Resources");
        this.clusterSearch = page.getByPlaceholder("Search clusters");
        this.textContentForCreatedResource = this.page.locator(`//*[contains(@data-testid,'input-label')]`);
        this.filterByLabelInputField = this.page.locator('//*[@name="QuerySelector"]');
        this.fieldSelectorKeyInputField = this.page.locator(`//*[@placeholder="Enter key"]`);
        this.celExpressionInputField = this.page.locator('//*[@placeholder="Write CEL Expression"]');
        this.fieldSelectorValueInputField = this.page.locator(`//*[@placeholder="Enter value"]`);
        this.celFilterApplyButton = this.page.locator(`//*[text()="Apply"]`);
        this.defaultClusterTerminalNamespaceDropdown = this.page.locator(`//*[contains(@class,'cluster-terminal-name-space__value-container ')]`);
        this.resourceListDivSection = this.page.getByTestId(`created-resource-name`);
        this.addTaintButton = this.page.getByTestId(`add-taint`);


        // Assign locators related to resource filtering and searching.
        this.resourceFilterButton = page.locator(".resource-filter-select__control");
        this.resourceFilterList = page.locator(".resource-filter-select__menu-list");
        this.resourceSearchBar = page.getByTestId("search-bar");

        // Assign locators specific to pods.
        this.podsList = page.getByTestId("created-resource-name");
        this.podsRunningStatus = page.locator('//*[contains(@class,"running")]');
        this.podTerminalButton = page.getByTestId("terminal-nav-link");
        this.resourcePopupMenuButton = this.resourceListDivSection.locator(`//*[@aria-label="Open action menu"]`);
        this.resourcePopupManifestOption = page.getByTestId("action-menu-item-manifest")
        this.podDeleteButton = page.getByTestId('action-menu-item-delete');
        this.podEventsButton = page.getByTestId("pod-events-button");
        this.podManifestsButton = page.getByTestId("pod-manifests-button");
        this.podLogsButton = page.getByTestId("logs-nav-link");
        this.podTerminalEditor = page.locator('//*[@role="list"]//*[contains(text(),"#")]')
        // this.clearFiltersButton = page.locator('//button[text()="Clear filters"]');
        this.clearFiltersButton = this.page.getByTestId('clear-filters');
        this.checkVulnerabilityButton = page.getByTestId(`action-menu-item-vulnerability`);

        // Assign locators related to creating and applying resources.
        this.createResourceButton = page.getByTestId("create-resource");
        // reverting new code editor
        this.yamlEditor = page.locator('//*[@class="cm-activeLine cm-line"]').first();
        //this.yamlEditor = page.locator('//div[@class="view-line"]');
        this.resourceApplyButton = page.getByTestId("create-kubernetes-resource-button");
        this.resourceCreated = page.locator('//*[@class="flexbox"]');//Todo create data test id
        this.createResourceCloseButton = page.getByTestId("close-after-resource-creation");

        // Assign locators related to secrets.
        this.secretButton = page.getByTestId("Secret");
        this.showDecodedValueCheckBox = page.locator('//*[@id="showDecodedValue"]');
        // Assign locators related to resource deletion.
        this.resourceDeleteButton = page.getByText("Delete");
        this.editYAMLButton = page.getByTestId("edit-yaml-button");
        this.resourceDeletionToast = page.getByText("Resource deleted successfully");

        // Assign locators related to terminal and cluster operations.
        this.terminalButton = page.locator(`//*[@aria-label="Select tab Terminal 'default_cluster'"]`);
        this.clusterTerminalButton = page.getByTestId("cluster-terminal-button");
        this.terminalConnectionVerification = page.locator('//*[contains(text(),"root@terminal-access-")]').first();
        this.terminalDisconnectButton = page.getByTestId("disconnect-button");
        this.terminalDisconnectionStripMessage = page.getByText("Disconnected")

        // Assign locators for replicaSets
        this.numberOfReadyReplicaSets = page.getByTestId("ready-count")

        //Assign locators for Node Columns filter
        this.pageSyncButton = page.getByTestId("refresh-icon");
        this.columnsFilter = page.locator('.node-column-list-filter__control');
        this.columnsFilterApplyButton = page.getByText("Apply");
        this.columnsListCheckBox = page.getByTestId("undefined-chk-span");
        this.columnsList = page.locator(`//*[contains(@class,'sortable-table-header__container')]`);
        this.genericEmptyState = this.page.getByTestId('generic-empty-state');
        this.createdResourceList = this.page.locator('//*[@data-testid="created-resource-name"]//button').first();
        this.editLiveManifestButton = this.page.locator(`//*[text()="Edit live manifest"]`);
        this.appplyChangesButton = this.page.locator(`//*[text()="Apply changes"]`);
        this.clusterNodesLink = this.page.locator(`//*[@data-testid="created-resource-name"]//*[@data-kind="Node"]`);
        this.cordonButton = this.page.locator(`//*[text()="Cordon"]`);
        this.editTaintsButton = this.page.locator(`//*[text()="Edit taints"]`);
        this.apiVersionFieldInManifest = this.page.locator(`//*[text()='apiVersion']`);

        this.resourceRecommenderButton = this.page.locator(`//div[@id='Resource Recommender']`)
        // search-bar
        this.openResourceRecommenderActionMenu = this.page.getByTestId('open-resource-recommender-action-menu')
        this.rescanForRecommendations = this.page.getByRole('button', { name: 'Re-scan for recommendations' });
        this.resourceRecommFilterDrowpdown = page.locator(".resource-filter__gvk-select__control");
        this.resourceRecommFilterInput = page.locator(".resource-filter__gvk-select__input");
        this.resourceRecommFilterMenuList = page.locator(".resource-filter__gvk-select__menu-list");

    }


    // A function to navigate to the specified URL, click on the resource browser button,
    // wait for the network to be idle, and hover over the refresh button on the resource browser.
    async goToResourceBrowser(url: string) {
        await BaseTest.clickOnDarkMode(this.page);
        await BaseTest.clickOnToolTipOkayButton(this.page);
        // Navigate to the specified URL.
        await this.page.goto(url);

        // Click on the resource browser button.
        await this.resourceBrowserButton.click();

        // Wait for the network to be in an idle state.
        await this.page.waitForLoadState('load');

        // Hover over the refresh button on the resource browser.
        await this.refreshButtonOnResourceBrowser.hover();
    }

    // A function to go to a specific cluster by filling the cluster search, pressing Enter, and clicking on the cluster list container.
    async goToCluster(clusterName: string) {
        // Fill the cluster search with the specified cluster name.
        await expect(async () => {
            await this.clusterSearch.fill(clusterName);

            // Press Enter to initiate the search.
            await this.clusterSearch.press("Enter");

            // Click on the cluster list container to select the specified cluster.
            await this.page.locator(`//*[@data-testid="cluster-row-${clusterName}"]//a`).first().click({ timeout: 12000 });
            await expect(this.createResourceButton).toBeVisible({ timeout: 20000 });
        }).toPass({ timeout: 3 * 1000 * 60 });

    }

    async reScanResourceRecommendationForACluster(clusterName: string) {
        // Fill the cluster search with the specified cluster name.
        await this.goToCluster(clusterName);
        await this.resourceRecommenderButton.click({ delay: 1000 });
        await this.page.waitForLoadState('networkidle');
        await this.openResourceRecommenderActionMenu.click({ delay: 1000 });
        await this.rescanForRecommendations.click({ delay: 1000 });

    }



    // Check the column filter options for the Node view.
    async checkColumnFilterForNode(noOfOptionsInColumnFilter: number) {
        await this.deselectAllCheckBoxesOfColumnFilter(noOfOptionsInColumnFilter);
        await this.selectAllCheckBoxesOfColumnFilter(noOfOptionsInColumnFilter);
        await this.deselectTwoCheckBoxesOfColumnFilter(noOfOptionsInColumnFilter);
    }

    async deselectAllCheckBoxesOfColumnFilter(noOfOptionsInColumnFilter: number) {
        // Deselect all checkboxes in the column filter.
        await expect(async () => {
            await this.columnsFilter.click();
            //await this.columnsListCheckBox.getByText(columnsArray[0],{exact:true}).click();
            for (let i = 0; i < noOfOptionsInColumnFilter - 1; i++) {
                if (await this.columnsListCheckBox.nth(i).isChecked() === true) {
                    await this.columnsListCheckBox.nth(i).click({ timeout: 5000 });
                }
            }
            // Apply the column filter changes and assert the count of visible columns.
            await this.columnsFilterApplyButton.click({ timeout: 8000 });
            expect(await this.columnsList.count()).toEqual(3);
        }).toPass({ timeout: 3 * 1000 * 60 });

    }

    async selectAllCheckBoxesOfColumnFilter(noOfOptionsInColumnFilter: number) {
        // Select all checkboxes in the column filter.
        await expect(async () => {
            await this.columnsFilter.click();
            for (let i = 0; i < noOfOptionsInColumnFilter - 1; i++) {
                if (await this.columnsListCheckBox.nth(i).isChecked() === false) {
                    await this.columnsListCheckBox.nth(i).click({ timeout: 5000 });
                }
            }
            await this.columnsFilterApplyButton.click({ timeout: 5000 });
            expect(await this.columnsList.count()).toEqual(noOfOptionsInColumnFilter + 2);
        }).toPass({ timeout: 3 * 1000 * 60 });

    }

async deselectTwoCheckBoxesOfColumnFilter(noOfOptionsInColumnFilter: number) {
    // Deselecting 2 specific options in the column filter and verifying the changes.
    await this.columnsFilter.click();
    
    // Deselect the checkboxes for the 4th and 7th options.
    //improve logic instead of hardcoding nth index
    await this.columnsListCheckBox.nth(1).click({ timeout: 5000 });
    await this.columnsListCheckBox.nth(6).click({ timeout: 5000 });

    // Apply the column filter changes and assert the count of visible columns.
    await this.columnsFilterApplyButton.click({ timeout: 6000 });

    // Wait for specific column names to be hidden after deselecting options.
     await this.page.getByText("CPU USAGE").waitFor({ state: 'hidden', timeout: 10000 });
    await this.page.getByText("Taints").waitFor({ state: 'hidden', timeout: 10000 });
    
    console.log(await this.columnsList.count());
    expect(await this.columnsList.count()).toEqual(noOfOptionsInColumnFilter);
}



    // A function to check the default terminal for a specific resource.
    //resourceName: string, resourceType: string, resourceNamespace: string = 'automation'
    //kubectl get ${resourceType} -n ${resourceNamespace} | grep ${resourceName}
    async executeCommandsInDefaultTerminal(data: { commandToExecute: string, resultOfCommandToVerify: string, count: number }[]) {
        // Close the create resource dialog.
        if (await this.createResourceCloseButton.isVisible({ timeout: 8000 })) {
            await this.createResourceCloseButton.click();
        }
        // Click on the terminal button to open the default terminal.
        await this.terminalButton.click();

        // Expect the terminal connection verification message to be visible .
        await expect(this.terminalConnectionVerification).toBeVisible({ timeout: 2 * 60 * 1000 });
        // Type a kubectl command in the terminal to get information about the specified resource.
        for (const key of data) {
            await this.page.keyboard.type(key.commandToExecute);
            await this.page.keyboard.press("Enter", { delay: 30000 });
            await expect(this.page.locator(`//*[@class="xterm-accessibility-tree"]//*[contains(text(),'${key.resultOfCommandToVerify}')]`)).toHaveCount(key.count, { timeout: 2 * 1000 * 60 });
        }

        // Expect the specified resource name to be visible in the terminal output.
    }

    // A function to check the default terminal for pod events.
    async checkDefaultTerminalEvents() {
        // Click on the pod events button to view events in the default terminal.
        await this.podEventsButton.click();

        // Expect the "Started" event to be visible in the terminal output
        await expect(this.page.locator('//*[text()="Started"]').first()).toBeVisible({ timeout: 10000 });
    }

    // A function to check the default terminal for pod manifests.
    async checkDefaultTerminalManifests() {
        // Click on the pod manifests button to view manifests in the default terminal.
        await this.podManifestsButton.click();

        // Expect the "apiVersion" field to be visible in the terminal output
        await expect(this.page.locator('//*[text()="apiVersion"]')).toBeVisible({ timeout: 10000 });
    }

    // A function to check the default terminal disconnection and return to the node view.
    async checkDefaultTerminalDisconnection() {
        // Click on the cluster terminal button and then disconnect the terminal.
        await this.clusterTerminalButton.click();
        await this.terminalDisconnectButton.click();

        // Expect the terminal disconnection strip message to be visible
        await expect(this.terminalDisconnectionStripMessage.first()).toBeVisible({ timeout: 2 * 60 * 1000 });

        // Return to the node view
        await this.nodeAndPodButton.click();
    }



    // Check the count and status of pods for a given resource name and expected number of replicas.
    async checkPodsCountAndStatus(resourceName: string, replicas: number, resourceType: string = "Pod", isSearchRequired: boolean = true) {
        // Search for pods with the specified resource name.
        await this.searchAnyResourceType(resourceType);
        isSearchRequired ? await this.searchAnyResourceName(resourceName) : '';
        // Log and assert the count of pods matches the expected number of replicas.
        console.log(await this.podsList.count());
        expect(await this.podsList.count()).toEqual(replicas);

        // Log and assert the count of running pods matches the expected number of replicas.
        if (resourceType == "Pod") {
            console.log(await this.podsRunningStatus.count());
            await expect(this.podsRunningStatus).toHaveCount(replicas, { timeout: 3 * 1000 * 60 });
            // expect(await this.podsRunningStatus.count()).toEqual(replicas);
        }
    }

    // Check the visibility of a pod manifest with a specified resource name within a timeout.
    async checkPodManifest(resourceName: string) {
        expect(this.page.locator(`//*[contains(text(),"${resourceName}")]`).first()).toBeVisible({ timeout: 5000 });
    }

    // Check the functionality of viewing pod logs.
    async checkPodLogs() {
        // Click on the pod logs button and expect the terminal to be present.
        await this.podLogsButton.click();
        expect(this.page.locator('//*[@class="terminal xterm"]')).not.toBeNull();
    }

    // Check the functionality of executing commands in the pod terminal and verifying the output.
    async checkPodTerminal(terminalCommand: string, terminalOutput: string, isEligible: boolean = true) {
        // Click on the pod terminal button, enter a command, and verify the output.
        if (!isEligible) {
            await BaseTest.checkToast(this.page, this.podTerminalButton, 'Not authorized');
        }
        else {
            await this.podTerminalButton.click({ delay: 1000 });
            await expect(this.page.locator('//*[@role="list"]//*[contains(text(),"#")]').first()).toBeVisible({ timeout: 10000 });
            await this.podTerminalEditor.first().click();
            await this.page.keyboard.type(terminalCommand);
            await this.page.keyboard.press("Enter");
            await expect(this.page.locator(`//*[@class="xterm-accessibility-tree"]//*[contains(text(),"${terminalOutput}")]`)).toBeVisible({ timeout: 3000 });
            // Click on the pod button to navigate back.
            await this.nodeAndPodButton.click();
            await this.pageSyncButton.click();
            await this.page.waitForLoadState('load');
        }
    }

    // Delete a pod, verify deletion toast
    async externalDeletionOfResource(messageToVerify: string = "Resource deleted successfully") {

        // Open pod popup menu, click on delete, check deletion toast, wait for synchronization,
        await this.resourcePopupMenuButton.first().click();
        await this.page.waitForLoadState('load')
        await this.podDeleteButton.click({ delay: 2000 });
        await this.copyAndPasteTheResourceNameToDelete();
        await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, messageToVerify);
        await this.page.waitForLoadState('load');

    }

    async copyAndPasteTheResourceNameToDelete() {
        await this.textContentForCreatedResource.waitFor();
        let textToCopy = await this.textContentForCreatedResource.textContent();
        console.log(' actual text content is' + textToCopy);
        await this.deleteModalInputField.fill(textToCopy!.split("‘")[1].split("’")[0]);
        console.log('value has been filled');
    }
    async checkRecreationOfPod(resourceName: string, replicas: number) {
        let n = 0;
        while (n <= 5) {
            await this.pageSyncButton.click()
            await this.podsList.first().waitFor({ timeout: 15000 });
            if (await this.podsList.count() == replicas && await this.podsRunningStatus.count() == replicas) {
                break;
            }
            n++;
        }
    }
    async clickOnAnyResource(resourceName: string) {
        await this.searchAnyResourceName(resourceName);
        await this.podsList.first().click();
    }

    // Perform various operations in the pod terminal including checking manifest, logs, executing a command,
    // deleting the pod, and returning to the node and pod view.
    async podTerminalOperations(resourceName: string, terminalCommand: string, terminalOutput: string) {
        // Click on the first pod in the list after a delay, check pod manifest, logs, terminal,
        // delete the pod, and return to the pod view.
        await this.podsList.first().click({ delay: 3000 });
        await this.checkPodManifest(resourceName);
        await this.checkPodLogs();
        await this.checkPodTerminal(terminalCommand, terminalOutput);
        await this.externalDeletionOfResource();
        await this.nodeAndPodButton.click();
    }
    async checkMessageOnEditingManifest(messageToVerify: string) {
        await this.createdResourceList.first().click();
        await expect(async () => {
            await this.editLiveManifestButton.click({ delay: 2000 });
            console.log('this has been clicked already');
            await this.page.locator('//*[text()="Review and save changes"]').click();
            await BaseTest.checkToast(this.page, this.appplyChangesButton, messageToVerify);
        }).toPass({ timeout: 2 * 1000 * 60 });
    }
    async checkwarningForNodeActionsForNonSuperAdmin() {
        await this.page.getByTestId('tree-view-item-Nodes').click();
        await this.clusterNodesLink.first().click();
        await this.editTaints('deep', 'aman', 'Not authorized');
        await this.page.reload();
        await this.cordonNode("Not authorized");
        await this.page.reload();
    }

    async editTaints(key: string, value: string, message: string) {
        await this.editTaintsButton.click();
        await this.addTaintButton.click();
        await this.page.getByPlaceholder(`Enter key`).first().fill(key);
        await this.page.getByPlaceholder(`Enter value`).first().fill(value);
        await BaseTest.checkToast(this.page, this.page.locator(`//*[text()="Save"]`), message);
    }
    async cordonNode(messageToVerify: string) {
        await this.cordonButton.click();
        await BaseTest.checkToast(this.page, this.page.locator(`//*[text()="Cordon"]`).nth(1), messageToVerify);
    }

    // A function to apply a resource filter based on a namespace.
    async applyResourceFilter(namespace: string, deployName: string) {
        // Click on the resource filter.
        await this.resourceFilterButton.click();
        await this.page.keyboard.insertText(namespace);
        await this.page.keyboard.press('Enter');
        await this.podsList.first().waitFor({ timeout: 15000 });
        // Click on the specific namespace option in the resource filter dropdown.
        if (await this.resourceSearchBar.textContent() != deployName) {
            await this.searchAnyResourceName(deployName);
        }
        if (await this.resourceFilterButton.textContent() != namespace) {
            await this.resourceFilterButton.click();
            await this.page.keyboard.insertText(namespace);
            await this.page.keyboard.press('Enter');
        }

        // Log the count of pods list and the count of elements with the specified namespace.
        console.log(this.podsList.count());
        console.log(this.page.locator(`//*[text()="${namespace}"]`).count());

        // Assert that the count of pods list equals the count of elements with the specified namespace.
        await this.podsList.first().waitFor({ timeout: 15000 });
        await this.page.locator('//*[@data-testid="namespace-count"]').first().waitFor();
        var namespaceCount = await this.page.locator('//*[@data-testid="namespace-count"]').all();
        var podCount = await this.podsList.all();
        console.log('pod lenght' + podCount.length);
        console.log('namespace count' + namespaceCount.length);
        expect(podCount.length).toEqual(namespaceCount.length);
        // Click on the page sync button.
    }


    // A function to check search filter for a given resource name.

    async checkSearchFilter(resourceName: string, shouldBeVisible: boolean = true) {
        // Step 1: Perform a search for the specified resource name
        await this.searchAnyResourceName(resourceName, shouldBeVisible);

        // Step 2: Expectation - Check if "No matching results" message is visible within the specified timeout
        expect(this.page.locator('//*[text()="No matching results"]')).toBeVisible({ timeout: 10000 });

        // Step 3: Expectation - Check if the clear filters button is visible within the specified timeout
        expect(this.clearFiltersButton).toBeVisible({ timeout: 5000 });

        // Step 4: Perform an action - Click the clear filters button
        await this.clearFiltersButton.click();

        // Step 5: Expectation - Wait for the "No matching results" message to be hidden
        await this.page.locator('//*[text()="No matching results"]').waitFor({ state: 'hidden' });
    }
    // Check the details of a ReplicaSet, such as its visibility and the number of ready replicas.
    async checkReplicaSet(resourceName: string, replicas: number) {
        // Search for the ReplicaSet with the specified name.
        await this.searchAnyResourceType("Replicaset");
        await this.searchAnyResourceName(resourceName);

        // Expect the visibility of the ReplicaSet within a specified timeout.
        await expect(this.page.locator(`//*[contains(@data-name,"${resourceName}")]`)).toBeVisible({ timeout: 5000 });
        const replica = "" + replicas
        // Assert the number of ready replicas matches the expected value.
        expect(await this.numberOfReadyReplicaSets.textContent()).toBe(replica);
    }

    // Search for any type of resource using the specified resource name and type.
    async searchAnyResourceType(resourceType: string, shouldBeVisible: boolean = true) {
        // Type the resource type in the search bar, press Enter, wait for the network to be idle.
        await BaseTest.clickOnDarkMode(this.page);
        await BaseTest.clickOnToolTipOkayButton(this.page);
        await expect(async () => {
            await this.searchButtonOnResourceBrowser.pressSequentially(resourceType, { delay: 100 });
            await this.page.keyboard.press("Enter");
            if (shouldBeVisible) {
                let locatorTobeVisibleForVerification: Locator = resourceType == "Nodes" ? this.page.locator(`//*[contains(@class,'k8s-object-container')]//*[text()="Nodes"]`) : this.resourceSearchBar;
                await expect(locatorTobeVisibleForVerification).toBeVisible({ timeout: 25000 });
            }
            else {
                await this.page.locator(`//div[text()="No matching kind"]`).waitFor({ timeout: 5000 });
            }

        }).toPass({ timeout: 3 * 1000 * 60 });
    }

    async searchAnyResourceName(resourceName: string, shouldBeVisible: boolean = true, isSearchRequired: boolean = true) {
        // Click on the resource search bar, type the resource name, and press Enter.
        // await expect(async () => {
        //     await this.page.locator('//*[@data-testid="refresh-icon"]').click();
        //     await expect(this.page.locator('//*[@class="loader__svg"]')).toBeHidden({ timeout: 25000 });
        // }).toPass({ timeout: 2 * 1000 * 60 });
        await this.resourceSearchBar.waitFor({ state: 'visible', timeout: 20000 });
        await this.resourceSearchBar.clear();
        await this.resourceSearchBar.click({ timeout: 20000 });
        await this.resourceSearchBar.pressSequentially(resourceName, { delay: 100 });
        await this.page.keyboard.press("Enter");
        let isResourceVisible = false;
        try {
            await expect(this.genericEmptyState).toBeVisible({ timeout: 10000 });
        } catch (error) {
            console.log("genericEmptyState is not visible within the timeout. Proceeding without failing the test.");
            isResourceVisible = true;
        }
        return isResourceVisible;
    }



    async createArgoObjectResource(
        generateYamlFn: () => Promise<void>
    ) {
        try {
            await expect(async () => {
                await this.page.reload();
                await this.createResourceButton.click();
                await expect(this.yamlEditor).toBeVisible({ timeout: 9000 });
                await generateYamlFn();
                await this.yamlEditor.waitFor({ timeout: 25000 });
                await this.yamlEditor.click();
                await this.page.keyboard.press("Shift+Insert");
                await this.page.waitForTimeout(4000);
                await this.resourceApplyButton.click({ timeout: 20000 });
                await expect(
                    this.page.locator('//*[text()="Created" or text()="Updated"]')
                ).toBeVisible({ timeout: 12000 });
            }).toPass({ timeout: 4 * 60 * 1000 });
        } catch (error) {
            console.error(`An error occurred during resource creation: ${error.message}`);
            throw error;
        }
    }

    // Create a resource with the specified name and type, handling different resource types.
    async createResource(resourceName: string, resourceType: string, resourceData: any, resourceNamespace: string = 'automation') {
        try {
            // Click the "Create Resource" button and expect the YAML editor to be visible.
            // Click on the YAML editor and populate it with data based on the resource type.
            await expect(async () => {
                await this.page.reload();
                await this.createResourceButton.click();
                await expect(this.yamlEditor).toBeVisible({ timeout: 9000 });
                if (resourceType === "secret")
                    await dataForSecret(resourceName, resourceNamespace, resourceData.SecretUserName);
                else if (resourceType === "deployment")
                    await dataForDeployment(resourceName, resourceNamespace, resourceData.DeploymentReplicas, resourceData.DeploymentContainerName, resourceData.DeploymentContainerImage);
                else if (resourceType == "configmap") {
                    await dataForConfigMap(resourceName, resourceData.keyName, resourceData.valueName);
                }
                // Paste the content into the YAML editor, click the "Apply" button, and wait for the resource creation confirmation.
                await this.yamlEditor.waitFor({ timeout: 25000 });
                //  await this.page.locator('//*[@class="view-lines monaco-mouse-cursor-text"]').click();
                await this.yamlEditor.click();
                //await this.page.locator('//*[@data-language="yaml"]').click();
                await this.page.keyboard.press("Shift+Insert");
                await this.page.waitForTimeout(4000);
                await this.resourceApplyButton.click({ timeout: 20000 });
                await expect(this.page.locator('//*[text()="Created" or text()="Updated"]')).toBeVisible({ timeout: 12000 });
                await this.createResourceCloseButton.click();
            }).toPass({ timeout: 4 * 1000 * 60 });
            // Assert that the resource has been successfully created.

        } catch (error) {
            console.error(`An error occurred during resource creation: ${error.message}`);
            throw error;
        }
    }

    // Update a resource by editing its YAML content, applying the changes, and closing the editor.
    async updateResource(resourceName: string, resourceData: any, resourceNamespace: string = 'automation') {
        // Click the "Edit YAML" button and expect the YAML editor to be visible.
        await expect(async () => {

            await this.page.reload();
            await this.createResourceButton.click();
            await expect(this.yamlEditor).toBeVisible({ timeout: 9000 });
            await this.yamlEditor.click();
            // Populate the YAML editor with updated data and apply the changes.
            await dataForSecret(resourceName, resourceNamespace, resourceData.SecretUpdatedUserName);
            await this.page.keyboard.press("Control+A");
            await this.page.keyboard.press("Backspace");
            await this.yamlEditor.first().click();
            await this.page.keyboard.press("Shift+Insert");
            await this.resourceApplyButton.click();

            // Wait for the resource update confirmation and assert its success.
            await this.resourceCreated.waitFor();
            expect(await this.resourceCreated.textContent()).toBe("Updated");

            // Close the create resource dialog.
            await this.createResourceCloseButton.click();
        }).toPass({ timeout: 4 * 1000 * 60 });

    }

    // Check the decoded values for a secret by clicking "Show Decoded Value" and verifying the displayed username and password.
    async checkDecodedValue(username: string) {
        // Click on "Show Decoded Value" to reveal decoded values.

        await this.page.reload();
        // await this.showDecodedValueCheckBox.click({timeout:15000});
        await this.page.locator('//*[@class="form__checkbox-container"]').nth(1).click({ timeout: 12000 });
        // Expect the decoded password and username to be visible within a specified timeout.
        //reverting new code editor
        await expect(this.page.locator(`//div[contains(@class, 'cm-line') and contains(., '${username}')]`).first()).toBeVisible({ timeout: 40000 });
        //await expect(this.page.locator(`//*[text()="${username}"]`).first()).toBeVisible({ timeout: 40000 });
    }

    // Perform the internal deletion of a resource (e.g., a secret) by searching, selecting, checking decoded values, clicking delete, and verifying the deletion toast.
    async internalDeletionOfResource(resourceName: string, resourceType: string, resourceData: any) {
        // Search for the resource with the specified name and type.
        await this.searchAnyResourceType(resourceType);
        await this.podsList.first().waitFor({ timeout: 25000 });
        await this.searchAnyResourceName(resourceName);
        await this.podsList.first().waitFor({ timeout: 15000 });
        const resourceRow = this.page.locator('[data-testid="created-resource-name"]', { hasText: resourceName });
        const scopedPopupButton = resourceRow.locator('//*[@aria-label="Open action menu"]');
        console.log('Clicking resource popup menu...');
        await scopedPopupButton.click();

        console.log('Waiting for manifest option...');
        await this.resourcePopupManifestOption.waitFor({ state: 'visible' });

        console.log('Clicking manifest option...');
        await this.resourcePopupManifestOption.click();

        if (resourceType === "secret") {
            await expect(async () => {
                await this.checkDecodedValue(resourceData.SecretDecodedUserName);
            }).toPass({ timeout: 3 * 1000 * 60 });
        }

        // Click on the delete button, check the resource deletion toast, and confirm deletion.
        await this.resourceDeleteButton.click({ delay: 1000 });
        await this.copyAndPasteTheResourceNameToDelete();
        await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, "Resource deleted successfully");
    }

    async clickOnResourceBrowserLink() {
        await this.resourceBrowserLinkOnHeader.click();
    }
    async checkNullState(): Promise<boolean> {
        try {
            await expect(this.page.locator('//*[text()="Clear Filters"]')).toBeVisible({ timeout: 10000 });
            return true;
        }
        catch (error) {
            if (error.name = "TimeoutError") {
                return false;
            }
        }
        return false;
    }

    async applyCelFilter(data: { type: string, key?: string, value: string }) {
        await this.page.keyboard.press('f');
        let locatorToClick: Locator = data.type == "filter by label" ? this.filterByLabelInputField : data.type == "filter by field selector" ? this.fieldSelectorValueInputField : this.celExpressionInputField;
        await locatorToClick.fill(data.value);
        data.key ? await this.fieldSelectorKeyInputField.fill(data.key) : console.log('this is not the case of field selector');
        this.celFilterApplyButton.click();
        await this.celFilterApplyButton.waitFor({ state: "hidden", timeout: 10000 });
    }
    async verifyExistingNamespaceApplied(isDefaultClusterTerminal: boolean, namespaceToVerify: string) {

        isDefaultClusterTerminal == false ? await this.podsList.first().waitFor() : '';
        let locatorToSearchFor = isDefaultClusterTerminal ? this.defaultClusterTerminalNamespaceDropdown : this.resourceFilterButton;
        expect(await locatorToSearchFor.textContent()).toContain(namespaceToVerify);
    }

    async checPodTargetPlatform() {
        await this.page.locator('//*[text()="apiVersion"]').first().waitFor();
        if (!await this.page.locator('//*[text()="buildx.docker.com/platform"]').isVisible()) {
            return 'runnerManifest';
        }
        else if (await this.page.locator('//*[text()="buildx.docker.com/platform"]').isVisible()) {
            let text = await this.page.locator('//*[text()="buildx.docker.com/platform"]').locator('xpath=parent::div').textContent();
            if (text?.includes('linux/amd64')) {
                return 'linux/amd64'
            }
            else if (text?.includes('linux/arm64')) {
                return 'linux/arm64'
            }
            else if (text?.includes('linux/arm/v7')) {
                return 'linux/arm/v7'
            }
        }

    }

    async EnterFlagInConfigMapAndBouncePod(page: Page, key: string, value: string, microservice: string) {
        await page.goto(`${process.env.BASE_SERVER_URL}/resource-browser/1/all/configmap/k8sEmptyGroup?search=${microservice}`);
        await page.getByTestId(`created-resource-name`).click({ delay: 3000 });
        await page.locator(`//*[contains(text(),'Edit live manifest')]`).click({ delay: 3000 });
        var isKeyPresent: boolean = false;
        var isKeyValuePresent: boolean = false;
        const baseDeploymentTemplatePage = new BaseDeploymentTemplatePage(page);
        await baseDeploymentTemplatePage.scrollToParticularField(key);
        var str: string = "";
        if (await page.locator(`//*[contains(text(),'${key}')]`).isVisible({ timeout: 7000 })) {
            str = await page.locator(`//*[contains(text(),'${key}')]/ancestor::*[@class='view-line']`).textContent() as string;
            isKeyPresent = true;
        }

        if (str === `${key}: ${value}`) {
            isKeyValuePresent = true
        }

        if (isKeyPresent) {
            if (!isKeyValuePresent) {
                const baseDeploymentTemplatePage = new BaseDeploymentTemplatePage(page);
                await baseDeploymentTemplatePage.scrollToParticularField(key);
                await page.locator(`//*[contains(text(),${key})]`).click({ delay: 3000 });
                const str: string = await page.locator(`//*[contains(text(),${key})]/ancestor::*[@class='view-line']`).textContent() as string;
                await page.keyboard.press('End');
                for (let i = 0; i < str.length; i++) {
                    await page.keyboard.press('Backspace');
                }
                await page.keyboard.press('Enter');
                await page.keyboard.insertText(`${key}: ${value}`);
                const applyChanges = await page.locator(`//*[contains(@class,'resource-details-container')]//*[contains(@class,'dc__unset-button-styles')][1]`);
                await BaseTest.checkToast(page, applyChanges, "Manifest is updated");
                await page.waitForTimeout(2000);
            }
        } else {
            const pageUrl = await page.url();
            await page.goto(pageUrl);
            expect(await page.locator(`//*[@data-testid="app-manifest-container"]`).waitFor());
            await page.locator(`//*[contains(text(),'Edit live manifest')]`).click({ delay: 3000 });
            await page.locator(`//*[contains(text(),'data')]`).nth(1).click({ delay: 3000 });
            await page.keyboard.press('End');
            await page.keyboard.press('Enter');
            await page.keyboard.insertText(`${key}: ${value}`);
            const applyChanges = await page.locator(`//*[contains(@class,'resource-details-container')]//*[contains(@class,'dc__unset-button-styles')][1]`);
            await BaseTest.checkToast(page, applyChanges, "Manifest is updated");
            await page.waitForTimeout(2000);
        }
        await page.goto(`${process.env.BASE_SERVER_URL}/resource-browser/1/all/deployment/apps?search=${microservice}`);
        const deploymentName = await page.getByTestId(`created-resource-name`).textContent({ timeout: 5000 });
        await page.goto(`${process.env.BASE_SERVER_URL}/resource-browser/1/all/pod/k8sEmptyGroup?search=${deploymentName}`);
        await page.getByTestId(`popup-menu-button`).click({ delay: 3000 });
        await page.getByTestId(`delete-option-link`).waitFor({ timeout: 7000 });
        await page.getByTestId(`delete-option-link`).click({ delay: 3000 });

        await page.locator('//*[@placeholder="Type to confirm"]').waitFor({ timeout: 7000 });
        let text = await page.locator(`//*[@data-testid="label-"]`).textContent();
        console.log(text);
        console.log('mine value is ' + text!.split("'")[1].trim());
        await page.locator(`//*[@placeholder="Type to confirm"]`).fill(text!.split("'")[1].trim());

        await page.getByTestId(`dialog-delete`).click();
        try {
            await page.locator('//*[text()="Force Delete"]').waitFor({ timeout: 3000 });
            await page.locator(`//*[text()="Force Delete"]`).click();
        }
        catch (error) {
            console.log()
        }
        await page.waitForTimeout(1000 * 60);
        await page.reload();



    }

    async gotoSecuritySummaryCard(devtronApp: string) {
        await this.page.goto(`${process.env.BASE_SERVER_URL}/resource-browser/1/deployment/apps?searchKey=${devtronApp}-automation`);
        await this.resourcePopupMenuButton.waitFor();
        await this.resourcePopupMenuButton.click();
        await this.checkVulnerabilityButton.waitFor();
        await this.checkVulnerabilityButton.click();

    }

    async closeCreateKubernetesResourceModal() {
        await this.createResourceCloseButton.click();
    }

    async openManifestOfSpecificResource(resourceName: string) {
        const resourceLocators = this.page.locator('.generic-table__row');

        const count = await resourceLocators.count();
        for (let i = 0; i < count; i++) {
            const button = resourceLocators.nth(i);
            const name = await button.locator(this.resourceListDivSection).textContent();
            if (name === resourceName) {
                await button.scrollIntoViewIfNeeded();
                await button.getByText(name).click({ force: true });
                console.log(`Clicked row for resource: ${resourceName}`);
                return;
            }
        }

        throw new Error(`No row found with resource name: ${resourceName}`);
    }

    async clickOnEditLiveManifestButton() {
        await this.editLiveManifestButton.click();
    }

    async setNestedProperty(obj: any, path: string, value: any) {
        const keys = path.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
            current[keys[i]] = current[keys[i]] || {};
            current = current[keys[i]];
        }
        current[keys[keys.length - 1]] = value;
    }

    async fetchK8sResourceYamlViaHittingApi(updateK8sResourceYamlDTO: UpdateK8sResourceYamlDTO) {
        const {
            clusterId,
            kind,
            namespace,
            resourceName,
            request,
            group,
            version
        } = updateK8sResourceYamlDTO;

        if (!resourceName) {
            throw new Error("resourceName is missing or empty before making API call.");
        }

        const baseUrl = process.env.BASE_SERVER_URL!;
        const serverUrl = baseUrl.slice(0, baseUrl.indexOf('/dashboard'));
        console.log('serverUrl:', serverUrl);

        const apiUtilPage = new ApiUtils(request);
        const token = await apiUtilPage.login(process.env.PASSWORD!);

        const response = await request.post(`${serverUrl}/orchestrator/k8s/resource`, {
            headers: {
                'accept': '*/*',
                'content-type': 'text/plain;charset=UTF-8',
                'cookie': `argocd.token=${token}`,
            },
            data: {
                appId: '',
                clusterId: clusterId,
                k8sRequest: {
                    resourceIdentifier: {
                        groupVersionKind: {
                            Group: group,
                            Version: version,
                            Kind: kind,
                        },
                        name: resourceName,
                        namespace: namespace,
                    },
                },
            },
        });

        if (response.status() !== 200) {
            const errorText = await response.text();
            throw new Error(`API call failed with status ${response.status()}: ${errorText}`);
        }

        const json = await response.json();

        if (!json?.result?.manifestResponse?.manifest) {
            throw new Error('Invalid response: manifest not found.');
        }

        return json.result.manifestResponse.manifest;
    }




    async updateManifestProperty(updateK8sResourceYamlDTO: UpdateK8sResourceYamlDTO) {
        const manifest = await this.fetchK8sResourceYamlViaHittingApi(updateK8sResourceYamlDTO);
        const updated = JSON.parse(JSON.stringify(manifest));
        this.setNestedProperty(updated, updateK8sResourceYamlDTO.keyPath, updateK8sResourceYamlDTO.newValue);
        return YAML.stringify(updated, { indent: 2 });
    }

    async updateCodeMirrorEditor(yamlContent: string, selector = '.cm-content[contenteditable="true"]') {
        await this.page.waitForSelector(selector);
        await this.page.evaluate(([sel, content]) => {
            const editor = document.querySelector(sel);
            if (editor) {
                editor.textContent = content;
                ['input', 'change'].forEach(e => editor.dispatchEvent(new Event(e, { bubbles: true })));
            }
        }, [selector, yamlContent]);
        await this.page.locator(`//*[text()="Review and save changes"]`).click();
        await this.page.getByRole('button', { name: 'Apply changes' }).click();
    }

    async copyManifestatResourceBrowserPage() {
        await this.apiVersionFieldInManifest.waitFor();
        await this.apiVersionFieldInManifest.click();
        const clipboardContent = await this.page.locator('//*[@class="cm-content"]').innerText();
        console.log(clipboardContent);
        return clipboardContent;
    }

    async deletePropertyFromManifestJson(manifestJson: Json, fieldsPath: string[]) {
        for (let fieldPath of fieldsPath) {
            // Use the fieldPath variable to delete the property
            const keys = fieldPath.split('.'); // Handle nested properties
            let current = manifestJson;

            for (let i = 0; i < keys.length - 1; i++) {
                if (!current[keys[i]]) {
                    // If the path doesn't exist, exit the loop
                    break;
                }
                current = current[keys[i]];
            }

            // Delete the final key if it exists
            if (current && current[keys[keys.length - 1]] !== undefined) {
                delete current[keys[keys.length - 1]];
            }
        }
    }

    async updateK8sResourceYaml(updateK8sResourceYamlDTO: UpdateK8sResourceYamlDTO) {
        const yaml = await this.updateManifestProperty(updateK8sResourceYamlDTO);
        await this.updateCodeMirrorEditor(yaml);
    }

    getK8sResourcePayload(params: {
        request: APIRequestContext;
        keypath: string;
        newValue: any;
        resourceName: string;
        clusterId: number;
        namespace: string;
        kind: string;
        group: string;
        version: string;
    }): UpdateK8sResourceYamlDTO {
        return {
            request: params.request,
            keyPath: params.keypath,
            newValue: params.newValue,
            resourceName: params.resourceName,
            clusterId: params.clusterId,
            namespace: params.namespace,
            kind: params.kind,
            group: params.group,
            version: params.version
        };
    }
}