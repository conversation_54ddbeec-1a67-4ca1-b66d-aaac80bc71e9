import { Page, Locator, expect, Keyboard } from '@playwright/test';
import { BaseTest } from '../../utilities/BaseTest';
import { BaseDeploymentTemplatePage } from '../ApplicationManagement/Applications/BaseDeploymentTemplatePage';
import { BasePage } from '../BasePage';

const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class JobsPage extends BasePage {
    // Locator for the job tab button
    readonly jobTabButton: Locator;
    // Locator for the create job button
    readonly createJobButton: Locator;
    // Locator for the job pipeline button
    readonly jobPipeLineButton: Locator;
    // Locator for the workflow name input field
    readonly workflowNameInput: Locator;
    // Locator for the create workflow button
    readonly createWorkflowButton: Locator;
    // Locator for the empty workflow modal
    readonly emptyWorkflowModal: Locator;
    // Locator for the pipeline name input field
    readonly pipelineNameInput: Locator;
    // Locator for the branch name input field
    readonly branchNameInput: Locator;
    // Locator for the tasks to be executed tab
    readonly tasksToBeExecutedTab: Locator;
    // Locator for the add task button
    readonly addTaskButton: Locator;
    // Locator for the execute custom script button
    readonly executeCustomScriptButton: Locator;
    // Locator for the create pipeline button
    readonly createPipelineButton: Locator;
    // Locator for the config map tab
    readonly configMapTab: Locator;
    // Locator for the add config map button
    readonly addConfigMapButton: Locator;
    // Locator for the config map name input field
    readonly configMapName: Locator;
    // Locator for the config map and secret save button
    readonly configMapAndSecretSaveButton: Locator;
    // Locator for the file permission text box
    readonly filePermissonTextbox: Locator;
    // Locator for the task name input field
    readonly taskNameInput: Locator;
    readonly subPathInput: Locator;
    readonly jobPipelineEnvironmentDropdown: Locator;
    readonly jobPipelineEnvironmentDropdownOption: Locator;
    readonly executeInApplicationEnvironmentCheckbox: Locator;
    readonly closeIcon: Locator;
    readonly jobNode: Locator;
    readonly guiKeyInputField: Locator;
    readonly guiValueInputField: Locator;
    readonly gobackToworkflowPageIcon: Locator;
    readonly jobConfigLink: Locator;
    readonly baseDeploymentTemplate: BaseDeploymentTemplatePage;
    readonly monacoEditor: Locator;
    readonly addRowButtonInCMCS: Locator;
    //scope variable component related locators



    constructor(public page: Page) {
        // Initialize locators using Playwright API methods or custom locators
        super(page);
        // Assign locators to respective properties
        this.jobTabButton = page.getByTestId("click-on-job");
        this.createJobButton = page.getByTestId("create-job-button-in-dropdown");
        this.jobPipeLineButton = page.getByTestId("job-pipeline-button");
        this.workflowNameInput = page.getByTestId("workflow-name");
        this.createWorkflowButton = page.getByText("Create Workflow");
        this.emptyWorkflowModal = page.getByText("Add job pipeline to this workflow");
        this.pipelineNameInput = page.getByPlaceholder('e.g. my-first-pipeline');
        this.branchNameInput = page.getByPlaceholder("Eg. main");
        this.tasksToBeExecutedTab = page.getByTestId("pre-build-button");
        this.addTaskButton = page.getByTestId("pre-build-add-task-button");
        this.executeCustomScriptButton = page.getByTestId("execute-custom-script-button");
        this.createPipelineButton = page.getByTestId("build-pipeline-button");
        this.configMapTab = page.getByTestId('configmaps-link');
        this.addConfigMapButton = page.getByTestId("add-configmap-button");
        this.configMapName = page.getByPlaceholder("random-configmap");
        this.configMapAndSecretSaveButton = page.getByTestId('cm-secret-form-submit-btn');
        this.filePermissonTextbox = page.getByTestId("configmap-file-permission-textbox");
        this.taskNameInput = page.locator('//*[@name="task-name"]');
        this.subPathInput = page.locator('//*[@name="externalSubpathValues"]');
        this.jobPipelineEnvironmentDropdown = page.locator(".job-pipeline-environment-dropdown__control");
        this.jobPipelineEnvironmentDropdownOption = page.locator(".job-pipeline-environment-dropdown__option");
        this.executeInApplicationEnvironmentCheckbox = page.getByText("Execute tasks in application environment").locator("//input[@type='checkbox']");
        this.closeIcon = this.page.getByTestId('close-build-deploy-button');
        this.jobNode = this.page.locator("//*[contains(@data-testid,'workflow-editor-ci')]");
        this.guiKeyInputField = this.page.locator('//*[@placeholder="Enter Key"]');
        this.guiValueInputField = this.page.locator('//*[@placeholder="Enter Value"]');
        this.gobackToworkflowPageIcon = this.page.locator('//*[contains(@href,"edit/workflow")]').first();
        this.jobConfigLink = this.page.getByTestId('job-config-link');
        this.baseDeploymentTemplate = new BaseDeploymentTemplatePage(page);
        this.monacoEditor = page.locator('//*[@class="view-lines monaco-mouse-cursor-text"]');
        this.addRowButtonInCMCS = this.page.getByTestId(`data-table-add-row-button`);
    }

    /**
     * use this method to create workflow in jobs 
     * @param workflowName 
     */
    async createWorkflow(workflowName: string) {
        // Click on the job pipeline button
        await expect(async () => {
            await this.jobPipeLineButton.click({ timeout: 15000 });
            await expect(this.workflowNameInput).toBeVisible({ timeout: 25000 });
        }).toPass({ timeout: 3 * 1000 * 60 });
        // Fill the workflow name input field
        await expect(async () => {
            await this.workflowNameInput.fill(workflowName);
            // Check for toast notification after creating an empty workflow
            await BaseTest.checkToast(this.page, this.createWorkflowButton, "Empty Workflow Created successfully")
            // Wait for the page to load
            await this.page.waitForLoadState("domcontentloaded");
        }).toPass({ timeout: 3 * 1000 * 60 });
    }


    /**
     * use this method to create pipeline in the workflow in jobs
     * @param pipelineName 
     * @param branchName 
     */
    async createPipeline(pipelineName: string, branchName: string) {
        // Click on the empty workflow modal
        await this.emptyWorkflowModal.click();
        await this.createPipelineButton.waitFor({ timeout: 40000 });
        // Fill the pipeline name input field
        await this.fillJobPipelineDetails(pipelineName, branchName);
        await this.createPipelineButton.click();
    }
    async fillJobPipelineDetails(pipelineName: string, branchName: string) {
        await this.pipelineNameInput.fill(pipelineName);
        // Fill the branch name input field
        await this.branchNameInput.fill(branchName);
    }
    async clickOnJobNode() {
        await this.jobNode.click();
    }


    /**
     * use this method to execute custom script , note you first have to call method of add pre-post task to open this page 
     * @param taskName 
     * @param script 
     * @param isLastTask 
     * @param options 
     */
    async executeCustomScript(taskName: string, script: string[], isLastTask: boolean = true, options?: { inclusterEnv?: string, isJob: boolean }) {
        // Fill the task name input field
        await this.createPipelineButton.waitFor();
        await this.taskNameInput.fill(taskName);
        // Click on the monaco editor
        //revert new code editor
        await this.codeMirrorEditorTextArea.click();

        // Insert script lines into the editor
        for (let i = 0; i < script.length; i++) {
            await this.page.keyboard.insertText(script[i]);
            await this.page.keyboard.press("Enter");
        }
        //click on execute in application environment
        if (options && options.isJob && options.inclusterEnv) {
            await expect(this.jobPipelineEnvironmentDropdown).toBeVisible();
            await this.jobPipelineEnvironmentDropdown.click();
            await expect(this.jobPipelineEnvironmentDropdownOption.getByText(options.inclusterEnv)).toBeVisible();
            await this.jobPipelineEnvironmentDropdownOption.getByText(options.inclusterEnv).click();
        } else if (options && !options.isJob && options.inclusterEnv) {
            await this.executeInApplicationEnvironmentCheckbox.click();
        }

        // Check for toast notification after creating a 
        if (isLastTask) {
            await this.createPipelineButton.click();
        }
        try {
            await this.closeIcon.click({ timeout: 5000 });
        }
        catch (error) {
            if (error.name === 'TimeoutError') {
                console.log('timeout');
            }
        }
    }

    async imageScanPlugin() {
        await this.createPipelineButton.waitFor({ timeout: 40000 });
        await this.page.locator(`//*[text()="SCAN_VIA_V2"]/parent::div/following-sibling::div[2]`).click();
        await this.page.keyboard.type('TRUE');
        await this.page.keyboard.press('Enter');
        await this.page.locator(`//*[text()="SCAN_RETRY_COUNT"]/parent::div/following-sibling::div[2]`).click();
        await this.page.keyboard.type('2');
        await this.page.locator(`//*[text()="SCAN_RETRY_INTERVAL"]/parent::div/following-sibling::div[2]`).click();
        await this.page.keyboard.press('3');
        await BaseTest.checkToast(this.page, this.createPipelineButton, "Pipeline Updated");
    }



    /**
     * use this method to add cm/secret any where , but make sure to reach this page we have to call another method operBaseOrEnvOverrirde resources
     * @param resourceData 
     * @param isDevtronAPP 
     */
    async addConfigMapOrSecret(resourceData: any, isDevtronAPP: boolean = true, isBuildInfra: boolean = false) {
        if (!isBuildInfra) {
            await this.operBaseOrEnvOverRideResources(resourceData.stage, isDevtronAPP);
            // Click on the appropriate resource link
            await this.page.locator(`//*[contains(text(),"${resourceData.resource}")]/ancestor::div[contains(@class,"flexbox dc__align-items-center")]//*[@class="flex dc__align-self-start"]`).click();
        }
        // // Click on the add button for the specified resource type
        // Select the resource type from the dropdown
        await this.selectResourceTypeFromDropdown(resourceData.resource, resourceData.resourceType);
        // Fill the resource name input field
        // await this.page.getByPlaceholder(`${resourceData.resource}-name`.toLowerCase()).fill(resourceData.resourceName);
        await this.page.locator(`//*[@name="name"]`).fill(resourceData.resourceName);
        // Add environment variables or data volumes based on the specified type
        if (resourceData.EnvVariableOrDataVolume === "EnvironmentVariable") {
            await this.addEnvironmentVariables(resourceData.resource);
        } else if (resourceData.EnvVariableOrDataVolume === "DataVolume") {
            if (resourceData.resourceType.includes("External") || resourceData.resourceType.includes("Existing")) {
                await this.addDataVolume(resourceData.resource, resourceData.mountPath, resourceData.setSubPath, resourceData.setFilePermission, resourceData.filePermission, resourceData.subPath);
            }
            else {
                await this.addDataVolume(resourceData.resource, resourceData.mountPath, resourceData.setSubPath, resourceData.setFilePermission, resourceData.filePermission);
            }
        }
        // Enter data for the config map or secret
        if (!resourceData.resourceType.includes("External") && !resourceData.resourceType.includes("Existing")) {

            await this.enterDataForConfigMapOrSecret(resourceData.mode, resourceData.Data);
        }
        if (!isBuildInfra) {
            await BaseTest.checkToast(this.page, this.configMapAndSecretSaveButton, "Updated");
        }
        // Check for toast notification after saving the change
    }



    /**
     * use this method for selecting tyor of cm /sectet
     * @param configMapOrSecret 
     * @param resourceType 
     */
    async selectResourceTypeFromDropdown(configMapOrSecret: string, resourceType: string) {
        // Click on the dropdown control based on the resource type (ConfigMap or Secret)
        await expect(async () => {
            await this.page.locator(`//*[contains(@class,'cm-cs-data-type__control')]`).first().click();
            await this.page.locator(`//*[@role="listbox"]//*[text()="${resourceType}"]`).click();
        }).toPass({ timeout: 4 * 1000 * 60 });
    }



    /**
     * use this method to open resources either of base or env in both jobs and devtron apps
     * @param stage 
     * @param isDevtronAPP 
     */
    async operBaseOrEnvOverRideResources(stage: string | "base-configurations", isDevtronAPP: boolean = true) {
        try {
            var stageToSearch = stage == "base-configurations" ? 'Base configurations' : stage;
            await this.page.locator('//*[@class="env-config-selector__input"]').fill(stageToSearch, { timeout: 6000 });
            await this.page.keyboard.press('Enter');
        }
        catch (error) {
            if(isDevtronAPP){
                await this.page.locator(`//*[@data-testid="${stage}-link"]`).click({ timeout: 10000 }) 
            }
            else{
                stage!='base-configurations' ? await this.page.locator(`//*[@data-testid="env-deployment-template"]//*[text()="${stage}"]`).click({ timeout: 10000 }) :
                 await this.page.locator('//*[@data-testid="configmaps-&-secrets-link"]').click({ timeout: 10000 });
            }
        }
    }


    /**
     * use this method to go back to configuration page , after opening the base or env resources
     */
    async goBackToWorkflowPage() {
        await this.gobackToworkflowPageIcon.click();
    }


    /**
     * this method is used to set option of env variable in cm/secret 
     * @param configMapOrSecret 
     */
    async addEnvironmentVariables(configMapOrSecret: string) {
        // Click on the environment variable radio button for the specified resource type
        await this.page.locator(`//*[text()="Environment Variable"]`).click();
    }



    /**
     * similarly like setting env variable , use this method to set up data volume s
     * @param configMapOrSecret 
     * @param volumeMountPath 
     * @param setSubPath 
     * @param setFilePermission 
     * @param filePermission 
     * @param subPath 
     */
    async addDataVolume(configMapOrSecret: string, volumeMountPath: string, setSubPath: boolean, setFilePermission: boolean, filePermission: string, subPath: string = '') {
        // Click on the data volume radio button for the specified resource type
        // await this.page.getByTestId(`${configMapOrSecret.toLocaleLowerCase()}-data-volume-radio-button-span`).click();
        await this.page.locator(`//*[text()="Data Volume"]`).click();
        // Fill the volume path textbox with the specified volume mount path
        //await this.page.getByTestId(`${configMapOrSecret.toLocaleLowerCase()}-volume-path-textbox`).fill(volumeMountPath);
        await this.page.locator(`//*[@name="volumeMountPath"]`).fill(volumeMountPath);
        // Check if subpath needs to be set and set it if required
        if (setSubPath) {
            //await this.page.locator(`//*[@data-testid="${configMapOrSecret.toLocaleLowerCase()}-sub-path-checkbox"]/parent::span/preceding-sibling::input`).check({ force: true });
            await this.page.locator(`//*[@name="isSubPathChecked"]/following-sibling::span[@class="form__checkbox-container"]`).click();
            if (await this.subPathInput.isVisible({ timeout: 3000 })) {
                await this.subPathInput.fill(subPath);
            }
        }
        // Check if file permission needs to be set and set it if required
        if (setFilePermission) {
            // await this.page.getByTestId(`configmap-file-permission-checkbox`).click();
            await this.page.locator(`//*[@name="isFilePermissionChecked"]/following-sibling::span[@class="form__checkbox-container"]`).click();
            await this.filePermissonTextbox.fill(filePermission);
        }
    }




    /**
     * use this method to enter data in cm/secret
     * @param configMapOrSecret 
     * @param mode gui or yaml
     * @param script 
     */
    async enterDataForConfigMapOrSecret(mode: "gui" | "yaml", script: string[]) {
        // Check if the Monaco editor is visible and the mode is YAML, or if the mode is GUI and the Monaco editor is not visible, then switch the mode
        await this.clickOnGuiOrYamlForCMCS(mode);
        // If the mode is GUI, enter data using GUI elements
        if (mode === "gui") {
            // Iterate over each key-value pair in the script
            for (let i = 0; i < script.length; i++) {
                // Split the key-value pair
                await this.addRowButtonInCMCS.click();
                var key = (await this.splitKeyValuePair(script[i])).key;
                var value = (await this.splitKeyValuePair(script[i])).value;
                // Fill the key and value fields with the corresponding data
                await this.guiKeyInputField.nth(0).fill(key);
                await this.guiValueInputField.nth(0).fill(value);
            }
        }
        // If the mode is YAML, enter data using the Monaco editor
        else {
            // Click on the Monaco editor to focus on it
            // rever new editor 
            await this.codeMirrorEditorTextArea.click();
            // Select all text and delete it
            await this.page.keyboard.press(process.env.OS === "Mac" ? "Meta+A" : "Control+A");

            for (let i = 0; i < script.length; i++) {
                await this.page.keyboard.insertText(script[i]);
                await this.page.keyboard.press("Enter");
            }
        }
    }
    async clickOnGuiOrYamlForCMCS(mode: "yaml" | "gui") {
        await this.page.locator('//*[@class="radio__item-label"]').first().waitFor({ timeout: 9000 });
        // revert new code editor
        if ((await this.codeMirrorEditorTextArea.isVisible() === false && mode === "yaml") || (mode === "gui" && await this.codeMirrorEditorTextArea.isVisible())) {
            // Click on the mode radio button based on the provided mode
            await this.page.locator(`//*[@class="radio__item-label" and text()="${mode.toUpperCase()}"]`).click();
        }
        if ((await this.codeMirrorEditorTextArea.isVisible() === false && mode === "yaml") || (mode === "gui" && await this.codeMirrorEditorTextArea.isVisible())) {
            // Click on the mode radio button based on the provided mode
            await this.page.locator(`//*[@class="radio__item-label" and text()="${mode.toUpperCase()}"]`).click();
        }
    }

    async splitKeyValuePair(input: string): Promise<{ key: string; value: string; }> {
        // Split the input string into key and value
        const [key, value] = input.split(':').map(part => part.trim());
        // Return the key-value pair
        return { key, value };
    }
    async checkToastMessageOnUpateNode(toastMessageToVerify: string) {
        await this.jobConfigLink.click();
        await this.jobNode.click();
        await BaseTest.checkToast(this.page, this.createPipelineButton, toastMessageToVerify);
    }

    /**
     * for cm give  ConfigMaps and for secret give Secrets
     */
    async verifyCmSecrets(
        verificationDetails: {
            keyName: string[];
            valueName: string[];
        }) {
        await this.clickOnGuiOrYamlForCMCS('gui');
        // Loop through the key-value pairs to verify them
        for (let index = 0; index < verificationDetails.keyName.length; index++) {
            expect(await this.guiKeyInputField.nth(index).textContent()).toEqual(verificationDetails.keyName[index]);
            expect(await this.guiValueInputField.nth(index).textContent()).toEqual(verificationDetails.valueName[index]);
        }
    }






    async checkWhetherWorkflowsShouldBeVisibleOrNot(isVisible: boolean) {
        await this.jobConfigLink.click();
        if (isVisible) {
            await expect(this.page.locator(`//*[@data-testid="workflow-editor-page"]`)).toBeVisible({ timeout: 12000 });
        }
        else {
            await expect(this.jobPipeLineButton).toBeVisible({ timeout: 12000 });
        }
    }
    async editCmCs(resourceType: string, resourceName: string, stage: string, isProtectConfigEnabled: boolean, key?: string, value?: string, isBuildInfra: boolean = false, editThroughExpressEdit: boolean = false) {
        if (!this.page.url().includes(resourceName))
            await this.page.locator(`//*[contains(text(),"${resourceType}")]/parent::button/parent::div/following-sibling::div//*[text()="${resourceName}"]`).click();
        await this.clickOnGuiOrYamlForCMCS("gui");
        if (key) {
            let presentKeyValues = await this.guiKeyInputField.all();
            let keyFound: boolean = false;
            for (let i = 0; i < presentKeyValues.length; i++) {
                if (await this.guiKeyInputField.nth(i).textContent() == key) {
                    keyFound = true;
                    await this.guiValueInputField.nth(i).fill(value!);
                    break;
                }
            }
            if (!keyFound) {
                await this.enterDataForConfigMapOrSecret("gui", [`${key}:${value}`])
            }
        }
        if (!isBuildInfra) {
            let locatorForsavingTheChanges = editThroughExpressEdit ? this.page.getByTestId(`cm-secret-express-edit-form-submit-btn`) : this.configMapAndSecretSaveButton;
            if (!isProtectConfigEnabled) {
                let toastToVerify: string = stage.includes("base") ? 'Updated' : "Overridden"
                await locatorForsavingTheChanges.click();
            }
            else {
                await locatorForsavingTheChanges.click();
            }
        }
    }
    async clickOnAnyCmCSAndReturnExistence(resourceType: string, resourceName: string) {
        try {
            await this.page.locator(`//*[contains(text(),"${resourceType}")]/parent::button/parent::div/following-sibling::div//*[text()="${resourceName}"]`).click({ timeout: 4000 });
            return true;
        }
        catch (error) {
            return false;
        }

    }

}
