import { expect, Locator, Page } from '@playwright/test'
import { BaseTest } from '../../utilities/BaseTest';
import { ApiUtils } from '../../utilities/ApiUtils';
import { SecurityCenterUrls } from '../../enums/UrlNavigations/SecurityCenter';
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class SecurityGlobalPage {

    readonly mainHeader: Locator;
    readonly securityScan: Locator;
    readonly securityPolicies: Locator;
    readonly searchBar: Locator;
    readonly globalSecurityPoliciesTitle: Locator;


    constructor(private page: Page) {
        this.mainHeader = page.getByTestId('main-header');
        this.securityScan = page.getByText('Security Scans');
        this.securityPolicies = page.getByText('Security Policies');
        this.searchBar = page.getByTestId('search-bar');
        this.globalSecurityPoliciesTitle = page.getByTestId('global-security-policy');
    }

    async gotoSecurityGlobalPage() {
        await this.page.goto(`${process.env.BASE_SERVER_URL}${SecurityCenterUrls.securityGlobalPage}`);
    }

    async gotoGlobalSecurityPoliciesPage() {
        await this.page.goto(`${process.env.BASE_SERVER_URL}${SecurityCenterUrls.globalSecurityPoliciesPage}`);

    }

    async verifyGlobalSecurityPoliciesPage() {
        await this.globalSecurityPoliciesTitle.waitFor({ timeout: 5000 });
    }

    async verifyGlobalSecurityScanPage() {
        await this.mainHeader.waitFor();
        await expect(this.mainHeader).toHaveText('Security');
        await expect(this.securityScan.first()).toBeVisible();
        await expect(this.securityPolicies).toBeVisible();
    }

    async searchApplication(applicationName: string) {
        await this.searchBar.waitFor();
        await this.searchBar.fill(applicationName);
        await this.page.keyboard.press('Enter');

    }
    async verifySecurityScanResultOnGlobalSecurityPageForAppAndEnv(applicationName: string, environments: string[]) {
        for (let env in environments) {
            await expect(this.page.locator(`//*[@data-testid="scanned-app-list-${applicationName}"]/following-sibling::*[contains(text(),'${environments[env]}')]`)).toBeVisible();
        }
    }
    async clickOnApplicaitonWithEnvironment(applicationName: string, environment: string) {
        await this.page.locator(`//*[@data-testid="scanned-app-list-${applicationName}"]/following-sibling::*[contains(text(),'${environment}')]`).waitFor();
        await this.page.locator(`//*[@data-testid="scanned-app-list-${applicationName}"]/following-sibling::*[contains(text(),'${environment}')]`).click();

    }

    async goAndVerifyEnvForAppAtGlobalSecurityPage(applicationName: string, environments: string[]) {
        await this.gotoSecurityGlobalPage();
        await this.verifyGlobalSecurityScanPage();
        await this.searchApplication(applicationName);
        await this.verifySecurityScanResultOnGlobalSecurityPageForAppAndEnv(applicationName, environments);

    }


    async verfiySecurityPoliciesAllCombination(apiUtils: ApiUtils, url: string) {

        const urlObj = new URL(url);
        const pathSegments = urlObj.pathname.split('/');
        const queryParams = new URLSearchParams(urlObj.search);

        const appId = Number(pathSegments[3]);
        const pipelineId = Number(queryParams.get('cd-node'));

        //const apiUtils = new ApiUtils(request);
        let token: string;
        if (process.env.isStaging != 'true') {
            token = await apiUtils.login(process.env.PASSWORD!);
        }
        else {
            token = process.env.stagingSuperAdminToken!;
        }
        const responseBody = await apiUtils.fetchArtifactId(token, pipelineId);
        const artifactId = responseBody.result.latest_wf_artifact_id;
        console.log(appId);
        console.log(pipelineId);
        console.log(artifactId);

        //Global Filter
        const str: string[] = ["block", "blockiffixed", "allow"];

        for (let id = 1; id < 5; id++) {
            for (let j = 0; j < str.length; j++) {
                let action = str[j];
                await apiUtils.updateSecurityPolicy(token, id, action);
                const triggerResponse = await apiUtils.triggerCdPipeline(token, pipelineId, appId, artifactId);
                //console.log(triggerResponse);
                await apiUtils.updateSecurityPolicy(token, id, "allow");
                if (action[j] == "block") {
                    // expect(triggerResponse.status).toBe('Conflict');
                    expect(triggerResponse.code).not.toBe(200);
                    // expect(triggerResponse.errors?.[0]?.internalMessage).toBe("deployment trigger is blocked, found vulnerability on image");
                }
            }
        }

        //Cluster 
        const severity: string[] = ["critical", "medium", "low", "high"];
        const action: string[] = ["block", "blockiffixed", "allow"];


        for (let i = 0; i < severity.length; i++) {
            for (let j = 0; j < action.length; j++) {

                const saveResponse = await apiUtils.saveClusterSecurityPolicy(token, 1, action[j], severity[i]);
                let id = saveResponse.result.id;
                const triggerResponse = await apiUtils.triggerCdPipeline(token, pipelineId, appId, artifactId);
                //console.log(triggerResponse);
                if (action[j] == "block") {
                    console.log(severity[i]);
                    console.log(action[j]);
                    // expect(triggerResponse.status).toBe('Conflict');
                    expect(triggerResponse.code).not.toBe(200);
                    //expect(triggerResponse.errors?.[0]?.internalMessage).toBe("deployment trigger is blocked, found vulnerability on image");
                }
                await apiUtils.updateSecurityPolicy(token, id, "inherit");
            }
        }
        let envId = await apiUtils.fetchEnvironmentId(token, "automation");
        //Environment

        for (let i = 0; i < severity.length; i++) {
            for (let j = 0; j < action.length; j++) {

                const saveResponse = await apiUtils.saveEnvironmentSecurityPolicy(token, envId, action[j], severity[i]);
                let id = saveResponse.result.id;
                const triggerResponse = await apiUtils.triggerCdPipeline(token, pipelineId, appId, artifactId);
                //console.log(triggerResponse);
                if (action[j] == "block") {
                    console.log(severity[i]);
                    console.log(action[j]);
                    // expect(triggerResponse.status).toBe('Conflict');
                    expect(triggerResponse.code).not.toBe(200);
                    //expect(triggerResponse.errors?.[0]?.internalMessage).toBe("deployment trigger is blocked, found vulnerability on image");
                }
                await apiUtils.updateSecurityPolicy(token, id, "inherit");
            }
        }

        //Applications

        for (let i = 0; i < severity.length; i++) {
            for (let j = 0; j < action.length; j++) {

                const saveResponse = await apiUtils.saveApplicationSecurityPolicy(token, appId, envId, action[j], severity[i]);
                let id = saveResponse.result.id;
                const triggerResponse = await apiUtils.triggerCdPipeline(token, pipelineId, appId, artifactId);
                //console.log(triggerResponse);
                if (action[j] == "block") {
                    console.log(severity[i]);
                    console.log(action[j]);
                    // expect(triggerResponse.status).toBe('Conflict');
                    expect(triggerResponse.code).not.toBe(200);
                    // expect(triggerResponse.errors?.[0]?.internalMessage).toBe("deployment trigger is blocked, found vulnerability on image");
                }
                await apiUtils.updateSecurityPolicy(token, id, "inherit");
            }
        }
    }

    async blockCDTriggerViaSecurityPolicies(apiUtils: ApiUtils, url: string) {

        let token: string;
        if (process.env.isStaging != 'true') {
            token = await apiUtils.login(process.env.PASSWORD!);
        }
        else {
            token = process.env.stagingSuperAdminToken!;
        }

        await apiUtils.updateSecurityPolicy(token, 1, "block");


    }


    async allowCDTriggerViaSecurityPolicies(apiUtils: ApiUtils, url: string) {
        //const apiUtils = new ApiUtils(request);
        let token: string;
        if (process.env.isStaging != 'true') {
            token = await apiUtils.login(process.env.PASSWORD!);
        }
        else {
            token = process.env.stagingSuperAdminToken!;
        }
        await apiUtils.updateSecurityPolicy(token, 1, "allow");


    }

}