import { expect, Locator, <PERSON> } from "@playwright/test";


export class RolloutHistoryPage {
    readonly rolloutHistoryTab: Locator;
    readonly appFieldDropdown: Locator;
    readonly envFieldDropdown: Locator;
    readonly fromThisReleaseOnlyInput: Locator;
    readonly fromThisReleaseOnlyClickableButton: Locator;
    readonly compareWithDropdownForPrevDeployments: Locator;
    readonly deploymentCardInsideCompareWithDropdown: Locator;
    readonly configurationButtonInHistory: Locator;
    constructor(public page: Page) {
        this.rolloutHistoryTab = this.page.getByTestId(`Release-Rollout-History-click`);
        this.appFieldDropdown = this.page.locator(`//*[contains(@class,'release-configuration-app-selector__control')]`);
        this.envFieldDropdown = this.page.locator(`//*[contains(@class,'release-configuration-env-selector__control')]`);
        this.fromThisReleaseOnlyInput = this.page.locator(`#current-release-deployments`);
        this.fromThisReleaseOnlyClickableButton = this.page.locator(`//*[@for="current-release-deployments"]`);
        this.compareWithDropdownForPrevDeployments = this.page.locator(`//*[contains(@class,'deployment-config-diff-deployment-selector__control')]`);
        this.deploymentCardInsideCompareWithDropdown = this.page.locator(`//*[contains(@class,'deployment-config-diff-deployment-selector__option')]`);
        this.configurationButtonInHistory = this.page.getByTestId(`deployment-history-configuration-link`);
    }
    async setAppAndEnv(appName: string, envName: string, isRollOutHistory: boolean = true) {
        isRollOutHistory ? await this.rolloutHistoryTab.click() : console.log();
        await this.appFieldDropdown.click();
        await this.page.keyboard.type(appName);
        await this.page.keyboard.press('Enter');
        await this.envFieldDropdown.click();
        await this.page.keyboard.type(envName);
        await this.page.keyboard.press('Enter');
    }

    /**
     * this function is used to verify the number of deployments 
     * @param isFromThisReleaseOnly 
     * @param verificationDetails 
     */
    async verifyDeploymenyHistory(isFromThisReleaseOnly: boolean, numberOfSucceededDeployments: number) {
        await this.selectAndDisselectFromThisReleaseCheckbox(isFromThisReleaseOnly);
        await expect(this.page.locator(`//*[@class="w-100 deployment-history-card"]`)).toHaveCount(numberOfSucceededDeployments);
    }

    async clickOnConfigurationSectionAndSelectPreviousDeployment(data: { verificationInRelease?: { fromThisReleaseOnly: boolean }, configType: string, deploymentNumberToCompareWith: number, deploymentNumberToCompare: number }) {
        await this.configurationButtonInHistory.click();
        if (data.verificationInRelease) {
            await this.selectAndDisselectFromThisReleaseCheckbox(data.verificationInRelease.fromThisReleaseOnly);
        }
        else {
            await this.selectAndDisselectFromThisReleaseCheckbox(false);
            await this.configurationButtonInHistory.click();
        }
        await this.page.getByTestId(`deployment-history-${data.deploymentNumberToCompare}`).click();
        await this.page.locator(`//*[text()="${data.configType}"]`).click();
        await this.compareWithDropdownForPrevDeployments.click();
        await this.deploymentCardInsideCompareWithDropdown.nth(data.deploymentNumberToCompareWith).click();
    }

    async selectAndDisselectFromThisReleaseCheckbox(fromThisReleaseOnly: boolean) {
        if (await this.fromThisReleaseOnlyInput.isChecked() != fromThisReleaseOnly) {
            await this.fromThisReleaseOnlyClickableButton.click();
        }
    }

}