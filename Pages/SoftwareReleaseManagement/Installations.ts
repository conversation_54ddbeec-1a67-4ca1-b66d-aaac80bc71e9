import { expect, Locator, <PERSON> } from "@playwright/test";
import { ReleaseHub } from "./ReleaseHub";
import { Tenants } from "./Tenants";
import { INSPECT_MAX_BYTES } from "buffer";

export class Installation extends Tenants {

    readonly addInstallationButton: Locator;
    readonly installationNameInputField: Locator;
    readonly installationIdField: Locator;
    readonly subscribeToReleaseChannelCheckBox: Locator;
    readonly releaseChannelInputField: Locator;
    readonly listDropdownLocator: Locator;
    readonly mapEnvButton: Locator
    readonly clusterListSection: Locator;
    readonly checkboxOfEnvList: Locator;
    readonly envListRowLocator: Locator;
    readonly clearAndContinueButton: Locator;
    readonly uploadManifestToOciButton: Locator;
    readonly downloadManifestToLocalButton: Locator;
    readonly virtualClusterManifestSaveButton: Locator;
    readonly registryNameInputField: Locator;
    readonly repositoryNameInputField: Locator;
    readonly getHostNameAndToken: Locator;
    readonly mapEnvPopupCard: Locator;
    constructor(page: Page) {
        super(page);
        this.page = page
        this.addInstallationButton = this.page.locator(`//*[text()="Add Installation" or text()="Add"]`);
        this.installationNameInputField = this.page.locator(`//*[@name="installation-name"]`);
        this.installationIdField = this.page.locator(`//*[@name="installation-id"]`);
        this.subscribeToReleaseChannelCheckBox = this.page.locator(`//*[@id="subscribe-to-release-channel"]/parent::label`);
        this.releaseChannelInputField = this.page.locator(`//*[@name="subscribe-release-channel"]`);
        this.listDropdownLocator = this.page.locator(`//*[@name="subscribe-release-channel"]`);
        this.mapEnvButton = this.page.getByTestId(`map-env-button`);
        this.clusterListSection = this.page.locator(`//*[text()="Clusters"]/parent::div/following-sibling::div`);
        this.envListRowLocator = this.page.locator(`//*[contains(@class,'tenants__installation') and not(contains(@class,'--header'))]`)
        this.checkboxOfEnvList = this.envListRowLocator.locator(`//span[@class="form__checkbox-container"]`);
        this.clearAndContinueButton = this.page.getByTestId(`clear-selection`);
        this.uploadManifestToOciButton = this.page.locator(`//*[@value="uploadManifestToOCI"]/parent::label`);
        this.downloadManifestToLocalButton = this.page.locator(`//*[@value="downloadManifestToLocal"]/parent::label`);
        this.virtualClusterManifestSaveButton = this.page.getByTestId("save-manifest");
        this.registryNameInputField = this.page.locator(`//*[@name="docker-registry"]`);
        this.repositoryNameInputField = this.page.locator(`//*[@name="repository"]`);
        this.getHostNameAndToken = this.page.getByTestId("get-hostname-and-api-token");
        this.mapEnvPopupCard = this.page.locator(`//*[contains(@class,'dc__right')]`);



    }
    async addInstallation(data: { installationName: string, installationId: string, releaseChannel?: string }[]) {
        for (let key of data) {
            await this.addInstallationButton.click();
            await this.installationNameInputField.fill(key.installationName);
            await this.installationIdField.fill(key.installationId);
            if (key.releaseChannel) {
                await this.subscribeTenantToReleaseChannel(key.releaseChannel);
            }
            await this.saveButton.click();
        }
    }
    async subscribeTenantToReleaseChannel(channelName: string) {
        await this.subscribeToReleaseChannelCheckBox.click();
        await this.releaseChannelInputField.fill(channelName);
        await this.listDropdownLocator.waitFor({ timeout: 2000 });
        await this.page.keyboard.press("Enter");
    }
    async mapEnvironmentsToInstallations(installationName: string, data: { clusterName: string, envName: string, isVirtualEnv: boolean, isBlocked?: boolean }[], ociRegistryData?: { registryName: string, repoName: string }) {
        await this.page.locator(`//*[contains(@href,'/installations/${installationName}')]`).click();
        await this.mapEnvButton.click();
        let isvirtualEnvSelected: boolean = false;
        let count: number = 0;
        for (let key of data) {
            await this.clusterListSection.locator(`//*[text()="${key.clusterName}"]`).click();
            await this.mapEnvPopupCard.locator(this.releaseNameSearchBar).fill(key.envName);
            await this.page.keyboard.press("Enter");
            if (key.isBlocked) {
                await expect(this.checkboxOfEnvList).toBeDisabled();
            }
            else {
                await this.checkboxOfEnvList.click();
                if (count == 0) {
                    isvirtualEnvSelected = key.isVirtualEnv;
                }
                else {
                    if (isvirtualEnvSelected != key.isVirtualEnv) {
                        await this.dialogDeleteConfirmationButton.click();
                        isvirtualEnvSelected = key.isVirtualEnv;
                    }
                }
                count++;
            }
        }
        await this.saveButton.click();
        if (isvirtualEnvSelected) {
            if (ociRegistryData) {
                await this.uploadManifestToOciButton.click();
                await this.registryNameInputField.fill(ociRegistryData.registryName);
                await this.page.keyboard.press('Enter');
                await this.repositoryNameInputField.fill(ociRegistryData.repoName);
            }
            await this.virtualClusterManifestSaveButton.click();
        }
    }

    async fetchHostNameAndToken(installationName: string) {
        await this.clickOnAnyInstallation(installationName);
        await this.getHostNameAndToken.isVisible() ? await this.getHostNameAndToken.click() : console.log('already clicked');
        await this.page.locator(`//*[text()="Host name"]/following-sibling::span`).waitFor();
        let hostUrl: string = await this.page.locator(`//*[text()="Host name"]/following-sibling::span`).textContent() as string;
        let tokenValue: string = await this.page.locator(`//*[contains(text(),'Auth token')]/parent::span/following-sibling::span`).textContent() as string;
        return { hostUrl, tokenValue };
    }
    async clickOnAnyInstallation(installationName: string) {
        await this.page.locator(`//*[contains(@href,'/installations/${installationName}')]`).click();
    }
}