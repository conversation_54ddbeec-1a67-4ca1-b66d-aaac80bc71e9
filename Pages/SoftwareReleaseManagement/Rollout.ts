import { expect, Locator, Page } from "@playwright/test";
import { exec } from "child_process";
import { allowedNodeEnvironmentFlags } from "process";
import { BaseTest } from "../../utilities/BaseTest";
import { BasePage } from "../BasePage";
import { AppDetailsCardResourceCountVerificationDTO } from "../../DTOs/SoftwareReleaseManagement/AppDetailsCardResourceCountVerificationDTO";
import { AppDetailsCardStatusVerificationDTO } from "../../DTOs/SoftwareReleaseManagement/AppDetailsCardStatusVerificationDTO";

export class Rollout extends BasePage {
    readonly triggerReleaseTab: Locator
    readonly installationViewToglleButton: Locator;
    readonly applyFiltersButton: Locator;
    readonly checkBoxLocator: Locator;
    readonly installationViewToggleButtonInputField: Locator;
    readonly rolloutStatusTab: Locator;
    readonly rolloutReleaseTab: Locator;
    readonly deployButton: Locator;
    readonly feasibilityPageDiv: Locator;
    readonly feasibilityPageDeployButton: Locator;
    readonly feasibilityModalButtonGroup: Locator;
    readonly goToRolloutStatusButton: Locator;
    readonly clearFiltersButton: Locator;
    readonly reloadButton: Locator;
    readonly autopollingButton: Locator;
    readonly autopollingButtonInputAttribute: Locator;
    readonly deploymentStatusButtonOnApplicationRow: Locator;
    readonly appDetailsModalCloseButton: Locator;
    constructor(public page: Page) {
        super(page);
        this.installationViewToglleButton = this.page.getByTestId("installation-view-toggle").first();
        this.triggerReleaseTab = this.page.locator(`//*[text()="Trigger Release"]`);
        this.applyFiltersButton = this.page.locator(`//*[text()="Apply Filters"]`);
        this.checkBoxLocator = this.page.getByTestId(`undefined-chk-span`);
        this.installationViewToggleButtonInputField = this.installationViewToglleButton.locator(`xpath=preceding-sibling::input`).first();
        this.rolloutStatusTab = this.page.locator(`//*[text()="Rollout Status"]`);
        this.rolloutReleaseTab = this.page.getByTestId(`Rollout-Release-click`);
        this.deployButton = this.page.locator(`//*[text()="Deploy"]`);
        this.feasibilityPageDiv = this.page.locator(`//*[@class="drawer right show"]`);
        this.feasibilityPageDeployButton = this.feasibilityPageDiv.locator(this.deployButton);
        this.feasibilityModalButtonGroup = this.page.locator(`//*[contains(@class,'confirmation-dialog__button-group')]`);
        this.goToRolloutStatusButton = this.page.locator(`//*[text()="Go to rollout status"]`);
        this.clearFiltersButton = this.page.locator(`//*[text()="Clear All Filters"]`);
        this.reloadButton = this.page.getByTestId('reload-content-button');
        this.autopollingButton = this.page.getByTestId(`should-poll-toggle`);
        this.autopollingButtonInputAttribute = this.autopollingButton.locator('xpath=preceding-sibling::input');
        this.deploymentStatusButtonOnApplicationRow = this.page.getByTestId('deployment-stage-status-undefined-undefined');
        this.appDetailsModalCloseButton = this.page.getByTestId(`close-modal-header-icon-button`);
    }




    async openAllReleaseStages() {
        let openStages = await this.page.locator(`//*[contains(@class,'shadow__card')]`).all();
        for (let element of openStages) {
            if (!await element.locator(`//*[@class="form__checkbox-container"]`).first().isVisible()) {
                await element.locator(`//*[@role="tab"]`).click();
            }
        }
    }

    async getRowDivOfApplicationsOnTriggerReleasePage(envName: string, appName: string): Promise<undefined | Locator> {
        let appsList: Locator[] = await this.page.locator(`//*[contains(@class,'releases__deploy-table-row') and not(contains(@class,'--header')) or contains(@class,'last-child')]`).all();
        for (let element of appsList) {
            let text = await element.textContent();
            if (text?.includes(envName) && text.includes(appName)) {
                return element;
            }
        }
        return undefined;
    }

    async applyFiltersAndVerifyResult(data: { applyingFilter: { filterType: string, subFilterType?: string, filterValue: string[] }[], verification: { appName: string, envName: string, isVisible: boolean }[] }) {
        await this.rolloutReleaseTab.click();
        await this.triggerReleaseTab.click();
        await this.turnOnOffInstallationView(false);
        await this.openAllReleaseStages();
        for (let key of data.applyingFilter) {
            await this.openDropdownsOfFilterTypes(key.filterType);
            let refParentHeading: string = key.subFilterType ? key.subFilterType : key.filterType;
            await this.selectFilterValues(refParentHeading, key.filterValue);
        }
        await this.applyFiltersButton.click();
        for (let key of data.verification) {
            await this.verifyVisibilityOfApplicationsOnTriggerReleasePage(key.envName, key.appName, key.isVisible);
        }
        await this.clearFiltersButton.click({ delay: 2000 });
    }

    async verifyVisibilityOfApplicationsOnTriggerReleasePage(envName: string, appName: string, isVisible: boolean) {
        await this.rolloutReleaseTab.click();
        await this.triggerReleaseTab.click();
        await this.installationViewToglleButton.waitFor();
        let returnedLocator = await this.getRowDivOfApplicationsOnTriggerReleasePage(envName, appName);
        isVisible ? expect(returnedLocator).not.toBe(undefined) : expect(returnedLocator).toBe(undefined);
    }


    async openDropdownsOfFilterTypes(filterType: string) {
        if (!await this.page.locator(`//*[text()="${filterType}"]/parent::button/following-sibling::div`).isVisible()) {
            await this.page.locator(`//*[text()="${filterType}"]`).click();
        }
    }


    async selectFilterValues(refHeading: string, valueToSelect: string[]) {
        let filtersHavingSubFilters: string[] = ['deployment', 'pre-deployment', 'post-deployment'];
        let locatorForRefHeading: Locator;
        locatorForRefHeading = !filtersHavingSubFilters.includes(refHeading) ? this.page.locator(`//*[text()="${refHeading}"]/parent::button/parent::div`) : this.page.locator(`//*[text()="${refHeading}"]/parent::div`);
        for (let key of valueToSelect) {
            await locatorForRefHeading.locator(`//*[text()="${key}"]`).click();
        }
    }
    async turnOnOffAutoPolling(turnOn: boolean) {
        await this.triggerReleaseTab.click();
        if (turnOn != await this.autopollingButtonInputAttribute.isChecked()) {
            await this.autopollingButton.click();
        }
    }

    async selectAppsFromListAndVerifyIsBlocked(data: { envName: string, appName: string, isBlocked: boolean }[]) {
        for (let key of data) {
            let returnedLocator: Locator | undefined = await this.getRowDivOfApplicationsOnTriggerReleasePage(key.envName, key.appName);
            await returnedLocator?.hover();
            if (key.isBlocked) {
                expect(await returnedLocator?.locator(this.checkBoxLocator).isDisabled()).toBeTruthy();
            }
            else {
                await returnedLocator?.locator(this.checkBoxLocator).click();
            }
        }
    }
    async clickOnDeploymentStatusButtonInAnApplicationRow(envName: string, appName: string) {
        await this.triggerReleaseTab.click();
        let applicationRow: Locator = await this.getRowDivOfApplicationsOnTriggerReleasePage(envName, appName) as Locator;
        await applicationRow.locator(this.deploymentStatusButtonOnApplicationRow).click();
    }

    async turnOnOffInstallationView(turnOn: boolean) {
        await this.rolloutReleaseTab.click();
        await this.triggerReleaseTab.click();
        if (await this.installationViewToggleButtonInputField.isChecked() != turnOn) {
            await this.installationViewToglleButton.click();
        }
    }
    async verifyRolloutStatus(data: { [key: string]: string }) {
        await this.rolloutStatusTab.click();
        let sections = Object.keys(data);
        for (let key of sections) {
            let refreshCount = 0;
            while (refreshCount < 10 && !(await this.page.locator(`//*[text()="${key}"]/parent::div//*[text()="${data[key]}"]`).isVisible())) {
                await this.reloadButton.click();
                await this.page.waitForTimeout(5000);
                refreshCount++;
            }
            await expect(this.page.locator(`//*[text()="${key}"]/parent::div//*[text()="${data[key]}"]`)).toBeVisible({ timeout: 1000 });
        }
    }



    async triggerDeployment(data: { envSelectionRelatedData: { envName: string, appName: string, isBlocked: boolean }[], feasibilityPage: { canTriggerCount: number, cantTriggerCount: number }, isHoldOrRescinded: boolean, isVirtualEnv: boolean }, prePostCdStage: 'Pre' | 'Post' | 'cd' = 'cd') {
        await this.rolloutReleaseTab.click();
        if (!data.isVirtualEnv) {
            await this.turnOnOffInstallationView(false);
            await this.openAllReleaseStages();
        }
        else {
            await this.turnOnOffInstallationView(true);
        }
        for (let key of data.envSelectionRelatedData) {
            await this.selectAppsFromListAndVerifyIsBlocked([{ appName: key.appName, envName: key.envName, isBlocked: key.isBlocked }]);
        }
        if (data.isHoldOrRescinded) {
            await BaseTest.checkToast(this.page, this.deployButton, 'Release is On hold');
        }
        else {
            if (prePostCdStage == 'cd') {
                await this.deployButton.click();
            }
            else {
                await this.page.locator(`//button[contains(@class,'button-with-selector')]`).click();
                await this.page.locator(`//*[text()="Trigger ${prePostCdStage}-deployment stage"]`).click();
            }
            await expect(this.page.locator(`//*[text()="Can trigger"]`)).toHaveCount(data.feasibilityPage.canTriggerCount);
            await expect(this.page.locator(`//*[text()="Cannot trigger"]`)).toHaveCount(data.feasibilityPage.cantTriggerCount);
            if (data.feasibilityPage.canTriggerCount != 0) {
                await this.feasibilityPageDeployButton.click();
                await expect(async () => {
                    try {
                        await this.dialogDeleteConfirmationButton.locator(this.deployButton).click({ timeout: 6000 });
                    }
                    catch (error) {
                        console.log('this has already been triggered once ');
                    }
                    await this.goToRolloutStatusButton.click();
                }).toPass({ timeout: 3 * 1000 * 60 });

            }

        }
    }

    async verifyResourceStatusOnAppDetailsCard(appDetailsCardResourceStatusVerification: AppDetailsCardStatusVerificationDTO) {
        let resourceNamesToVerify: string[] = Object.keys(appDetailsCardResourceStatusVerification);
        for (let resourceName of resourceNamesToVerify) {
            let statusToVerify: string = appDetailsCardResourceStatusVerification[resourceName];
            statusToVerify = resourceName == 'Application Status' ? statusToVerify.toUpperCase() : statusToVerify;
            await expect(this.getRowDivLocatorOfResourcesOnAppDetailsCard(resourceName as keyof AppDetailsCardStatusVerificationDTO)).toContainText(statusToVerify, { timeout: 3 * 1000 * 60 });
        }
        await this.appDetailsModalCloseButton.click();
    }

    async verifyResourcesCountInSectionsOnAppDetailsCard(appDetailsCardResourceStatusVerification: AppDetailsCardResourceCountVerificationDTO, count: string) {
        let sectionsToCheck = Object.keys(appDetailsCardResourceStatusVerification);
        for (let section of sectionsToCheck) {
            let locatorForSection = this.getLocatorForResourceCountSectionsOnAppDetailsCard(section as keyof AppDetailsCardResourceCountVerificationDTO);
            await expect(locatorForSection).toContainText(count);
        }

    }


    getLocatorForResourceCountSectionsOnAppDetailsCard(sectionName: keyof AppDetailsCardResourceCountVerificationDTO): Locator {
        return this.page.locator(`//input[@value="${sectionName}"]`).locator('xpath=parent::div');
    }

    getRowDivLocatorOfResourcesOnAppDetailsCard(resourceName: keyof AppDetailsCardStatusVerificationDTO): Locator {
        return this.page.locator(`//*[text()="${resourceName}"]`).locator(`xpath=parent::div`);
    }
}