import { expect, Locator, Page } from "@playwright/test";
import { ReleaseTrackConfigurePage } from "./ReleaseTrackConfigurePage";
import { completeReleaseStatus } from "../../enums/SoftwareReleaseManagement/ReleaseRolloutPageEnum";
import { TurnOnOffTargetInstallationsDTO } from "../../DTOs/SoftwareReleaseManagement/TurnOnOffTargetInstallationsDTO";
import { BaseTest } from "../../utilities/BaseTest";

export class Requirements extends ReleaseTrackConfigurePage {
    readonly appListDiv: Locator;
    readonly selectImageOrEditImageButton: Locator;
    readonly workflowListDiv: Locator;
    readonly workflowLocator: Locator;
    readonly selectImagesFromPreviousDeployment: Locator;
    readonly imageDiv: Locator;
    readonly saveButton: Locator;
    readonly releaseStatusDropdown: Locator;
    readonly editAppsOrderingButton: Locator;
    readonly removeReleaseOrder: Locator;
    readonly inputOfRemoveReleaseOrder: Locator;
    readonly addApplicationButtonInRequirementPage: Locator;
    readonly commentTextArea: Locator;
    readonly rescindedReleaseConfirmationField: Locator;
    readonly tippyDialogBox: Locator;
    readonly targetTenantInstallationsDiv: Locator;
    readonly targetTentantInstallationsDropdown: Locator;
    readonly targetInstallationToggleButton: Locator;
    readonly targetInstallationToggleButtonInputAttribute: Locator;
    readonly requirementsTab: Locator;
    readonly targetInstallationToggleButtonDisabledState: Locator;
    readonly deleteReleaseButton: Locator;
    readonly releaseStagesHeading: Locator;

    constructor(public page: Page) {
        super(page)
        this.appListDiv = this.page.locator(`//*[contains(@class,'releases__requirements-column')]`).first();
        this.selectImageOrEditImageButton = this.page.locator(`//*[@aria-label="open-deployment-source-modal" or text()="Select a image"]`);
        this.workflowListDiv = this.page.locator(`//*[contains(@class,'releases__deployment')]`);
        this.workflowLocator = this.page.locator(`//button[not(contains(@class,'content-space'))]`);
        this.selectImagesFromPreviousDeployment = this.page.locator(`//span[@data-testid="toggle-image-selection-chk-span"]`);
        this.imageDiv = this.page.locator(`//*[@class="material-history material-history--cd image-tag-parent-card "]`);
        this.saveButton = this.page.locator(`//*[text()="Save"]`);
        this.releaseStatusDropdown = this.page.locator(`//*[contains(@class,'release-status-dropdown__control')]`);
        this.editAppsOrderingButton = this.page.locator(`//*[@aria-label="Manage release order"]`);
        this.removeReleaseOrder = this.page.getByTestId(`toggle-enable-release-ordering`);
        this.inputOfRemoveReleaseOrder = this.removeReleaseOrder.locator('xpath=preceding-sibling::input');
        this.addApplicationButtonInRequirementPage = this.page.locator(`//span[text()="Add Application"]`)
        this.commentTextArea = this.page.locator(`//*[@name="release-comment"]`);
        this.tippyDialogBox = this.page.locator(`//*[@class="tippy-content"]`);
        this.targetTenantInstallationsDiv = this.page.locator(`//*[contains(@data-testid,"tree-view-heading-")]`);
        this.targetTentantInstallationsDropdown = this.targetTenantInstallationsDiv.locator(`//span`);
        this.targetInstallationToggleButton = this.page.locator(`//*[contains(@data-testid,'map-tenant')]`);
        this.targetInstallationToggleButtonInputAttribute = this.targetInstallationToggleButton.locator('xpath=preceding-sibling::input');
        this.requirementsTab = this.page.getByTestId('Requirement-click');
        this.targetInstallationToggleButtonDisabledState = this.page.locator(`//button[@disabled]`).first();
        this.releaseStagesHeading = this.page.locator(`//h3[contains(text(),"Release")]`);
        this.deleteReleaseButton = this.page.locator('//*[@aria-label="Delete release"]')

    }
    async setImageForAnApp(data: { appName: string, workflowNumber: number, image?: string, selectFromPreviousRelease?: boolean }[]) {
        for (let key of data) {
            // await this.appListDiv.locator(`//*[text()="${key.appName}"]`).click();
            await this.page.locator(`//a[contains(@href,'requirements/${key.appName}')]`).click();
            await this.selectImageOrEditImageButton.click();
            await this.workflowListDiv.locator(this.workflowLocator).nth(key.workflowNumber).click();
            if (key.selectFromPreviousRelease == true) {
                await this.selectImagesFromPreviousDeployment.click();
            }
            key.image ? await this.selectAnImage(key.image) : await this.selectAnImage();
            await this.saveButton.click();

        }
    }

    async clickOnApp(appName: string) {
        await this.page.locator(`//a[contains(@href,'requirements/${appName}')]`).click();
    }
    async reviewAndUpdate() {
        await this.page.locator('//*[text()="Review update"]').click();
        await this.page.locator(`//*[text()="Accept Changes"]`).click();
    }

    async selectAnImage(image?: string) {
        if (image) {
            await this.page.locator(`//*[text()="${image}"]/ancestor::div[@class="material-history material-history--cd image-tag-parent-card "]//*[text()="Select"]`).click();
        }
        else {
            await this.imageDiv.nth(0).locator(`//*[text()="Select"]`).click();
        }
    }

    async changeStatus(status: string) {
        await this.releaseStatusDropdown.click();
        await this.page.locator(`//*[@role="option"]//*[text()="${status}"]`).click();
        if (status == "On hold" || status == "Rescinded") {
            await this.commentTextArea.fill('aman-test');
            if (status == "Rescinded") {
                await this.rescindedReleaseConfirmationField.fill('rescind release');
            }
        }
        await this.dialogDeleteConfirmationButton.click();

    }

    async verifyCompleteReleaseStatus(status: completeReleaseStatus, timeout: number = 1 * 1000 * 60) {
        await expect(this.releaseStatusDropdown).toContainText(status, { timeout: timeout });
    }


    async editApplications(removeReleaseorder: boolean, data: { appName: string, isDeletionBlocked?: boolean, removeApplication?: boolean, addAsNewApp?: boolean }[]) {
        let noAppsExisted: boolean = false;
        try {
            await this.editAppsOrderingButton.click({ timeout: 3000 });
        }
        catch (error) {
            await this.addApplicationButtonInRequirementPage.click();
            noAppsExisted = true;
        }
        if (!noAppsExisted) {
            if (
                removeReleaseorder != !await this.inputOfRemoveReleaseOrder.isChecked()) {
                await this.removeReleaseOrder.click();
            }
        }
        for (let element of data) {
            if (element.isDeletionBlocked) {
                expect(await this.page.locator(`//*[@aria-label="remove-${element.appName}-from-release-order"]`).isDisabled()).toBe(element.isDeletionBlocked);
            }
            if (element.removeApplication) {
                await this.page.locator(`//*[@aria-label="remove-${element.appName}-from-release-order"]`).click();
                await this.dialogRemoveButton.click();
            }
            if (element.addAsNewApp) {
                await this.selectAppsFromDropdown(element.appName);
            }
        }

        await this.saveChangesButton.click();
    }


    async verifyEnvMappingToTenants(data: { installationName: string, tenantName: string, envNames: string[] }[]) {
        await this.openAllInstallationsDropdowns();
        for (let installationAndTenantObject of data) {
            await this.page.locator(`//*[text()="${installationAndTenantObject.tenantName} (${installationAndTenantObject.installationName})"]`).hover();
            await this.verifyTextInTippyDialogBox(installationAndTenantObject.envNames);
        }
    }
    async verifyTextInTippyDialogBox(textToVerify: string[]) {
        await this.tippyDialogBox.waitFor();
        for (let text of textToVerify) {
            await expect(this.tippyDialogBox.locator(`//*[text()="${text}"]`)).toBeVisible({ timeout: 4000 });
        }
    }

    async openAllInstallationsDropdowns() {
        await this.targetTenantInstallationsDiv.first().waitFor();
        let installationDropdowns = await this.page.locator(`//*[@role="treeitem" and @aria-expanded="false"]`).all();
        for (let key of installationDropdowns) {
            await installationDropdowns[0].click();
        }
    }
    async lockOrUnlockRequirements(lockRequirements: boolean) {
        let buttonToLookFor: Locator;
        if (lockRequirements) {
            buttonToLookFor = this.page.locator(`//*[text()="Lock Requirements"]`)
        }
        else {
            buttonToLookFor = this.page.locator(`//*[text()="Unlock to edit"]`);
        }
        if (await buttonToLookFor.isVisible()) {
            await buttonToLookFor.click();
            await this.page.locator(`//*[text()="Unlock" or text()="Lock"]`).click();
        }
    }
    async verifyVisibilityOfAppsOnRequirementPage(data: { appName: string, isVisible: boolean }[]) {
        for (let key of data) {
            //expect(await this.appListDiv.locator(`//*[text()="${key.appName}"]`).isVisible()).toBe(key.isVisible);
            expect(await this.page.locator(`//a[contains(@href,'requirements/${key.appName}')]`).isVisible()).toBe(key.isVisible);
        }
    }

    async turnOnOffTargetInstallationOnChannelAndTenantLevel(turnOnOffTargetInstallation: TurnOnOffTargetInstallationsDTO) {
        await this.openAllInstallationsDropdowns();
        if (turnOnOffTargetInstallation.performActionOnChannelLevel) {
            await this.turnOnOffTargetInstallationOnChannelLevel(turnOnOffTargetInstallation.channelName!, turnOnOffTargetInstallation.turnOnTargetInstallation);
        }
        else {
            await this.turnOnOffTargetInstallationOnTenantLevel(turnOnOffTargetInstallation.tenantName!, turnOnOffTargetInstallation.installationName!, turnOnOffTargetInstallation.turnOnTargetInstallation);
        }

    }

    async turnOnOffTargetInstallationOnChannelLevel(channelName: string, turnOnTargetInstallation: boolean) {
        let textToSearchInDropdowns: string = channelName ? channelName : "Not subscribed Installations";
        let AllInstallationsDropdowns: Locator[] = await this.targetTentantInstallationsDropdown.all();
        for (let dropdown of AllInstallationsDropdowns) {
            let textPresentInCurrentDropdown = await dropdown.textContent();
            if (textPresentInCurrentDropdown?.includes(textToSearchInDropdowns)) {
                if (await dropdown.locator(this.targetInstallationToggleButtonInputAttribute).isChecked() != turnOnTargetInstallation) {
                    await dropdown.locator(this.targetInstallationToggleButton).click();
                    turnOnTargetInstallation ? await expect(dropdown.locator(this.targetInstallationToggleButtonInputAttribute)).toBeChecked() :
                        await expect(dropdown.locator(this.targetInstallationToggleButtonInputAttribute)).not.toBeChecked();
                }
            }
        }
    }

    async turnOnOffTargetInstallationOnTenantLevel(tenantName: string, installationName: string, turnOnTargetInstallation: boolean) {
        let tenantAndInstallationRowLocator = this.getRowDivLocatorOfTenantsInstallationsInTargetInstallationDiv(tenantName, installationName);

        if (turnOnTargetInstallation != await tenantAndInstallationRowLocator.locator(this.targetInstallationToggleButtonInputAttribute).isChecked()) {
            await tenantAndInstallationRowLocator.locator(this.targetInstallationToggleButton).click();
            turnOnTargetInstallation? await expect(tenantAndInstallationRowLocator.locator(this.targetInstallationToggleButtonInputAttribute)).toBeChecked() :
                await expect(tenantAndInstallationRowLocator.locator(this.targetInstallationToggleButtonInputAttribute)).not.toBeChecked();
        }

    }

    async verifyToggleButtonForTargetInstallationIsDisabledOrNot(tenantName: string, installationName: string, isDisabled: boolean) {
        let tenantAndInstallationRowLocator = this.getRowDivLocatorOfTenantsInstallationsInTargetInstallationDiv(tenantName, installationName);
        isDisabled? await expect(tenantAndInstallationRowLocator.locator(this.targetInstallationToggleButtonDisabledState)).toBeVisible({ timeout: 20000 }) :
            await expect(tenantAndInstallationRowLocator.locator(this.targetInstallationToggleButton)).toBeVisible({ timeout: 20000 });

    }

    async deleteReleaseFromReleaseTrack(releaseName: string, isDeletionSuccessfull: boolean) {
        await this.requirementsTab.click();
        await this.deleteReleaseButton.click();
        let toastMessageToVerify: string = isDeletionSuccessfull ? "Success" : "Cannot Delete Release";
        await this.deleteModalInputField.fill(releaseName);
        await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, toastMessageToVerify);
        isDeletionSuccessfull == false ? await this.page.getByTestId('confirmation-modal-secondary-button').click() : null;
    }

    async verifyTheCountOfStagesOfRelease(numberOfReleaseStagesToVerify: number) {
        await this.requirementsTab.click();
        await expect(this.releaseStagesHeading).toHaveCount(numberOfReleaseStagesToVerify);
    }
    getRowDivLocatorOfTenantsInstallationsInTargetInstallationDiv(tenantName: string, installationName: string): Locator {
        return this.page.locator(`//*[text()="${tenantName} (${installationName})"]/ancestor::div[@role="treeitem"][1]`);
    }
}
