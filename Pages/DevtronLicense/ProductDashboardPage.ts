import { Locator, <PERSON> } from "playwright/test";
import { BaseTest } from "../../utilities/BaseTest";

export class ProductDashboardPage{

    readonly activateLicense:Locator;
    readonly licenseKeyTextArea: Locator;
    readonly EnterNewEnterpriseLicenseKey: Locator;
    readonly getLicense: Locator;
    readonly loginAsAdministratorLink: Locator;
    readonly passwordTextbox: Locator;
    readonly loginButton: Locator;
    readonly gotItButton: Locator;
    readonly licenseKey: Locator;

    constructor(private page: Page){
        
        this.activateLicense=this.page.getByTestId('activate-license');
        this.licenseKeyTextArea=this.page.getByPlaceholder('Enter license key');
        this.EnterNewEnterpriseLicenseKey=this.page.getByText('Enter new enterprise license key');
        this.getLicense=this.page.getByTestId('get-license');
        this.loginAsAdministratorLink=this.page.getByText('Login as administrator');
        this.passwordTextbox=this.page.getByTestId('password');
        this.loginButton=this.page.getByTestId('login-button');
        this.gotItButton=this.page.getByTestId('qr-dialog-got-it');
        this.licenseKey=this.page.getByTestId('license-key');
    }
    async verifyProductDashboardPage(){
        await this.EnterNewEnterpriseLicenseKey.waitFor();
        await this.licenseKeyTextArea.waitFor();
        await this.activateLicense.waitFor();
    }
    async clickLicenseButton(){
        // await this.getLicense.click();
        const [newPage]=await Promise.all([this.page.waitForEvent('popup'),this.getLicense.click()]);
        return newPage;
    }

    async enterLicenseKey(){
        if(await this.gotItButton.isVisible()){
            await this.gotItButton.click();
           }
        await this.licenseKey.waitFor();
        await this.licenseKey.click();
        await this.page.keyboard.press('Meta+V');
        //await this.activateLicense.click({delay:2000});
        await BaseTest.checkToast(this.page,this.activateLicense,"License activated successfully");
    }



    async loginAsAdministrator(password: string) {
        try {
          // Click the "Login as administrator" link
          await this.loginAsAdministratorLink.click();
    
          // Click the password textbox and fill in the password
          await this.passwordTextbox.click();
          await this.passwordTextbox.fill(password);
    
          // Click the login button
          await this.loginButton.click();
        } catch (error) {
          console.error(`An error occurred during login: ${error.message}`);
          throw error; // Rethrow the error to indicate test failure
        }
      }
    
}