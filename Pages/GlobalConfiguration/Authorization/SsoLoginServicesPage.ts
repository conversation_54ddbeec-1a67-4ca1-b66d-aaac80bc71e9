import { expect, Locator, <PERSON> } from "@playwright/test";
import clipboardy from 'clipboardy';
import { error } from "console";
import { GlobalConfigurationsUrls } from "../../../enums/UrlNavigations/GlobalConfigurations";
export class SsoLoginServicesPage {
    readonly profileButton: Locator
    readonly logOutButton: Locator;
    readonly saveButton: Locator;
    readonly clickToUseUrl: Locator;
    readonly ssoConfigTextArea: Locator;
    readonly handleUserUsingAdToggleButton: Locator;
    readonly inputOfhandleUserUsingAdToggleButton: Locator;
    readonly ssoSaveConfirmationModalButton: Locator;
    constructor(private page: Page) {
        this.profileButton = this.page.getByTestId('profile-button');
        this.logOutButton = this.page.locator('//span[text()="Logout"]');
        this.saveButton = this.page.getByTestId('sso-save-button');
        this.clickToUseUrl = this.page.locator(`//*[text()="Click to use:"]/following-sibling::button`);
        this.ssoConfigTextArea = this.page.locator('//*[@role="textbox"]').first();
        this.handleUserUsingAdToggleButton = this.page.getByTestId('sso-permissions-auto-assign');
        this.inputOfhandleUserUsingAdToggleButton = this.page.locator('//*[@data-testid="handle-toggle-button"]/preceding-sibling::input');
        this.ssoSaveConfirmationModalButton = this.page.getByTestId('confirmation-modal-primary-button');
    }
    async loggingOut() {
        await expect(async () => {
            await this.profileButton.click();
            await this.logOutButton.click();
        }).toPass({ timeout: 3 * 1000 * 60 });
    }
    async navigateToSsoServicesPage() {
        await this.page.goto(process.env.BASE_SERVER_URL! + `${GlobalConfigurationsUrls.ssoLoginServiceProviderPages}`);
    }
    async clickOnAnySsoProvider(ssoName: string) {
        await this.page.locator(`//*[@data-testid="sso-${ssoName}-button"]`).click();
    }
    async clickOnSaveButton() {


        await this.saveButton.click();
        //const confirmationModalvisible = await this.ssoSaveConfirmationModalButton.isVisible();

            try {
                await this.ssoSaveConfirmationModalButton.click({ delay: 800, timeout: 10000 });
            } catch (error) {
                console.log('save update changes modal did not come ');
            }
            console.log('Save button is visible');

        await this.page.waitForTimeout(25000);
    }
    async editSsoConfiguration(configuration: string) {
        await expect(async () => {
            await this.clickToUseUrl.click({ delay: 1000 });
            await this.ssoConfigTextArea.dblclick();
            let keyToClick: string = process.env.OS == "Mac" ? 'Meta+a' : 'Control+a';
            for (let i = 0; i < 5; i++) {
                await this.page.keyboard.press(keyToClick);
                await this.page.waitForTimeout(1000);
            }
            await clipboardy.write(configuration);
            await this.page.keyboard.press('Shift+Insert');
            const isChecked = await this.handleUserUsingAdToggleButton.getAttribute('aria-checked');

            if (isChecked === 'true') {
            console.log('Toggle is ON');
            } else {
            console.log('Toggle is OFF');
            await this.handleUserUsingAdToggleButton.click()
            }

            // if (!await this.inputOfhandleUserUsingAdToggleButton.isChecked()) {
            //     await this.handleUserUsingAdToggleButton.click()
            // }
            //await expect(this.page.locator(`//*[text()="ad.example.com:636"]`)).toBeHidden();
        }).toPass({ timeout: 4 * 1000 * 60 });
    }


    async setPermissionsFromAd(fromAd: boolean) {
        try {
            if (fromAd != await this.inputOfhandleUserUsingAdToggleButton.isChecked()) {
                await this.handleUserUsingAdToggleButton.click()
            }
        }
        catch (error) {
            console.log('ad option was not available');
        }
    }
}