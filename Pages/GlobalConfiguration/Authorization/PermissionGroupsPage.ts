import { Locator, <PERSON> } from "@playwright/test";
import { read } from "fs";
import { BaseTest } from "../../../utilities/BaseTest";
import { BasePage } from "../../BasePage";
import { GlobalConfigurationsUrls } from "../../../enums/UrlNavigations/GlobalConfigurations";

export class permissionGroupsPage extends BasePage {
    readonly createGroupButton: Locator
    readonly searchGroupNameInputField: Locator;
    readonly groupNameRow: Locator;
    readonly permissionGroupNameInputField: Locator;
    readonly savePermissionGroupButton: Locator;
    readonly deletePermissionGroupButton: Locator;
    constructor(public page: Page) {
        super(page);
        this.savePermissionGroupButton = this.page.locator(`//*[text()="Save"]`);
        this.createGroupButton = this.page.locator(`//*[text()="Add group" or text()="Add Group" or text()="Create Group"]`);
        this.searchGroupNameInputField = this.page.locator(`//*[@placeholder="Search group"]`);
        this.groupNameRow = this.page.locator(`//*[contains(@class,'user-permission__row')]`);
        this.permissionGroupNameInputField = this.page.locator(`//*[@name="permission-group-name-textbox"]`);
        this.deletePermissionGroupButton = this.page.getByTestId('delete-group');
    }


    /**
     * simply navigate to user permission page
     */
    async navigatetoUserPermissionPage() {
        await this.page.goto(process.env.BASE_SERVER_URL! + `${GlobalConfigurationsUrls.permissionGroupsListingPage}`);
    }


    /**
     * use this method to create permission group
     * @param tokenName (permission group Name)
     */
    async clickOnPermissionGroupOrCreateNew(tokenName: string) {
        await this.createGroupButton.waitFor({ timeout: 12000 });
        try {
            await this.searchGroupNameInputField.fill(tokenName, { timeout: 5000 });
            await this.page.keyboard.press('Enter');
            await this.groupNameRow.first().locator(`//*[text()="${tokenName}"]`).click({ timeout: 5000 });
        }
        catch (error) {
            await this.createGroupButton.click();
            await this.permissionGroupNameInputField.fill(tokenName);
        }
    }


    /**
     * 
     */
    async saveOrUpdatePermissionGroup() {
        await BaseTest.checkToast(this.page, this.savePermissionGroupButton, 'Success');
    }

    /**
     * use this method to delete the permission group
     * @param permissionGroupName 
     */
    async deletePermissionGroup(permissionGroupName: string) {
        await this.searchGroupNameInputField.fill(permissionGroupName);
        await this.page.keyboard.press('Enter');
        await this.groupNameRow.first().locator(`//*[text()="${permissionGroupName}"]`).click({ timeout: 6000 });
        await this.deletePermissionGroupButton.click();
        await this.dialogDeleteConfirmationButton.click();
    }

}