import { Page, Locator, expect } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest';
import { AllTypes } from '../../../utilities/Types';
import { BasePage } from '../../BasePage';
import { GlobalConfigurationsUrls } from '../../../enums/UrlNavigations/GlobalConfigurations';
//TODO to handle all combinations of permission

export class ApiTokenPage extends BasePage {
  readonly apiGroupPage: Locator;
  readonly apiGroupPageHeader: Locator;
  readonly apiGroupSearchBox: Locator;
  readonly apiTokenGenerateButton: Locator;
  readonly apiTokenName: Locator;
  readonly apiTokenString: Locator;
  readonly apiTokenDescription: Locator;
  readonly addedApiTokenNameList: Locator;
  readonly selectTokenExpiryDuration: Locator;
  readonly selectTokenExpiryDurationMenu: Locator;
  readonly superAdminPermissionRadioButton: Locator;
  readonly generateApiFormButton: Locator;
  readonly apiVisibleModal: Locator;
  readonly generateApiVisibleModalClose: Locator;
  readonly regenerateApiVisibleModalClose: Locator;
  readonly generatedApiToken: Locator;
  readonly updateApiFormButton: Locator;
  readonly cancelApiFormButton: Locator;
  readonly deleteApiFormButton: Locator;
  readonly deleteApiFormDialog: Locator;
  readonly genericEmptyState: Locator;
  readonly searchTokenInputField: Locator;
  readonly regenerateTokenButton: Locator;
  readonly modalRegenerateButton: Locator;
  readonly deletePermissionRowIcon: Locator;
  readonly apitokenListDiv: Locator;
  readonly superAdminPermissionText: Locator;
  readonly tokenExpiredText: Locator;
  readonly dropdownList: Locator;
  readonly genericSaveAndUpdateButton: Locator;
  readonly closeModalIcon: Locator;
  readonly apitokenPageLink: Locator;
  readonly generatedTokenField: Locator;
  readonly expirationDateOpeningDropdown: Locator;
  readonly addPermissionButton: Locator;
  readonly specificUserPermissionRadioBox: Locator;
  readonly chartGroupsButton: Locator;
  readonly createCheckBoxInputField: Locator;
  readonly createCheckBoxClickableField: Locator;
  readonly dropdownForEditCharts: Locator;
  readonly specificChartsGroupText: Locator;
  readonly dropdownForSpecificChartGroups: Locator;
  readonly k8sPermissionButton: Locator;
  readonly addK8sPermissionLink: Locator;
  readonly k8sPermissionSaveButton: Locator;
  readonly selectProjectText: Locator;

  constructor(public page: Page) {
    super(page);
    this.apiGroupPage = page.getByTestId('api-token-page');
    this.apiGroupPageHeader = page.getByTestId('api-token-page-header');
    this.apiGroupSearchBox = page.getByTestId('search-token-input');
    this.apiTokenString = page.getByTestId('api-token-string');
    this.apiTokenName = page.getByPlaceholder('Name');
    this.apiTokenDescription = page.getByTestId(
      'api-token-description-textbox'
    );
    this.addedApiTokenNameList = page.getByTestId('api-list-row');
    this.selectTokenExpiryDuration = page.locator(
      '.select-token-expiry-duration__control'
    );
    this.selectTokenExpiryDurationMenu = page.locator(
      '.select-token-expiry-duration__menu-list'
    );
    this.superAdminPermissionRadioButton = page.getByTestId(
      'super-admin-permission-radio-button'
    );
    this.generateApiFormButton = this.page.getByTestId('generate-token');
    this.apiVisibleModal = this.page.getByTestId('visible-modal-close');
    this.generateApiVisibleModalClose = this.page.getByTestId('generated-token-modal-close');
    this.regenerateApiVisibleModalClose = this.page.getByTestId('regenerated-token-modal-close');
    this.generatedApiToken = this.page.getByTestId('generated-token');
    this.updateApiFormButton = this.page.getByTestId('update-token');
    this.cancelApiFormButton = this.page.getByTestId('cancel-token');
    this.deleteApiFormButton = this.page.getByTestId('delete-token');
    this.deleteApiFormDialog = this.page.getByTestId('visible-modal2-close');
    this.genericEmptyState = page.getByTestId("generic-empty-state");
    this.apiTokenGenerateButton = page.locator('//button[text()="Generate new token"]');
    this.searchTokenInputField = this.page.getByTestId('search-token-input');
    this.regenerateTokenButton = this.page.locator('//*[text()="Regenerate token"]');
    this.modalRegenerateButton = this.page.getByTestId('regenerate-token');
    this.deletePermissionRowIcon = this.page.locator('//*[@aria-label="Delete permission" or @aria-label="Delete row"]');
    this.apitokenListDiv = this.page.getByTestId('api-list-row');
    this.superAdminPermissionText = this.page.locator('//*[text()="Super admin permission"]');
    this.tokenExpiredText = this.page.locator("//*[contains(text(),'This token expired on')]");
    this.dropdownList = this.page.locator('//*[@role="listbox"]');
    this.genericSaveAndUpdateButton = this.page.locator('//*[contains(text(),"Update token") or contains(text(),"Generate token") or contains(text(),"Save")]');
    this.closeModalIcon = this.page.getByTestId('create-ci-cd-pipeline-modal-close-button');
    this.generatedTokenField = this.page.locator('//*[@data-testid="api-token-string" or @data-testid="generated-token"]');
    this.apitokenPageLink = this.page.getByTestId('authorization-api-tokens-link');
    this.expirationDateOpeningDropdown = this.page.locator('//*[contains(@class,"select-token-expiry-duration__control")]');
    this.addPermissionButton = this.page.locator(`//*[text()="Add Permission"]`);
    this.specificUserPermissionRadioBox = this.page.getByTestId("specific-user-permission-radio-button-span");
    this.chartGroupsButton = this.page.getByTestId(`chart-groups`);
    this.createCheckBoxInputField = this.page.locator(`//*[contains(@class,"chart-permission__row")]//input[@class="form__checkbox"]`);
    this.createCheckBoxClickableField = this.page.locator(`//*[contains(@class,"chart-permission__row")]//input[@class="form__checkbox"][1]/parent::label`);
    this.dropdownForEditCharts = this.page.locator(`//*[contains(@class,"chart-permission__row")]//*[@role="log"]/following-sibling::div`);
    this.specificChartsGroupText = this.page.locator(`//*[text()="Specific Chart Groups"]`);
    this.dropdownForSpecificChartGroups = this.page.locator(`//*[text()="Select Chart Group"]/parent::div`);
    this.k8sPermissionButton = this.page.getByTestId(`kubernetes-objects`);
    this.addK8sPermissionLink = this.page.getByTestId(`add-k8s-permission-link`);
    this.k8sPermissionSaveButton = this.page.getByTestId(`k8s-permission-save`);
    this.selectProjectText = this.page.locator(`//*[text()="Select project"]`);


  }


  /**
   * navigate directly to api token page 
   */
  async goToApiTokenPage() {
    await BaseTest.clickOnDarkMode(this.page);
    await BaseTest.clickOnToolTipOkayButton(this.page);
    await this.page.goto(process.env.BASE_SERVER_URL as string + `${GlobalConfigurationsUrls.apiTokens}`);
  }


  /**
   * 
   * @param name 
   */

  async deleteApiToken(name: string) {
    await expect(this.addedApiTokenNameList.getByText(name)).toBeVisible();
    await this.addedApiTokenNameList.getByText(name).hover();
    await this.addedApiTokenNameList.getByText(name).locator('xpath=../..').getByTestId('api-token-delete-button').click();
    await expect(this.deleteApiFormDialog).toBeVisible();
    await expect(this.dialogDeleteConfirmationButton).toBeVisible();
    await this.dialogDeleteConfirmationButton.click();
  }

  /**
   * verification of apitoken page by checking the visibilty of that 
   */
  async isApiTokenPageValid() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(2000);
    if (await this.genericEmptyState.isVisible()) {
      console.log("No Api Tokens Added");
    } else {
      await expect(this.apiGroupPage).toBeVisible();
      await expect(this.apiGroupPageHeader).toBeVisible();
      await expect(this.apiGroupSearchBox).toBeVisible();
    }
  }

  /**
   * 
   * @param expirationDetails if you want to set no expiration then just pass type , and we are capturing the locator through text 
   * @param isOldExpiredToken if token was already there but got expired , so we are updating that 
   */

  async setExpiratonDateForToken(expirationDetails: AllTypes.apiTokenPage.tokenExpirationDetails, isOldExpiredToken: boolean = false) {
    if (isOldExpiredToken) {
      await this.regenerateTokenButton.click();
    }
    await this.expirationDateOpeningDropdown.click();
    await this.page.locator(`//*[text()="${expirationDetails.type}"]`).click();
    if (expirationDetails.type.toLowerCase() == "custom") {
      await this.page.locator('//*[@class="DateInput DateInput_1"]').click();
      await this.setCustomDate(expirationDetails.day!, expirationDetails.month!, expirationDetails.year!);
    }
    if (isOldExpiredToken) {
      await this.modalRegenerateButton.click();
      await this.page.reload();
    }

  }





  /**
   * 
   * @param data -> name of the token ,and expiration details and permissions -> these are objects basically containing some optional params
   * @returns -> this will return you the updated or newly generated api token 
   */

  async editOrGenerateNewToken(data: AllTypes.apiTokenPage.apiTokenObject): Promise<string> {
    let isNewToken = await this.clickOnTokenOrCreateNew(data.tokenName!);
    await this.superAdminPermissionText.waitFor({ timeout: 12000 });
    isNewToken ? await this.apiTokenName.fill(data.tokenName!) : console.log('token name already present');
    isNewToken ? await this.setExpiratonDateForToken(data.expirationDetails!) : !isNewToken && await this.tokenExpiredText.isVisible() ? await this.setExpiratonDateForToken(data.expirationDetails!, true) : console.log('token already exists and is updated');
    if (isNewToken || (!isNewToken && !await this.page.locator(`//*[@data-testid="${data.permissionData.permissionType}-radio-button"]`).isChecked())) {
      await this.setSuperAdminOrSpecificPermissions(data);
      await this.clickOnSaveOrUpdateTokenButton();
    }
    return await this.fetchTokenValue(data.tokenName!);
  }
  async clickOnTokenOrCreateNew(tokenName: string): Promise<boolean> {
    var isNewToken = true;
    await this.apiTokenGenerateButton.waitFor({ timeout: 6000 });
    try {
      await this.searchTokenInputField.fill(tokenName, { timeout: 6000 });
      await this.page.keyboard.press('Enter');
      await this.apitokenListDiv.first().locator(`//*[text()="${tokenName}"]`).click({ timeout: 30000 });

      isNewToken = false;
    }
    catch (error) {
      await this.apiTokenGenerateButton.click();
      await this.apiTokenName.fill(tokenName);
    }
    return isNewToken;
  }


  /**
   * Super admin permission -> use this string to set super admin permission  
   * @param data -> this function is to set the permission whether super admin or custom , the properties of objects are optional use accordingly
   * for custom permission you can use this method for devtron apps , helm apps and jobs 
   */

  async setSuperAdminOrSpecificPermissions(data: AllTypes.apiTokenPage.apiTokenObject) {
    await this.page.locator(`//*[@data-testid="${data.permissionData.permissionType}-radio-button-span"]`).click();
    if (data.permissionData.specificPermission) {
      await this.page.locator(`//*[@role="tablist"]//*[text()="${data.permissionData.specificPermission.ResourceType}"]`).click();
      try {
        await this.deletePermissionRowIcon.waitFor({ timeout: 6000 });
        await this.deletePreviousPermissions();
      }
      catch (error) {
        console.error('no previous record found');
      }
      var permissionHeadings = Object.keys(data.permissionData.specificPermission.permissions);
      for (const key of permissionHeadings) {
        let locatorToClick: Locator = key == "role" && data.permissionData.specificPermission.ResourceType != 'Jobs' ? this.page.locator(`//*[@class='css-qyb8h2-control']`) : key == "role" && data.permissionData.specificPermission.ResourceType == 'Jobs' ? this.page.locator(`//*[contains(@class,'jobs-role-selector__control')]`) : this.page.locator(`//*[contains(@class,'dropdown-for-${key}__control')]`)
        await expect(async () => {
          await locatorToClick.first().click();
          await this.dropdownList.first().waitFor({ timeout: 8000 });
        }).toPass({ timeout: 2 * 1000 * 60 });
        for (const element of data.permissionData.specificPermission.permissions[key]) {
          await this.dropdownList.locator(`//*[text()="${element}"]`).click();
          if (key == "View only") {
            await this.page.locator(`//*[text()="${element}"]/parent::div`).click();
          }
        }
      }
    }
  }

  async setSpecificPermissionForK8sResources(data: AllTypes.Rbac.permissionDetailsForK8s, isPermissionGroup: boolean = false) {
    await this.specificUserPermissionRadioBox.click();
    await this.k8sPermissionButton.click();
    await this.addK8sPermissionLink.waitFor();
    await this.deletePreviousPermissions(true);
    await this.addK8sPermissionLink.click();
    let keys = Object.keys(data);
    for (const key of keys) {
      if ((key == 'status' && isPermissionGroup) || (process.env.clusterType?.includes('oss') && key == "status")) {
        continue;
      }
      let keyToClickForDropdown: string = key == 'api' ? 'api-group' : key == "status" ? 'user-status' : key
      let locatorForDropdown: Locator = key == 'status' ? this.page.locator(`//*[@class="drawer right show"]//*[contains(@class,"user-status-dropdown__control")]`).first() : this.page.locator(`//*[contains(@class,'${keyToClickForDropdown}-dropdown__control')]`);
      await locatorForDropdown.click();
      if (key == "cluster") {
        await BaseTest.waitForApiResponse(this.page, 'orchestrator/user/resource/options/cluster/namespaces/alpha1', 200, async function (page: Page) { await page.locator(`//*[@role="listbox"]//*[text()="${data[key]}"]`).click() })
      }
      else {
        await this.page.locator(`//*[@role="listbox"]//*[text()="${data[key]}"]`).click();
      }
      if (key != "status" && key != "role" && key != 'cluster')
        await locatorForDropdown.click();
    }
    await this.k8sPermissionSaveButton.click();

  }


  /**
   * this function is to remove the previous permission rows assigned to users 
   */

  async deletePreviousPermissions(isk8sResources: boolean = false) {
    await expect(async () => {
      var elements = await this.deletePermissionRowIcon.all();
      console.log('element count is ' + elements.length);
      for (const element of elements) {
        await element.click();
      }
      if (isk8sResources) {
        await expect(this.deletePermissionRowIcon).toBeHidden();
      }
      else {
        await expect(this.selectProjectText).toBeVisible();
        await expect(this.deletePermissionRowIcon).toHaveCount(1);
      }
    }).toPass({ timeout: 3 * 1000 * 60 });

  }
  async checkWhetherpermissionRowExistOrNot() {
    await this.addPermissionButton.waitFor();
    console.log('checking visibility' + await this.selectProjectText.isVisible());
    console.log()
    if (await this.selectProjectText.isVisible() && await this.deletePermissionRowIcon.count() == 1) {
      return false;
    }
    return true;
  }

  /**
   * generic function to save or update tokens or users
   */

  async clickOnSaveOrUpdateTokenButton(rbacRelatedData?: { isEligible: boolean }) {
    let messageToVerify: string = rbacRelatedData?.isEligible ? 'Success' : 'Not authorized';
    if (rbacRelatedData) {
      await BaseTest.checkToast(this.page, this.genericSaveAndUpdateButton, messageToVerify);
    }
    else {
      await this.genericSaveAndUpdateButton.click();
    }
    try {
      await this.page.locator('//*[@data-testid="generated-token"]').waitFor({ timeout: 10000 });
      await this.closeModalIcon.click();
      if(process.env.HIDE_API_TOKEN === "false") {
        await this.superAdminPermissionText.waitFor({ timeout: 6000 });
      }
    }
    catch (error) {
      console.log('modal did not opened');
    }
  }


  /**
   * 
   * @param tokenName 
   * @returns the token generated value
   */

  async fetchTokenValue(tokenName: string): Promise<string> {
    let token: string | null
    try {
      await this.generatedTokenField.waitFor({ timeout: 5000 });
    }
    catch (error) {
      await this.apitokenPageLink.click();
      await this.searchTokenInputField.fill(tokenName);
      await this.page.keyboard.press('Enter');
      await this.apitokenListDiv.first().locator(`//*[text()="${tokenName}"]`).click();
      if(process.env.HIDE_API_TOKEN === "false") {
       await this.generatedTokenField.waitFor({ timeout: 10000 });
      }
    }
    if(process.env.HIDE_API_TOKEN === "true") {
      await this.regenerateTokenButton.click({delay: 1000});
      await this.modalRegenerateButton.click({delay: 1000});
      await this.page.waitForTimeout(5000);
    }
    token = await this.generatedTokenField.textContent();
    return token!;
  }
  async assignPermissionGroup(permissionGroupName: string) {
    await expect(async () => {
      await this.page.locator(`//*[contains(@class,'permission-groups-dropdown__control')]`).click();
      await expect(this.page.locator('//*[@role="option"]').first()).toBeVisible({ timeout: 5000 });
    }).toPass({ timeout: 2 * 1000 * 60 });
    await this.page.keyboard.type(permissionGroupName);
    await this.page.keyboard.press('Enter');
  }

  async setPermissionsForChartGroups(data: AllTypes.Rbac.dataForAcessForChartGroups) {
    await this.specificUserPermissionRadioBox.click();
    await this.chartGroupsButton.click();
    await this.page.locator(`//*[contains(@class,"chart-permission__row")]`).waitFor();
    if (await this.createCheckBoxInputField.nth(1).isChecked() != data.create)
      await this.createCheckBoxClickableField.nth(1).click();
    if (data.editForSpecificChartNames) {
      await this.dropdownForEditCharts.click();
      await this.specificChartsGroupText.click();
      await this.dropdownForSpecificChartGroups.click({ force: true });
      await expect(this.page.locator('//*[@role="listbox"]')).toBeVisible({ timeout: 5000 });
      for (let element of data.editForSpecificChartNames) {
        await this.page.keyboard.type(element);
        await this.page.keyboard.press('Enter');
      }
    }
  }


}