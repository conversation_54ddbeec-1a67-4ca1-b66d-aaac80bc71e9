import { Page, Locator, expect, Keyboard } from '@playwright/test';
import { BaseTest } from '../../../utilities/BaseTest'
import { BasePage } from '../../BasePage';
import { GlobalConfigurationsUrls } from '../../../enums/UrlNavigations/GlobalConfigurations';

export class ClusterAndEnvironments extends BasePage {
    // Cluster-related elements
    readonly addClusterButton: Locator;
    readonly deleteClusterButton: Locator;
    readonly addIsolatedClusterMenuItem: Locator;
    readonly virtualClusterNameInput: Locator;
    readonly saveClusterButton: Locator;

    // Environment-related elements
    readonly environmentNameField: Locator;
    readonly namespaceField: Locator;
    readonly environmentDescription: Locator;
    readonly environmentSaveButton: Locator;

    // Radio buttons for environment type
    readonly nonProductionRadioButton: Locator;
    readonly productionRadioButton: Locator;

    // Other elements
    readonly clusterAndEnvironmentButton: Locator;

    readonly actionMenuItemEditCluster: Locator;
    readonly actionMenuItemDelteCluster: Locator;
    readonly searchClusterEnvironment: Locator;
    readonly clusterFilterOnEnvPage: Locator;
    readonly dialogDeleteConfirmationInputBox: Locator;


    constructor(public page: Page) {
        super(page);
        // Cluster-related elements
        this.addClusterButton = page.getByTestId("add-cluster-button");
        this.deleteClusterButton = page.locator('//*[@data-testid="delete-virtual-cluster-button" or @data-testid="delete_cluster"]');
        this.addIsolatedClusterMenuItem = page.getByTestId("action-menu-item-add-isolated-cluster");
        this.virtualClusterNameInput = page.getByTestId("cluster_name");
        this.saveClusterButton = page.getByTestId("save-virtual-cluster-button");

        // Environment-related elements
        this.environmentNameField = page.getByTestId("envName");
        this.namespaceField = page.getByTestId("namespace");
        this.environmentDescription = page.getByPlaceholder("Add a description for this environment");
        this.environmentSaveButton = page.getByTestId("save-and-update-environment");

        // Radio buttons for environment type
        this.nonProductionRadioButton = page.getByTestId("non-production");
        this.productionRadioButton = page.getByTestId("production");

        // Other elements
        this.clusterAndEnvironmentButton = this.page.locator(`//*[@data-testid="Clusters & Environments-page" or @data-testid="Clusters-page"]`);
        this.actionMenuItemEditCluster = this.page.getByTestId("action-menu-item-edit-cluster");
        this.actionMenuItemDelteCluster = this.page.getByTestId("action-menu-item-delete-cluster");
        this.searchClusterEnvironment = page.getByTestId('search-cluster-env');
        this.clusterFilterOnEnvPage = page.locator(`.cluster-filter__input`)
        this.dialogDeleteConfirmationInputBox = this.page.getByTestId('delete-cluster-confirmation-input');

    }

    // Navigates to the Clusters & Environments page
    async goToClustersAndEnvironmentsPage() {
        await BaseTest.clickOnDarkMode(this.page);
        await this.page.goto(`${process.env.BASE_SERVER_URL}${GlobalConfigurationsUrls.clusterAndEnvironments}`);
        // Click on the button to access Clusters & Environments page
        await BaseTest.clickOnToolTipOkayButton(this.page);
        await this.clusterAndEnvironmentButton.click();
    }

    // Adds a virtual cluster
    async addVirtualCluster(virtualClusterName: string) {
        // Click on the button to add a cluster
        await this.addClusterButton.click();
        // Switch to add virtual cluster
        await this.addIsolatedClusterMenuItem.click();
        // Fill in the name for the virtual cluster
        await this.virtualClusterNameInput.fill(virtualClusterName);
        // Check for a toast message indicating successful save
        await BaseTest.checkToast(this.page, this.saveClusterButton, "Successfully saved.");
    }

    // Creates an environment
    async clickOnAddEnvironmentButtonOnClusterPage(clusterName:string){
        await this.searchCluster(clusterName);
        await this.page.locator('//*[contains(@data-testid,"add-env-")]').click({force:true});
    }
    async createEnvironment(clusterName: string, clusterType: string, environmentName: string, environmentType: string) {
        // Click on the button to add an environment for the specified cluster
        //await this.page.getByTestId(`add-environment-button-${clusterName}`).click();
        // Fill in the environment name
        await expect(async () => {
            await this.page.locator(`.create-env-select-cluster__input`).fill(clusterName);
            await this.page.keyboard.press('Enter');
            await this.environmentNameField.fill(environmentName);
            // Fill in the namespace
            await this.namespaceField.fill(environmentName);
            // If the environment type is "production" and the cluster type is not "virtual", select the production radio button
            if (environmentType === "production" && clusterType !== "virtual") {
                await this.productionRadioButton.click();
            }
            // Fill in the environment description
            await this.environmentDescription.fill(`${environmentName}-description`);
            // Check for a toast message indicating successful save
            await BaseTest.checkToast(this.page, this.environmentSaveButton, "Success");
        }).toPass({ timeout: 4 * 1000 * 60 })

    }
    // Function to delete one or more environments
    async deleteEnvironment(environmentName: string[],clusterName: string[]) {
        for (let i = 0; i < environmentName.length; i++) {
            await this.searchEnvironment(environmentName[i], clusterName[i]);
            // Hover over the environment container
            await this.page.getByText(`${environmentName}`).nth(0).hover();
            // Click on the delete button for the environment
            await this.page.getByTestId(`env-edit-button-${environmentName[i]}`).click();
            await this.page.getByTestId("environment-delete-btn").click();
            // Check for toast message indicating successful deletion
            await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, "Successfully deleted");
        }
    }
    //return true if cluster present and false if not
    async searchCluster(clusterName:string):Promise<Boolean>{
        await this.searchClusterEnvironment.waitFor({state:'visible'});
        await this.searchClusterEnvironment.fill(clusterName);
        await this.page.keyboard.press('Enter');
        if(await this.page.getByTestId('clear-filters').isVisible({timeout:5*1000})){
            console.log("Cluster is not present");
            await this.page.getByTestId('clear-filters').click();
            return false;
        }
        return true;

    }





    // Function to update the name of a virtual cluster
    async updateVirtualCluster(virtualClusterName: string, newVirtualClusterName: string) {
         await this.page.goto(`${process.env.BASE_SERVER_URL} ${GlobalConfigurationsUrls.clusterAndEnvironments}`);
        // Click on the edit button for the virtual cluster
            await this.searchCluster(virtualClusterName);
            await this.page.getByText(virtualClusterName).waitFor({state:'visible'});
            await this.page.locator(`//*[text()='${virtualClusterName}']`).hover();
            await this.page.locator(`//*[text()='${virtualClusterName}']/ancestor::*[@class=' generic-table__cell']/following-sibling::*//*[contains(@data-testid,'edit-cluster-')]`).click({force:true});
            // Fill in the new virtual cluster name
            await this.virtualClusterNameInput.fill(newVirtualClusterName);
            // Check for toast message indicating successful update
            await BaseTest.checkToast(this.page, this.saveClusterButton, "Success");
            await this.page.getByTestId('clear-search').click();
    }

    // Function to update the description of an environment
    async updateEnvironment(environmentName: string,clusterName:string) {
        // Click on the environment to update
        await this.searchCluster(clusterName);
        await this.page.getByText(clusterName).waitFor({state:'visible'});
        await this.page.getByText(clusterName).click();
        await expect(async () => {
            await this.page.getByText(`${environmentName}`).nth(0).waitFor({state:'visible'});
            await this.page.getByText(`${environmentName}`).nth(0).hover()
            await this.page.getByTestId(`env-edit-button-${environmentName}`).waitFor({state:'visible'});
            await this.page.getByTestId(`env-edit-button-${environmentName}`).click();
            // Fill in the updated environment description
            await this.environmentDescription.fill(`updated-description`);
            // Check for toast message indicating successful update
            await BaseTest.checkToast(this.page, this.environmentSaveButton, "Success");
        }).toPass({ timeout: 3 * 1000 * 60 });

    }

    async searchEnvironment(environmentName: string, clusterName: string): Promise<boolean> {
        await this.page.goto(`${process.env.BASE_SERVER_URL}${GlobalConfigurationsUrls.envSectionInClustersAndEnvironmentsPage}`)
        await this.searchClusterEnvironment.waitFor({state:'visible'});
        await this.searchClusterEnvironment.fill(environmentName);
        await this.page.keyboard.press('Enter')
        await this.clusterFilterOnEnvPage.waitFor({state:'visible'});
        await this.clusterFilterOnEnvPage.fill(clusterName);
        await this.page.keyboard.press('Enter')
        
       // Locate the environment container within the cluster
       const environmentDiv = this.page.getByText(`${environmentName}`).nth(0);
        if (await environmentDiv.isVisible({ timeout: 3000 })) {
            console.log("Environment found.");
            return true;
        }

        // Environment not found
        return false;
    }
    

    async createEnvironmentBasedOnRequirement(environmentName: string[], clusterName: string, clusterType: string, environmentType: string[]) {
        for (let i = 0; i < environmentName.length; i++) {
            // If environment doesn't exist, create it
            if (await this.searchEnvironment(environmentName[i], clusterName) === false) {
                await this.createEnvironment(clusterName, clusterType, environmentName[i], environmentType[i]);
            } else if (clusterType !== "virtual") {
                // If not a virtual cluster, verify production or non-production status of existing environment
                await this.verifyProductionOrNonProductionEnvironment(environmentName[i], environmentType[i]);
            }
        }
    }

    async   deleteCluster(clusterName: string) {
        await this.page.goto(`${process.env.BASE_SERVER_URL}${GlobalConfigurationsUrls.clusterAndEnvironments}`);
        await this.searchCluster(clusterName);


        await this.page.locator('//*[contains(@data-testid,"cluster-actions-")]').click({force:true});
        await this.actionMenuItemDelteCluster.click();
        await this.dialogDeleteConfirmationInputBox.fill(clusterName);
        // Check for success toast message
        await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, "Successfully deleted");
    }


    async verifyProductionOrNonProductionEnvironment(environmentName: string, environmentType: string) {
        // Click on the environment
        await this.page.getByTestId(`env-${environmentName}`).click();

        // Select production or non-production radio button based on environment type
        if (environmentType === "production") {
            await this.productionRadioButton.click();
        } else if (environmentType === "non-production") {
            await this.nonProductionRadioButton.click();
        }

        // Click the save button
        await this.environmentSaveButton.click();
    }
    async isClusterAdded(virtualClusterName: string) {
        await expect(this.addClusterButton).toBeVisible();
        if (await this.page.locator(`//*[@data-testid="${virtualClusterName}-cluster-container"]`).isVisible()) return true;
        return false;
    }
}