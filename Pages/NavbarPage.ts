import { Locator, <PERSON> } from "playwright";
import { BasePage } from "./BasePage";
import { sidePanelParentComponentsEnum } from "../enums/Navbar/sidePanelParentComponentsEnum";
import { parentChildMapping } from "../DTOs/Navbar/parentAndChildComponentMappingDto";

export class NavbarPage extends BasePage{

    readonly navBarComponent:Locator;
    readonly navBarSearchInputField:Locator;
    readonly navBarGlobalSearchInputField:Locator;
    readonly genericLocatorForChildEntitiesInNavbar:Locator;

    constructor (public page: Page) {
        super(page);
        this.navBarComponent= this.page.locator(`//*[contains(@class,'navigation ')]`);
        this.navBarSearchInputField= this.page.getByTestId('search-bar');
        this.navBarGlobalSearchInputField= this.page.locator(`//*[@aria-label="Search"]`);
        this.genericLocatorForChildEntitiesInNavbar= this.page.locator(`//*[contains(@class,'nav-item') or @role="treeitem"]`);
    }   

    public async navigateToAnySectionInNavBar<k extends sidePanelParentComponentsEnum>(parentEntity:k , childEntity:parentChildMapping[k] ):Promise<void>{
      await this.page.goto(process.env.BASE_SERVER_URL!);
      let parentComponentLocator:Locator=await this.setLocatorForSidePanelParentComponents(parentEntity);
      await parentComponentLocator.click();
      if(childEntity){
            await this.searchChildEntitiesInNavbar(childEntity);
            await this.helpButton.click({force:true});
      }
    }

    private async searchChildEntitiesInNavbar(entityName:string){
        await this.navBarComponent.locator(this. navBarSearchInputField).fill(entityName);
        await this.page.keyboard.press('Enter');
        await this.genericLocatorForChildEntitiesInNavbar.first().click();
    }



    private async setLocatorForSidePanelParentComponents(parentComponentName:sidePanelParentComponentsEnum):Promise<Locator>{
        let locatorIndex:number = parentComponentName == sidePanelParentComponentsEnum.applicationManagement ? 1 :0;
        console.log("printing the index is "+locatorIndex);
        let locatorForParentComponent:Locator =parentComponentName==sidePanelParentComponentsEnum.globalSearch? this.navBarComponent.locator(this.navBarGlobalSearchInputField) : this.navBarComponent.locator(`//*[@href="${parentComponentName}"]`).nth(locatorIndex);
        console.log('locator is '+locatorForParentComponent);
        return locatorForParentComponent;
    }
}