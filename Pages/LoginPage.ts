import { Page, Locator, expect } from '@playwright/test';
import { getOTP } from '../utilities/ThirdPartyClients/googleSsoTokenGenerator';

export class LoginPage {
  readonly passwordTextbox: Locator;
  readonly loginButton: Locator;
  readonly loginAsAdministratorLink: Locator;
  readonly loginWithLdapSsoButton: Locator;
  readonly ldapEmailInputField: Locator;
  readonly ldapPasswordInputField: Locator;
  readonly ldapLoginButton: Locator;
  constructor(private page: Page) {
    this.passwordTextbox = page.getByTestId('password-textbox');
    this.loginButton = page.getByTestId('login-button');
    this.loginAsAdministratorLink = page.getByRole('link', { name: 'Login as administrator' });
    this.loginWithLdapSsoButton = this.page.getByTestId('login-with-ldap');
    this.ldapEmailInputField = this.page.locator('//*[@placeholder="email address"]');
    this.ldapPasswordInputField = this.page.locator(`//*[@placeholder="password"]`);
    this.ldapLoginButton = this.page.locator(`#submit-login`);
  }

  /**
   * Performs the login process as an administrator.
   * @param password - The password for the administrator login.
   * @param serverBaseURL - The base URL of the server.
   */
  async loginAsAdministrator(password: string, serverBaseURL: string) {
    try {
      // Navigate to the server base URL
      await this.page.goto(serverBaseURL);

      // Navigate to the login page
      await this.page.goto(`${serverBaseURL}/dashboard/login/sso?continue=/app/list/d`);

      // Click the "Login as administrator" link
      await this.loginAsAdministratorLink.click();

      // Click the password textbox and fill in the password
      await this.passwordTextbox.click();
      await this.passwordTextbox.fill(password);

      // Click the login button
      await this.loginButton.click();
    } catch (error) {
      console.error(`An error occurred during login: ${error.message}`);
      throw error; // Rethrow the error to indicate test failure
    }
  }

  async loginWithLdap(userName: string, password: string) {
    await this.loginWithLdapSsoButton.click();
    await expect(async () => {
      await this.page.reload()
      await this.ldapEmailInputField.fill(userName);
      await this.ldapPasswordInputField.fill(password);
      await this.ldapLoginButton.click(); 
    }).toPass({ timeout: 3 * 1000 * 60 })
  }

}
