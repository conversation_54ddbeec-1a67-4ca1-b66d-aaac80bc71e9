import { BasePage } from '../../BasePage';
import { Page, Locator, expect } from '@playwright/test';
import { PhaseStatusDTO } from '../../../DTOs/Global Configurations/Clusters And Environments/InfraProvisioning/PhaseStatusDTO';
import { PhaseStatusEnum } from '../../../enums/Global Configurations/Clusters and Environments/InfraProvisioning/PhaseStatusEnum';
import { ClusterInfoOnInstallationPageDTO } from '../../../DTOs/Global Configurations/Clusters And Environments/InfraProvisioning/ClusterInfoOnInstallationPageDTO';
import { BaseTest } from '../../../utilities/BaseTest';

export class InfraProvisioningPage extends BasePage {
  readonly resourceBrowserButton: Locator;
  readonly addNewClusterButton: Locator;
  readonly createEKSClusterTab: Locator;
  readonly eKSAutoModeToggleButton: Locator;
  readonly nameInputbox: Locator;
  readonly regionDropdown: Locator;
  readonly vpcCIDRIputbox: Locator;
  readonly authenticationModeDropdown: Locator;
  readonly enableIRSAToggleButton: Locator;
  readonly allowPublicAccessToggleButton: Locator;
  readonly teamDropdown: Locator;
  readonly environmentDropdown: Locator;
  readonly clusterVersionsDropdown: Locator;
  readonly addAvailabilityZonesButton: Locator;
  readonly addPrivateAccessCIDRsButton: Locator;
  readonly cancelCreateClusterButton: Locator;
  readonly createClusterButton: Locator;
  readonly refreshButtonOnResourceBrowser: Locator;
  readonly nameTooltipMessage: Locator;
  readonly enableIRSATooltipMessage: Locator;
  readonly regionTooltipMessage: Locator;
  readonly vpcCidrTooltipMessage: Locator;
  readonly authenticationModeTooltipMessage: Locator;
  readonly allowPublicAccessTooltipMessage: Locator;
  readonly teamTooltipMessage: Locator;
  readonly environmentTooltipMessage: Locator;
  readonly clusterVersionTooltipMessage: Locator;
  readonly deleteClusterButton: Locator;
  readonly menuButton: Locator;
  readonly searchBoxOnClusterListPage: Locator;
  readonly dialogDeleteConfirmationInputBox: Locator;
  readonly editClusterConfigButton: Locator;
  readonly goToClusterOverPageButton: Locator;
  readonly clusterInstallationStatus: Locator;

  constructor(public page: Page) {
    super(page);
    // Assign locators to corresponding UI elements on the resource Browser.
    this.addNewClusterButton = page.getByTestId("add-cluster-button");
    this.resourceBrowserButton = page.getByTestId("click-on-resource-browser");
    this.createEKSClusterTab = page.getByTestId("create-eks-cluster-tab");
    this.eKSAutoModeToggleButton = page.getByTestId("eks-mode-toggle");
    this.nameInputbox = page.locator('#root_name');
    this.regionDropdown = page.locator("#root_region");
    this.vpcCIDRIputbox = page.locator("#root_vpc_cidr");
    this.authenticationModeDropdown = page.locator("#root_auth_mode");
    this.enableIRSAToggleButton = page.locator('#root_enable_irsa [data-testid="handle-toggle-button"]');
    this.allowPublicAccessToggleButton = page.locator('#root_public_access [data-testid="handle-toggle-button"]');
    this.teamDropdown = page.locator("#root_resource_tags_team");
    this.environmentDropdown = page.locator('#root_resource_tags_environment [data-testid="overview-project-edit-dropdown"]');
    this.clusterVersionsDropdown = page.locator('#root_cluster_version [data-testid="overview-project-edit-dropdown"]');
    this.addAvailabilityZonesButton = page.locator('button:has-text("Add Availability Zones")');
    this.addPrivateAccessCIDRsButton = page.locator('button:has-text("Add Private access CIDRs")');
    this.cancelCreateClusterButton = page.getByTestId("cancel-create-cluster-button");
    this.createClusterButton = page.getByTestId("create-cluster-button");
    this.refreshButtonOnResourceBrowser = page.getByTestId("cluster-list-refresh-button");
    this.nameTooltipMessage = page.locator('label[for="root_name"] span.text-underline-dashed-300');
    this.regionTooltipMessage = page.locator('label[for="root_region"] span.text-underline-dashed-300');
    this.vpcCidrTooltipMessage = page.locator('label[for="root_vpc_cidr"] span.text-underline-dashed-300');
    this.enableIRSATooltipMessage = page.locator('label[for="root_enable_irsa"] span.text-underline-dashed-300');
    this.authenticationModeTooltipMessage = page.locator('label[for="root_auth_mode"] span.text-underline-dashed-300');
    this.allowPublicAccessTooltipMessage = page.locator('label[for="root_public_access"] span.text-underline-dashed-300');
    this.teamTooltipMessage = page.locator('label[for="root_resource_tags_team"] span.text-underline-dashed-300');
    this.environmentTooltipMessage = page.locator('label[for="root_resource_tags_environment"] span.text-underline-dashed-300');
    this.clusterVersionTooltipMessage = page.locator('label[for="root_cluster_version"] span.text-underline-dashed-300');
    this.deleteClusterButton = this.page.getByTestId('action-menu-item-DELETE');
    this.menuButton = this.page.getByTestId('resource-browser-page-header-popup-button');
    this.searchBoxOnClusterListPage = this.page.getByTestId('search-bar');
    this.dialogDeleteConfirmationInputBox = this.page.getByTestId('delete-cluster-confirmation-input');
    this.editClusterConfigButton = this.page.getByTestId('edit-cluster-config-button');
    this.goToClusterOverPageButton = this.page.getByTestId('go-to-cluster-overview');
    this.clusterInstallationStatus = this.page.getByTestId('cluster-installation-status')
  }

  async goToInfraProvisioningForm(url: string) {
    await this.page.goto(url);
    await this.resourceBrowserButton.click();
    await this.page.waitForLoadState('load');
    await this.refreshButtonOnResourceBrowser.hover();
    await this.addNewClusterButton.click();
    await this.createEKSClusterTab.click();
  }

  async verifyInfraProvisioningForm() {
    await this.validateAllTooltipMessages();
  }

  async validateAllTooltipMessages() {
    const tooltipTriggers: Locator[] = [
      this.nameTooltipMessage,
      this.enableIRSATooltipMessage,
      this.regionTooltipMessage,
      this.vpcCidrTooltipMessage,
      this.authenticationModeTooltipMessage,
      this.allowPublicAccessTooltipMessage,
      this.teamTooltipMessage,
      this.environmentTooltipMessage,
      this.clusterVersionTooltipMessage,
    ];

    const expectedMessages: string[] = [
      'The name of the EKS cluster. Must be unique and up to 50 characters.',
      'Enable IAM Roles for Service Accounts (IRSA)',
      'AWS region where the EKS cluster will be provisioned.',
      'The CIDR block for the VPC that will be created for the cluster.',
      'Authentication method for the cluster. Options: API, CONFIG_MAP, or both.',
      'Determines whether the EKS control plane endpoint is publicly accessible.',
      'The owning team of the resources for visibility and management.',
      'Deployment environment for the cluster resources (e.g., production, QA).',
      'Kubernetes version to use for the EKS cluster.',
    ];

    for (let i = 0; i < tooltipTriggers.length; i++) {
      const trigger = tooltipTriggers[i];
      const expectedMessage = expectedMessages[i];
      await trigger.hover();
      const tooltip = this.page.locator('div[data-tippy-root] .tippy-box[data-state="visible"] .tippy-content > div');
      await tooltip.waitFor({ state: 'visible' });
      const tooltipText = await tooltip.textContent();
      expect(tooltipText).toContain(expectedMessage);
    }
  }

  /**
   * Selects a value from a React-Select dropdown by role="option"
   * @param dropdownInputSelector CSS selector for the dropdown input (e.g., '#react-select-2-input' or '#react-select-3-input')
   * @param optionText The visible text of the option to select
   */
  async selectExpectedOptionFromExpectedDropdown(
    dropdownInputSelector: string,
    valueYouWantToSelect: string
  ) {
    await this.page.click(dropdownInputSelector);
    // Get all visible options
    const options = this.page.locator('div[role="option"]');
    // Find the exact text match (use regex if needed)
    const exactMatchOption = options.filter({
      hasText: new RegExp(`^${valueYouWantToSelect}$`, 'i')  // Case-insensitive exact match
    });
    await exactMatchOption.first().waitFor({ state: 'visible' });
    await exactMatchOption.first().click();
  }

  async enterClusterNameYouWantToCreate(clusterName: string) {
    await this.nameInputbox.waitFor({ state: 'visible' });
    await this.nameInputbox.fill(clusterName);
  }

  async enterVpcCidr(vpcidr: string) {
    await this.vpcCIDRIputbox.waitFor({ state: 'visible' });
    await this.vpcCIDRIputbox.fill(vpcidr);
  }

  async addAvailabilityZones(zones: string[]) {
    for (let i = 0; i < zones.length; i++) {
      // Click the button to add a new input field
      await this.addAvailabilityZonesButton.click();

      // Wait for the input to appear (adjust selector if needed)
      const inputLocator = this.page.locator('input[id^="root_availability_zones"]');

      // Select the input at the current index
      const input = inputLocator.nth(i);

      // Wait for the input to be visible and fill it
      await input.waitFor({ state: 'visible' });
      await input.fill(zones[i]);
    }
  }

  async addPrivateAccessCidrs(cidrs: string[]) {
    for (let i = 0; i < cidrs.length; i++) {
      // Click the button to add a new input field
      await this.addPrivateAccessCIDRsButton.click();

      // Wait for the input to appear (adjust selector if needed)
      const inputLocator = this.page.locator('input[id^="root_private_access_cidrs"]');

      // Select the input at the current index
      const input = inputLocator.nth(i);

      // Wait for the input to be visible and fill it
      await input.waitFor({ state: 'visible' });
      await input.fill(cidrs[i]);
    }
  }

  async getPhaseData(phaseName: string): Promise<PhaseStatusDTO> {
    const button = this.page.locator(`[data-testid="installation status ${phaseName} state"]`);
    const wrapper = button.locator('xpath=ancestor::div[contains(@class, "flexbox-col")]');
    const section = wrapper.locator('div').nth(1);

    const extractValue = async (field: string): Promise<string> => {
      try {
        const labelSpan = section.locator('span', { hasText: `${field}:` }).first();
        await labelSpan.waitFor({ timeout: 5000 });
        const text = await labelSpan.textContent();
        return text?.split(':').slice(1).join(':').trim() || '';
      } catch {
        return '';
      }
    };

    return {
      status: await extractValue('status'),
      lastTransitionTime: await extractValue('lastTransitionTime'),
      reason: await extractValue('reason'),
      message: await extractValue('message'),
    };
  }





  async VerifyPhaseData(actual: PhaseStatusDTO, expected: PhaseStatusDTO) {
    expect(actual.status).toBe(expected.status);

    expect(actual.reason).toBe(expected.reason);
    expect(actual.message).toBe(expected.message);
  }

  // Gets the phase section element based on phase name
  getPhaseSection(phase: PhaseStatusEnum): Locator {
    return this.page.getByTestId(`phase-${phase.toLowerCase()}-section`);
  }

  getToggleButton(phase: PhaseStatusEnum): Locator {
    return this.page.getByTestId(`installation status ${phase} state`);
  }

  // Clicks the expand/collapse toggle button inside a phase section
  async togglePhaseSection(phase: PhaseStatusEnum): Promise<void> {
    const toggleButton = this.getToggleButton(phase);
    await toggleButton.click();
  }

  // Returns whether status line is visible inside a phase section
  async isStatusVisible(phase: PhaseStatusEnum): Promise<boolean> {
    const section = this.getPhaseSection(phase);
    const statusRow = section.locator('text=status');
    return await statusRow.isVisible();
  }

  async verifyClusterDetailsOnInstallationModal(clusterInfoOnInstallationPageDTO: ClusterInfoOnInstallationPageDTO) {
    // Helper function to extract value by label
    const getValueByLabel = async (label: string): Promise<string> => {
      const locator = this.page.locator(`div.catalog__item:has(span:has-text("${label}")) div.dc__word-break span`);
      return locator.innerText();
    };

    expect(await getValueByLabel('Region')).toBe(clusterInfoOnInstallationPageDTO.Region);
    expect(await getValueByLabel('VPC CIDR')).toBe(clusterInfoOnInstallationPageDTO.VPC_CIDR);
    expect(await getValueByLabel('Authentication Mode')).toBe(clusterInfoOnInstallationPageDTO.Authentication_Mode);
    expect((await getValueByLabel('Enable IRSA')).toLowerCase()).toBe(String(clusterInfoOnInstallationPageDTO.Enable_IRSA).toLowerCase());
    expect((await getValueByLabel('Allow public access')).toLowerCase()).toBe(String(clusterInfoOnInstallationPageDTO.Allow_Public_Access).toLowerCase());
    expect(await getValueByLabel('Cluster Version')).toBe(clusterInfoOnInstallationPageDTO.Cluster_Version);

    const getRawTextByLabel = async (label: string): Promise<string> => {
      const text = await this.page.locator(`div.catalog__item:has(span:has-text("${label}"))`).innerText();
      return text.replace(label, '').trim();
    };

    expect(await getRawTextByLabel('Team')).toBe(clusterInfoOnInstallationPageDTO.Team);
    expect(await getRawTextByLabel('Environment')).toBe(clusterInfoOnInstallationPageDTO.Environment);
    const actualAZs = await this.getAvailabilityZonesListByLabel('Availability Zones');
    this.verifyActualListHaveExpectedValues(actualAZs, clusterInfoOnInstallationPageDTO.Availability_Zones || []);
    expect(await getRawTextByLabel('Private access CIDRs')).toBe(clusterInfoOnInstallationPageDTO.Private_Access_CIDRs?.[0] || '-');
  }

  async createEKSCluster(clusterInfoOnInstallationPageDTO: ClusterInfoOnInstallationPageDTO) {
    await this.goToInfraProvisioningForm(process.env.BASE_SERVER_URL as string);
    await this.enterClusterNameYouWantToCreate(clusterInfoOnInstallationPageDTO.clusterName);
    //await this.toggleSwitchByLabel("root_enable_eks_auto_mode", clusterInfoOnInstallationPageDTO.EKS_AUTO_MODE || false);
    await this.enterVpcCidr(clusterInfoOnInstallationPageDTO.VPC_CIDR);
    await this.selectExpectedOptionFromExpectedDropdown('#react-select-2-input', clusterInfoOnInstallationPageDTO.Region);
    await this.selectExpectedOptionFromExpectedDropdown('#react-select-3-input', clusterInfoOnInstallationPageDTO.Authentication_Mode);
    await this.selectExpectedOptionFromExpectedDropdown('#react-select-4-input', clusterInfoOnInstallationPageDTO.Team);
    await this.selectExpectedOptionFromExpectedDropdown('#react-select-5-input', clusterInfoOnInstallationPageDTO.Environment);
    await this.selectExpectedOptionFromExpectedDropdown('#react-select-6-input', clusterInfoOnInstallationPageDTO.Cluster_Version);
    if (clusterInfoOnInstallationPageDTO.Private_Access_CIDRs) {
      await this.addPrivateAccessCidrs(clusterInfoOnInstallationPageDTO.Private_Access_CIDRs);
    }
    await this.toggleSwitchByLabel("root_enable_irsa", clusterInfoOnInstallationPageDTO.Enable_IRSA);
    await this.toggleSwitchByLabel("root_public_access", clusterInfoOnInstallationPageDTO.Allow_Public_Access);
    if (clusterInfoOnInstallationPageDTO.Availability_Zones) {
      await this.addAvailabilityZones(clusterInfoOnInstallationPageDTO.Availability_Zones);
    }
    await BaseTest.checkToast(this.page, this.createClusterButton, 'Success');
  }

  async toggleSwitchByLabel(forAttribute: string, enable: boolean) {
    const toggleContainer = this.page.locator(`label[for="${forAttribute}"]`).locator('xpath=..');
    const toggleStateText = toggleContainer.locator('span:text-is("true"), span:text-is("false")');
    const toggleSlider = toggleContainer.locator('[data-testid="handle-toggle-button"]');
    const currentStateText = (await toggleStateText.textContent())?.trim().toLowerCase();
    const currentState = currentStateText === 'true';
    if (currentState !== enable) {
      await toggleSlider.click();
    }
  }

  async getAvailabilityZonesListByLabel(label: string): Promise<string[]> {
    const section = this.page.locator(`.catalog__item:has(span:text-is("${label}"))`);
    const valueSpans = section.locator('.flexbox-col .catalog__item--array-bullets span');
    const items = await valueSpans.allTextContents();
    return items.map(item => item.trim());
  }


  verifyActualListHaveExpectedValues(actualList: string[], ExpectedList: string[]): boolean {
    if (actualList.length !== ExpectedList.length) return false;
    const set1 = new Set(actualList.map(item => item.trim()));
    const set2 = new Set(ExpectedList.map(item => item.trim()));
    for (const item of set1) {
      if (!set2.has(item)) return false;
    }
    return true;
  }

  async deleteCluster(clusterName: string) {
    try {
      await this.page.goto(`${process.env.BASE_SERVER_URL}`);
      console.log(`Opening resource browser...`);
      await this.resourceBrowserButton.click();
      await this.page.waitForLoadState('load');
      await this.refreshButtonOnResourceBrowser.hover();
      await this.searchBoxOnClusterListPage.fill(clusterName);
      await this.page.keyboard.press('Enter');
      const clusterRowLink = this.page.locator(
        `[data-testid="cluster-row-${clusterName}"] a.dc__ellipsis-right`,
      ).filter({ hasText: clusterName });
      await clusterRowLink.waitFor({ state: 'visible', timeout: 10000 });
      await clusterRowLink.scrollIntoViewIfNeeded();
      await clusterRowLink.click();
      await this.page.waitForTimeout(500);
      await this.menuButton.click();
      await this.deleteClusterButton.click();
      await this.selectDeleteInstalledClusterCheckbox();
      await this.dialogDeleteConfirmationInputBox.fill(clusterName);
      await this.dialogDeleteConfirmationButton.click();
    } catch (error) {
      throw new Error(`Failed to delete cluster "${clusterName}": ${error instanceof Error ? error.message : error}`);
    }
  }

  async selectDeleteInstalledClusterCheckbox() {
    const checkbox = this.page.locator('input[data-testid="delete-installed-cluster"]');
    const visualCheckboxSpan = this.page.locator('span[data-testid="delete-installed-cluster-chk-span"]');

    try {
      // Wait for checkbox to be attached, but timeout after 5s and ignore if not found
      await checkbox.waitFor({ state: 'attached', timeout: 5000 }).catch(() => {
        console.log('Checkbox not attached within timeout, skipping selection.');
        return;
      });

      const isVisible = await checkbox.isVisible();
      if (!isVisible) {
        console.log('Checkbox is not visible, skipping selection.');
        return;
      }

      const isChecked = await checkbox.isChecked();
      if (!isChecked) {
        console.log('Checkbox is not checked. Clicking visual span to check it...');
        await visualCheckboxSpan.click();
      } else {
        console.log('Checkbox is already checked.');
      }
    } catch (error) {
      throw new Error(`Failed to select delete-installed-cluster checkbox: ${error instanceof Error ? error.message : error}`);
    }
  }




  async VerifyClusterCreationSuccessfully(timeoutMs: number = 17 * 60 * 1000): Promise<void> {
    const pollInterval = 5000; // 5 seconds
    const deadline = Date.now() + timeoutMs;

    console.log(`Polling for "Cluster created" status (timeout: ${timeoutMs / 60000} minutes)...`);

    while (Date.now() < deadline) {
      const remainingMs = deadline - Date.now();
      const remainingMin = Math.floor(remainingMs / 60000);
      const remainingSec = Math.floor((remainingMs % 60000) / 1000);

      try {
        if (await this.clusterInstallationStatus.isVisible()) {
          const text = await this.clusterInstallationStatus.textContent();
          const cleanText = text?.trim();

          console.log(`Cluster status: "${cleanText}" | Time remaining: ${remainingMin}m ${remainingSec}s`);

          if (cleanText?.includes('Cluster created')) {
            console.log('"Cluster created" status confirmed.');
            return;
          }
        } else {
          console.log(`Cluster status element not visible yet. Time remaining: ${remainingMin}m ${remainingSec}s`);
        }
      } catch (e) {
        console.warn(`Error accessing cluster status: ${(e as Error).message}`);
      }

      await this.page.waitForTimeout(pollInterval);
    }

    throw new Error(`Timeout: "Cluster created" status not detected within ${timeoutMs / 60000} minutes.`);
  }



  async goToEditClusterFormViaInsllationStatusPage() {
    this.editClusterConfigButton.click();
  }

  async goToClusterOverviewPageViaInsllationStatusPage() {
    this.goToClusterOverPageButton.click();
  }

}


