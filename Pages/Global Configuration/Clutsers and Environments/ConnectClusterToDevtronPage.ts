import path from 'path';
import fs from 'fs';
import { BasePage } from '../../BasePage';
import { Locator, Page, expect } from 'playwright/test';

export class ConnectClusterToDevtronPage extends BasePage {
  readonly clusterNameInput: Locator;
  readonly serverUrlInput: Locator;
  readonly bearerTokenTextarea: Locator;

  readonly useServerUrlAndTokenRadio: Locator;
  readonly useKubeconfigRadio: Locator;

  readonly productionRadio: Locator;
  readonly nonProductionRadio: Locator;

  readonly connectionTypeDirect: Locator;
  readonly connectionTypeProxy: Locator;
  readonly connectionTypeSshTunnel: Locator;

  readonly useSecureTlsCheckbox: Locator;
  readonly seeMetricsToggle: Locator;

  readonly saveButton: Locator;
  readonly cancelButton: Locator;

  readonly kubeconfigEditor: Locator;
  readonly kubeconfigFileInput: Locator;
  readonly browseFileButton: Locator;
  readonly getClusterButton: Locator;

  readonly validClusterBar: Locator;
  readonly saveSelectedClustersButton: Locator;

  readonly manageCategoriesButton: Locator;
  readonly addCategoriesButton: Locator;
  readonly categoryNameInputs: Locator;
  readonly categoryDescriptionInputs: Locator;
  readonly searchInput: Locator;
  readonly createCategoryButton: Locator;
  readonly categoryWeightInput: Locator;
  readonly submitCreateCategoryButton: Locator;
  readonly saveCategoryButton: Locator;
  readonly clearSearchIcon: Locator;

  readonly environmentNameField: Locator;
  readonly namespaceField: Locator;
  readonly environmentDescription: Locator;
  readonly environmentSaveButton: Locator;
  readonly productionRadioButton: Locator;

  constructor(page: Page) {
    super(page);

    this.useServerUrlAndTokenRadio = page.locator('input[type="radio"][name="trigger-type"][value="BLANK"]');
    this.useKubeconfigRadio = page.getByTestId('add_cluster_from_kubeconfig_file-span');

    this.clusterNameInput = page.locator('input[data-testid="cluster_name"]');
    this.serverUrlInput = page.getByTestId('url');
    this.bearerTokenTextarea = page.getByTestId('token');
    this.productionRadio = page.getByTestId('cluster-production');
    this.nonProductionRadio = page.getByTestId('cluster-non-production');
    this.connectionTypeDirect = page.getByTestId('cluster-connection-direct');
    this.connectionTypeProxy = page.getByTestId('cluster-connection-proxy');
    this.connectionTypeSshTunnel = page.getByTestId('cluster-connection-ssh');
    this.useSecureTlsCheckbox = page.getByTestId('secure-tls-checkbox');
    this.seeMetricsToggle = page.getByTestId('metrics-toggle');
    this.saveButton = page.getByTestId('save_cluster_after_entering_cluster_details');
    this.cancelButton = page.getByTestId('cancel-create-cluster-button');
    this.kubeconfigEditor = page.locator('[contenteditable="true"][data-language="yaml"]');
    this.kubeconfigFileInput = page.getByTestId('select_code_editor');
    this.browseFileButton = page.getByTestId('browse_file_to_upload');
    this.validClusterBar = page.getByTestId('valid_cluster_infocolor_bar');
    this.saveSelectedClustersButton = page.getByTestId('save_cluster_list_button_after_selection');
    this.getClusterButton = page.getByTestId('get_cluster_button');
    this.manageCategoriesButton = page.getByTestId('manage_categories_button');
    this.addCategoriesButton = page.getByTestId('add_categories_button');
    this.searchInput = page.getByTestId('search-category-input');
    this.createCategoryButton = page.getByTestId('create-category-button');
    this.categoryWeightInput = page.getByTestId('category-weight-input');
    this.submitCreateCategoryButton = page.getByTestId('submit-create-category');
    this.categoryNameInputs = page.locator('textarea[placeholder="Eg. staging"]');
    this.categoryDescriptionInputs = page.locator('textarea[placeholder="Enter description"]');
    this.saveCategoryButton = page.getByTestId('save-category-btn');
    this.clearSearchIcon = page.getByTestId('clear-search');
    this.environmentNameField = page.getByTestId("environmentName");
    this.namespaceField = page.getByTestId("namespace");
    this.environmentDescription = page.getByPlaceholder("Add a description for this environment");
    this.environmentSaveButton = page.getByTestId("save-and-update-environment");
    this.productionRadioButton = page.getByTestId("production");
  }
  async selectUseServerUrlAndToken() {
    if (!(await this.useServerUrlAndTokenRadio.isChecked())) {
      await this.useServerUrlAndTokenRadio.click();
    }
  }

  async selectUseKubeconfig() {
    await this.useKubeconfigRadio.click();
  }

  async fillClusterName(name: string) {
    await this.clusterNameInput.fill(name);
  }

  async fillServerUrl(url: string) {
    await this.serverUrlInput.fill(url);
  }

  async fillBearerToken(token: string) {
    await this.bearerTokenTextarea.fill(token);
  }

  async selectProduction(isProduction: boolean) {
    const productionRadio = this.page.locator('input[type="radio"][name="isProd"][value="true"]');
    const nonProductionRadio = this.page.locator('input[type="radio"][name="isProd"][value="false"]');

    const radioToSelect = isProduction ? productionRadio : nonProductionRadio;

    const isChecked = await radioToSelect.isChecked();
    if (!isChecked) {
      await radioToSelect.check();
    }
  }

  async selectConnectionType(type: 'DIRECT' | 'PROXY' | 'SSH') {
    const locator = this.page.locator(`input[type="radio"][name="cluster-connection-type"][value="${type}"]`);

    const isChecked = await locator.isChecked();
    if (!isChecked) {
      await locator.check();
    }
  }

  async toggleSecureTlsConnection(shouldCheck: boolean) {
    const checkbox = this.page.locator('input[type="checkbox"][value="CHECKED"]');

    const isChecked = await checkbox.isChecked();
    if (shouldCheck !== isChecked) {
      await checkbox.check({ force: true }); // or .uncheck() if needed
    }
  }

  async toggleSeeMetrics(enable: boolean) {
    const isChecked = await this.seeMetricsToggle.isChecked();
    if (enable !== isChecked) {
      await this.seeMetricsToggle.click();
    }
  }

  async clickSaveButton() {
    await this.saveButton.click();
  }

  async clickSaveButtonForKubeconfig() {
    await this.getClusterButton.click();
  }
  async clickCancelButton() {
    await this.cancelButton.click();
  }

  /**
   * Types kubeconfig YAML directly into the editor
   */
  async fillKubeconfigManually(yaml: string) {
    await this.kubeconfigEditor.click();
    await this.page.keyboard.press(process.platform === 'darwin' ? 'Meta+A' : 'Control+A');
    await this.page.keyboard.press('Backspace');
    await this.kubeconfigEditor.fill(yaml);
  }

  /**
   * Uploads a kubeconfig file via the hidden file input
   */
  async uploadKubeconfigFile(filePath: string) {
    // await this.browseFileButton.click(); // Optional: click visible "Browse file..." text
    await this.kubeconfigFileInput.setInputFiles(filePath);
  }

  async validateGetClusterAndVerifyStatusAfterSave(clusterName: string) {
    await expect(this.validClusterBar).toBeVisible();
    await expect(this.validClusterBar).toContainText('valid cluster');

    const checkbox = this.page.getByTestId(`checkbox_selection_of_cluster-${clusterName}-chk-span`);
    await checkbox.check();

    await expect(this.saveSelectedClustersButton).toBeEnabled();
    await this.saveSelectedClustersButton.click();

    const row = this.page.getByTestId(`validate-cluster-${clusterName}`);
    await expect(row).toBeVisible();
    await expect(row).toContainText(clusterName);

    const addedLabel = this.page.getByTestId('validate-cluster-Added');
    await expect(addedLabel).toBeVisible();
    await expect(addedLabel).toContainText('Added');

    await expect(this.page.locator('text=Cluster Added')).toBeVisible();
  }

  async ValidateClusterAdditionUsingUrlAndToken(clusterName: string) {
    await expect(this.page.getByTestId(`${clusterName}-cluster-container`)).toBeVisible();

    const clusterTitle = this.page.locator(`[data-testid="${clusterName}-cluster-container"] .list__title`);
    await expect(clusterTitle).toContainText(clusterName);

    const addEnvButton = this.page.getByTestId(`add-environment-button-${clusterName}`);
    await expect(addEnvButton).toBeVisible();

    const manageCategoriesButton = this.page.getByTestId("manage_categories_button");
    await expect(manageCategoriesButton).toBeVisible();
  }



  static getKubeconfigYaml(clusterName: string, serverUrl: string, token: string): string {
    const templatePath = path.join(__dirname, '../../TestData/kubeconfig-template.yaml');
    let yaml = fs.readFileSync(templatePath, 'utf-8');

    yaml = yaml.replace(/__CLUSTER_NAME__/g, clusterName);
    yaml = yaml.replace(/__SERVER_URL__/g, serverUrl);
    yaml = yaml.replace(/__TOKEN__/g, token);

    return yaml;
  }

  async navigateToCategoriesPage() {
    await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/cluster-env`);
    await this.manageCategoriesButton.click();
    await this.page.waitForSelector('.dynamic-data-table__row textarea[data-testid$="-categories-cell"]', { timeout: 10000 });
  }

  async searchCategoryByNameAndValidate(name: string) {
    await this.navigateToCategoriesPage();
    // 1. Fill and trigger search
    await this.searchInput.fill(name);
    await this.searchInput.press('Enter');

    // 2. Wait for search results
    const rowSelector = '.dynamic-data-table__row';
    const rows = this.page.locator(rowSelector);
    await expect(rows.first()).toBeVisible({ timeout: 10000 });

    // 3. Locate the matching row
    const matchingRow = rows.filter({
      has: this.page.locator('[data-testid$="-categories-cell"]', { hasText: name }),
    });

    await expect(matchingRow).toHaveCount(1); // Ensure match exists

    const categoryCell = matchingRow.first().locator('[data-testid$="-categories-cell"]');
    const text = (await categoryCell.textContent())?.trim();

    if (text !== name) {
      throw new Error(`Expected category name "${name}" but found "${text}"`);
    }

    // 4. Clear search
    if (await this.clearSearchIcon.isVisible()) {
      await this.clearSearchIcon.click();
    }
  }


  async addNewCategories(categoryNames: string[], defaultDescription = 'Default description') {
    for (const _ of categoryNames) {
      await this.addCategoriesButton.click();
    }

    for (let i = 0; i < categoryNames.length; i++) {
      const nameInput = this.categoryNameInputs.nth(i);
      const descInput = this.categoryDescriptionInputs.nth(i);

      await expect(nameInput).toBeEnabled();
      await expect(descInput).toBeEnabled();

      await nameInput.fill(categoryNames[i]);
      await descInput.fill(defaultDescription);
    }

    if (await this.saveCategoryButton.isVisible()) {
      await this.saveCategoryButton.click();
    }
  }

  async editCategoryByName(oldName: string, newDescription: string) {
    await this.navigateToCategoriesPage();

    await this.page.waitForSelector('.dynamic-data-table__row textarea[data-testid$="-categories-cell"]', { timeout: 10000 });

    const rows = this.page.locator('.dynamic-data-table__row');
    const rowCount = await rows.count();

    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const categoryTextarea = row.locator('textarea[data-testid$="-categories-cell"]');

      if (await categoryTextarea.count() === 0) continue;
      if (!(await categoryTextarea.isVisible())) continue;

      const categoryName = (await categoryTextarea.inputValue()).trim();

      if (categoryName === oldName) {
        const descriptionTextarea = row.locator('textarea[data-testid$="-description-cell"]');
        await descriptionTextarea.fill('');
        await descriptionTextarea.fill(newDescription);

        await this.saveCategoryButton.click();
        return;
      }
    }
    throw new Error(`Category "${oldName}" not found`);
  }


  async deleteCategoryByName(name: string) {
    await this.navigateToCategoriesPage();
    const rowSelector = '.dynamic-data-table__row';
    const matchingRow = this.page.locator(rowSelector).filter({
      has: this.page.locator('[data-testid$="-categories-cell"]', { hasText: name }),
    });
    const count = await matchingRow.count();
    if (count === 0) {
      throw new Error(`Category "${name}" not found for deletion`);
    }

    const row = matchingRow.first();
    const categoryCell = row.locator('[data-testid$="-categories-cell"]');
    await categoryCell.waitFor({ state: 'visible', timeout: 5000 });
    const categoryName = (await categoryCell.textContent())?.trim();
    if (categoryName !== name) {
      throw new Error(`Category name mismatch: expected "${name}", found "${categoryName}"`);
    }

    const deleteButton = row.locator('[data-testid="dynamic-data-table-row-delete-btn"]');
    await deleteButton.click();

    await this.saveCategoryButton.click();
  }


  async expectCategoryDescriptionByName(name: string, expectedDescription: string) {
    await this.navigateToCategoriesPage();
    const rows = this.page.locator('.dynamic-data-table__row');
    const rowCount = await rows.count();

    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const categoryCell = row.locator('[data-testid$="-categories-cell"]');

      try {
        await categoryCell.waitFor({ state: 'visible', timeout: 3000 });
        const actualName = (await categoryCell.textContent())?.trim();

        if (actualName === name) {
          const descCell = row.locator('[data-testid$="-description-cell"]');

          await descCell.waitFor({ state: 'visible', timeout: 3000 });
          const actualDesc = (await descCell.textContent())?.trim();

          if (actualDesc !== expectedDescription) {
            throw new Error(`Expected description "${expectedDescription}" but got "${actualDesc}"`);
          }

          return;
        }
      } catch (err) {
        console.warn(`Skipping row ${i}:`, err);
      }
    }

    throw new Error(`Category "${name}" not found for description validation.`);
  }


  async selectSpecificCategoryFromDropdown(categoryName: string) {
    const input = this.page.locator('input#assign-category-menu-list');
    const option = this.page.locator(`.assign-category-menu-list__option`, { hasText: categoryName });
    await input.click();
    await input.fill(categoryName);
    await this.page.waitForSelector('.assign-category-menu-list__option');
    await option.click();
  }

  async navigateToAddClusterAndEnvPage() {
    await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/cluster-env/create/cluster/connect-cluster`);
  }

  async validateCategoryAssignmentForCluster(clusterName: string, expectedCategoryName: string) {
    const container = this.page.locator(`[data-testid="${clusterName}-cluster-container"]`);
    await expect(container).toBeVisible();
    const titleParagraphs = container.locator('.list__title p');
    const clusterText = await titleParagraphs.nth(0).textContent();
    const categoryText = await titleParagraphs.nth(1).textContent();
    expect(clusterText?.trim()).toBe(clusterName);
    expect(categoryText?.trim()).toBe(expectedCategoryName);
  }

  /**
   * 
   * @param clusterName 
   * @param clusterType 
   * @param environmentName 
   * @param environmentType 
   * @param categoryName 
   */
  async createEnvironmentWithOptionalCategory(
    clusterName: string,
    clusterType: string,
    environmentName: string,
    environmentType: string,
    categoryName?: string
  ) {
    await this.page.getByTestId(`add-environment-button-${clusterName}`).click();
    await expect(async () => {
      await this.environmentNameField.fill(environmentName);
      await this.namespaceField.fill(environmentName);
      if (environmentType === "production" && clusterType !== "virtual") {
        await this.productionRadioButton.click();
      }
      await this.environmentDescription.fill(`${environmentName}-description`);
      if (categoryName) {
        const categoryDropdownInput = this.page.locator('input#assign-category-menu-list');
        const dropdownOption = this.page.locator('.assign-category-menu-list__option', {
          hasText: categoryName,
        });

        await categoryDropdownInput.click();
        await categoryDropdownInput.fill(categoryName);
        await dropdownOption.click();
      }
      await BasePage.checkToast(this.page, this.environmentSaveButton, "Success");
    }).toPass({ timeout: 4 * 60 * 1000 });
  }

  /**
   * 
   * use this function to delete specific environment
   * @param environmentName 
   */
  async deleteSpecificEnvironment(environmentName: string) {
    const envContainer = this.page.getByTestId(`env-container-${environmentName}`);
    await envContainer.hover();
    const deleteButton = this.page.getByTestId(`env-delete-button-${environmentName}`);
    await deleteButton.click();
    await BasePage.checkToast(this.page, this.dialogDeleteConfirmationButton, "Successfully deleted");
  }

}