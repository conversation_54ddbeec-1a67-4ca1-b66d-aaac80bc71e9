import { Page, Locator, expect } from '@playwright/test';
import { BaseTest } from '../../utilities/BaseTest'
import { BasePage } from '../BasePage';
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");


export class ContainerRegistryPage extends BasePage {
    // Container Registry Elements
    readonly containerRegistryButton: Locator; // Button to access OCI Registry page
    readonly addRegistryButton: Locator; // Button to add a new registry
    readonly registryTypeInput: Locator; // Input field for selecting registry type
    readonly registryNameInput: Locator; // Input field for registry name
    readonly registryUrlInput: Locator; // Input field for registry URL
    readonly registryUserNameInput: Locator; // Input field for registry username
    readonly registryListOfReposInput: Locator; // Input field for listing repositories
    readonly registryTokenInput: Locator; // Input field for registry token
    readonly pushHelmPackCheckbox: Locator; // Checkbox for pushing Helm pack
    readonly useAsChartRepoCheckbox: Locator; // Checkbox for using as chart repo
    readonly setDefaultRegistryCheckbox: Locator; // Checkbox for setting as default registry
    readonly saveRegistryButton: Locator; // Button to save registry settings
    readonly publicRegistryRadioButton: Locator; // Radio button for public registry
    readonly privateRegistryRadioButton: Locator; // Radio button for private registry
    readonly deleteRegistryButton: Locator; // Button to delete registry
    readonly errorMessage: Locator; // Locator for error message display

    // ECR Elements
    readonly userAuthRadioButton: Locator; // Radio button for user authentication
    readonly ecrAccessIdField: Locator; // Input field for AWS access ID
    readonly ecrSecretKeyField: Locator; // Input field for AWS secret key

    // GCP and GCR Elements
    readonly serviceAccountFileInput: Locator; // Input field for GCP service account file

    //Others
    readonly allowOnlySecureConnections: Locator;
    readonly allowInsecureConnection: Locator;
    readonly clickOnAdvancedRegistryUrl: Locator;
    readonly clickOnSecureWithCaCertificate: Locator;
    readonly clickOnPasteCaCertificateInput: Locator;


    constructor(public page: Page) {
        super(page);
        this.containerRegistryButton = page.locator('//*[contains(@data-testid,"OCI Registry-page")]');
        this.addRegistryButton = page.getByTestId("add-registry-button");
        this.registryTypeInput = page.locator('//*[@class="select-container-registry-type__input"]');//Todo:data-testid
        this.registryNameInput = page.getByTestId('id');
        this.registryUrlInput = page.getByTestId('registryUrl');
        this.registryUserNameInput = page.getByTestId("username");
        this.registryListOfReposInput = page.locator('//*[@name="repository-list"]')
        this.registryTokenInput = page.getByTestId("password");
        this.pushHelmPackCheckbox = page.getByTestId('store-container-and-chart-chk-span');
        this.useAsChartRepoCheckbox = page.getByTestId("store-checkbox-chk-span");
        this.setDefaultRegistryCheckbox = page.getByTestId("set-as-default-registry-checkbox-chk-span");
        this.saveRegistryButton = page.getByTestId('container-registry-save-button');
        this.publicRegistryRadioButton = page.getByTestId("oci-prublic-registry-radio-button-span");
        this.privateRegistryRadioButton = page.getByTestId('oci-private-registry-radio-button-span')
        this.deleteRegistryButton = page.getByTestId("delete-container-registry");
        this.errorMessage = page.locator('//*[@class="form__error"]');//Todo :data-testid

        //ECR
        this.userAuthRadioButton = page.getByTestId("user-auth-button-span");
        this.ecrAccessIdField = page.getByTestId("awsAccessKeyId");
        this.ecrSecretKeyField = page.getByTestId("awsSecretAccessKey");

        //GCP and GCR
        this.serviceAccountFileInput = page.getByTestId("artifact-service-account-textbox");

        //Otherss
        this.allowOnlySecureConnections = page.getByText('Allow only secure connection');
        this.allowInsecureConnection = page.getByText('Allow insecure connection');
        this.clickOnAdvancedRegistryUrl = page.getByText('Advanced Registry URL Connection Options')
        this.clickOnSecureWithCaCertificate = page.getByText('Allow secure connection with CA certificate')
        this.clickOnPasteCaCertificateInput = page.locator('//*[@name="certInput"]')
    }


    //A function to click on container registry
    async goToContainerRegistry() {
        // Click on Container Registry button
        await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/host-url`);
        await this.containerRegistryButton.click();
    }

    //A function to select a registry from the dropdown
    async selectRegistryProvider(registryProvider: string) {
        // Click on registry type input field
        await this.registryTypeInput.click();
        // Fill the registry provider into the input field
        await this.registryTypeInput.fill(registryProvider);
        // Press Enter to confirm selection
        await this.page.keyboard.press("Enter");
    }
    //A function to add a public registry for all registry providers
    async addPublicRegistry(registryName: string, registryUrl: string, repoList: string[]) {
        // Click on public registry radio button
        await this.publicRegistryRadioButton.click();
        // Fill the registry name input field
        await this.registryNameInput.fill(registryName);
        // Check if registry URL input field is enabled, fill if enabled
        if (await this.registryUrlInput.isEnabled()) {
            await this.registryUrlInput.fill(registryUrl);
        }
        // Fill the repository list field
        await this.fillRepoListField(repoList);
    }
    //A function to fill the list of repositories seperated by commas
    async fillRepoListField(repoList: string[]) {

        for (var i = 0; i < repoList.length; i++) {
            await this.registryListOfReposInput.fill(repoList[i])
            await this.page.keyboard.press('Enter');
        }
    }

    // A function to add a the common elements in a private registry
    async addPrivateRegistry(registryName: string, registryUrl: string) {
        // Check if private registry radio button is visible, click if so
        if (await this.privateRegistryRadioButton.isVisible()) {
            await this.privateRegistryRadioButton.click();
        }
        // Fill the registry name input field
        await this.registryNameInput.fill(registryName);
        // Check if registry URL input field is enabled, fill if enabled
        if (await this.registryUrlInput.isEnabled()) {
            await this.registryUrlInput.fill(registryUrl);
        }
    }

    //A functon add the user name and token in case of private registry of some registry providers i.e,Docker,Azure,GCP, Quay
    async privateRegistryUserNameAndToken(registryUserName: string, registryToken: string) {
        // Fill the registry username input field
        await this.registryUserNameInput.fill(registryUserName);
        // Fill the registry token input field
        await this.registryTokenInput.fill(registryToken);
    }

    // A function to check the common checkboxes in all registry i.e, push helm packages, use as chart repository
    //and use as default registry
    async checkCommonCheckBoxesOfRegistry(pushHelmPack: boolean, useAsChartRepo: boolean, setDefaultRegistry: boolean, repoList: string[]) {
        if (!process.env.clusterType?.includes('ea')) {
            // Check if push Helm pack checkbox is not checked and needs to be checked
            if (pushHelmPack && await this.pushHelmPackCheckbox.isChecked() === false) {
                await this.pushHelmPackCheckbox.click();
            }
            // Check if use as chart repo checkbox is not checked and needs to be checked
            if (useAsChartRepo && await this.useAsChartRepoCheckbox.last().isChecked() === false) {
                await this.useAsChartRepoCheckbox.last().click();
            }
            // Check if set as default registry checkbox is not checked and needs to be checked
            if (setDefaultRegistry && await this.setDefaultRegistryCheckbox.isChecked() === false) {
                await this.setDefaultRegistryCheckbox.click();
            }
        }
        // Fill repository list field if using as chart repo

        await this.fillRepoListField(repoList);
    }

    //A function for user authentication in the case of ecr registry
    async userAuthInEcrRegistry(accessId: string, secretKey: string) {
        // Click on user authentication radio button
        await this.userAuthRadioButton.click();
        // Fill the ECR access ID field
        await this.ecrAccessIdField.fill(accessId);
        // Fill the ECR secret key field
        await this.ecrSecretKeyField.fill(secretKey);
    }

    //A function to add a service account in the case of GCP and GCR registry.
    async serviceAccountFileinGCP_GCR(fileData: string) {
        // Fill the service account file input field with provided file data
        await this.serviceAccountFileInput.fill(fileData);
    }

    //A function to check the invalid input warning message in case of wrong input in the registry name
    async checkInvalidInputError() {
        // Click on add registry button
        await this.addRegistryButton.click();
        // Array of registry providers
        const registryProvider: string[] = ["ECR", "Docker", "Azure", "GCP", "GCR", "Quay", "Other"];
        // Loop through each registry provider
        for (let i = 0; i < registryProvider.length; i++) {
            // Select registry provider
            await this.selectRegistryProvider(registryProvider[i]);
            // Generate an invalid registry name
            const invalidRegistryName = BaseTest.generateRandomStringWithUpperCaseAndSpecialChars(8);
            // Fill the registry name input field with the invalid name
            await this.registryNameInput.fill(invalidRegistryName);
            // Expect error message to be visible
            await expect(this.errorMessage).toBeVisible();
        }
        // Click on add registry button again to reset
        await this.addRegistryButton.click();
    }
    // A function to generate a array of random string for the names of the registries
    async generateRegistryNamesArray(registryCount: number) {
        let registryNames: string[] = [];
        // Loop to generate registry names
        for (let i = 0; i < registryCount; i++) {
            // Generate a random registry name
            registryNames[i] = "registry-" + BaseTest.generateRandomStringWithCharsOnly(6);
            console.log(registryNames[i]); // Log the generated registry name
        }
        // Return the array of generated registry names
        return registryNames;
    }
    // A function to check if the registry exists if it doesnot exist it create it one by one from  the array provided
    async addMultipleRegistry(registryNames: string[], registryProvider: string[], registryType: string[], registryData) {
        // Loop through each registry
        for (let i = 0; i < registryProvider.length; i++) {
            // Check if registry exists
            console.log(await this.searchRegistry(registryNames[i]));
            // If registry doesn't exist, add it
            if (await this.searchRegistry(registryNames[i]) === false) {
                // Select and add the registry
                await this.selectAndAddRegistry(registryNames[i], registryProvider[i], registryType[i], registryData);
            }
        }
    }

    // A fuction to select and add a registry of the particular type based on the prefernce of user
    async selectAndAddRegistry(registryName: string, registryProvider: string, registryType: string, registryData: any) {
        var otherRegistryType: string = "";
        if (registryProvider.includes("Other")) {
            var split = registryProvider.split(".");
            otherRegistryType = split[1];
            registryProvider = split[0];
        }
        // Click on add registry button to start adding a registry
        await this.addRegistryButton.click();
        // Select the registry provider
        await this.selectRegistryProvider(registryProvider);

        // Switch case based on registry provider
        switch (registryProvider) {
            case "Ecr":
                // If registry type is public
                if (registryType === "public") {
                    // Add public registry with provided data
                    await this.addPublicRegistry(registryName, process.env.ECR_REGISTRY_URL as string, registryData.EcrRegistry.publicRepoList);
                } else {
                    // Add private ECR registry with provided data
                    await this.addPrivateRegistry(registryName, process.env.ECR_REGISTRY_URL as string);
                    // Fill ECR user authentication details and check common checkboxes
                    await this.userAuthInEcrRegistry(process.env.ECR_REGISTRY_ACCESS_ID as string, process.env.ECR_REGISTRY_SECRETKEY as string);
                    await this.checkCommonCheckBoxesOfRegistry(registryData.EcrRegistry.pushHelmPackages, registryData.EcrRegistry.useAsChartRepo, registryData.EcrRegistry.useAsDefaultRegistry, registryData.EcrRegistry.privateRepoList);
                }
                break;
            case 'Azure':
                // If registry type is public, add public registry with provided data
                if (registryType === "public") {
                    await this.addPublicRegistry(registryName, process.env.AZURE_REGISTRY_URL as string, registryData.AzureRegistry.publicRepoList);
                } else {
                    // Otherwise, add private Azure registry and configure with provided data
                    await this.addPrivateRegistry(registryName, process.env.AZURE_REGISTRY_URL as string);
                    await this.privateRegistryUserNameAndToken(process.env.AZURE_REGISTRY_USERNAME as string, process.env.AZURE_REGISTRY_TOKEN as string);
                    await this.checkCommonCheckBoxesOfRegistry(registryData.AzureRegistry.pushHelmPackages, registryData.AzureRegistry.useAsChartRepo, registryData.AzureRegistry.useAsDefaultRegistry, registryData.AzureRegistry.privateRepoList);
                }
                break;

            case 'Docker':
                // Handle Docker registry similarly as Azure registry
                if (registryType === "public") {
                    await this.addPublicRegistry(registryName, process.env.DOCKER_REGISTRY_URL as string, registryData.DockerRegistry.publicRepoList);
                } else {
                    await this.addPrivateRegistry(registryName, process.env.DOCKER_REGISTRY_URL as string);
                    await this.privateRegistryUserNameAndToken(process.env.DOCKER_REGISTRY_USERNAME as string, process.env.DOCKER_REGISTRY_TOKEN as string);
                    await this.checkCommonCheckBoxesOfRegistry(registryData.DockerRegistry.pushHelmPackages, registryData.DockerRegistry.useAsChartRepo, registryData.DockerRegistry.useAsDefaultRegistry, registryData.DockerRegistry.privateRepoList);
                }
                break;

            case 'GCP':
                // Handle GCP registry similarly as Azure registry
                if (registryType === "public") {
                    await this.addPublicRegistry(registryName, process.env.GCP_REGISTRY_URL as string, registryData.GCPRegistry.publicRepoList);
                } else {
                    await this.addPrivateRegistry(registryName, process.env.GCP_REGISTRY_URL as string);
                    await this.serviceAccountFileinGCP_GCR(process.env.GCP_REGISTRY_SERVICE_ACCOUNT as string);
                    await this.checkCommonCheckBoxesOfRegistry(registryData.GCPRegistry.pushHelmPackages, registryData.GCPRegistry.useAsChartRepo, registryData.GCPRegistry.useAsDefaultRegistry, registryData.GCPRegistry.privateRepoList);
                }
                break;
            // Handle GCR registry
            case 'GCR':
                // Add private GCR registry and configure with provided data
                await this.addPrivateRegistry(registryName, process.env.GCR_REGISTRY_URL as string);
                await this.serviceAccountFileinGCP_GCR(process.env.GCR_REGISTRY_SERVICE_ACCOUNT as string);
                break;
            // Handle Quay registry
            case 'Quay':
                // If registry type is public, add public Quay registry with provided data
                if (registryType === "public") {
                    await this.addPublicRegistry(registryName, process.env.QUAY_REGISTRY_URL as string, registryData.QuayRegistry.publicRepoList);
                } else {
                    // Otherwise, add private Quay registry and configure with provided data
                    await this.addPrivateRegistry(registryName, process.env.QUAY_REGISTRY_URL as string);
                    await this.privateRegistryUserNameAndToken(process.env.QUAY_REGISTRY_USERNAME as string, process.env.QUAY_REGISTRY_TOKEN as string);
                    await this.checkCommonCheckBoxesOfRegistry(registryData.QuayRegistry.pushHelmPackages, registryData.QuayRegistry.useAsChartRepo, registryData.QuayRegistry.useAsDefaultRegistry, registryData.QuayRegistry.privateRepoList);
                }
                break;
            // Handle other registry types
            case 'Other':
                await this.otherRegistry(registryName, otherRegistryType, registryType, registryData)
                break;
        }

        await BaseTest.checkToast(this.page, this.saveRegistryButton, "Successfully saved");
    }

    async otherRegistry(registryName: string, registryProvider: string, registryType: string, registryData: any) {
        if (registryProvider === "Quay") {
            if (registryType === "public") {
                await this.addPublicRegistry(registryName, process.env.QUAY_REGISTRY_URL as string, registryData.QuayRegistry.publicRepoList)
            } else {
                // Otherwise, add private registry and configure with provided data
                await this.addPrivateRegistry(registryName, process.env.QUAY_REGISTRY_URL as string);
                await this.privateRegistryUserNameAndToken(process.env.QUAY_REGISTRY_USERNAME as string, process.env.QUAY_REGISTRY_TOKEN as string)
                await this.checkCommonCheckBoxesOfRegistry(registryData.QuayRegistry.pushHelmPackages, registryData.QuayRegistry.useAsChartRepo, registryData.QuayRegistry.useAsDefaultRegistry, registryData.QuayRegistry.privateRepoList);
            }
        }
        if (registryProvider === "Harbor") {
            if (registryType === "public") {
                await this.addPublicRegistry(registryName, process.env.HARBOR_REGISTRY_URL as string, registryData.HarborRegistry.publicRepoList)
            } else {
                // Otherwise, add private registry and configure with provided data
                await this.addPrivateRegistry(registryName, process.env.HARBOR_REGISTRY_URL as string);
                await this.privateRegistryUserNameAndToken(process.env.HARBOR_REGISTRY_USERNAME as string, process.env.HARBOR_REGISTRY_PASSWORD as string)
                await this.selectAdvancedOptions("secure with ca cert", registryData.HarborRegistry.caCertificate);
                await this.checkCommonCheckBoxesOfRegistry(registryData.HarborRegistry.pushHelmPackages, registryData.HarborRegistry.useAsChartRepo, registryData.HarborRegistry.useAsDefaultRegistry, registryData.HarborRegistry.privateRepoList);
            }
        }
    }

    // Search for a registry with the given name.
    async searchRegistry(registryName: string) {
        // Get the registry div element by test id
        await this.page.locator('//*[@data-testid="add-registry-button"]').waitFor();
        const registryDiv = this.page.getByTestId(registryName);
        // Check if the registry div is visible within the timeout
        if (await registryDiv.isVisible({ timeout: 5000 })) {
            return true; // Return true if registry is found
        }
        return false; // Return false if registry is not found or not visible
    }

    // Delete one or more registries.
    async deleteRegistry(registryName: string[]) {
        // Loop through each registry name
        for (let i = 0; i < registryName.length; i++) {
            // Get the registry div element by test id
            const registryDiv = this.page.getByTestId(registryName[i]);
            // Check if the registry exists and is not set as default
            if (await this.searchRegistry(registryName[i]) && !(await registryDiv.getByText("DEFAULT").isVisible({ timeout: 3000 }))) {
                // Click on the registry div
                await registryDiv.click();
                // Click on the delete registry button
                await this.deleteRegistryButton.click();
                // Check for success toast message after deletion
                await BaseTest.checkToast(this.page, this.dialogDeleteConfirmationButton, "Successfully deleted");
            }
        }
    }

    async selectAdvancedOptions(option: string, caCertificate: string = "") {
        await this.clickOnAdvancedRegistryUrl.click();
        if (option === "only secure") {
            await this.allowOnlySecureConnections.click();
        }
        else if (option === "secure with ca cert") {
            await this.clickOnSecureWithCaCertificate.click();
            await this.clickOnPasteCaCertificateInput.fill(caCertificate);
        }
        else if (option === "insecure") {
            await this.allowInsecureConnection.click()
        }
    }

    async isRegistryBuilt(name: string) {
        await expect(this.addRegistryButton).toBeVisible({ timeout: 5000 });
        if (await this.page.getByText(name).isVisible()) {
            return true;
        } else {
            return false;
        }
    }

    async removeRepoContainerRegistry(repositoryName: string, registryName: string) {

        await expect(async () => {
            await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/docker/${registryName}`);
            if (await this.useAsChartRepoCheckbox.last().isChecked() === true) {
                if (await this.page.locator(`//*[@aria-label="Remove ${repositoryName}"]`).nth(0).isVisible()) {
                    await this.page.locator(`//*[@aria-label="Remove ${repositoryName}"]`).nth(0).click();
                }
                if (await this.page.locator("//*[text()='Enter repository name and press enter']").isVisible()) {
                    await this.useAsChartRepoCheckbox.last().click({ delay: 2000 });
                }

            }

            await BaseTest.checkToast(this.page, this.saveRegistryButton, "Successfully saved");

        }).toPass({ timeout: 4 * 1000 * 60 });

    }
}

