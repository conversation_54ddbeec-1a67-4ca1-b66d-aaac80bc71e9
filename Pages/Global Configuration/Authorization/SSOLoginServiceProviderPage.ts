import { Locator, <PERSON> } from 'playwright';
import { SSOProvidersEnum } from '../../../enums/Global Configurations/Authorization/SSOProvidersEnum';

export class SSOLoginServiceProviderPage {
  readonly googleSSOButton: Locator;
  readonly githubSSOButton: Locator;
  readonly gitlabSSOButton: Locator;
  readonly microsoftSSOButton: Locator;
  readonly ldapSSOButton: Locator;
  readonly oidcSSOButton: Locator;
  readonly openshiftSSOButton: Locator;
  readonly ssoSaveButton: Locator;
  readonly ssoUrlInputBox: Locator;
  readonly yamlEditor: Locator;
  readonly confirmationModalButton : Locator;


  constructor(public page: Page) {
    this.googleSSOButton = page.getByTestId('sso-google-button');
    this.githubSSOButton = page.getByTestId('sso-github-button');
    this.gitlabSSOButton = page.getByTestId('sso-gitlab-button');
    this.microsoftSSOButton = page.getByTestId('sso-microsoft-button');
    this.ldapSSOButton = page.getByTestId('sso-ldap-button');
    this.oidcSSOButton = page.getByTestId('sso-oidc-button');
    this.openshiftSSOButton = page.getByTestId('sso-openshift-button');
    this.ssoSaveButton = page.getByTestId('sso-save-button');
    this.ssoUrlInputBox = page.getByTestId('sso-url');
    this.yamlEditor = page.locator('[contenteditable="true"][data-language="yaml"]');
    this.confirmationModalButton = page.getByTestId('confirmation-modal-primary-button');

  }

  async navigateToSSOConfigurationsPage() {
    await this.page.goto(`${process.env.BASE_SERVER_URL}/global-config/auth/login-service`, {
      waitUntil: 'networkidle', // Ensure all network activity is done
    });
  
    // Wait until the SSO provider buttons are available (adjust selector if needed)
    await this.page.getByTestId('sso-gitlab-button').waitFor({ timeout: 10000 });
  }
  
  async selectSpecificSSOProvider(provider: SSOProvidersEnum): Promise<void> {
    switch (provider) {
      case SSOProvidersEnum.GOOGLE:
        await this.googleSSOButton.click();
        break;
      case SSOProvidersEnum.GITHUB:
        await this.githubSSOButton.click();
        break;
      case SSOProvidersEnum.GITLAB:
        await this.gitlabSSOButton.click();
        break;
      case SSOProvidersEnum.MICROSOFT:
        await this.microsoftSSOButton.click();
        break;
      default:
        throw new Error(`Unhandled SSO provider: ${provider}`);
    }
  }

  async fillSSOConfigurationYaml(yamlContent: string): Promise<void> {
    await this.yamlEditor.click(); 
  
    await this.page.evaluate(({ selector, yaml }) => {
      const el = document.querySelector(selector) as HTMLElement | null;
      if (!el) throw new Error('YAML editor not found');
  
      // Set the text content directly
      el.innerText = yaml;
  
      // Dispatch input event to simulate user typing
      const inputEvent = new Event('input', { bubbles: true });
      el.dispatchEvent(inputEvent);
    }, {
      selector: '[contenteditable="true"][data-language="yaml"]',
      yaml: yamlContent
    });
  }
  
  async SaveUpdateSpecificSSOProvider(){
    await this.ssoSaveButton.click();
    await this.verifyConfirmationModal();
    await this.page.keyboard.press('Enter');
  }

  async verifyConfirmationModal()
  {
    await this.confirmationModalButton.isVisible();
  }

  async verifySSOHasConfiguredForDesiredProvider(provider: string): Promise<boolean> {
    const testId = `login-with-${provider.toLowerCase()}`;
    const button = this.page.locator(`[data-testid="${testId}"]`);
    return await button.isVisible();
  }

   async clickLogout(): Promise<void> {
    const profileButton = this.page.getByTestId('profile-button');
    await profileButton.click();
  
    const logoutLink = this.page.locator('a[href="/dashboard/login"]:has-text("Logout")');
    await logoutLink.click();
  }
}

