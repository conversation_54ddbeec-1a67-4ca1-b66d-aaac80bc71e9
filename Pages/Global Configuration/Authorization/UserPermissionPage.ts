import { Locator, <PERSON> } from "@playwright/test";
import { BaseTest } from "../../../utilities/BaseTest";
import { ApiUtils } from "../../../utilities/ApiUtils";
import { BasePage } from "../../BasePage";
import MailSlurpClient from "mailslurp-client";
const credentials = BaseTest.readParametersFromFile("TestData/BaseCredentials.json");
export class UserPermissionPage extends BasePage {
  readonly addUserButton: Locator;
  readonly saveButton: Locator;
  readonly emailInputField: Locator;
  readonly searchInputField: Locator;
  readonly deleteUserIconButton: Locator;
  readonly singleUserListDiv: Locator;
  readonly userGroupDropdown: Locator;


  constructor(public page: Page) {
    super(page);
    this.page = page;
    this.addUserButton = this.page.locator('//*[@href="/dashboard/global-config/auth/users/add"]');
    this.saveButton = this.page.locator('//*[text()="Save"]');
    this.emailInputField = this.page.locator('//*[text()="Type email and press enter"]');
    this.searchInputField = this.page.getByTestId("search-bar");
    this.deleteUserIconButton = this.page.getByTestId("delete-user");
    this.singleUserListDiv = this.page.locator('//*[contains(@class,"user-permission__row user-permission__row")]');
    this.userGroupDropdown = this.page.locator(`//*[contains(@class,'user-group-selector__control')]`);
  }
  async navifateToUserPermissionPage() {
    await this.page.goto(process.env.BASE_SERVER_URL! + '/global-config/auth/users');
  }
  async searchAanUser(userEmail: string, goInsideUserDetails: boolean = false) {
    await this.searchInputField.fill(userEmail);
    await this.page.keyboard.press('Enter');
    if (goInsideUserDetails) {
      await this.page.locator(`//*[contains(@class,'user-permission__row')]//*[text()="${userEmail}"]`).click({ delay: 1000 });
    }

  }
  async deleteAnUser(UserEmail: string) {
    try {
      await this.searchAanUser(UserEmail, true);
      await this.deleteUserIconButton.click({ timeout: 5000 });
      await this.dialogDeleteConfirmationButton.click({
        timeout: 5000
      });
    }
    catch (e) {
      console.log('user was not created , so skipping the deletion');
    }
  }
  async clickOnAddUserButtonAndFillEmail(email: string) {
    await this.addUserButton.click();
    await this.emailInputField.click({ force: true });
    await this.page.keyboard.insertText(email);
    await this.page.keyboard.press('Enter');
  }

  async CreateUserGoupForAddingMannualUser(apiutils: ApiUtils, mailSlurpClient: MailSlurpClient, userGroupCount: number = 2, userCount: number = 2) {
    var k = 0;
    let token: string;
    if (process.env.isStaging == "true") {
      token = process.env.stagingSuperAdminToken!;
    } else {
      token = await apiutils.login(process.env.PASSWORD!);
    }
    for (let i = 0; i < userGroupCount; i++) {
      var name = "qa-team-" + BaseTest.generateRandomString(5);
      await apiutils.createUserGroup(token, 0, `${name}-${i}`, `${name}-${i}`, "Testing-Description");
      for (let j = 0; j < userCount; j++) {
        await apiutils.addUserInUserGroup(token, `${name}-${i}`, credentials.emailCreateUserArray[k++]);
      }
    }
  }
  async assignAPermissionGroupToAUser(apiUtils: ApiUtils, userGroupName: string) {
    await this.userGroupDropdown.click();
    await this.page.waitForTimeout(2000);
    if (!await this.page.locator(`//*[@role="listbox"]//*[text()="${userGroupName}"]`).isVisible()) {
      await apiUtils.createUserGroup(await apiUtils.login(process.env.PASSWORD!), 0, userGroupName, userGroupName, "testing");
      await this.page.reload();
      await this.userGroupDropdown.click();
    }
    if (!await this.userGroupDropdown.textContent().then(key => key!.includes(userGroupName))) {
      await this.page.locator(`//*[@role="option"]//*[text()="${userGroupName}"]`).click();
    }
    await this.saveButton.click();
  }

}