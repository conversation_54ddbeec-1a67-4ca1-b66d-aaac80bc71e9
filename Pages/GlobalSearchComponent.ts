import { Locator, <PERSON> } from "playwright";
import { globalSearchComponentParentEntities } from "../enums/GlobalSearchComponentEnum";
import { globalSearchComponentParentChildMapping } from "../DTOs/GlobalSearchComponentDTOs";
import { expect } from "playwright/test";


/**
 * This class represents the Global Search Component in the application and provides
 * methods to interact with various elements on the page, particularly for managing
 * global search functionalities.
 */
export class GlobalSearchComponent {
    // locators
    readonly searchInputfield:Locator;
    readonly recentNavigationDiv:Locator;
    readonly globalSearchComponent:Locator;

    constructor(private page: Page) {
        this.searchInputfield= this.page.getByTestId('search-bar');
        this.recentNavigationDiv= this.page.locator(`//*[@aria-labelledby="command-bar-recent-navigation-group"]`);
        this.globalSearchComponent= this.page.locator(`//*[@aria-label="Command Menu"]`);
    }   

    /**
     * use this method to search and click on any entity
     * @param parentEntity this is the parent entity under which child entity is present
     * @param childEntity this is the child entity which is present under parent entity
     */
    async searchAndClickOnAnyEntity<k extends globalSearchComponentParentEntities>(parentEntity:k, childEntity:globalSearchComponentParentChildMapping[k]){
        await this.searchInputfield.fill(childEntity);
        let locatorToClick= this.setupLocatorForClickingOnChildEntities(parentEntity);
        await locatorToClick.first().click();
    }


    /**
     * this method is used to setup the locator for clicking on child entities
     * @param parentEntity this is the parent entity under which child entity is present
     * @returns 
     */
    setupLocatorForClickingOnChildEntities(parentEntity:globalSearchComponentParentEntities):Locator{
        let locator:Locator=this.page.locator(`//*[@aria-labelledby="${parentEntity}"]//div`);
        return locator;
    }


    /**
     * this method is used to verify the recent navigations sections
     * @param verificationData this is the data which we want to verify
     */
    async verifyRecentNavigationsSections(verificationData:{indexNumber:number, textToVerify:string}[]){
        await this.page.waitForTimeout(3000);
        for (const key of verificationData) {
           await this.recentNavigationDiv.locator('//div').nth(key.indexNumber).waitFor();
           let textContent= await this.recentNavigationDiv.locator('//div').nth(key.indexNumber).textContent();
           expect(textContent!.toLowerCase()).toContain(key.textToVerify);
        }
    }

    /**
     * this method is used to verify the key shortcuts of global search component
     */
    async verifyKeyShortcutsOfGlobalSearchComponent(){
       await this.openOrCloseGlobalSearchComponentThroughShortcut(true);
       await this.openOrCloseGlobalSearchComponentThroughShortcut(false);
       await this.openOrCloseGlobalSearchComponentThroughShortcut(true);
        
    }


    /**
     * this method is used to open or close the global search component through shortcut keys
     * @param open this is the boolean value which tells us whether to open or close the global search component
     */
    async openOrCloseGlobalSearchComponentThroughShortcut(open:boolean){
        await expect(async()=>{
             if(open){
                
             await this.page.keyboard.press('MetaOrControl+k');
             console.log('pressed');
             await this.verifyGlobalSearchComponentIsOpenOrClose(true);
        }else{
            await this.page.keyboard.press('Escape');
            await this.verifyGlobalSearchComponentIsOpenOrClose(false);
        }
        }).toPass({timeout:1*1000*60});
    }


    /**
     * this method is used to verify whether the global search component is open or close
     * @param isOpen this is the boolean value which tells us whether the global search component is open or close
     */
    async verifyGlobalSearchComponentIsOpenOrClose(isOpen:boolean){
        if(isOpen){
            await expect(this.globalSearchComponent).toBeVisible();
        }
        else{
            await expect(this.globalSearchComponent).toBeHidden();
        }
    }


    /**
     * this method is used to verify the key up and down and enter functionality is working
     */
    async verifyKeyUpDownAndEnterFunctionalityIsWorking(){
            await this.page.locator(`//*[@role="option"]`).first().waitFor();
            console.log('attr is '+await this.page.locator(`//*[@role="option"]`).first().getAttribute('aria-selected'));
            await expect(this.page.locator(`//*[@role="option"]`).first()).toHaveAttribute('aria-selected', 'true');
            await this.page.keyboard.press('ArrowDown');
            await expect(this.page.locator(`//*[@role="option"]`).nth(1)).toHaveAttribute('aria-selected', 'true');
            await this.page.keyboard.press('Enter');
            await this.verifyGlobalSearchComponentIsOpenOrClose(false);
    }
    
}