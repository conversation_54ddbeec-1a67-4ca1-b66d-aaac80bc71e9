import { expect, Locator, <PERSON> } from "@playwright/test";
import { monthMappingForCalendar } from "../utilities/clipboardyYamls.ts/customObjectsOrYaml";
import clipboardy from 'clipboardy';
export class BasePage {
    readonly dialogDeleteConfirmationButton: Locator;
    readonly codeMirrorEditorTextArea: Locator;
    readonly codeMirrorSingleLineClass: string
    readonly deleteModalInputField: Locator;
    readonly yearAndMonthTextOnCalendar: Locator;
    readonly calendarNavigateRightButton: Locator;
    readonly calendarNavigateLeftButton: Locator;
    readonly undefinedChkSpan: Locator;
    readonly helpButton: Locator;
    readonly tooltipModal: Locator;
    readonly bulkSelectionApplyProfileCheckbox: Locator;
    readonly singleEntityInsideDropdownOptions: Locator;
    readonly dropdownOptionsDivContainer: Locator;
    constructor(public page: Page) {
        this.dialogDeleteConfirmationButton = this.page.getByTestId('confirmation-modal-primary-button');
        this.codeMirrorEditorTextArea = this.page.locator('//*[@role="textbox"]');
        this.codeMirrorSingleLineClass = 'cm-line';
        this.deleteModalInputField = this.page.locator('//*[@placeholder="Type to confirm"]');
        this.yearAndMonthTextOnCalendar = this.page.locator('//*[@class="CalendarMonth_caption CalendarMonth_caption_1"]');
        this.calendarNavigateRightButton = this.page.locator("//*[contains(@class,'DayPickerNavigation_rightButton')]");
        this.calendarNavigateLeftButton = this.page.locator("//*[contains(@class,'DayPickerNavigation_leftButton')]");
        this.undefinedChkSpan = this.page.getByTestId('undefined-chk-span');
        this.helpButton = page.getByTestId("page-header-help-button");
        this.tooltipModal = this.page.locator('//*[@class="tippy-content"]');
        this.bulkSelectionApplyProfileCheckbox = this.page.locator(`//*[@aria-haspopup="listbox"]//*[@class="form__checkbox-container"]`);
        this.singleEntityInsideDropdownOptions = this.page.locator('//*[@role="option"]');
        this.dropdownOptionsDivContainer = this.page.locator(`//*[@role="listbox"]`);
    }

    /**
* This function is to select date from calendar , if you want to set custom date , use this method
* @param date 
* @param month 
* @param year 
*/

    async setCustomDate(date: string, month: string, year: string) {
        console.log('we are printing our date');
        console.log(date);
        var yearAndMonth = await this.yearAndMonthTextOnCalendar.nth(1).textContent();
        while (yearAndMonth?.split(' ')[1] != year || yearAndMonth?.split(' ')[0] != month) {
            if (Number(year) > Number(yearAndMonth?.split(' ')[1]) || Number(monthMappingForCalendar[month]) > Number(monthMappingForCalendar[yearAndMonth?.split(' ')[0]!])) {
                await this.calendarNavigateRightButton.click();
                console.log('verified value is ' + Number(monthMappingForCalendar[yearAndMonth?.split(' ')[0]!]));
            }
            else {
                await this.calendarNavigateLeftButton.click();
            }
            yearAndMonth = await this.yearAndMonthTextOnCalendar.nth(1).textContent();
        }
        await this.page.locator(`//*[text()="${date}"]`).nth(1).click();
    }

    async clearAnyTextArea(textAreaLocator: Locator) {
        await textAreaLocator.click();
        for (let i = 0; i < 10; i++) {
            if (process.env.OS as string == "Mac") {
                await this.page.keyboard.press('Meta+a');
                await this.page.waitForTimeout(300);
            }
            else {
                await this.page.keyboard.press('Control+a');
                await this.page.waitForTimeout(300);
            }
        }
        await this.page.keyboard.press('Backspace', { delay: 300 });
    }

    async enterConfigurationInTextArea(textAreaLocator: Locator, configuration: string) {
        await textAreaLocator.click();
        await clipboardy.write(configuration);
        if (process.env.OS as string == "Mac") {
            await this.page.keyboard.press('Meta+v', { delay: 300 });
        }
        else {
            await this.page.keyboard.press('Control+v', { delay: 300 });
        }
    }
    static async checkToast(page: Page, buttonResponsibleForToast: Locator, toastMessage: string) {
        try {
            await Promise.all([
                expect(page.locator(`//*[contains(@class,"Toastify__toast")]//*[text()="${toastMessage}"]`).nth(0)).toBeVisible({ timeout: 40000 }),
                buttonResponsibleForToast.click()
            ]);
            await expect(page.locator('//*[text()="Success"]').first()).toBeHidden({ timeout: 12000 });
            return true;
        } catch (error) {
            console.error(`An error occurred during ${toastMessage}: ${error.message}`);
            throw error;
        }
    }
    async verifyTheValuesInsideTooltip(valuesToVerify: string[]) {
        for (let value of valuesToVerify) {
            await expect(this.tooltipModal).toContainText(value);
        }
    }
    static async verifyTextAreaIsEmptyOrNot(textAreaLocator: Locator, isEmpty: boolean) {
        let textContent = await textAreaLocator.textContent();
        if (isEmpty) {
            expect(textContent).toBeNull();
        }
        else {
            expect(textContent).not.toBeNull();
        }
    }

    captureCurrentUrlAndReturnValue(): string {
        return this.page.url();
    }


}