import { expect, Locator, <PERSON> } from "@playwright/test";
import { BaseTest } from "../../utilities/BaseTest";
import { BasePage } from "../BasePage";

export class ReleaseTrackConfigurePage extends BasePage {
    readonly manageApplicationsButton: Locator;
    readonly editButton: Locator;
    readonly searchAndAddAppsDropdownLocator: Locator
    readonly addReleaseStageButton: Locator;
    readonly saveChangesButton: Locator;
    readonly activeReleasesTab: Locator;
    readonly removeDeletedApplicationButton: Locator;
    readonly dialogRemoveButton: Locator;
    readonly addApplicationsButton: Locator;
    constructor(public page: Page) {
        super(page);
        this.manageApplicationsButton = this.page.locator('//*[@value="manageApplications"]/parent::label');
        this.editButton = this.page.locator(`//*[text()="Edit"]`);
        this.searchAndAddAppsDropdownLocator = this.page.locator(`//*[contains(@class,'dc__right')]`).locator(this.page.locator(`//*[contains(@class,'indicatorContainer')]`));
        this.addReleaseStageButton = this.page.getByTestId("add-release-stage-button").first();
        this.saveChangesButton = this.page.locator(`//*[text()="Save changes"]`);
        this.activeReleasesTab = this.page.locator(`//*[text()="Active Releases"]`);
        this.removeDeletedApplicationButton = this.page.locator(`//*[text()="Remove application"]`);
        this.dialogRemoveButton = this.page.locator(`//*[text()="Remove"]`);
        this.addApplicationsButton = this.page.locator(`//*[text()="Add Applications"]`);
    }
    async manageApplications(data: { appName: string, isMandatory: boolean, releaseStageNumber: number }[]) {
        await this.manageApplicationsButton.click();
        let existingReleaseStages: Set<number> = new Set<number>([0]);
        try {
            await this.editButton.click({ timeout: 4000 });
        }
        catch (error) {
            await this.addApplicationsButton.click();
        }
        for (const key of data) {
            await this.selectAppsFromDropdown(key.appName)
            if (!key.isMandatory) {
                await this.page.locator(`//*[@data-testid="drag-${key.appName}"]/parent::div/following-sibling::div//*[@data-testid="handle-toggle-button"]`).click();
            }
            if (!existingReleaseStages.has(key.releaseStageNumber)) {
                await this.addReleaseStageButton.click();
                existingReleaseStages.add(key.releaseStageNumber);
            }
            if (key.releaseStageNumber != 0) {
                await this.page.locator(`//*[@data-testid="drag-${key.appName}"]`).dragTo(this.page.locator(`//*[@data-testid="remove-release-stage-${key.releaseStageNumber}-button"]/ancestor::div[contains(@class,'visible-hover--parent')]/following-sibling::div`));
            }
        }
        await BaseTest.checkToast(this.page, this.saveChangesButton, "Success");
    }
    async selectAppsFromDropdown(appName: string) {
        await this.searchAndAddAppsDropdownLocator.click();
        await this.page.locator(`//*[@role="listbox"]//*[text()="${appName}"]`).click();
        await this.searchAndAddAppsDropdownLocator.click();
    }
    async checkLastTriggeredReleaseForInstallation(tenantName: string, installationName: string, releaseName: string) {
        await this.activeReleasesTab.click();
        await expect(this.page.locator(`//*[text()='${tenantName} (${installationName})']/ancestor::div[contains(@class,'releases__active-release')]//*[text()="${releaseName}"]`)).toBeVisible({ timeout: 15000 });
    }
    async removeAlreadyDeletedApps(appName: string) {
        await this.page.locator(`//*[text()="${appName}"]/ancestor::div[contains(@class,'releases')]`).locator(this.removeDeletedApplicationButton).click();
        await this.dialogRemoveButton.click();
    }
    async removeApplications(appName: string) {
        await this.manageApplicationsButton.click();
        await this.editButton.click({ timeout: 4000 });
        await this.page.locator(`//*[@aria-label="remove-${appName}-from-release-order"]`).click();
        await BaseTest.checkToast(this.page, this.saveChangesButton, "Success");
    }
}