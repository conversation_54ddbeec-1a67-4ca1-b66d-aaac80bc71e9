import { expect, Locator, Page } from "@playwright/test";
import { ReleaseHub } from "./ReleaseHub";

export class Tenants extends ReleaseHub {
    readonly TenantsTabLocator: Locator;
    readonly addTenantButton: Locator;
    readonly tenantInputField: Locator;
    readonly tenantidField: Locator;
    readonly saveButton: Locator;
    readonly editTenantImageButton: Locator;
    readonly tenantImageInputField: Locator;
    readonly previewButton: Locator;
    readonly imageAppliedLocator: Locator;
    readonly releaseHubpage: ReleaseHub;
    readonly viewReleaseChannels: Locator;
    readonly addReleaseChannelButton: Locator;
    readonly releaseChannelNameInputField: Locator;
    readonly releaseChannelIdInputField: Locator;
    readonly setChannelAsDefaultCheckbox: Locator;
    readonly closeModalButtonOfReleaseChannel: Locator;
    readonly releaseChannelSearchBar: Locator;
    readonly releaseChannelModal: Locator;

    constructor(page: Page) {
        super(page);
        this.addTenantButton = this.page.getByTestId(`add-tenant-button`);
        this.tenantInputField = this.page.getByTestId(`tenant-display-name`);
        this.tenantidField = this.page.getByTestId(`tenant-id-field`);
        this.saveButton = this.page.locator(`//*[text()="save" or text()="Save"]`);
        this.editTenantImageButton = this.page.getByTestId(`edit-tenant-image`);
        this.tenantImageInputField = this.page.locator(`//*[@name="tenant-image-url"]`);
        this.previewButton = this.page.locator(`//*[text()='Preview']`);
        this.imageAppliedLocator = this.page.locator(`//*[@alt="tenant-logo"]`);
        this.releaseHubpage = new ReleaseHub(this.page);
        this.viewReleaseChannels = this.page.getByTestId('view-release-channel');
        this.addReleaseChannelButton = this.page.locator(`//*[text()="Add Release Channel"]`);
        this.releaseChannelNameInputField = this.page.locator(`//*[@name="release-channel-name"]`);
        this.releaseChannelIdInputField = this.page.locator(`//*[@name="release-channel-id"]`);
        this.setChannelAsDefaultCheckbox = this.page.locator(`//*[@class="form__checkbox-container"]`);
        this.TenantsTabLocator = this.page.getByTestId("tenants-click");
        this.closeModalButtonOfReleaseChannel = this.page.locator(`//*[@aria-label="close-modal"]`);
        this.releaseChannelModal = this.page.locator(`//*[@class="drawer right show"]`);
        this.releaseChannelSearchBar = this.releaseChannelModal.locator(this.page.getByTestId(`search-bar`));
    }

    async addTenant(data: { tenantName: string, tenantId: string, imageUrl: string }) {
        await this.navigateToReleaseHubPage();
        await this.TenantsTabLocator.click();
        await this.addTenantButton.click();
        await this.tenantInputField.fill(data.tenantName);
        await this.tenantidField.fill(data.tenantId);
        await this.editTenantImage(data.imageUrl);
        await this.saveButton.click();
    }
    async editTenantImage(imageurl: string) {
        await this.editTenantImageButton.click();
        await this.tenantImageInputField.fill(imageurl);
        await this.previewButton.click();
        await expect(this.imageAppliedLocator).toBeVisible({ timeout: 5000 });
    }

    async clickOnAnyTenant(tenantName: string) {
        await this.TenantsTabLocator.click();
        await this.releaseHubpage.searchAReleaseOrTenant(tenantName);
        await this.releaseCard.click();

    }

    async createReleaseChannel(data: { releaseChannelName: string, isDefault: boolean, channelId: string }) {
        await this.viewReleaseChannels.click();
        await this.addReleaseChannelButton.click();
        await this.releaseChannelNameInputField.fill(data.releaseChannelName);
        await this.releaseChannelIdInputField.fill(data.channelId);
        data.isDefault ? await this.setChannelAsDefaultCheckbox.click() : '';
        await this.saveButton.click();
        await this.closeModalButtonOfReleaseChannel.click();
    }
    async deleteReleaseChannel(channelName: string) {
        await this.TenantsTabLocator.click();
        await this.viewReleaseChannels.click();
        await this.releaseChannelSearchBar.fill(channelName);
        await this.page.keyboard.press('Enter');
        await this.page.locator(`//*[contains(@class,'release-channel__table-row')]`).nth(1).click();
        await this.page.locator(`//*[@data-testid="release-channel__delete-button-${channelName}"]`).click();
        await this.deleteModalInputField.fill(channelName);
        await this.dialogDeleteConfirmationButton.click();
        await this.page.locator(`//*[@aria-label="close-modal"]`).click();
    }
    async deleteAtenant(tenantName: string) {
        await this.clickOnAnyTenant(tenantName);
        await this.page.locator(`//*[@data-testid="edit-${tenantName}-options-button"]`).click();
        await this.page.locator(`//*[@data-testid="delete-${tenantName}"]`).click();
        await this.deleteModalInputField.fill(tenantName);
        await this.dialogDeleteConfirmationButton.click();
    }

}