import { expect, Locator, <PERSON> } from "@playwright/test";
import { read } from "fs";

export class ReleaseOverviewPage {
    readonly imageExpandButton: Locator;
    readonly readmeEditButton: Locator;
    readonly detailsTextArea: Locator;
    readonly saveButton: Locator;
    constructor(public page: Page) {
        this.imageExpandButton = this.page.locator(`//*[@aria-label="Expand image column"]`);
        this.readmeEditButton = this.page.getByTestId("description-edit-button");
        this.detailsTextArea = this.page.getByTestId("text-area");
        this.saveButton = this.page.locator(`//*[text()="Save"]`);
    }
    async verifyRequirementsOnOverviewPage(appName: string, image: string) {
        await this.imageExpandButton.click();
        await expect(this.page.locator(`//*[text()="aman-test5"]/parent::div`).locator(`//*[contains(text(),'${image}')]`)).toBeVisible({ timeout: 12000 });

    }
    async editReadme(detailsToAdd: string) {
        await this.detailsTextArea.fill(detailsToAdd);
        await this.saveButton.click();
    }
}