import { expect, Locator, Page } from "@playwright/test";
import { BaseTest } from "../../utilities/BaseTest";
import { BasePage } from "../BasePage";

export class ReleaseHub extends BasePage {
    readonly releaseNameSearchBar: Locator;
    readonly releaseCard: Locator;
    readonly releaseTrackFilterDropdown: Locator;
    readonly releaseTrackFilterListDiv: Locator;
    readonly createReleaseTrackButton: Locator;
    readonly releaseTrackNameInputField: Locator;
    readonly createReleaseTrackSaveButon: Locator;
    readonly createReleaseButton: Locator;
    readonly releaseTrackDropdownInCreateReleaseModal: Locator;
    readonly releaseVersionInputField: Locator;
    readonly releaseNameInputField: Locator;
    readonly cloneReleaseRadioButton: Locator;
    readonly dropdownForCloneReleaseList: Locator;
    readonly modalForCreateReleaseDetails: Locator;
    readonly releaseTrackListInCreateReleaseModal: Locator;
    readonly createReleaseInsideFormButton: Locator;
    readonly applyButton: Locator;
    constructor(public page: Page) {
        super(page);
        this.releaseNameSearchBar = this.page.getByTestId(`search-bar`);
        this.releaseCard = this.page.locator(`//button[contains(@class,'info-card')]`);
        this.releaseTrackFilterListDiv = this.page.locator(`//*[contains(@class,'release-track-filter__menu-list')]`);
        this.createReleaseTrackButton = this.page.getByTestId(`create-release-track`);
        this.releaseTrackNameInputField = this.page.locator(`//*[@name="release-track-name"]`);
        this.createReleaseTrackSaveButon = this.page.locator(`//*[text()="Create release track"]`);
        this.createReleaseButton = this.page.getByTestId(`create-release`);
        this.releaseTrackDropdownInCreateReleaseModal = this.page.locator(`//*[contains(@class,'release-track-select__control')]`);
        this.releaseVersionInputField = this.page.locator(`//*[@name="release-version"]`);
        this.releaseNameInputField = this.page.locator('//*[@name="release-name"]');
        this.cloneReleaseRadioButton = this.page.getByTestId(`CLONE-radio-item-span`);
        this.dropdownForCloneReleaseList = this.page.locator('//*[@for="select-release-clone-source"]/following-sibling::div');
        this.modalForCreateReleaseDetails = this.page.locator(`//*[@class="drawer right show"]`);
        this.releaseTrackListInCreateReleaseModal = this.page.locator(`//*[contains(@class,'release-track-select__option')]`);
        this.createReleaseInsideFormButton = this.modalForCreateReleaseDetails.locator(this.page.locator(`//*[text()="Create release"]`));
        this.applyButton = this.page.locator(`//*[text()="Apply"]`);
    }



    async navigateToReleaseHubPage() {
        await BaseTest.waitForApiResponse(this.page, 'resource/list/release-track/alpha1', 200, async () => { await this.page.goto(process.env.BASE_SERVER_URL! + '/software-distribution-hub/releases'); })
    }



    async searchAReleaseOrTenant(releaseNameOrTenantName: string) {
        await this.releaseNameSearchBar.fill(releaseNameOrTenantName);
        await this.page.keyboard.press('Enter');
    }



    async clickOnSpecificRelease(releaseName: string, releaseTrackNames: string[]) {
        if (!this.page.url().endsWith('/releases')) {
            await this.navigateToReleaseHubPage();
        }
        await this.applyFilterOnReleaseTrackOrReleaseChannel(releaseTrackNames, false);
        await this.searchAReleaseOrTenant(releaseName);
        await this.releaseCard.click();
    }



    async applyFilterOnReleaseTrackOrReleaseChannel(releaseTrackNames: string[], isReleaseChannel: boolean) {
        let trackFilterOrChannelFilter: string = isReleaseChannel ? 'release-channel' : 'release-track'
        await expect(async () => {
            await this.page.locator(`//*[contains(@class,"${trackFilterOrChannelFilter}-filter__control")]`).click({ delay: 2000 });
            for (let key of releaseTrackNames)
                await this.releaseTrackFilterListDiv.locator(`//*[text()="${key}"]`).click();
            await this.applyButton.click();
        }).toPass({ timeout: 3 * 1000 * 60 });
    }



    async createReleaseTrack(releaseTrackName: string) {
        await this.navigateToReleaseHubPage();
        await this.createReleaseTrackButton.click();
        await this.releaseTrackNameInputField.fill(releaseTrackName);
        await BaseTest.checkToast(this.page, this.createReleaseTrackSaveButon, "Success");
    }



    async createReleaseOrCloneRelease(data: { releaseTrackName: string, releaseVerName: string, cloneReleaseFromExistingRelease?: string, releaseName: string }) {
        this.page.url().endsWith('/releases') ? console.log('already on release page') : await this.navigateToReleaseHubPage()
        await this.createReleaseButton.click();
        await expect(async () => {
            await this.releaseTrackDropdownInCreateReleaseModal.click({ timeout: 10000 });
            await this.releaseTrackListInCreateReleaseModal.locator(`//*[text()="${data.releaseTrackName}"]`).click({ timeout: 10000 });
        }).toPass({ timeout: 2 * 1000 * 60 });
        await this.releaseVersionInputField.fill(data.releaseVerName);
        await this.releaseNameInputField.fill(data.releaseName);
        if (data.cloneReleaseFromExistingRelease) {
            await this.cloneReleaseRadioButton.click();
            await this.dropdownForCloneReleaseList.click();
            await this.page.locator(`//*[@role="listbox"]//*[text()="${data.cloneReleaseFromExistingRelease}"]`).click();

        }
        await BaseTest.checkToast(this.page, this.createReleaseInsideFormButton, "Success");
    }

    async clickOnReleaseTrackToAddApps(releaseTrackName: string) {
        this.page.url().endsWith('/releases') ? console.log('already on release page') : await this.navigateToReleaseHubPage()
        await this.applyFilterOnReleaseTrackOrReleaseChannel([releaseTrackName], false);
        await this.page.locator(`//*[contains(@href,'/track/detail/${releaseTrackName}')]`).click();
    }




}