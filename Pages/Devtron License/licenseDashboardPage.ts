import { expect, Locator, Page } from "playwright/test";
import { generateToken } from 'authenticator';

export class LicenseDashboardPage {

    readonly devtronTitle: Locator;
    readonly contactSupportButton: Locator;
    readonly logoutButton: Locator;
    readonly enterpriseTrialText: Locator;

    readonly updateFirstName: Locator;
    readonly updateLastName: Locator;
    readonly countryCodeorName: Locator;
    readonly phoneNumber: Locator;
    readonly companyName: Locator;
    readonly submitButton: Locator;
    readonly installdevtronsubmitButton: Locator;
    readonly companyHeadquartersInput: Locator;
    readonly jobTitleInput: Locator;
    
    readonly continueWithEmailButton: Locator;
    readonly workEmailInput: Locator;
    readonly sendLoginLinkButton: Locator;
    readonly emailTooltipMessage: Locator;
    readonly emailSentHeader: Locator;
    readonly emailSentDescription: Locator;
    readonly resendEmailButton: Locator;
    readonly navigateToLoginButton: Locator;

  constructor(private page: Page) {
      this.devtronTitle = page.getByTitle('Devtron Homepage');
      this.contactSupportButton = page.getByTestId('contact-support-button');
      this.logoutButton = page.getByTestId('logout-button');
      this.enterpriseTrialText =page.getByText('Get Devtron enterprise trial for 14 days');

      this.updateFirstName = page.getByTestId('UPDATE_FIRST_NAME');
      this.updateLastName=page.getByTestId('UPDATE_LAST_NAME');
      this.countryCodeorName=page.locator(`//*[contains(@class,('select-picker__country-select--license-form__country-code__input'))]`);
      this.phoneNumber=page.getByTestId('license-form__phone-number');
      this.companyName=page.getByTestId('UPDATE_COMPANY_NAME');
      this.submitButton=page.getByTestId('user-details-submit');
      this.installdevtronsubmitButton= page.getByTestId('install-devtron-submit');
      this.companyHeadquartersInput=page.locator('.select-picker__country-select--license-form__company-headquarter__input');
      this.jobTitleInput= page.getByTestId('UPDATE_JOB_TITLE');
      this.continueWithEmailButton = page.getByTestId('continue-with-custom-email');
      this.workEmailInput = page.getByTestId('user-email');
      this.sendLoginLinkButton = page.getByTestId('send-login-link');
      this.emailTooltipMessage = page.locator('#user-email-helper-text');
      this.emailSentHeader = page.locator('h4.title.dc__align-center'); // header text like "Login link sent to..."
      this.emailSentDescription = page.locator('p.subtitle'); // paragraph describing expiry, etc.
      this.resendEmailButton = page.getByTestId('resend-login-link');
      this.navigateToLoginButton = page.getByTestId('navigate-to-login');
  }

  async verifyHeadingSection() {
    const { devtronTitle, contactSupportButton, logoutButton } = this;
    await Promise.all([
      devtronTitle.waitFor(),
      contactSupportButton.waitFor(),
      logoutButton.waitFor(),
    ]);
  }

  async verifyLicenseFormFields(): Promise<boolean> {
    await expect(this.page.getByText('Get Freemium Plan')).toBeVisible();
    await expect(this.page.getByText('Free forever')).toBeVisible();
    const {
      updateFirstName,
      updateLastName,
      phoneNumber,
      companyName,
      companyHeadquartersInput,
      jobTitleInput,
      submitButton,
    } = this;

    await Promise.all([
      updateFirstName.waitFor(),
      updateLastName.waitFor(),
      companyName.waitFor(),
      companyHeadquartersInput.first().waitFor(),
      jobTitleInput.waitFor(),
    ]);

    const firstName = (await updateFirstName.inputValue()).trim();
    const lastName = (await updateLastName.inputValue()).trim();
    const phone = (await phoneNumber.inputValue()).trim();
    const company = (await companyName.inputValue()).trim();
    const headquarters = (await companyHeadquartersInput.inputValue()).trim();
    const jobTitle = (await jobTitleInput.inputValue()).trim();

    if (!firstName) {
      console.error('First Name is required');
      return false;
    }
    if (!lastName) {
      console.error('Last Name is required');
      return false;
    }
    if (!company) {
      console.error('Company Name is required');
      return false;
    }
    if (!headquarters) {
      console.error('Company Headquarters is required');
      return false;
    }
    if (!jobTitle) {
      console.error('Job Title is required');
      return false;
    }
    if (phone && !/^\+?\d{7,15}$/.test(phone)) {
      console.error('Phone Number format invalid');
      return false;
    }
    if (!(await submitButton.isEnabled())) {
      console.error('Submit button is disabled');
      return false;
    }
    return true;
  }


  async fillLicenseForm(firstName: string, LastName: string, countryCodeorName: string, phoneNumber: string, companyName: string, companyHeadquaters: string) {
    const { updateFirstName, updateLastName, countryCodeorName: countryInput, phoneNumber: phoneInput, companyName: companyInput, jobTitleInput, submitButton, installdevtronsubmitButton } = this;

    await updateFirstName.waitFor();
    await updateFirstName.fill(firstName);

    await updateLastName.waitFor();
    await updateLastName.fill(LastName);

    await countryInput.nth(1).waitFor();
    await countryInput.nth(1).fill(countryCodeorName);
    await this.page.keyboard.press('Enter');

    await phoneInput.waitFor();
    await phoneInput.fill(phoneNumber);

    await companyInput.waitFor();
    await companyInput.fill(companyName);

    await this.fillCompanyHeadquaters(companyHeadquaters);

    await jobTitleInput.click({ delay: 1000 });
    await jobTitleInput.fill('Testing');

    await submitButton.first().click();
    await installdevtronsubmitButton.first().click({ delay: 500 });
  }

  async fillCompanyHeadquaters(companyHeadquaters: string) {
    await this.companyHeadquartersInput.fill(companyHeadquaters);
    await this.page.keyboard.press('Enter');
  }

  async loginToLicenseBoardVerification() {
    try {
      await this.page.getByText('Get Free Enterprise Trial').waitFor();
      if (await this.page.getByTestId('login-via-google').isVisible()) {
        await this.page.getByTestId('login-via-google').click({ force: true });
      } else {
        console.log("No login via Google button");
      }

      if (await this.page.locator('//*[@class="dex-btn-text"]').first().isVisible()) {
        await this.page.locator('//*[@class="dex-btn-text"]').first().click({ force: true });
      } else {
        console.log("No Dex button");
      }
    } catch (error) {
      console.error("Error during login:", error);
    }
    return this.page.url();
  }

  async loginWithGoogle() {
    const email = process.env.GMAIL_EMAIL;
    const password = process.env.GMAIL_PASSWORD_LICENSE;
    const otpSecret = process.env.AUTHENTICATOR_SECRET;

    if (!email || !password || !otpSecret) {
        throw new Error('Missing required environment variables! Please set GMAIL_EMAIL, GMAIL_PASSWORD, AUTHENTICATOR_SECRET, and BASE_SERVER_URL.');
    }

    await expect(this.page.getByTestId('login-via-google')).toBeVisible({ timeout: 10000 });
    await this.page.getByTestId('login-via-google').click();

    // Enter email
    await this.page.locator('#identifierId').waitFor({ state: 'visible' });
    await this.page.locator('#identifierId').fill(email);
    await this.page.keyboard.press('Enter');

    // Wait and enter password
    const passwordInput = this.page.getByLabel('Enter your password');
    await expect(passwordInput).toBeVisible({ timeout: 10000 });
    await passwordInput.fill(password);
    await this.page.keyboard.press('Enter');

    // Optional delay for 2FA screen
    await this.page.waitForTimeout(2000);

    // Click "Try another way"
    const tryAnotherWay = this.page.getByRole('button', { name: 'Try another way' });
    if (await tryAnotherWay.isVisible()) {
        await tryAnotherWay.click();
        await this.page.waitForTimeout(1000);
    }

    // Select TOTP option
    const otpOption = this.page.getByText('Get a verification code from');
    if (await otpOption.isVisible()) {
        await otpOption.click();
    }

    // Wait for OTP input field
    const otpInput = this.page.getByLabel('Enter code');
    await expect(otpInput).toBeVisible();

    // Generate and fill OTP
    const otp = generateToken(otpSecret);
    console.log('Generated TOTP code:', otp);
    await otpInput.fill(otp);
    await this.page.keyboard.press('Enter');
}

  async enterFingerPrint() {
    await this.page.getByTestId('input-fingerprint-submit').first().waitFor();
    await this.page.getByTestId('input-fingerprint-submit').first().click({ force: true });
    // await BaseTest.checkToast(this.page,this.page.getByTestId('input-fingerprint-submit'),)
  }

  async devtronEnterpriseLicensePage() {
    await this.page.getByText('Use this key to activate your Devtron installation').waitFor();
    await this.page.locator(`//*[contains(@aria-label,'Copy ')]`).waitFor();
    await this.page.locator(`//*[contains(@aria-label,'Copy ')]`).click();
  }

  async verifyEmailTooltipMessage(): Promise<boolean> {
    await this.emailTooltipMessage.waitFor({ state: 'visible' });
  
    const messageText = await this.emailTooltipMessage.textContent();
    const expectedText = 'You will receive a login link on your email';
  
    if (messageText?.includes(expectedText)) {
      return true;
    } else {
      console.error(`Tooltip message mismatch. Expected: "${expectedText}", Found: "${messageText}"`);
      return false;
    }
  }
  async ContinueWithEmail(workEmail: string) {
    await this.continueWithEmailButton.waitFor({ state: 'visible' });
    await this.continueWithEmailButton.click();
  
    // Wait for the email input field to appear
    await this.workEmailInput.waitFor({ state: 'visible' });
    await this.workEmailInput.fill(workEmail);
  
    // Click the Send Login Link button
    await this.sendLoginLinkButton.waitFor({ state: 'visible', timeout: 5000 });
    await this.sendLoginLinkButton.click();
  }

  async verifyEmailSentConfirmation(email: string): Promise<boolean> {
    // Wait for header to appear
    await this.emailSentHeader.waitFor({ state: 'visible', timeout: 5000 });
  
    const headerText = await this.emailSentHeader.textContent();
    const expectedHeader = `Login link sent to ${email}`;
  
    const descriptionText = await this.emailSentDescription.textContent();
    const expectedDescriptionIncludes = [
      'Click the link in the email to log in',
      'expires in 10 minutes',
      'check your spam folder',
    ];
  
    const allDescriptionChecks = expectedDescriptionIncludes.every((fragment) =>
      descriptionText?.includes(fragment)
    );
  
    const headerMatch = headerText?.trim() === expectedHeader;
  
    if (!headerMatch) {
      console.error(`❌ Expected header: "${expectedHeader}", but got: "${headerText}"`);
    }
  
    if (!allDescriptionChecks) {
      console.error(`❌ Description text is missing expected parts: ${descriptionText}`);
    }
  
    return headerMatch && allDescriptionChecks;
  }
  
  async clickResendEmail() {
    await this.resendEmailButton.click();
  }
  
  async navigateBackToLogin() {
    await this.navigateToLoginButton.click();
  }

  async verifyResendEmailConfirmation() {
    const resendCooldownMessage = this.page.locator('span', {
      hasText: 'You can resend login link in',
    });
  
    await expect(resendCooldownMessage).toBeVisible({
      timeout: 5000, // Adjust if needed
    });
  
    // Optionally validate the format using regex
    const text = await resendCooldownMessage.textContent();
    expect(text).toMatch(/You can resend login link in \d+ seconds/);
  }  

  async sendMagicLinkToEmail(email: string): Promise<void> {
    await this.continueWithEmailButton.waitFor({ state: 'visible' });
    await this.continueWithEmailButton.click();

    await this.workEmailInput.waitFor({ state: 'visible' });
    await this.workEmailInput.fill(email);

    await this.sendLoginLinkButton.waitFor({ state: 'visible' });
    await this.sendLoginLinkButton.click();
}

async verifyMagicLinkSentAndResend(email: string): Promise<void> {
    const isConfirmationVisible = await this.verifyEmailSentConfirmation(email);
    expect(isConfirmationVisible).toBe(true);

    await this.clickResendEmail();
    await this.verifyResendEmailConfirmation();

    await this.navigateBackToLogin();
}

  
}
