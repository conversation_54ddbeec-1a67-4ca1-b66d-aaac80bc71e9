import { expect, Locator, Page } from "@playwright/test";
import { BaseTest } from "../utilities/BaseTest";
import { AllTypes } from "../utilities/Types";
import test from "node:test";
export class OverviewPage {
    readonly overviewTab: Locator;
    readonly ResourceCatalogeditIcon: Locator;
    readonly saveButton: Locator;
    readonly formErrors: Locator;
    readonly jsonTab: Locator;
    readonly dependencyTab: Locator;
    readonly addDependencyButton: Locator;
    readonly addDependenctButtonInsideModal: Locator;
    readonly mapEnvironemtnsButton: Locator;
    readonly mapEnvironmentsInputField: Locator;
    readonly dependencyRowLocator: Locator;
    readonly envDropdownLocator: Locator;
    readonly dependencyRowsWithExpandableOn: Locator;
    readonly lastDeployedExpandableButton: Locator;
    readonly currentAppDependencyRow: Locator;
    readonly editDependencyButton: Locator;
    readonly emptyStateOfDependencyInjection: Locator;
    readonly checkBoxClickableSpan: Locator;
    readonly bulkHibernateUnhibernateResponseDiv: Locator;
    readonly restartWorkloadButton: Locator;
    readonly bulkRestartDiv: Locator;
    readonly closePopupButton: Locator;
    readonly clonePipelineConfigButton: Locator;
    readonly targetEnvironmentInputField: Locator;
    readonly clonePipelineButton: Locator;
    readonly restartWorkloadsButtonInsideModal: Locator;
    constructor(private page: Page) {

        this.overviewTab = this.page.locator('//*[text()="Overview"]');
        this.ResourceCatalogeditIcon = this.page.locator('//*[text()="Catalog"]/parent::div/following-sibling::div[//span[text()="Edit"]]');
        this.saveButton = this.page.locator('//*[text()="Save"]');
        this.formErrors = this.page.locator('//*[@class="form__error"]');
        this.jsonTab = this.page.locator('//*[text()="Json"]');
        this.dependencyTab = this.page.locator('//*[text()="Dependencies"]');
        this.addDependencyButton = this.page.locator('//*[text()="Add Dependency"]');
        this.addDependenctButtonInsideModal = this.page.locator('//*[@class="drawer right show"]//*[text()="Add Dependency"]');
        this.mapEnvironemtnsButton = this.page.locator('//*[text()="Map Environments"]');
        this.mapEnvironmentsInputField = this.page.locator('//*[@class="drawer right show"]//input[@type="text"]');
        this.dependencyRowLocator = this.page.locator("//*[contains(@class,'dependencies-row')]");
        this.envDropdownLocator = this.dependencyRowLocator.locator("//*[contains(@class,'-indicatorContainer')]");
        this.dependencyRowsWithExpandableOn = this.page.locator("//*[contains(@class,'dependencies-row last-deployed-expanded ')]");
        this.lastDeployedExpandableButton = this.page.locator('//*[@aria-label="Expand last deployment"]');
        this.currentAppDependencyRow = this.page.locator("//*[contains(@class,'dependencies-row__current-app')]");
        this.editDependencyButton = this.page.locator('//*[text()="Edit Dependency"]');
        this.emptyStateOfDependencyInjection = this.page.getByTestId('generic-empty-state');
        this.checkBoxClickableSpan = this.page.locator('//*[@class="form__checkbox-container"]');
        this.bulkHibernateUnhibernateResponseDiv = this.page.locator("//*[contains(@class,'hibernate-status-body')]");
        this.restartWorkloadButton = this.page.getByTestId('environment-overview-action-widget-restart-workloads');
        this.bulkRestartDiv = this.page.locator(`//*[@class="drawer right show"]`);
        this.closePopupButton = this.page.locator('//*[@aria-label="Close modal" or @data-testid="close-popup" or text()="Close"]');
        this.clonePipelineConfigButton = this.page.getByTestId('clone-environment');
        this.targetEnvironmentInputField = this.page.locator('//*[text()="Select target environment"]');
        this.clonePipelineButton = this.page.getByTestId('clone-pipeline-cta');
        this.restartWorkloadsButtonInsideModal = this.page.getByTestId('rotate-workloads-button');


    }
    /*
    this function is only handling the dropdown not input field , for inptu field we can add one more flag on based of that 
    */

    async fillResourceCatalogDetails(data: { keyName: string, values: string[] }[], isFormErrorVisible: boolean = false) {
        await this.ResourceCatalogeditIcon.click();
        if (isFormErrorVisible) {
            await this.saveButton.click();
            await expect(this.formErrors.first()).toBeVisible({ timeout: 12000 });
        }
        for (const key of data) {
            for (const value of key.values) {
                await this.page.locator(`div[class*="__root_Contacts_${key.keyName}__control"]`).click();
                await this.page.keyboard.type(value);
                await this.page.keyboard.press('Enter');
            }
        }
        await this.saveButton.click();
    }


    /**
     * this method is used to verify catalog framework details that we have filled 
     * we are checking in both gui and jsons
     * @param data 
     */
    async verifyFieldsAfterSaving(data: { gui: { sectionName: string, keyName: string, valueName: string[], hiddenFields?: string[] }, json?: string[] }) {
        for (const key of data.gui.valueName)
            await expect(this.page.locator(`//*[text()="${data.gui.sectionName}"]/parent::div//*[text()="${data.gui.keyName}"]/parent::div//*[text()="${key}"]`)).toBeVisible({ timeout: 14000 });
        if (data.gui.hiddenFields) {
            for (const key of data.gui.hiddenFields) {
                await expect(this.page.locator(`//*[text()="${key}"]`)).toBeHidden({ timeout: 4000 });
            }
        }
        if (data.json) {
            await this.jsonTab.click();
            for (const key of data.json) {
                await expect(this.page.locator('//code')).toContainText(key);
            }
        }
    }



    /**
     * just to click on overview tab 
     */
    async clickOnOverviewTab() {
        await this.overviewTab.click();
    }


    /**
     * this method is used to add dependencies 
     * @param appName 
     */
    async addDependency(appName: string) {
        await this.dependencyTab.click();
        await this.addDependencyButton.click();
        await this.addDependenctButtonInsideModal.click();
        await this.page.keyboard.type(appName);
        await this.page.keyboard.press('Enter');
    }


    /**
     * after adding dependencies this method is used to map environments 
     * @param data 
     * @param isFirstTimeDependencyAddition 
     */
    async mapEnvironments(data: AllTypes.DependencyInjection.mappingOfEnvironments[], isFirstTimeDependencyAddition: boolean = true) {
        if (isFirstTimeDependencyAddition) {
            this.mapEnvironemtnsButton.click();
        }
        for (const value of data) {
            console.log('value is ' + value.currentAppEnvName);
            await this.page.locator(`//aside[contains(@class,'flexbox')]`).locator(`//*[text()="${value.currentAppEnvName}"]`).click();
            await this.mapEnvironmentsInputField.pressSequentially(value.mappedAppEnvName, { delay: 200 });
            await this.page.keyboard.press('Enter');
        }
        await this.saveButton.click();
    }



    /**
     * after mapping , we use this method to verify the details
     * @param data 
     */
    async verifyDependencyDetails(data: AllTypes.DependencyInjection.dependencyVerificationData[]) {
        await this.lastDeployedExpandableButton.click();
        for (const key of data) {
            console.log('first loop env name ' + key.currentEnvName);
            if (await this.currentAppDependencyRow.locator(`//*[text()="${key.currentEnvName}"]`).isHidden()) {
                await this.envDropdownLocator.click();
                await this.page.locator(`//*[@role="listbox"]//*[text()="${key.currentEnvName}"]`).click();
            }
            await this.page.locator(`//*[text()="${key.currentEnvName}"]`).first().waitFor();
            let arr = Object.keys(key.verificationData).map(key => Number(key));
            for (const element of arr) {
                console.log('index number' + element)
                let valueArray = Object.keys(key.verificationData[element]);
                for (const elementValue of valueArray) {
                    console.log('internal value to verify visibility is ' + elementValue);
                    expect(await this.dependencyRowsWithExpandableOn.nth(element).locator(`//*[contains(text(),"${elementValue}")]`).isVisible()).toBe(key.verificationData[element][elementValue])
                }
            }
        }

    }


    /**
     * this method is used to click on any parent app
     * @param appName 
     */
    async clickOnAnyParentDependentApp(appName: string) {
        await this.dependencyRowLocator.locator(`//*[text()="${appName}"]`).click()
    }


    /**
     * this method is used to remocal of dependencies 
     * @param parentAppName 
     */
    async removalOfDependencies(parentAppName: string[]) {
        await this.editDependencyButton.click();
        for (const key of parentAppName) {
            console.log('key value is ' + key);
            await this.page.locator(`//div[div[span[text()="${key}"]]]//*[@class="icon-dim-16 cursor"]`).click();
        }
        await this.saveButton.click();
    }


    /**
     * this method is to verify null state 
     */
    async verifyNullStateOfDependencyInjection() {
        await expect(this.emptyStateOfDependencyInjection).toBeVisible({
            timeout: 12000
        });
    }

    async verifyAppRelatedDetailsOnAppGroup(appName: string, textToVerify: string[]) {
        await this.overviewTab.click();
        await this.checkBoxClickableSpan.first().waitFor({ timeout: 6000 });
        for (const key of textToVerify) {
            await expect(this.page.locator(`//*[text()='${appName}']/ancestor::div[contains(@class,"app-deployments-info-row")]//*[text()="${key}"]`)).toBeVisible({ timeout: 9000 });
        }
    }

    async selectApplicationsOnAppGroupOverViewPage(appNames?: string[]) {
        if (appNames) {
            for (const key of appNames) {
                await this.page.locator(`//*[text()="${key}"]`).hover();
                await this.page.locator(`//*[text()="${key}"]/ancestor::div[contains(@class,'environment-overview-table__row ')]`).locator(this.checkBoxClickableSpan).click();
            }
        }
        else {
            await this.checkBoxClickableSpan.first().click();
        }
    }

    // Hibernation Initiated -> hibernation text 
    // Hibernation already in progress -> skipped text
    async bulkHibernateOrUnhibernateOnAppGroup(hibernateAction: boolean, messageToVerify: string) {
        let buttonToClick = hibernateAction ? 'hibernate' : 'unhibernate';
        await this.page.getByTestId(`environment-overview-action-widget-${buttonToClick}`).click();
        await this.page.locator('//*[text()="Cancel"]/following-sibling::button').click();
        await expect(this.bulkHibernateUnhibernateResponseDiv.locator(`//*[text()="${messageToVerify}"]`)).toBeVisible({ timeout: 32000 });
        await this.closePopupButton.click();
    }
    async bukRestartWorkloads(restartData: { message: string, numberOfOccurences: number }[]) {
        await this.restartWorkloadButton.click();
        await this.bulkRestartDiv.locator(this.restartWorkloadsButtonInsideModal).click();
        for (const key of restartData) {
            await expect(this.page.locator(`//*[text()="${key.message}"]`)).toHaveCount(key.numberOfOccurences, { timeout: 25000 });
        }
        await this.closePopupButton.click();
    }


    //Target pipeline already exists
    ////*[text()="Clone successful"]
    async clonePipelineConfiguration(data: { envName: string, cloneToSameWorkflow: boolean, responseVerification: { text: string, numberOfOccurrences: number }[] }) {
        let numberOfOccurences: number;
        let cloneOptionToSelect = data.cloneToSameWorkflow ? 'SAME_WORKFLOW' : 'NEW_WORKFLOW';
        await this.page.locator(`//*[contains(@class,'bulk')]//*[@data-testid="environment-overview-pop-up-menu"]`).click();
        await this.clonePipelineConfigButton.click();
        await expect(async () => {
            await this.targetEnvironmentInputField.click({ force: true });
            console.log('mine value is' + data.envName);
            await this.page.keyboard.type(data.envName);
            // await this.targetEnvironmentInputField.pressSequentially(data.envName);
            await this.page.keyboard.press('Enter');
            await this.page.getByTestId(`${cloneOptionToSelect}-radio-item-span`).click();
            await this.clonePipelineButton.click({ timeout: 12000 });
        }).toPass({ timeout: 3 * 1000 * 60 });
        for (const key of data.responseVerification) {
            numberOfOccurences = key.text == 'Clone successful' ? key.numberOfOccurrences * 2 : key.numberOfOccurrences;
            await expect(this.page.locator(`//*[text()="${key.text}"]`).first()).toHaveCount(key.numberOfOccurrences);
        }
        await this.closePopupButton.click();
    }

    async addTagsViaAppOverviewPage(key: string, value: string): Promise<void> {
        // Step 1: Click "Add Tags"
        const addTagsButton = this.page.locator('[data-testid="tag-chip-container"] span.cursor', { hasText: "Add Tags" });
        await addTagsButton.click();

        // Step 2: Click "+" Add Row
        const addRowButton = this.page.locator('[data-testid="data-table-add-row-button"]');
        await addRowButton.click();

        // Step 3: Fill key/value in the last editable row
        const lastRow = this.page.locator('.dynamic-data-table__row').last();
        const keyInput = lastRow.locator('textarea[data-testid$="-tagKey-cell"]');
        const valueInput = lastRow.locator('textarea[data-testid$="-tagValue-cell"]');

        await keyInput.fill(key);
        await valueInput.fill(value);

        // Step 4: Click "Save"
        const saveButton = this.page.locator('[data-testid="overview-tag-save-button"]');
        await saveButton.click();

        // ✅ Wait for Save button to disappear (modal close or UI refresh)
        await saveButton.waitFor({ state: 'detached' });

        // ⬇️ OR wait for tag to appear in the tag list area directly
        await this.page.locator(`[data-testid^="tag-key-overview-"]`, { hasText: key }).waitFor();
        await this.page.locator(`[data-testid^="tag-value-overview-"]`, { hasText: value }).waitFor();

        // Proceed to verification
        await this.verifyTagAdded(key, value);
    }


    async verifyTagAdded(key: string, value: string): Promise<void> {
        const tagKeyLocator = this.page.locator('[data-testid^="tag-key-overview-"]', { hasText: key });
        const tagValueLocator = this.page.locator('[data-testid^="tag-value-overview-"]', { hasText: value });

        const keyCount = await tagKeyLocator.count();
        const valueCount = await tagValueLocator.count();

        if (keyCount === 0 || valueCount === 0) {
            throw new Error(`❌ Tag verification failed. Key: "${key}", Value: "${value}" not found.`);
        }
    }

    async removeDesiredTags(key: string, value: string): Promise<void> {
        const editTagsButton = this.page
            .locator('div.flexbox.flex-justify.dc__gap-10', { hasText: 'Tags' })
            .locator('svg.icon-dim-16.cursor.mw-16');

        await editTagsButton.click();

        // Wait until at least one usable key input is visible
        const rows = this.page.locator('.dynamic-data-table__row');
        await this.page
            .locator('textarea[data-testid$="-tagKey-cell"]')
            .first()
            .waitFor({ state: 'visible' });

        const rowCount = await rows.count();

        for (let i = 0; i < rowCount; i++) {
            const row = rows.nth(i);
            const keyInput = row.locator('textarea[data-testid$="-tagKey-cell"]');
            const valueInput = row.locator('textarea[data-testid$="-tagValue-cell"]');

            if (!(await keyInput.isVisible()) || !(await valueInput.isVisible())) {
                continue;
            }

            const keyText = (await keyInput.inputValue()).trim();
            const valueText = (await valueInput.inputValue()).trim();

            if (keyText === key && valueText === value) {
                const deleteButton = row.locator('[data-testid="dynamic-data-table-row-delete-btn"]');
                await deleteButton.click();

                const saveButton = this.page.locator('[data-testid="overview-tag-save-button"]');
                await saveButton.click();
                return;
            }
        }

        throw new Error(`❌ Tag "${key}: ${value}" not found for deletion.`);
    }

}