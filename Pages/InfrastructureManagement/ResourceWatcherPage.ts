import { BasePage } from '../BasePage';
import { Page, Locator, expect } from '@playwright/test';
import { RunbookTrigger } from '../../enums/InfrastructureManagement/ResourceWatcher/RunbookTriggerEnum';
import { NamespaceSelectionOption } from '../../enums/InfrastructureManagement/ResourceWatcher/NamespaceSelectionOptionEnum';
import { InfrastructureManagementUrls } from '../../enums/UrlNavigations/InfrastructureManagementUrls';

export class ResourceWatcherPage extends BasePage {
  readonly namespacesToWatchTab: Locator;
  readonly interceptChangesTab: Locator;
  readonly executeRunbookTab: Locator;

  readonly nameInput: Locator;
  readonly descriptionTextarea: Locator;
  readonly createWatcherButtonInsideModal: Locator;

  readonly specificClustersRadio: Locator;

  readonly interceptTitle: Locator;
  readonly interceptSubTitle: Locator;
  readonly infoBarTitle: Locator;

  readonly resourceKindInput: Locator;
  readonly createdCheckbox: Locator;
  readonly updatedCheckbox: Locator;
  readonly deletedCheckbox: Locator;
  readonly celExpressionTextarea: Locator;
  readonly onlySelectedNamespacesMessage: Locator;

  readonly triggerDevtronJobRadio: Locator;
  readonly triggerWebhookRadio: Locator;
  readonly interceptedEventsTabButton: Locator;
  readonly searchEventsInputBox: Locator;
  readonly clusterFilterApplyButton: Locator;
  readonly clearAllFiltersButton: Locator;
  readonly closeButtonModal: Locator;
  readonly createWatcherButton:Locator;

  constructor(public page: Page) {
    super(page);

    this.namespacesToWatchTab = page.locator('[data-test-id="namespaceDetails-tab"]');
    this.interceptChangesTab = page.locator('[data-test-id="interceptChanges-tab"]');
    this.executeRunbookTab = page.locator('[data-test-id="runbook-tab"]');

    this.nameInput = page.getByTestId('name');
    this.descriptionTextarea = page.getByTestId('description');
    this.createWatcherButtonInsideModal= page.getByTestId('build-pipeline-button');

    this.specificClustersRadio = page.locator('[data-testid="specificClusters-radio-button-span"]');

    this.interceptTitle = page.locator('text=Intercept changes in Kubernetes resources');
    this.interceptSubTitle = page.locator('text=Select Kubernetes resource kinds and define conditions');
    this.infoBarTitle = page.locator('h3:has-text("Sample watch condition")');

    this.resourceKindInput = page.locator('input#resourceSelector');
    this.createdCheckbox = page.getByTestId('chk-CREATED');
    this.updatedCheckbox = page.getByTestId('chk-UPDATED');
    this.deletedCheckbox = page.getByTestId('chk-DELETED');
    this.celExpressionTextarea = page.getByTestId('cel-expression');

    this.onlySelectedNamespacesMessage = page.locator('text=Only selected namespaces will be watched for this cluster');

    this.triggerDevtronJobRadio = page.getByTestId('trigger-devtron-job-radio-button');
    this.triggerWebhookRadio = page.getByTestId('trigger-webhook-radio-button');
    this.interceptedEventsTabButton = page.getByTestId('intercepted-events');
    this.searchEventsInputBox = page.getByTestId('search-bar');
    this.clusterFilterApplyButton = page.getByTestId('filter-select-picker-apply');
    this.clearAllFiltersButton = page.getByRole('button', { name: 'Clear All Filters' });
    this.closeButtonModal = page.getByTestId('close-modal-button');
    this.createWatcherButton= this.page.locator('//*[text()="Create watcher"]');
  }



  async goToResourceWatcherListing() {
    await this.page.goto(`${process.env.BASE_SERVER_URL}${InfrastructureManagementUrls.resourceWatcherPageUrl}`);
  }
  async verifySidebarTabsVisible() {
    await expect(this.namespacesToWatchTab).toBeVisible();
    await expect(this.interceptChangesTab).toBeVisible();
    await expect(this.executeRunbookTab).toBeVisible();
  }

  async selectSpecificClusters(clustersToSelect: string[]): Promise<void> {
    await this.namespacesToWatchTab.click();
    await this.specificClustersRadio.click();
  
    for (const cluster of clustersToSelect) {
      const labelLocator = this.page.locator(`input[data-testid="cluster-${cluster}"]`).locator('..');
  
      if (await labelLocator.count() === 0) {
        console.warn(`Label for cluster "${cluster}" not found.`);
        continue;
      }
  
      await labelLocator.first().waitFor({ state: 'visible', timeout: 10000 });
      await labelLocator.first().scrollIntoViewIfNeeded();
  
      const inputLocator = labelLocator.locator('input');
      const isChecked = await inputLocator.isChecked();
  
      if (!isChecked) {
        await labelLocator.first().click();
      }
    }
  }
  async clickCreateWatcherButton() {
    await this.createWatcherButton.click();
  }
  
  async selectSpecificNamespacesToWatchForSpecificCluster(
    clusterName: string,
    namespaces: string[],
    option: NamespaceSelectionOption
  ) {
    const clusterCheckbox = this.page.locator(`[data-testid="cluster-${clusterName}"]`);
    if (!(await clusterCheckbox.isChecked())) {
      await clusterCheckbox.check();
    }

    const clusterContainer = clusterCheckbox.locator('xpath=ancestor::div[contains(@class, "flexbox-col")]');

    // Select subgroup option (e.g., Specific Namespaces)
    const subgroupDropdown = clusterContainer.locator('.subgroupSelect__value-container');
    await subgroupDropdown.click();

    const optionLocator = this.page.locator(`div[id^="react-select"] >> text=${option}`);
    await optionLocator.waitFor({ state: 'visible' });
    await optionLocator.click();

    // Namespace selector dropdown
    const namespaceSelector = clusterContainer.locator('.namespace__select__dropdown-indicator');
    await namespaceSelector.waitFor({ state: 'visible' });

    if (option === NamespaceSelectionOption.SPECIFIC_NAMESPACES) {
      for (const ns of namespaces) {
        await namespaceSelector.click();

        const dropdownOption = this.page.locator(`div[role="listbox"] >> text=${ns}`);
        await dropdownOption.click();
        await expect(this.page.locator(`.namespace__select__value-container:has-text("${ns}")`)).toBeVisible();
      }
    }
  }

  async verifyNamespaceSelection(clusterName: string, expectedNamespaces: string[]) {
    const clusterCheckbox = this.page.locator(`input[data-testid="cluster-${clusterName}"]`);
    await expect(clusterCheckbox).toBeChecked();

    const subgroupSelectInput = this.page.locator('input[name="subgroupSelect"]');
    await expect(subgroupSelectInput).toHaveValue('specificNamespace');

    const selectionTypeInput = this.page.locator('input[name="selectionTypeSelect"]');
    await expect(selectionTypeInput).toHaveValue('INCLUDED');

    for (const ns of expectedNamespaces) {
      await expect(this.page.locator(`.namespace__select__value-container:has-text("${ns}")`)).toBeVisible();
    }

    await expect(this.onlySelectedNamespacesMessage).toBeVisible();
  }

  async goToInterceptChanges() {
    await this.interceptChangesTab.click();
  }

  async verifyInterceptChangesUI() {
    await expect(this.interceptTitle).toBeVisible();
    await expect(this.interceptSubTitle).toBeVisible();
    await expect(this.infoBarTitle).toHaveText('Sample watch condition');
    await expect(this.resourceKindInput).toBeVisible();

    await expect(this.createdCheckbox).toBeChecked();
    await expect(this.updatedCheckbox).toBeChecked();
    await expect(this.deletedCheckbox).toBeChecked();

    await expect(this.celExpressionTextarea).toBeVisible();
  }

  async setInterceptChangeConfig(resourceKinds: string[], celExpression: string) {
    await this.resourceKindInput.fill('');
    for (const kind of resourceKinds) {
      await this.resourceKindInput.fill(kind);
      await this.page.keyboard.press('Enter');
    }
    await this.celExpressionTextarea.fill(celExpression);
  }

  async goToExecuteRunbook() {
    await this.executeRunbookTab.click();
  }

  async selectRunbookTriggerOption(option: RunbookTrigger) {
    if (option === RunbookTrigger.DEVTRON_JOB) {
      await this.triggerDevtronJobRadio.check();
    } else {
      await this.triggerWebhookRadio.check();
    }
  }

  async selectDevtronJob(jobName: string, pipelineName: string) {
    console.log("jobnameis",jobName)
    console.log("pipelinenameis",pipelineName)
    const dropdownIndicator = this.page.locator('.create-app__select-project__dropdown-indicator');
    const option = this.page.locator(`.create-app__select-project__option >> text="${jobName}"`);

    await dropdownIndicator.click({delay:500});
    await dropdownIndicator.type(pipelineName, { delay: 500 });
    await dropdownIndicator.press('Enter');
  }

  async selectJobEnvironment(envName: string) {
    const dropdownIndicator = this.page.locator('.job-pipeline-environment-dropdown__dropdown-indicator');
    const option = this.page.locator(`.job-pipeline-environment-dropdown__option >> text="${envName}"`);

    await dropdownIndicator.click();
    await option.waitFor({ state: 'visible' });
    await option.click();
    await expect(this.page.locator('.job-pipeline-environment-dropdown__single-value')).toHaveText(envName);
  }

  async clickCreateWatcherInsideModalButton() {
    await this.createWatcherButtonInsideModal.click();
  }

  async verifyThatClusterIsVisibleInWatcherList(clusterName: string) {
    await this.page.waitForURL('**/resource-watcher/watchers', { timeout: 30000 });

    const rows = this.page.locator('.watch-event-list__row');
    await rows.first().waitFor({ state: 'visible', timeout: 30000 });

    const count = await rows.count();
    expect(count).toBeGreaterThan(1);

    let clusterFound = false;
    for (let i = 1; i < count; i++) {
      const row = rows.nth(i);
      const nameCell = row.locator('[data-testid="Name"] span');

      if (await nameCell.count() > 0) {
        const text = (await nameCell.textContent())?.trim();
        if (text === clusterName) {
          clusterFound = true;
          break;
        }
      } else {
        const fullText = (await row.textContent())?.trim();
        if (fullText?.includes(clusterName)) {
          clusterFound = true;
          break;
        }
      }
    }

    expect(clusterFound).toBe(true);
  }

  async deleteWatcherByName(watcherName: string): Promise<void> {
    await this.goToResourceWatcherListing();
    await this.searchWatcherByName(watcherName);
    const watcherRow = this.page.locator(`.watch-event-list__row:has-text("${watcherName}")`);  
    await watcherRow.hover();
    const deleteButton = watcherRow.locator(`div.dc__visible-hover--child[data-test-id="delete-${watcherName}"]`);
    await deleteButton.waitFor({ state: 'visible' });
    await deleteButton.click();  
   await this.page.keyboard.press('Enter');
  }
   
  async searchWatcherByName(watcherName: string): Promise<void> {
    const searchInput = this.page.getByTestId('search-bar');
    await searchInput.fill(watcherName);
    await this.page.keyboard.press('Enter');
  }

  async searchAndVerifyInterceptedRows(
    searchTerm: string,
    expectedInterceptor: string,
    expectedClusterNamespace: string
  ) {
    const searchInput = this.page.getByTestId('search-bar');
    await searchInput.fill(searchTerm);
    await this.page.keyboard.press('Enter');
  
    // Wait until at least one result row appears
    const rows = this.page.locator('.generic-table__row');
    await expect(rows.first()).toBeVisible({ timeout: 10000 });
  
    const rowCount = await rows.count();
  
    if (rowCount === 0) {
      throw new Error('No matching intercepted event rows found.');
    }
  
    // Check if any row contains the expected interceptor and clusterNamespace
    let foundMatchingRow = false;
  
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
  
      // Get all direct child divs
      const divs = row.locator('> div');
      const divCount = await divs.count();
  
      let clusterNamespace = '';
      let interceptor = '';
  
      for (let j = 0; j < divCount; j++) {
        const div = divs.nth(j);
  
        // Check both visible text and data-test-id attribute in one go
        const textContent = await div.innerText().catch(() => '');
        const dataTestIdAttr = await div.getAttribute('data-test-id').catch(() => '');
  
        if (textContent.includes('/') && textContent.includes('_cluster')) {
          clusterNamespace = textContent.trim();
        }
  
        if (textContent.includes(expectedInterceptor) || (dataTestIdAttr && dataTestIdAttr.includes(expectedInterceptor))) {
          interceptor = textContent.trim() || dataTestIdAttr || '';
        }
      }
  
      if (clusterNamespace === expectedClusterNamespace && interceptor.includes(expectedInterceptor)) {
        foundMatchingRow = true;
        break; // We found at least one matching row, can stop
      }
    }
  
    if (!foundMatchingRow) {
      throw new Error(
        `No intercepted rows found matching interceptor "${expectedInterceptor}" and cluster/namespace "${expectedClusterNamespace}".`
      );
    }
  }  
  
  async selectSpecificClustersToApplyFilter(clustersToSelect: string[]): Promise<void> {
    const inputSelector = 'input#cluster-select';
    await this.page.click(inputSelector);
  
    for (const cluster of clustersToSelect) {
      await this.page.fill(inputSelector, cluster);
      const optionSelector = `div.cluster-select__option >> text="${cluster}"`;
      await this.page.waitForSelector(optionSelector, { timeout: 5000 });
      await this.page.click(optionSelector);
      await this.page.fill(inputSelector, '');
    }
    await this.clusterFilterApplyButton.click();
  }

  async selectSpecificActionTypesToApplyFilter(actionTypes: string[]): Promise<void> {
    const inputSelector = 'input#action-type-select';
  
    for (const type of actionTypes) {
      await this.page.click(inputSelector);
      await this.page.fill(inputSelector, type);
      const optionLocator = this.page.locator(`div.action-type-select__option >> text="${type}"`);
      await optionLocator.waitFor({ state: 'visible', timeout: 5000 });
      await optionLocator.click();
      await this.page.fill(inputSelector, '');
    }
    await this.clusterFilterApplyButton.click();
  }
  
  async selectNamespacesToApplyFilter(namespaces: string[]): Promise<void> {
    const inputSelector = 'input#namespace-select';
  
    for (const ns of namespaces) {
      await this.page.click(inputSelector);
      await this.page.fill(inputSelector, ns);
      const optionLocator = this.page.locator(`.namespace-select__option >> text="${ns}"`);
      await optionLocator.waitFor({ state: 'visible', timeout: 5000 });
      await optionLocator.click();
      await this.page.fill(inputSelector, '');
    }
    await this.clusterFilterApplyButton.click();
  }
  
  async selectWatchers(watchers: string[]): Promise<void> {
    const inputSelector = 'input#watcher-select';
  
    for (const watcher of watchers) {
      await this.page.click(inputSelector);
      await this.page.fill(inputSelector, watcher);
      const optionLocator = this.page.locator(`.watcher-select__option >> text="${watcher}"`);
      await optionLocator.waitFor({ state: 'visible', timeout: 5000 });
      await optionLocator.click();
      await this.page.fill(inputSelector, '');
    }
    await this.clusterFilterApplyButton.click();  
  }

 async clickOnInterceptedEventsTabButton(){
  await this.interceptedEventsTabButton.waitFor({state:'visible'});
  await this.interceptedEventsTabButton.click();  
 } 

 async verifyViewChangesModal(resourceActionStatus:String){
    await this.page.getByText('View Changes').nth(0).click();
    await this.page.getByText(`Resource ${resourceActionStatus.toLowerCase()}`).waitFor({state:'visible'})
    await this.closeButtonModal.click({force:true})
 }

 async verifyFilteredInterceptedEvents(expectedCluster: string, expectedNamespace: string, expectedStatus: string) {
     const rows = this.page.locator('.generic-table__row');

  const count = await rows.count();
  if (count === 0) {
    throw new Error('No intercepted events found after applying filters.');
  }

  for (let i = 0; i < count; i++) {
    const row = rows.nth(i);

    // Verify status (e.g., DELETED)
    await row.getByTestId(`change-in-resource-${expectedStatus.toLowerCase()}-icon`).waitFor({state: 'visible'});
     
    await this.verifyViewChangesModal(expectedStatus);

    // More specific and reliable selectors
    const clusterNamespace = await row.locator('div.lh-20.dc__truncate').innerText();
    const [cluster, namespace] = clusterNamespace.trim().split('/');

    if (cluster !== expectedCluster || namespace !== expectedNamespace) {
      throw new Error(`Row ${i} - Expected cluster/namespace: ${expectedCluster}/${expectedNamespace}, found: ${clusterNamespace}`);
    }

    // (Optional) Verify Intercepted By if needed:
    const interceptedBy = await row.locator('p.lh-20.dc__truncate').nth(1).innerText();
    if (!interceptedBy.startsWith('watcher-')) {
      throw new Error(`Row ${i} - Unexpected Intercepted By format: ${interceptedBy}`);
    }
  }
}

async clearDeletedFilter() {
  const filterChips = this.page.locator('div.flexbox.flex-align-center');

  const count = await filterChips.count();
  for (let i = 0; i < count; i++) {
    const chip = filterChips.nth(i);
    const valueSpan = chip.locator('span').nth(2);
    const valueText = (await valueSpan.textContent())?.trim();

    if (valueText === 'DELETED') {
      const removeBtn = chip.locator('button[aria-label="Remove filter"]');
      await removeBtn.click();
      break;
    }
  }
}

async clearAllFiters(){
  await this.clearAllFiltersButton.click();
}

}
