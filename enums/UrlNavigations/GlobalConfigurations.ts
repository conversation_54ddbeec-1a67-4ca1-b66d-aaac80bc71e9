export enum GlobalConfigurationsUrls{
    hostUrl="/global-configuration/host-url",
    clusterAndEnvironments="/global-configuration/cluster-env",
    envSectionInClustersAndEnvironmentsPage=`/global-configuration/cluster-env?selectedTab=environments`,
    containerOciRegistry="/global-configuration/docker",
    userPermissions="/global-configuration/auth/users",
    permissionGroups="/global-configuration/auth/permission-groups",
    apiTokens="/global-configuration/auth/api-token/list",
    permissionGroupsListingPage=`/global-configuration/auth/groups`,
    ssoLoginServiceProviderPages=`/global-configuration/auth/login-service`,
    userPermissionListingPage=`/global-configuration/auth/users`,
    loginPageUrl="/dashboard/login/sso?continue=/app/list/d"
}