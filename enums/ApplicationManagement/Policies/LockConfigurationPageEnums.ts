export enum ApplyProfileScopeCategoryEnum {
    global = "Global",
    byMatchCriteria = "By match criteria",
    specificDeploymentTemplates = "Specific deployment templates",
    specificCriteria = "Specific criteria"
}

export enum ApplyProfileFilterByEnum {
    baseDeploymentTemplate = "Base deployment template",
    baseConfiguration = "Base configuration",
    project = "Project",
    application = "Application",
    environment = "Environment",
    cluster = "Cluster"
}
