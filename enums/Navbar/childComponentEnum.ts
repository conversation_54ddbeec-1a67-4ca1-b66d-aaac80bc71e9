export enum applicationManagementChildComponentEnum{
    applications="applications",
    applicationGroups="application Groups",
    chartStore="chart Store",
    bulkEdit="bulk Edit",
    applicationTemplates="application Templates",
    projects="Projects",
    gitosp="Gitops",
    gitAccounts="Git accounts",
    externalLinks="External links",
    chartRepositories="Chart repository",
    DeploymentCharts="Deployment Charts",
    notifications="notifications",
    catalogFrameworks="Catalog frameworks",
    scopedVariables="scoped Variables",
    buildInfra= "build Infra",
    deploymentWindow= "Deployment Window",
    approvalPolicy="approval policy",
    pluginPolicy="plugin policy",
    pullImageDigest='pull image digest',
    tagPolicy="tag policy",
    filterConditions="filter conditions",
    imagePromotion ="image promotion",
    lockDeploymentConfiguration="Lock Deployment Configuration",
    overview="overview",
    
}

export enum  infrastructureManagementChildComponentsEnum{
    overview="overview",
    resourceBrowser="resource browser",
    resourceWatcher="resource watcher",
    catalogFramework="catalog framework"
}

export enum softwareReleaseManagementChildComponentsEnum{
    overview="overview",
    releaseHub="release hub",
    tenants="tenants"
}

export enum costVisibilityChildComponentsEnum{
    overview="overview",
    clusters="clusters",
    environments="environments",
    projects="projects",
    applications="applications",
    configurations="configurations"
}

export enum securityCenterChildComponentsEnum{
    overview="overview",
    securityScnas="security scans",
    securityPolicy="security policy"
}

export enum AutomationAndEnablementChildComponentEnums{
jobs="jobs",
alerting="alerting",
incidentResponse="incident response",
apiPortal="api portal",
runboookAutomation='runbook automation'
}
export enum BackupAndRestoreChildComponentEnums{
    overview="overview"
}
export enum AiRecommendationsChildComponentEnums{
    overview="overview"
}
export enum globalConfigurationsChildComponentEnums{
    ssoLoginServices="sso login services",
    hostUrls="host url",
    clusterAndEnvironments="cluster & environment",
    containerOciRegistry="container/oci registry",
    userPermissions="user permissions",
    permissionGroups="permission groups",
    apiTokens='api tokens'
}
