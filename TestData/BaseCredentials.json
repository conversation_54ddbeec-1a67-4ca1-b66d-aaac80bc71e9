{"ProjectName": "devtron-demo", "VirtualClusterName": "virtual-cluster-automation", "VirtualEnvironment": "playwright-vir", "customChartName": "playwright-automation-test", "customChartVersion": "0.4.0", "BranchName": ["main", "main"], "applicationGroup": "Application Groups", "GitAccountName": "Github Public", "ContainerRegistryName": "devtron-test", "ContainerRepository": "test", "EnvNameForCD": ["automation", "devtron-demo"], "envNameForCharts": "devtron-demo", "PluginPolicy": "K6 Load testing", "ChartName": "memcached", "ChartSource": "wsdkh", "TerminalScript": ["ls"], "InclusterEnv": "abc", "TerminalExpectedValue": ["home"], "ciTestFixedAutoPipeline": "testfixedautopipeline", "githubAccountOwner": "aman10000q", "gitRepoName": "sample-go-app", "customCiTag": ["@@@", "{x}{x}", "v1.x", "{x"], "addChartRepo": {"PublicRepositoryURL": "https://devtron-labs.github.io/helm-pilot/", "privateRepoUsername": "admin", "privateRepoPassword": ""}, "virtualData": {"pipelineType": "Automatic", "VirtualClusterName": "virtual-cluster-automation", "VirtualEnvironment": "virtual-env"}, "userDefinedGitRepo": ["useDefaultGit", "allowChangingGit", "Auto-create repository", "Commit manifest to a desired repository"], "notifiationStatusArray": ["Build pipeline triggered", "Build pipeline succeeded", "Deployment pipeline triggered Pre-deployment", "Deployment pipeline triggered Deployment", "Deployment pipeline succeeded Deployment", "Deployment pipeline succeeded Post-deployment"], "notifiationStatusArraySlack": ["build pipeline triggered", "build pipeline successful", "deployment pipeline triggered", "deployment pipeline successful", "build pipeline failed", "deployment pipeline failed"], "cdFilterConditions": ["'reen' in image<PERSON><PERSON><PERSON>", "containerRepository.contains('test')", "containerImageTag.startsWith('8a1869')", "containerImage.contains('quay')"], "cdFilterEnv": ["automation", "All existing + future prod environments", "All existing + future non-prod environments"], "notificationFilter": {"application": "", "project": "", "environment": "", "cluster": "default_cluster"}, "securityPolicyFilter": {"Global": ""}, "vulnerabilitiesLabel": {"Critical": "", "High": "", "Medium": "", "Low": ""}, "securitySidebarImageScan": ["<PERSON> Scan", "Vulnerability", "License Risks"], "securitySidebarCodeScan": ["Code Scan", "Vulnerability", "License Risks", "Misconfigurations", "Exposed Secrets"], "securitySidebarKubernetesManifest": ["<PERSON><PERSON><PERSON><PERSON>", "Misconfigurations", "Exposed Secrets"], "resourceBrowserData": {"SecretUserName": "dXNlcm5hbWU=", "SecretPassword": "", "SecretUpdatedUserName": "YWRtaW4=", "SecretUpdatedPassword": "cGFzcw==", "SecretDecodedUserName": "admin", "SecretDecodedPassword": "admin", "DeploymentContainerImage": "nginx:latest", "DeploymentReplicas": "5", "DeploymentContainerName": "nginx", "NamespaceForAllResources": "automation"}, "emailCreateUserArray": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "registryData": {"EcrRegistry": {"registryName": "", "publicRepoList": ["nginx", "prometheus"], "privateRepoList": ["amit-qa-test"], "pushHelmPackages": true, "useAsChartRepo": true, "useAsDefaultRegistry": false}, "DockerRegistry": {"registryName": "", "publicRepoList": ["nginx", "prometheus"], "privateRepoList": [], "pushHelmPackages": true, "useAsChartRepo": true, "useAsDefaultRegistry": false}, "AzureRegistry": {"registryName": "devtron-azure", "publicRepoList": ["nginx", "prometheus"], "privateRepoList": ["nginx", "prometheus"], "pushHelmPackages": true, "useAsChartRepo": true, "useAsDefaultRegistry": false}, "GCPRegistry": {"registryName": "devtron-gcp", "publicRepoList": ["nginx", "prometheus"], "privateRepoList": ["nginx", "prometheus"], "pushHelmPackages": true, "useAsChartRepo": true, "useAsDefaultRegistry": false}, "GCRRegistry": {"registryName": "devtron-gcr"}, "QuayRegistry": {"registryName": "devtron-quay", "publicRepoList": ["nginx", "prometheus"], "privateRepoList": ["nginx", "prometheus"], "pushHelmPackages": true, "useAsChartRepo": true, "useAsDefaultRegistry": false}, "HarborRegistry": {"registryName": "devtron-harbor", "publicRepoList": ["nginx", "prometheus"], "privateRepoList": ["nginx", "prometheus"], "pushHelmPackages": false, "useAsChartRepo": false, "useAsDefaultRegistry": false, "caCertificate": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIDFDCCAfygAwIBAgIRAPub6zFH2b6z5iWGgEa6HkcwDQYJKoZIhvcNAQELBQAw\nFDESMBAGA1UEAxMJaGFyYm9yLWNhMB4XDTI0MDUyODEyNTcxOFoXDTI1MDUyODEy\nNTcxOFowFDESMBAGA1UEAxMJaGFyYm9yLWNhMIIBIjANBgkqhkiG9w0BAQEFAAOC\nAQ8AMIIBCgKCAQEAqPpeE2tpBwhlntEqPSXsAnkfXbD8f6eM4lvuGhLrThQ52I+9\nW+YGDIB4DBD+8XlYSiKsQnrZjCg5/MuGMaELArqukKGHhsZKB7gnmlU9InjPlCN+\ndZLe/qt01p1fRKePSR+nvCwboZJz9YNKWV6ylZQBA1Yqu+fbSjc4NvaH2moD1Guo\nyriduURLXDoBTyUR1WDsYq7iTu7dsO49JhArbed3X4FleSY1b8WmIXMxbxMgWvl6\nltEEFdQmZqDrNDdtGQCE/ep1ykREOs39WgUx8CnnAASF6vCjajolqYvLM0x2M7W/\nU7wfOuvEkSto+ls5bow8CY/Qi0+1ADpZfamltwIDAQABo2EwXzAOBgNVHQ8BAf8E\nBAMCAqQwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMCMA8GA1UdEwEB/wQF\nMAMBAf8wHQYDVR0OBBYEFM9Rf87XbNEPYwj8BKojUwR0IppGMA0GCSqGSIb3DQEB\nCwUAA4IBAQBSUOYbdJPcbGgX0n0H+A2g7vuZjG0vuO5biB6PxWu5ChKxjEg7F+5X\nsRVEW/IhgqbBzEZ21L8xMRPZuL8H8QmPKLVc/y+DF4EAoMilrQCbe5E4swgLFNRM\nmWcVB+fggwRAt/uFVPmOc2PKHdd0GSuB1k0nAnTeIfipB+UELZE0H6ZpH5F6Bqnt\nv6rfbhrZ03ATYWlaXW+ykg4auHBKnYRZ4PGwaMV3GgSGPNuQuXQydQz5ughCaDdj\nhW4kxSzNEWe7X0/0KZVms4vcakM22+RFLD41eTABUgYClFtCnxXIlHlxaAZxnVx/\nf1PZ1DMpAHCTftmH48CzGdLmYmPkX8DW\n-----END CERTIFICATE-----"}}, "skopeoData": [{"parentNode": "ci", "stage": "post", "destination_path": "bains|deep10/reen", "nodeNumber": 0, "imageRegistryAndRepo": "docker.io/deep10/reen", "environment": "automation", "iterationCount": 1}, {"parentNode": "automation", "stage": "pre", "destination_path": "bains|deep10/teen", "prePostNodeNumber": 0, "nodeNumber": 1, "imageRegistryAndRepo": "docker.io/deep10/teen", "environment": "automation", "imageCountTobeVisible": 2, "iterationCount": 2}, {"parentNode": "automation", "stage": "post", "destination_path": "bains|deep10/seen", "prePostNodeNumber": 2, "nodeNumber": 3, "imageRegistryAndRepo": "docker.io/deep10/seen", "environment": "devtron-demo", "imageCountTobeVisible": 1, "iterationCount": 2}], "configMapOrSecretData": [{"resource": "ConfigMap", "stage": "base-configurations", "resourceType": "Kubernetes ConfigMap", "EnvVariableOrDataVolume": "EnvironmentVariable", "resourceName": "configmap-non-ext-env", "mode": "gui", "Data": ["checkCmKeyenv: checkCmValueenv", "configenv: mapenv"], "TerminalScript": ["echo $checkCmKeyenv"], "TerminalExpectedValue": "checkCmValueenv"}, {"resource": "ConfigMap", "stage": "base-configurations", "resourceType": "Kubernetes ConfigMap", "EnvVariableOrDataVolume": "DataVolume", "resourceName": "configmap-non-ext-datavol", "mountPath": "/reen", "setSubPath": true, "setFilePermission": false, "filePermission": "777", "mode": "gui", "Data": ["checkCmKeydatavol: checkCmValuedatavol", "configdatavol: mapdatavol"], "TerminalScript": ["cd reen", "cat checkCmKeydatavol"], "TerminalExpectedValue": "checkCmValuedatavol"}, {"resource": "ConfigMap", "stage": "base-configurations", "resourceType": "Kubernetes External ConfigMap", "EnvVariableOrDataVolume": "EnvironmentVariable", "resourceName": "configmap-ext-env", "TerminalScript": ["echo $key1"], "TerminalExpectedValue": "value1"}, {"resource": "ConfigMap", "stage": "base-configurations", "resourceType": "Kubernetes External ConfigMap", "EnvVariableOrDataVolume": "DataVolume", "resourceName": "configmap-ext-datavol", "mountPath": "/dir", "setSubPath": true, "subPath": "key2", "setFilePermission": false, "filePermission": "777", "TerminalScript": ["cd dir", "cat key2"], "TerminalExpectedValue": "value2"}, {"resource": "Secret", "stage": "base-configurations", "resourceType": "Kubernetes Secret", "EnvVariableOrDataVolume": "EnvironmentVariable", "resourceName": "secret-non-ext-env", "mode": "gui", "Data": ["checkSecretKeyenv: checkSecretValueenv", "secretenv: secretValueenv"], "TerminalScript": ["echo $checkSecretKeyenv"], "TerminalExpectedValue": "checkSecretValueenv"}, {"resource": "Secret", "stage": "base-configurations", "resourceType": "Kubernetes Secret", "EnvVariableOrDataVolume": "DataVolume", "resourceName": "secret-non-ext-datavol", "mountPath": "/been", "setSubPath": true, "setFilePermission": false, "filePermission": "777", "mode": "gui", "Data": ["checkSecretKeydatavol: checkSecretValuedatavol", "secretdatavol: secretValuedatavol"], "TerminalScript": ["cd been", "cat checkSecretKeydatavol"], "TerminalExpectedValue": "checkSecretValuedatavol"}, {"resource": "Secret", "stage": "base-configurations", "resourceType": "Mount Existing Kubernetes Secret", "EnvVariableOrDataVolume": "EnvironmentVariable", "resourceName": "secret-ext-env", "TerminalScript": ["echo $username"], "TerminalExpectedValue": "username"}, {"resource": "Secret", "stage": "base-configurations", "resourceType": "Mount Existing Kubernetes Secret", "EnvVariableOrDataVolume": "DataVolume", "resourceName": "secret-ext-datavol", "mountPath": "/seen", "setSubPath": true, "subPath": "username", "setFilePermission": false, "filePermission": "777", "TerminalScript": ["cd seen", "cat username"], "TerminalExpectedValue": "username"}], "securityScanData": [{"gitRepoUrl": "https://github.com/kushagra-devtron/sample-php-app", "imageScanVul": true, "imageScanLicense": true, "codeScanVul": true, "codeScanLicense": true, "codeScanMisconfig": true, "exposedSecret": true, "kubernetesManifestmisconfig": true, "kubernetesManifestexposedSecret": false}, {"gitRepoUrl": "https://github.com/kushagra-devtron/gradle-sample-app", "imageScanVul": true, "imageScanLicense": false, "codeScanVul": false, "codeScanLicense": false, "codeScanMisconfig": true, "exposedSecret": false, "kubernetesManifestmisconfig": true, "kubernetesManifestexposedSecret": false}, {"gitRepoUrl": "https://github.com/devtron-labs/kubelink", "imageScanVul": true, "imageScanLicense": false, "codeScanVul": false, "codeScanLicense": false, "codeScanMisconfig": true, "exposedSecret": false, "kubernetesManifestmisconfig": true, "kubernetesManifestexposedSecret": false}, {"gitRepoUrl": "https://github.com/devtron-labs/getting-started-nodejs", "imageScanVul": true, "imageScanLicense": true, "codeScanVul": false, "codeScanLicense": false, "codeScanMisconfig": true, "exposedSecret": false, "kubernetesManifestmisconfig": true, "kubernetesManifestexposedSecret": false}], "securityScanHelmAppData": {"chartName": "apache", "imageScanVul": true, "imageScanLicense": true, "kubernetesManifestmisconfig": true, "kubernetesManifestexposedSecret": false}, "RuntimeParametersData": [{"stage": "post", "condition": "fail", "keyValuePair": ["postfail", "500"], "taskName": "post-fail-condition", "status": "Failed", "script": ["echo $postfail"], "outputVariableType": ["Number"], "verificationTextForLogs": ["500"]}, {"stage": "pre", "condition": "pass", "keyValuePair": ["prepass", "400"], "taskName": "pre-pass-condition", "status": "Succeeded", "script": ["echo $prepass", "echo $testcm"], "outputVariableType": ["Number"], "verificationTextForLogs": ["400", "testvalue"]}], "buildxData": [{"setTargetPlatform": "", "logsVerificationText": ["COPY --from=builder /app/. ."], "sourceOfImage": "Create Docker<PERSON>le"}, {"setTargetPlatform": "", "sourceOfImage": "Build without Dockerfile", "logsVerificationText": ["GOOGLE_RUNTIME_VERSION=1.19"], "configuration": {"language": "Go", "builder": "gcr.io/buildpacks/builder:v1", "version": "1.19"}}, {"setTargetPlatform": "linux/amd64", "sourceOfImage": "have a Dockerfile", "logsVerificationText": ["testing"], "envVariables": ["MY_VAR:testing"]}], "ephimeralContainer": [{"containerNamePrefix": "", "containerImage": "", "isSuccessfull": true, "targetContainerName": ""}, {"containerNamePrefix": "playwright", "containerImage": "Alpine: Kubernetes utilites", "isSuccessfull": true, "targetContainerName": ""}, {"containerNamePrefix": "playwright", "containerImage": "Alpine: Kubernetes utilites", "isSuccessfull": false, "targetContainerName": "playwright"}], "globalCMCSdata": [{"name": "automation-test", "data": {"key": "testcm", "value": "testvalue"}, "configOrSecret": "CONFIGMAP", "cicd": "CI/CD"}], "ciCdNodeTermination": "key:value12345", "GlobalApplicationName": "recommendation-dcjhcj"}