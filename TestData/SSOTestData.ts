import { SSOProvidersEnum } from '../enums/GlobalConfigurations/Authorization/SSOProvidersEnum';

export type SSOConfigParams = {
  [key: string]: string | boolean | string[];
};

export const ssoYamlTemplates: Record<SSOProvidersEnum, (params: SSOConfigParams) => string> = {
  [SSOProvidersEnum.GOOGLE]: ({ clientID, clientSecret, redirectURI }) => `
clientID: ${clientID}
clientSecret: ${clientSecret}
redirectURI: ${redirectURI}
`.trim(),

  [SSOProvidersEnum.GITHUB]: ({ clientID, clientSecret, redirectURI }) => `
clientID: ${clientID}
clientSecret: ${clientSecret}
redirectURI: ${redirectURI}
`.trim(),

  [SSOProvidersEnum.GITLAB]: ({ baseURL, clientID, clientSecret, redirectURI, groups, useLoginAsID }) => `
baseURL: ${baseURL}
clientID: ${clientID}
clientSecret: ${clientSecret}
redirectURI: ${redirectURI}
groups:
${(groups as string[]).map(group => `  - ${group}`).join('\n')}
useLoginAsID: ${useLoginAsID}
`.trim(),

  [SSOProvidersEnum.MICROSOFT]: ({ clientID, clientSecret, tenant, redirectURI }) => `
clientID: ${clientID}
clientSecret: ${clientSecret}
tenant: ${tenant}
redirectURI: ${redirectURI}
`.trim(),
};
