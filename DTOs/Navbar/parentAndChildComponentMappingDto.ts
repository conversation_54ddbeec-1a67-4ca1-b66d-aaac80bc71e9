import { AiRecommendationsChildComponentEnums, applicationManagementChildComponentEnum, AutomationAndEnablementChildComponentEnums, BackupAndRestoreChildComponentEnums, costVisibilityChildComponentsEnum, globalConfigurationsChildComponentEnums, infrastructureManagementChildComponentsEnum, securityCenterChildComponentsEnum, softwareReleaseManagementChildComponentsEnum } from "../../enums/Navbar/childComponentEnum";
import { sidePanelParentComponentsEnum } from "../../enums/Navbar/sidePanelParentComponentsEnum";

export interface parentChildMapping{
    [sidePanelParentComponentsEnum.applicationManagement]:applicationManagementChildComponentEnum,
    [sidePanelParentComponentsEnum.infrastructureManagement]:infrastructureManagementChildComponentsEnum,
    [sidePanelParentComponentsEnum.AutomationAndEnablement]:AutomationAndEnablementChildComponentEnums
    [sidePanelParentComponentsEnum.globalConfigurations]:globalConfigurationsChildComponentEnums
    [sidePanelParentComponentsEnum.securityCenter]:securityCenterChildComponentsEnum
    [sidePanelParentComponentsEnum.AiRecommendations]:AiRecommendationsChildComponentEnums
    [sidePanelParentComponentsEnum.globalSearch]:null,
    [sidePanelParentComponentsEnum.Overview]:null,
    [sidePanelParentComponentsEnum.softwareReleaseManagement]:softwareReleaseManagementChildComponentsEnum,
    [sidePanelParentComponentsEnum.costVisibility]:costVisibilityChildComponentsEnum,
    [sidePanelParentComponentsEnum.BackupAndRestore]:BackupAndRestoreChildComponentEnums
   
}