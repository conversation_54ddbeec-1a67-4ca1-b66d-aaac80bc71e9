import { SourceTypeEnum } from '../../../enums/Application Management/Applications/SourceTypeEnum';
import { BaseCdModuleDTO } from './AddCdModuleDTO'; 

// Base configuration interface for all workflows
export interface BaseWorkflowConfigDTO {
  cdConfig?: BaseCdModuleDTO;
}

// Config for 'build-deploy-from' workflow
export interface BuildDeployFromSourceCodeConfigDTO extends BaseWorkflowConfigDTO {
  sourceTypeEnum: SourceTypeEnum;
  branchNamesList: string[];
}

// Config for 'linked-build' workflow
export interface LinkedPipelineConfigDTO extends BaseWorkflowConfigDTO {
  linkedPipelineName: string;
  sourcePipelineName: string;
  appName: string;
  checkValidation: boolean;
}

// Config for 'deploy-image' workflow
export interface DeployImageFromExternalServiceConfigDTO extends BaseWorkflowConfigDTO {
  // cdConfig is required for this case, as per your existing logic
  cdConfig: BaseCdModuleDTO;
}

// Config for 'linked-cd' workflow
export interface LinkedCdConfigDTO extends BaseWorkflowConfigDTO {
  sourceEnv: string;
  destEnv: string;
}


export interface JobCiConfigDTO extends BaseWorkflowConfigDTO {
  pipelineName: string;
  branchName: string;
  script: string[];
}

// Union type for workflow configurations
export interface WorkflowConfigDTO {
  buildDeployFromSourceCodeConfigDTO?: BuildDeployFromSourceCodeConfigDTO;
  linkedBuildPipelineConfigDTO?: LinkedPipelineConfigDTO;
  deployImageFromExternalServiceConfigDTO?: DeployImageFromExternalServiceConfigDTO;
  linkedCdConfigDTO?: LinkedCdConfigDTO;
  jobCiConfigDTO?: JobCiConfigDTO;
}