import { DeploymentStrategyEnum } from "../../../enums/Application Management/Applications/DeploymentStrategyEnum";
import { DeploymentTriggerEnum } from "../../../enums/Application Management/Applications/DeploymentTriggerEnum";
import { HelmOrGitopsCdModuleDTO } from "./HelmOrGitopsCdModuleDTO";


export interface BaseCdModuleDTO {
  envNameForCd: string;
  clusterName: string;
  deploymentStrategyEnum: DeploymentStrategyEnum;
  deploymentTriggerEnum: DeploymentTriggerEnum;
}

export interface AddCdModuleDTO extends BaseCdModuleDTO {
  HelmOrGitopsCdModuleDTO:HelmOrGitopsCdModuleDTO
}





