import { JobsPage } from "../../../Pages/AutomationandEnablement/Jobs"

export interface allowOrDeleteOverrideDTO {
    configuration: { allowOverrideOrDelete: 'delete' } | { allowOverrideOrDelete: 'allow', replaceMergerStrat: boolean }
}
export interface editDTCMCSDTO {
    resType: string,
    resName: string,
    fieldsToEdit: string[],
    valuesToEdit: string[],
    stage: 'base-configurations' | string,
    jobsPage: JobsPage,
    mergeStrat?: allowOrDeleteOverrideDTO
}

export interface expressEditDTCMCS {
    configuration: {
        isEligible: true,
        editRelatedData: editDTCMCSDTO
    } | {
        isEligible: false
    }
}