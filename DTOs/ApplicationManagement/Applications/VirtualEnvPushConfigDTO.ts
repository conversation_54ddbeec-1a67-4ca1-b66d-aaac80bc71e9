import { PushBehaviorEnum } from "../../../enums/ApplicationManagement/Applications/PushBehaviorEnum";

// Base DTO interface for VirtualEnvPushConfig
export interface BaseVirtualEnvPushConfigDTO {
  pushOrNot: PushBehaviorEnum;
}

// DTO for "Do not push" configuration
export interface DoNotPushVirtualEnvPushConfigDTO extends BaseVirtualEnvPushConfigDTO {
  pushOrNot: PushBehaviorEnum.DO_NOT_PUSH;
}

// DTO for "Push to registry" configuration
export interface PushToRegistryVirtualEnvPushConfigDTO extends BaseVirtualEnvPushConfigDTO {
  pushOrNot: PushBehaviorEnum.PUSH_TO_REGISTRY;
  regName: string;
  repoName: string;
}

// Union type for VirtualEnvPushConfigDTO (Discriminated Union)
export interface VirtualEnvPushConfigDTO {
  DoNotPushVirtualEnvPushConfigDTO?:DoNotPushVirtualEnvPushConfigDTO,
  PushToRegistryVirtualEnvPushConfigDTO?:PushToRegistryVirtualEnvPushConfigDTO
}
