import { globalSearchComponentParentEntities } from "../enums/GlobalSearchComponentEnum";
import { AiRecommendationsChildComponentEnums, applicationManagementChildComponentEnum, AutomationAndEnablementChildComponentEnums, BackupAndRestoreChildComponentEnums, costVisibilityChildComponentsEnum, globalConfigurationsChildComponentEnums, infrastructureManagementChildComponentsEnum, securityCenterChildComponentsEnum, softwareReleaseManagementChildComponentsEnum } from "../enums/Navbar/childComponentEnum";

export interface globalSearchComponentParentChildMapping{
    [globalSearchComponentParentEntities.applicationManagement]:applicationManagementChildComponentEnum,
    [globalSearchComponentParentEntities.infrastructureManagement]:infrastructureManagementChildComponentsEnum,
    [globalSearchComponentParentEntities.softwareReleaseManagement]:softwareReleaseManagementChildComponentsEnum,
    [globalSearchComponentParentEntities.costVisibility]:costVisibilityChildComponentsEnum,
    [globalSearchComponentParentEntities.securityCenter]:securityCenterChildComponentsEnum,
    [globalSearchComponentParentEntities.AutomationAndEnablement]:AutomationAndEnablementChildComponentEnums,
    [globalSearchComponentParentEntities.BackupAndRestore]:BackupAndRestoreChildComponentEnums,
    [globalSearchComponentParentEntities.AiRecommendations]:AiRecommendationsChildComponentEnums,
    [globalSearchComponentParentEntities.globalConfigurations]:globalConfigurationsChildComponentEnums
}